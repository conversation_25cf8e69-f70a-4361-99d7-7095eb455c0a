/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.exam.jooq.tables.PaperClass;
import com.zxy.product.exam.jooq.tables.interfaces.IPaperClass;
import com.zxy.product.exam.jooq.tables.records.PaperClassRecord;

import javax.annotation.Generated;


/**
 * 试卷类
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaperClassEntity extends BaseEntity implements IPaperClass {

    private static final long serialVersionUID = 1L;

    private String  organizationId;
    private String  name;
    private Integer status;
    private Integer isSubjective;
    private Integer totalScore;
    private Integer questionNum;
    private Integer type;
    private Integer    associatedState;

    public PaperClassEntity() {}

    public PaperClassEntity(PaperClassEntity value) {
        this.organizationId = value.organizationId;
        this.name = value.name;
        this.status = value.status;
        this.isSubjective = value.isSubjective;
        this.totalScore = value.totalScore;
        this.questionNum = value.questionNum;
        this.type = value.type;
        this.associatedState = value.associatedState;
    }

    public PaperClassEntity(
        String  id,
        Long    createTime,
        String  organizationId,
        String  name,
        Integer status,
        Integer isSubjective,
        Integer totalScore,
        Integer questionNum,
        Integer type,
        Integer    associatedState
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.organizationId = organizationId;
        this.name = name;
        this.status = status;
        this.isSubjective = isSubjective;
        this.totalScore = totalScore;
        this.questionNum = questionNum;
        this.type = type;
        this.associatedState = associatedState;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getIsSubjective() {
        return this.isSubjective;
    }

    @Override
    public void setIsSubjective(Integer isSubjective) {
        this.isSubjective = isSubjective;
    }

    @Override
    public Integer getTotalScore() {
        return this.totalScore;
    }

    @Override
    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    @Override
    public Integer getQuestionNum() {
        return this.questionNum;
    }

    @Override
    public void setQuestionNum(Integer questionNum) {
        this.questionNum = questionNum;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public Integer getAssociatedState() {
        return this.associatedState;
    }

    @Override
    public void setAssociatedState(Integer associatedState) {
        this.associatedState = associatedState;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("PaperClassEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(organizationId);
        sb.append(", ").append(name);
        sb.append(", ").append(status);
        sb.append(", ").append(isSubjective);
        sb.append(", ").append(totalScore);
        sb.append(", ").append(questionNum);
        sb.append(", ").append(type);
        sb.append(", ").append(associatedState);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPaperClass from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setOrganizationId(from.getOrganizationId());
        setName(from.getName());
        setStatus(from.getStatus());
        setIsSubjective(from.getIsSubjective());
        setTotalScore(from.getTotalScore());
        setQuestionNum(from.getQuestionNum());
        setType(from.getType());
        setAssociatedState(from.getAssociatedState());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPaperClass> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends PaperClassEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                PaperClassRecord r = new PaperClassRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.ID, record.getValue(PaperClass.PAPER_CLASS.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.CREATE_TIME, record.getValue(PaperClass.PAPER_CLASS.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.ORGANIZATION_ID, record.getValue(PaperClass.PAPER_CLASS.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.NAME, record.getValue(PaperClass.PAPER_CLASS.NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.STATUS, record.getValue(PaperClass.PAPER_CLASS.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.IS_SUBJECTIVE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.IS_SUBJECTIVE, record.getValue(PaperClass.PAPER_CLASS.IS_SUBJECTIVE));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.TOTAL_SCORE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.TOTAL_SCORE, record.getValue(PaperClass.PAPER_CLASS.TOTAL_SCORE));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.QUESTION_NUM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.QUESTION_NUM, record.getValue(PaperClass.PAPER_CLASS.QUESTION_NUM));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.TYPE, record.getValue(PaperClass.PAPER_CLASS.TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(PaperClass.PAPER_CLASS.ASSOCIATED_STATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(PaperClass.PAPER_CLASS.ASSOCIATED_STATE, record.getValue(PaperClass.PAPER_CLASS.ASSOCIATED_STATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
