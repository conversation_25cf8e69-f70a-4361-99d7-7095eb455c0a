/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.exam.jooq.tables.CertificateRecord;
import com.zxy.product.exam.jooq.tables.interfaces.ICertificateRecord;
import com.zxy.product.exam.jooq.tables.records.CertificateRecordRecord;

import javax.annotation.Generated;
import java.sql.Timestamp;


/**
 * 证书发放记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CertificateRecordEntity extends BaseEntity implements ICertificateRecord {

    private static final long serialVersionUID = 1L;

    private String    examId;
    private String    memberId;
    private String    num;
    private String    professionId;
    private String    subProfessionId;
    private String    equipmentTypeId;
    private String    professionLevelId;
    private Integer   score;
    private Integer      scoreLevel;
    private Integer      accessType;
    private Integer      passStatus;
    private Long      issueTime;
    private Long      validDate;
    private String    reason;
    private Integer      isCurrent;
    private String    templateId;
    private Integer      historyFlag;
    private String    historyId;
    private String    name;
    private Long      passTime;
    private Integer   cloud;
    private Integer   grid;
    private Timestamp modifyDate;

    public CertificateRecordEntity() {}

    public CertificateRecordEntity(CertificateRecordEntity value) {
        this.examId = value.examId;
        this.memberId = value.memberId;
        this.num = value.num;
        this.professionId = value.professionId;
        this.subProfessionId = value.subProfessionId;
        this.equipmentTypeId = value.equipmentTypeId;
        this.professionLevelId = value.professionLevelId;
        this.score = value.score;
        this.scoreLevel = value.scoreLevel;
        this.accessType = value.accessType;
        this.passStatus = value.passStatus;
        this.issueTime = value.issueTime;
        this.validDate = value.validDate;
        this.reason = value.reason;
        this.isCurrent = value.isCurrent;
        this.templateId = value.templateId;
        this.historyFlag = value.historyFlag;
        this.historyId = value.historyId;
        this.name = value.name;
        this.passTime = value.passTime;
        this.cloud = value.cloud;
        this.grid = value.grid;
        this.modifyDate = value.modifyDate;
    }

    public CertificateRecordEntity(
        String    id,
        String    examId,
        String    memberId,
        String    num,
        String    professionId,
        String    subProfessionId,
        String    equipmentTypeId,
        String    professionLevelId,
        Integer   score,
        Integer      scoreLevel,
        Integer      accessType,
        Integer      passStatus,
        Long      issueTime,
        Long      validDate,
        String    reason,
        Long      createTime,
        Integer      isCurrent,
        String    templateId,
        Integer      historyFlag,
        String    historyId,
        String    name,
        Long      passTime,
        Integer   cloud,
        Integer   grid,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.examId = examId;
        this.memberId = memberId;
        this.num = num;
        this.professionId = professionId;
        this.subProfessionId = subProfessionId;
        this.equipmentTypeId = equipmentTypeId;
        this.professionLevelId = professionLevelId;
        this.score = score;
        this.scoreLevel = scoreLevel;
        this.accessType = accessType;
        this.passStatus = passStatus;
        this.issueTime = issueTime;
        this.validDate = validDate;
        this.reason = reason;
        super.setCreateTime(createTime);
        this.isCurrent = isCurrent;
        this.templateId = templateId;
        this.historyFlag = historyFlag;
        this.historyId = historyId;
        this.name = name;
        this.passTime = passTime;
        this.cloud = cloud;
        this.grid = grid;
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getExamId() {
        return this.examId;
    }

    @Override
    public void setExamId(String examId) {
        this.examId = examId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getNum() {
        return this.num;
    }

    @Override
    public void setNum(String num) {
        this.num = num;
    }

    @Override
    public String getProfessionId() {
        return this.professionId;
    }

    @Override
    public void setProfessionId(String professionId) {
        this.professionId = professionId;
    }

    @Override
    public String getSubProfessionId() {
        return this.subProfessionId;
    }

    @Override
    public void setSubProfessionId(String subProfessionId) {
        this.subProfessionId = subProfessionId;
    }

    @Override
    public String getEquipmentTypeId() {
        return this.equipmentTypeId;
    }

    @Override
    public void setEquipmentTypeId(String equipmentTypeId) {
        this.equipmentTypeId = equipmentTypeId;
    }

    @Override
    public String getProfessionLevelId() {
        return this.professionLevelId;
    }

    @Override
    public void setProfessionLevelId(String professionLevelId) {
        this.professionLevelId = professionLevelId;
    }

    @Override
    public Integer getScore() {
        return this.score;
    }

    @Override
    public void setScore(Integer score) {
        this.score = score;
    }

    @Override
    public Integer getScoreLevel() {
        return this.scoreLevel;
    }

    @Override
    public void setScoreLevel(Integer scoreLevel) {
        this.scoreLevel = scoreLevel;
    }

    @Override
    public Integer getAccessType() {
        return this.accessType;
    }

    @Override
    public void setAccessType(Integer accessType) {
        this.accessType = accessType;
    }

    @Override
    public Integer getPassStatus() {
        return this.passStatus;
    }

    @Override
    public void setPassStatus(Integer passStatus) {
        this.passStatus = passStatus;
    }

    @Override
    public Long getIssueTime() {
        return this.issueTime;
    }

    @Override
    public void setIssueTime(Long issueTime) {
        this.issueTime = issueTime;
    }

    @Override
    public Long getValidDate() {
        return this.validDate;
    }

    @Override
    public void setValidDate(Long validDate) {
        this.validDate = validDate;
    }

    @Override
    public String getReason() {
        return this.reason;
    }

    @Override
    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Integer getIsCurrent() {
        return this.isCurrent;
    }

    @Override
    public void setIsCurrent(Integer isCurrent) {
        this.isCurrent = isCurrent;
    }

    @Override
    public String getTemplateId() {
        return this.templateId;
    }

    @Override
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    @Override
    public Integer getHistoryFlag() {
        return this.historyFlag;
    }

    @Override
    public void setHistoryFlag(Integer historyFlag) {
        this.historyFlag = historyFlag;
    }

    @Override
    public String getHistoryId() {
        return this.historyId;
    }

    @Override
    public void setHistoryId(String historyId) {
        this.historyId = historyId;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Long getPassTime() {
        return this.passTime;
    }

    @Override
    public void setPassTime(Long passTime) {
        this.passTime = passTime;
    }

    @Override
    public Integer getCloud() {
        return this.cloud;
    }

    @Override
    public void setCloud(Integer cloud) {
        this.cloud = cloud;
    }

    @Override
    public Integer getGrid() {
        return this.grid;
    }

    @Override
    public void setGrid(Integer grid) {
        this.grid = grid;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CertificateRecordEntity (");

        sb.append(getId());
        sb.append(", ").append(examId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(num);
        sb.append(", ").append(professionId);
        sb.append(", ").append(subProfessionId);
        sb.append(", ").append(equipmentTypeId);
        sb.append(", ").append(professionLevelId);
        sb.append(", ").append(score);
        sb.append(", ").append(scoreLevel);
        sb.append(", ").append(accessType);
        sb.append(", ").append(passStatus);
        sb.append(", ").append(issueTime);
        sb.append(", ").append(validDate);
        sb.append(", ").append(reason);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(isCurrent);
        sb.append(", ").append(templateId);
        sb.append(", ").append(historyFlag);
        sb.append(", ").append(historyId);
        sb.append(", ").append(name);
        sb.append(", ").append(passTime);
        sb.append(", ").append(cloud);
        sb.append(", ").append(grid);
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICertificateRecord from) {
        setId(from.getId());
        setExamId(from.getExamId());
        setMemberId(from.getMemberId());
        setNum(from.getNum());
        setProfessionId(from.getProfessionId());
        setSubProfessionId(from.getSubProfessionId());
        setEquipmentTypeId(from.getEquipmentTypeId());
        setProfessionLevelId(from.getProfessionLevelId());
        setScore(from.getScore());
        setScoreLevel(from.getScoreLevel());
        setAccessType(from.getAccessType());
        setPassStatus(from.getPassStatus());
        setIssueTime(from.getIssueTime());
        setValidDate(from.getValidDate());
        setReason(from.getReason());
        setCreateTime(from.getCreateTime());
        setIsCurrent(from.getIsCurrent());
        setTemplateId(from.getTemplateId());
        setHistoryFlag(from.getHistoryFlag());
        setHistoryId(from.getHistoryId());
        setName(from.getName());
        setPassTime(from.getPassTime());
        setCloud(from.getCloud());
        setGrid(from.getGrid());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICertificateRecord> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CertificateRecordEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                CertificateRecordRecord r = new CertificateRecordRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.EXAM_ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.EXAM_ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.EXAM_ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.MEMBER_ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.MEMBER_ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.MEMBER_ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.NUM) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.NUM, record.getValue(CertificateRecord.CERTIFICATE_RECORD.NUM));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.PROFESSION_ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.PROFESSION_ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.PROFESSION_ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.SUB_PROFESSION_ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.SUB_PROFESSION_ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.SUB_PROFESSION_ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.PROFESSION_LEVEL_ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.PROFESSION_LEVEL_ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.PROFESSION_LEVEL_ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.SCORE) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.SCORE, record.getValue(CertificateRecord.CERTIFICATE_RECORD.SCORE));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.SCORE_LEVEL) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.SCORE_LEVEL, record.getValue(CertificateRecord.CERTIFICATE_RECORD.SCORE_LEVEL));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.ACCESS_TYPE) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.ACCESS_TYPE, record.getValue(CertificateRecord.CERTIFICATE_RECORD.ACCESS_TYPE));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.PASS_STATUS) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.PASS_STATUS, record.getValue(CertificateRecord.CERTIFICATE_RECORD.PASS_STATUS));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.ISSUE_TIME) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.ISSUE_TIME, record.getValue(CertificateRecord.CERTIFICATE_RECORD.ISSUE_TIME));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.VALID_DATE) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.VALID_DATE, record.getValue(CertificateRecord.CERTIFICATE_RECORD.VALID_DATE));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.REASON) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.REASON, record.getValue(CertificateRecord.CERTIFICATE_RECORD.REASON));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.CREATE_TIME) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.CREATE_TIME, record.getValue(CertificateRecord.CERTIFICATE_RECORD.CREATE_TIME));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.IS_CURRENT) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.IS_CURRENT, record.getValue(CertificateRecord.CERTIFICATE_RECORD.IS_CURRENT));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.TEMPLATE_ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.TEMPLATE_ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.TEMPLATE_ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.HISTORY_FLAG) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.HISTORY_FLAG, record.getValue(CertificateRecord.CERTIFICATE_RECORD.HISTORY_FLAG));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.HISTORY_ID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.HISTORY_ID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.HISTORY_ID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.NAME) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.NAME, record.getValue(CertificateRecord.CERTIFICATE_RECORD.NAME));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.PASS_TIME) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.PASS_TIME, record.getValue(CertificateRecord.CERTIFICATE_RECORD.PASS_TIME));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.CLOUD) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.CLOUD, record.getValue(CertificateRecord.CERTIFICATE_RECORD.CLOUD));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.GRID) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.GRID, record.getValue(CertificateRecord.CERTIFICATE_RECORD.GRID));
                    }
                    if(row.indexOf(CertificateRecord.CERTIFICATE_RECORD.MODIFY_DATE) > -1){
                        r.setValue(CertificateRecord.CERTIFICATE_RECORD.MODIFY_DATE, record.getValue(CertificateRecord.CERTIFICATE_RECORD.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
