/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.PaperClassTacticRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 组卷策略
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaperClassTactic extends TableImpl<PaperClassTacticRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_paper_class_tactic</code>
     */
    public static final PaperClassTactic PAPER_CLASS_TACTIC = new PaperClassTactic();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PaperClassTacticRecord> getRecordType() {
        return PaperClassTacticRecord.class;
    }

    /**
     * The column <code>exam.t_paper_class_tactic.f_id</code>. ID
     */
    public final TableField<PaperClassTacticRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_paper_class_tactic.f_create_time</code>. 创建时间	
     */
    public final TableField<PaperClassTacticRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间	");

    /**
     * The column <code>exam.t_paper_class_tactic.f_difficulty</code>. 难度
     */
    public final TableField<PaperClassTacticRecord, Integer> DIFFICULTY = createField("f_difficulty", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "难度");

    /**
     * The column <code>exam.t_paper_class_tactic.f_type</code>. 类型
     */
    public final TableField<PaperClassTacticRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "类型");

    /**
     * The column <code>exam.t_paper_class_tactic.f_amount</code>. 数量
     */
    public final TableField<PaperClassTacticRecord, Integer> AMOUNT = createField("f_amount", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "数量");

    /**
     * The column <code>exam.t_paper_class_tactic.f_score</code>. 分数
     */
    public final TableField<PaperClassTacticRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分数");

    /**
     * The column <code>exam.t_paper_class_tactic.f_paper_class_id</code>.
     */
    public final TableField<PaperClassTacticRecord, String> PAPER_CLASS_ID = createField("f_paper_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_paper_class_tactic.f_question_depot_id</code>.
     */
    public final TableField<PaperClassTacticRecord, String> QUESTION_DEPOT_ID = createField("f_question_depot_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_paper_class_tactic.f_organization_id</code>.
     */
    public final TableField<PaperClassTacticRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * Create a <code>exam.t_paper_class_tactic</code> table reference
     */
    public PaperClassTactic() {
        this("t_paper_class_tactic", null);
    }

    /**
     * Create an aliased <code>exam.t_paper_class_tactic</code> table reference
     */
    public PaperClassTactic(String alias) {
        this(alias, PAPER_CLASS_TACTIC);
    }

    private PaperClassTactic(String alias, Table<PaperClassTacticRecord> aliased) {
        this(alias, aliased, null);
    }

    private PaperClassTactic(String alias, Table<PaperClassTacticRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "组卷策略");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PaperClassTacticRecord> getPrimaryKey() {
        return Keys.KEY_T_PAPER_CLASS_TACTIC_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PaperClassTacticRecord>> getKeys() {
        return Arrays.<UniqueKey<PaperClassTacticRecord>>asList(Keys.KEY_T_PAPER_CLASS_TACTIC_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PaperClassTactic as(String alias) {
        return new PaperClassTactic(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PaperClassTactic rename(String name) {
        return new PaperClassTactic(name, null);
    }
}
