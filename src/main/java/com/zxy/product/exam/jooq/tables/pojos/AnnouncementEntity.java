/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.exam.jooq.tables.Announcement;
import com.zxy.product.exam.jooq.tables.interfaces.IAnnouncement;
import com.zxy.product.exam.jooq.tables.records.AnnouncementRecord;

import javax.annotation.Generated;


/**
 * 通知公告
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnnouncementEntity extends BaseEntity implements IAnnouncement {

    private static final long serialVersionUID = 1L;

    private String title;
    private String description;
    private Long   startTime;
    private Long   endTime;
    private String createMemberId;
    private Integer   topStatus;
    private String organizationId;

    public AnnouncementEntity() {}

    public AnnouncementEntity(AnnouncementEntity value) {
        this.title = value.title;
        this.description = value.description;
        this.startTime = value.startTime;
        this.endTime = value.endTime;
        this.createMemberId = value.createMemberId;
        this.topStatus = value.topStatus;
        this.organizationId = value.organizationId;
    }

    public AnnouncementEntity(
        String id,
        Long   createTime,
        String title,
        String description,
        Long   startTime,
        Long   endTime,
        String createMemberId,
        Integer   topStatus,
        String organizationId
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.title = title;
        this.description = description;
        this.startTime = startTime;
        this.endTime = endTime;
        this.createMemberId = createMemberId;
        this.topStatus = topStatus;
        this.organizationId = organizationId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getTitle() {
        return this.title;
    }

    @Override
    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Long getStartTime() {
        return this.startTime;
    }

    @Override
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    @Override
    public Long getEndTime() {
        return this.endTime;
    }

    @Override
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    @Override
    public String getCreateMemberId() {
        return this.createMemberId;
    }

    @Override
    public void setCreateMemberId(String createMemberId) {
        this.createMemberId = createMemberId;
    }

    @Override
    public Integer getTopStatus() {
        return this.topStatus;
    }

    @Override
    public void setTopStatus(Integer topStatus) {
        this.topStatus = topStatus;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AnnouncementEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(title);
        sb.append(", ").append(description);
        sb.append(", ").append(startTime);
        sb.append(", ").append(endTime);
        sb.append(", ").append(createMemberId);
        sb.append(", ").append(topStatus);
        sb.append(", ").append(organizationId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAnnouncement from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setTitle(from.getTitle());
        setDescription(from.getDescription());
        setStartTime(from.getStartTime());
        setEndTime(from.getEndTime());
        setCreateMemberId(from.getCreateMemberId());
        setTopStatus(from.getTopStatus());
        setOrganizationId(from.getOrganizationId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAnnouncement> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends AnnouncementEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                AnnouncementRecord r = new AnnouncementRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.ID, record.getValue(Announcement.ANNOUNCEMENT.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.CREATE_TIME, record.getValue(Announcement.ANNOUNCEMENT.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.TITLE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.TITLE, record.getValue(Announcement.ANNOUNCEMENT.TITLE));});
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.DESCRIPTION.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.DESCRIPTION, record.getValue(Announcement.ANNOUNCEMENT.DESCRIPTION));});
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.START_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.START_TIME, record.getValue(Announcement.ANNOUNCEMENT.START_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.END_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.END_TIME, record.getValue(Announcement.ANNOUNCEMENT.END_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.CREATE_MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.CREATE_MEMBER_ID, record.getValue(Announcement.ANNOUNCEMENT.CREATE_MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.TOP_STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.TOP_STATUS, record.getValue(Announcement.ANNOUNCEMENT.TOP_STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(Announcement.ANNOUNCEMENT.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(Announcement.ANNOUNCEMENT.ORGANIZATION_ID, record.getValue(Announcement.ANNOUNCEMENT.ORGANIZATION_ID));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
