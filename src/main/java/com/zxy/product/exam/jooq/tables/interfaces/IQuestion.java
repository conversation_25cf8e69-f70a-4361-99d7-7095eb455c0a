/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IQuestion extends Serializable {

    /**
     * Setter for <code>exam.t_question.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_question.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>exam.t_question.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_question.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_question.f_type</code>. 类型
     */
    public void setType(Integer value);

    /**
     * Getter for <code>exam.t_question.f_type</code>. 类型
     */
    public Integer getType();

    /**
     * Setter for <code>exam.t_question.f_content</code>. 试题题干，富文本格式
     */
    public void setContent(String value);

    /**
     * Getter for <code>exam.t_question.f_content</code>. 试题题干，富文本格式
     */
    public String getContent();

    /**
     * Setter for <code>exam.t_question.f_is_subjective</code>. 是否主观题
     */
    public void setIsSubjective(Integer value);

    /**
     * Getter for <code>exam.t_question.f_is_subjective</code>. 是否主观题
     */
    public Integer getIsSubjective();

    /**
     * Setter for <code>exam.t_question.f_parent_id</code>. 父题
     */
    public void setParentId(String value);

    /**
     * Getter for <code>exam.t_question.f_parent_id</code>. 父题
     */
    public String getParentId();

    /**
     * Setter for <code>exam.t_question.f_question_depot_id</code>. 题库
     */
    public void setQuestionDepotId(String value);

    /**
     * Getter for <code>exam.t_question.f_question_depot_id</code>. 题库
     */
    public String getQuestionDepotId();

    /**
     * Setter for <code>exam.t_question.f_difficulty</code>. 难度
     */
    public void setDifficulty(Integer value);

    /**
     * Getter for <code>exam.t_question.f_difficulty</code>. 难度
     */
    public Integer getDifficulty();

    /**
     * Setter for <code>exam.t_question.f_organization_id</code>. 所属部门
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>exam.t_question.f_organization_id</code>. 所属部门
     */
    public String getOrganizationId();

    /**
     * Setter for <code>exam.t_question.f_score</code>. 分数
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>exam.t_question.f_score</code>. 分数
     */
    public Integer getScore();

    /**
     * Setter for <code>exam.t_question.f_error_rate</code>. 易错率
     */
    public void setErrorRate(Integer value);

    /**
     * Getter for <code>exam.t_question.f_error_rate</code>. 易错率
     */
    public Integer getErrorRate();

    /**
     * Setter for <code>exam.t_question.f_status</code>. 状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>exam.t_question.f_status</code>. 状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>exam.t_question.f_mark_amount</code>. 收藏
     */
    public void setMarkAmount(Integer value);

    /**
     * Getter for <code>exam.t_question.f_mark_amount</code>. 收藏
     */
    public Integer getMarkAmount();

    /**
     * Setter for <code>exam.t_question.f_recovery_count</code>. 纠错
     */
    public void setRecoveryCount(Integer value);

    /**
     * Getter for <code>exam.t_question.f_recovery_count</code>. 纠错
     */
    public Integer getRecoveryCount();

    /**
     * Setter for <code>exam.t_question.f_source_type</code>. 来源类型 1:考试,2调研活动，3：调研问卷，4：评估问卷
     */
    public void setSourceType(Integer value);

    /**
     * Getter for <code>exam.t_question.f_source_type</code>. 来源类型 1:考试,2调研活动，3：调研问卷，4：评估问卷
     */
    public Integer getSourceType();

    /**
     * Setter for <code>exam.t_question.f_content_text</code>. 试题题干，纯文本格式
     */
    public void setContentText(String value);

    /**
     * Getter for <code>exam.t_question.f_content_text</code>. 试题题干，纯文本格式
     */
    public String getContentText();

    /**
     * Setter for <code>exam.t_question.f_exam_num</code>. 试题编号，同步试卷的时候落库，报错的时候返回第三方
     */
    public void setExamNum(String value);

    /**
     * Getter for <code>exam.t_question.f_exam_num</code>. 试题编号，同步试卷的时候落库，报错的时候返回第三方
     */
    public String getExamNum();

    /**
     * Setter for <code>exam.t_question.f_exam_profession_name</code>. 试题所属专业，同步试卷的时候落库
     */
    public void setExamProfessionName(String value);

    /**
     * Getter for <code>exam.t_question.f_exam_profession_name</code>. 试题所属专业，同步试卷的时候落库
     */
    public String getExamProfessionName();

    /**
     * Setter for <code>exam.t_question.f_exam_sub_profession_name</code>. 试题所属子专业，同步试卷的时候落库
     */
    public void setExamSubProfessionName(String value);

    /**
     * Getter for <code>exam.t_question.f_exam_sub_profession_name</code>. 试题所属子专业，同步试卷的时候落库
     */
    public String getExamSubProfessionName();

    /**
     * Setter for <code>exam.t_question.f_parsing</code>. 试题解析富文本
     */
    public void setParsing(String value);

    /**
     * Getter for <code>exam.t_question.f_parsing</code>. 试题解析富文本
     */
    public String getParsing();

    /**
     * Setter for <code>exam.t_question.f_parsing_text</code>. 试题解析纯文本
     */
    public void setParsingText(String value);

    /**
     * Getter for <code>exam.t_question.f_parsing_text</code>. 试题解析纯文本
     */
    public String getParsingText();

    /**
     * Setter for <code>exam.t_question.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>exam.t_question.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>exam.t_question.f_question_path_encrypt</code>. 试题任意层级目录是否加密（0：否 1：是）
     */
    public void setQuestionPathEncrypt(Integer value);

    /**
     * Getter for <code>exam.t_question.f_question_path_encrypt</code>. 试题任意层级目录是否加密（0：否 1：是）
     */
    public Integer getQuestionPathEncrypt();

    /**
     * Setter for <code>exam.t_question.f_encrypt_name</code>. 加密试题题干名称
     */
    public void setEncryptName(String value);

    /**
     * Getter for <code>exam.t_question.f_encrypt_name</code>. 加密试题题干名称
     */
    public String getEncryptName();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IQuestion
     */
    public void from(com.zxy.product.exam.jooq.tables.interfaces.IQuestion from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IQuestion
     */
    public <E extends com.zxy.product.exam.jooq.tables.interfaces.IQuestion> E into(E into);
}
