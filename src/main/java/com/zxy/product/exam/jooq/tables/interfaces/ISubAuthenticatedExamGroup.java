/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 子认证-考试组-考试内容关联关系表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISubAuthenticatedExamGroup extends Serializable {

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_id</code>. 关系表id
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_id</code>. 关系表id
     */
    public String getId();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_sub_authenticated_id</code>. 子认证id
     */
    public void setSubAuthenticatedId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_sub_authenticated_id</code>. 子认证id
     */
    public String getSubAuthenticatedId();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_exam_group_id</code>. 考试组id（学习t_sub_authenticated_content_configure表对应的f_content_id）
     */
    public void setExamGroupId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_exam_group_id</code>. 考试组id（学习t_sub_authenticated_content_configure表对应的f_content_id）
     */
    public String getExamGroupId();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_business_id</code>. 考试id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_business_id</code>. 考试id
     */
    public String getBusinessId();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_business_name</code>. 考试name
     */
    public void setBusinessName(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_business_name</code>. 考试name
     */
    public String getBusinessName();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_business_type</code>. 1:模拟考试 2：认证考试
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_business_type</code>. 1:模拟考试 2：认证考试
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_order</code>. 排序序号
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_order</code>. 排序序号
     */
    public Integer getOrder();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_exam_times</code>. 考试组允许考试次数
     */
    public void setExamTimes(Integer value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_exam_times</code>. 考试组允许考试次数
     */
    public Integer getExamTimes();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_is_publish</code>. 子认证是否发布，1：已发布；0：未发布
     */
    public void setIsPublish(Integer value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_is_publish</code>. 子认证是否发布，1：已发布；0：未发布
     */
    public Integer getIsPublish();

    /**
     * Setter for <code>exam.t_sub_authenticated_exam_group.f_exam_registration_frequency</code>. 考试组报名次数
     */
    public void setExamRegistrationFrequency(Integer value);

    /**
     * Getter for <code>exam.t_sub_authenticated_exam_group.f_exam_registration_frequency</code>. 考试组报名次数
     */
    public Integer getExamRegistrationFrequency();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISubAuthenticatedExamGroup
     */
    public void from(ISubAuthenticatedExamGroup from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISubAuthenticatedExamGroup
     */
    public <E extends ISubAuthenticatedExamGroup> E into(E into);
}
