/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AudienceObjectRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 资源门户受众对象
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AudienceObject extends TableImpl<AudienceObjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_audience_object</code>
     */
    public static final AudienceObject AUDIENCE_OBJECT = new AudienceObject();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AudienceObjectRecord> getRecordType() {
        return AudienceObjectRecord.class;
    }

    /**
     * The column <code>exam.t_audience_object.f_id</code>. ID
     */
    public final TableField<AudienceObjectRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_audience_object.f_target_id</code>. 目标ID
     */
    public final TableField<AudienceObjectRecord, String> TARGET_ID = createField("f_target_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "目标ID");

    /**
     * The column <code>exam.t_audience_object.f_activity_id</code>. 活动id
     */
    public final TableField<AudienceObjectRecord, String> ACTIVITY_ID = createField("f_activity_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "活动id");

    /**
     * The column <code>exam.t_audience_object.f_item_id</code>. 受众项ID
     */
    public final TableField<AudienceObjectRecord, String> ITEM_ID = createField("f_item_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "受众项ID");

    /**
     * The column <code>exam.t_audience_object.f_type</code>. 受众对象类型: 1.考试，2.调研
     */
    public final TableField<AudienceObjectRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "受众对象类型: 1.考试，2.调研");

    /**
     * The column <code>exam.t_audience_object.f_create_time</code>.
     */
    public final TableField<AudienceObjectRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_audience_object.f_modify_date</code>. 修改时间
     */
    public final TableField<AudienceObjectRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_audience_object</code> table reference
     */
    public AudienceObject() {
        this("t_audience_object", null);
    }

    /**
     * Create an aliased <code>exam.t_audience_object</code> table reference
     */
    public AudienceObject(String alias) {
        this(alias, AUDIENCE_OBJECT);
    }

    private AudienceObject(String alias, Table<AudienceObjectRecord> aliased) {
        this(alias, aliased, null);
    }

    private AudienceObject(String alias, Table<AudienceObjectRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "资源门户受众对象");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AudienceObjectRecord> getPrimaryKey() {
        return Keys.KEY_T_AUDIENCE_OBJECT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AudienceObjectRecord>> getKeys() {
        return Arrays.<UniqueKey<AudienceObjectRecord>>asList(Keys.KEY_T_AUDIENCE_OBJECT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AudienceObject as(String alias) {
        return new AudienceObject(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AudienceObject rename(String name) {
        return new AudienceObject(name, null);
    }
}
