package com.zxy.product.oauth.util;

import org.apache.commons.codec.binary.Base64;
import org.springframework.util.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class UnifiedUtil {

    /**
     * @desc 验证扫码token合法性
     * @param appId 扫码平台分配的appId
     * @param appSecret 扫码平台分配的appSecret
     * @param token 扫码登录凭证
     * @param time 时间戳
     * @param check 校验值
     * @return 校验结果
     */
    public static boolean checkQrToken(String appId, String appSecret, String token, String time, String check) {
        String text = appId + time + token.substring(token.length()-20) + appSecret;
        String md5Text = DigestUtils.md5DigestAsHex(text.getBytes());
        return md5Text.equals(check);
    }


    /**
     * @desc 解密
     * @param password 密文
     * @param key 密钥
     * @return 明文
     */
    public static String deCodeAES(String password, String key) throws Exception {
        byte[] keyBytes = DigestUtils.md5Digest(key.getBytes());
        byte[] debase64Bytes = Base64.decodeBase64(password.getBytes());
        return new String(decrypt(debase64Bytes, keyBytes));
    }

    private static byte[] decrypt(byte[] text, byte[] key) throws Exception {
        SecretKeySpec aesKey = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, aesKey);
        return cipher.doFinal(text);
    }

    /**
     * 拼接签名参数
     * @param appType 渠道类型：1|BOSS 2|web 3|wap 4|pc客户端 5|手机客户端
     * @param id 统一认证分配的sourceId
     * @param idType id类型：0|sourceid 1|appid
     * @param key 统一认证分配的sourceKey
     * @param msgid 标识请求的随机数
     * @param systemtime 请求消息发送的系统时间17位
     * @param token 扫码登录凭证
     * @param version 版本
     * @return 签名
     */
    public static String getSign(String appType, String id, String idType, String key, String msgid, String systemtime, String token, String version) {
        String text = appType+id+idType+key+msgid+systemtime+token+version;
        return DigestUtils.md5DigestAsHex(text.getBytes());
    }


    /**
     * 签名参数
     * 签名，规则MD5（appId+t+appSecret）32位小写
     * @return
     */
    public static String getQrCodeSing(String appId,String t,String appSecret){
        String sign = appId + t + appSecret;
        return DigestUtils.md5DigestAsHex(sign.getBytes());
    }

}
