package com.zxy.product.exam.async.listener;


import com.google.common.collect.Lists;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.exam.api.AnswerRecordService;
import com.zxy.product.exam.api.ExamRecordService;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.api.ExamStuRecordService;
import com.zxy.product.examstu.api.SignUpService;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * 监考配置监听
 * <AUTHOR>
 * @date 2017年11月1日 下午4:48:59
 *
 */
@Component
public class InvigilatorListener extends AbstractMessageListener {

	private final static Logger LOGGER = LoggerFactory.getLogger(InvigilatorListener.class);

	private CommonDao<Invigilator> invigilatorDao;
    private CommonDao<InvigilatorGrantDetail> invigilatorGrantDetailDao;
    private TransactionTemplate transactionTemplate;

    private CommonDao<Exam> examDao;
    
    private SignUpService signUpService;
    
    private com.zxy.product.examstu.api.ExamRecordService examStuRecordService;

    private com.zxy.product.examstu.api.ExamStuRecordService recordService;

    @Autowired
    public void setRecordService(ExamStuRecordService recordService) {
        this.recordService = recordService;
    }

    @Autowired
    public void setExamStuRecordService(com.zxy.product.examstu.api.ExamRecordService examStuRecordService) {
        this.examStuRecordService = examStuRecordService;
    }
    
    @Autowired
    public void setSignUpService(SignUpService signUpService) {
        this.signUpService = signUpService;
    }

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao){
        this.examDao=examDao;
    }

    @Autowired
    public void setInvigilatorDao(CommonDao<Invigilator> invigilatorDao) {
        this.invigilatorDao = invigilatorDao;
    }


    @Autowired
    public void setInvigilatorGrantDetailDao(CommonDao<InvigilatorGrantDetail> invigilatorGrantDetailDao) {
        this.invigilatorGrantDetailDao = invigilatorGrantDetailDao;
    }

    @Autowired
    public void setTransactionTemplate(TransactionTemplate transactionTemplate) {
        this.transactionTemplate = transactionTemplate;
    }


    @Override
    protected void onMessage(Message message) {
        LOGGER.info("exam/InvigilatorListener:" + message.toString());
        String ids = message.getHeader(MessageHeaderContent.IDS);
        // 添加事务
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                switch (message.getType()) {
                case MessageTypeContent.EXAM_INVIGILATOR_INSERT:
                    if (!StringUtils.isEmpty(ids)) {
//                        List<InvigilatorGrant> grantResults = invigilatorGrantDao.fetch(INVIGILATOR_GRANT.INVIGILATOR_ID.in(ids.split(",")));
//                        grantDetailInsert(grantResults);
                    }
                    break;
                case MessageTypeContent.EXAM_INVIGILATOR_GRANT_INSERT:
                    if (!StringUtils.isEmpty(ids)) {
//                        List<InvigilatorGrant> grantResults = invigilatorGrantDao.fetch(INVIGILATOR_GRANT.ID.in(ids.split(",")));
//                        grantDetailInsert(grantResults);
                    }
                    break;
                case MessageTypeContent.EXAM_DO_FORCE_SUBMIT_OVER_TIME_PAPER:
                    doForceSubmitOverTimePaper();
                    break;
                case MessageTypeContent.EXAM_DO_SIGNUP_COUNT:
                    doSignupCount();
                    break;
                case MessageTypeContent.EXAM_DO_JOIN_MEMBER_AND_TIMES:
                    doExamJoinMemberAndTimes();
                    break;
                default:
                    break;
                }
            }
        });
    }

    /**
     * 更新考试参考人数与人次
     */
    private void doExamJoinMemberAndTimes() {
        // 查询需要更新考试参考人数与人次的考试（考试为进行中的考试）
        List<String> examIds = examDao.execute(x -> x.select(
                Fields.start()
                .add(EXAM.ID).end())
                .from(EXAM)
                .where(EXAM.STATUS.eq(Exam.STATUS_STARTING))
                .and(EXAM.SOURCE_TYPE.eq(Exam.EXAM_ACTIVITY_SOURCE_TYPE))
                .fetch(EXAM.ID)
            );

        for (String examId : examIds) {
            // 查询stu库
            HashMap<String, Integer> northMap = examStuRecordService.doExamJoinMemberAndTimes(com.zxy.product.exam.entity.Exam.NORTH, examId);
            HashMap<String, Integer> sourceMap = examStuRecordService.doExamJoinMemberAndTimes(com.zxy.product.exam.entity.Exam.SOURCE, examId);
            int joinNumber = northMap.get("count") + sourceMap.get("count");
            int joinPersonTime = northMap.get("times") + sourceMap.get("times");
            examDao.execute(dslContext ->
                dslContext.update(EXAM)
                    .set(EXAM.JOIN_NUMBER,joinNumber)
                    .set(EXAM.JOIN_PERSON_TIME,joinPersonTime)
                .where(EXAM.ID.eq(examId))
                .execute());
        }

    }

    /**
     * 更新考试报名数
     */
    private void doSignupCount() {
        // 查询需要更新报名人数的考试（考试为未开始，报名中，开考中且需要报名）
        List<Exam> examList = examDao.execute(e ->
            e.select(
                Fields.start()
                .add(EXAM.ID)
                .add(EXAM.TYPE)
                .add(EXAM.APPLICANT_NUMBER)
                .end())
            .from(EXAM)
            .where(EXAM.STATUS.eq(Exam.STATUS_NOT_START)
                    .or(EXAM.STATUS.eq(Exam.STATUS_SIGNUPING))
                    .or(EXAM.STATUS.eq(Exam.STATUS_STARTING))
                    )
            .and(EXAM.NEED_APPLICANT.eq(Exam.EXAM_YES))
            .fetch(r -> {
                Exam exam = new Exam();
                exam.setId(r.getValue(EXAM.ID));
                exam.setType(r.getValue(EXAM.TYPE));
                exam.setApplicantNumber(r.getValue(EXAM.APPLICANT_NUMBER));
                return exam;
          }));

        for (Exam exam : examList) {
            Integer count;
            Integer northCount = signUpService.countSignUpByExamType(com.zxy.product.exam.entity.Exam.NORTH, exam.getType(), exam.getId());
            Integer sourceCount = signUpService.countSignUpByExamType(com.zxy.product.exam.entity.Exam.SOURCE, exam.getType(), exam.getId());
            count = northCount + sourceCount;
            if (!count.equals(exam.getApplicantNumber())) {
                examDao.execute(e -> e.update(EXAM).set(EXAM.APPLICANT_NUMBER, count).where(EXAM.ID.eq(exam.getId())).execute());
            }
        }

    }

    /**
     * 系统自动强制交卷,
     * 此功能是2018-05-26发版，之前的考试未提交的不做处理
     */
    private void doForceSubmitOverTimePaper() {
        recordService.doForceSubmitOverTimePaper(com.zxy.product.exam.entity.Exam.NORTH);
        recordService.doForceSubmitOverTimePaper(com.zxy.product.exam.entity.Exam.SOURCE);
    }


    /** 计算监考范围授权详情 */
    private void grantDetailInsert(List<InvigilatorGrant> grantResults) {
        // 监考范围授权详情
        List<InvigilatorGrantDetail> invigilatorGrantDetailList = new ArrayList<>();
        grantResults.forEach(t -> {
            if (t.getType() == InvigilatorGrant.ORG_INCLUDE_SUB) {
                invigilatorGrantDetailList.addAll(createInvigiltorGrantDetails(t, findSubOrgIds(t.getOrganizationId())));
                return;
            }
            InvigilatorGrantDetail invigilatorGrantDetail = new InvigilatorGrantDetail();
            invigilatorGrantDetail.forInsert();
            invigilatorGrantDetail.setInvigilatorId(t.getInvigilatorId());
            invigilatorGrantDetail.setExamId(t.getExamId());
            invigilatorGrantDetail.setOrganizationId(t.getOrganizationId());
            invigilatorGrantDetail.setMemberId(t.getMemberId());
            invigilatorGrantDetailList.add(invigilatorGrantDetail);
        });
        List<List<InvigilatorGrantDetail>> bigList = Lists.partition(invigilatorGrantDetailList, 100000);
        LOGGER.info("监考授权分批新增detail记录开始...共{}批", bigList.size());
        for (List<InvigilatorGrantDetail> temp : bigList) {
            invigilatorGrantDetailDao.insert(temp);
        }
        LOGGER.info("监考授权分批新增detail记录完成...");
    }

    /** 根据部门ID查询子部门ID */
    private List<String> findSubOrgIds(String organizationId) {
        return invigilatorDao.execute(e ->
                e.select(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL)
                        .leftJoin(ORGANIZATION).on(ORGANIZATION_DETAIL.SUB.eq(ORGANIZATION.ID))
                        .where(ORGANIZATION_DETAIL.ROOT.eq(organizationId), ORGANIZATION.STATUS.eq(1)).fetch(ORGANIZATION_DETAIL.SUB));
    }

    /** 组装grant_detail数据 */
    private List<InvigilatorGrantDetail>  createInvigiltorGrantDetails(InvigilatorGrant invigilatorGrant, List<String> organizationIds) {
        return organizationIds.stream().map(t -> {
            InvigilatorGrantDetail invigilatorGrantDetail = new InvigilatorGrantDetail();
            invigilatorGrantDetail.forInsert();
            invigilatorGrantDetail.setInvigilatorId(invigilatorGrant.getInvigilatorId());
            invigilatorGrantDetail.setExamId(invigilatorGrant.getExamId());
            invigilatorGrantDetail.setOrganizationId(t);
            invigilatorGrantDetail.setMemberId(invigilatorGrant.getMemberId());
            return invigilatorGrantDetail;
        }).collect(Collectors.toList());
    }

	@Override
	public int[] getTypes() {
		return new int[]{
		    MessageTypeContent.EXAM_INVIGILATOR_INSERT,
			MessageTypeContent.EXAM_INVIGILATOR_GRANT_INSERT,
			MessageTypeContent.EXAM_DO_FORCE_SUBMIT_OVER_TIME_PAPER,
			MessageTypeContent.EXAM_DO_SIGNUP_COUNT,
			MessageTypeContent.EXAM_DO_JOIN_MEMBER_AND_TIMES
		};
	}


}
