package com.zxy.product.course.service.support.course;

import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER;
import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER_SECTION;
import static com.zxy.product.course.jooq.Tables.COURSE_INFO;
import static com.zxy.product.course.jooq.Tables.COURSE_SECTION_SCORM_PROGRESS;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_AH;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_BJ;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_CM;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_CQ;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_EB;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_FJ;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_GD;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_GS;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_GX;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_GZ;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_HB;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_HL;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_HN;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_JL;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_JS;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_JX;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_LN;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_NM;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_NX;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_OTHER;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_QH;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_QO;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_SC;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_SD;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_SH;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_SN;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_SX;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_TJ;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_XJ;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_XN;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_XZ;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_YN;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_ZGTT;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_ZJ;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS_ZX;
import static com.zxy.product.course.jooq.Tables.MEMBER;
import static com.zxy.product.course.jooq.Tables.ORGANIZATION;
import static com.zxy.product.course.jooq.Tables.ORGANIZATION_DETAIL;
import static com.zxy.product.course.jooq.Tables.SPLIT_TABLE_CONFIG;
import static com.zxy.product.course.jooq.Tables.SUBJECT_SECTION_STUDY_LOG;
import static com.zxy.product.course.jooq.Tables.TEMP_MEMBER;
import static com.zxy.product.course.jooq.Tables.TEMP_SECTION_STUDY_LOG_GT_24;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.course.CourseProcessService;
import com.zxy.product.course.api.course.CourseSectionScormService;
import com.zxy.product.course.api.other.CourseExceptionService;
import com.zxy.product.course.api.sharding.ShardingConfigService;
import com.zxy.product.course.content.CacheKeyConstant;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.mongodb.CourseStudyLog;
import com.zxy.product.course.service.config.RepairDataConfig;
import com.zxy.product.course.service.util.DateUtil;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import com.zxy.product.course.util.CourseStudyException;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.jooq.Field;
import org.jooq.RowN;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;


/**
 * Created by keeley on 2017/4/25.
 */
@Service
public class CourseProcessServiceSupport implements CourseProcessService {
  private static Logger logger = LoggerFactory.getLogger(CourseProcessServiceSupport.class);
  private MessageSender messageSender;
  private CommonDao<CourseChapterSection> sectionDao;
  private CommonDao<CourseSectionStudyLog> logCommonDao;
  private CommonDao<CourseSectionStudyProgress> progressCommonDao;
  private CommonDao<CourseStudyProgress> courseStudyProgressCommonDao;
  private CommonDao<CourseInfo> courseInfoDao;
  private CommonDao<CourseSectionScormProgress> courseSectionScormLogCommonDao;
  private CommonDao<SubjectSectionStudyLog> subjectLogDao;
  private CourseCacheServiceSupport courseCacheServiceSupport;
  private CourseSectionScormService scormService;
  private Cache cache;
  private Redis redis;
  private CommonDao<SplitLogConfig> configDao;
  private CommonDao<Organization> orgDao;
  private ShardingConfigService shardingConfigService;
  @Resource
  private CommonDao<DeleteDataCourse> dataCourseCommonDao;
  private CourseCacheService courseCacheService;
  @Autowired
  public void setShardingConfigService(ShardingConfigService shardingConfigService) {
    this.shardingConfigService = shardingConfigService;
  }

  @Autowired
  private CommonDao<TempSectionStudyLogGt24> tempSectionStudyLogGt24CommonDao;

  @Autowired
  private RepairDataConfig repairDataConfig;

  @Autowired
  private CommonDao<SplitTableConfig> splitTableConfigDao;

  @Autowired
  private CommonDao<Member> memberCommonDao;

  @Autowired
  private CommonDao<CourseSectionStudyProgress> courseSectionStudyProgressCommonDao;
  @Autowired
  private CommonDao<TempMember> tempMemberCommonDao;


  @Autowired
  private CommonDao<RepeatCourseSectionStudyProgress> repeatCourseSectionStudyProgressDao;

  @Autowired
  private CommonDao<TempSubjectSectionStudyLog> tempSubjectSectionStudyLogDao;


  @Autowired
  private CourseRepairServiceSupport courseRepairServiceSupport;

  @Autowired
  public void setRedis(Redis redis) {
    this.redis = redis;
  }

  @Autowired
  public void setCache(Cache cache) {
    this.cache = cache;
  }

  @Autowired
  public void setCourseCacheServiceSupport(CourseCacheServiceSupport courseCacheServiceSupport) {
    this.courseCacheServiceSupport = courseCacheServiceSupport;
  }

  @Autowired
  public void setScormService(CourseSectionScormService scormService) {
    this.scormService = scormService;
  }

  @Autowired
  public void setCourseSectionScormLogCommonDao(
      CommonDao<CourseSectionScormProgress> courseSectionScormLogCommonDao) {
    this.courseSectionScormLogCommonDao = courseSectionScormLogCommonDao;
  }

  @Autowired
  public void setCourseInfoDao(CommonDao<CourseInfo> courseInfoDao) {
    this.courseInfoDao = courseInfoDao;
  }

  @Autowired
  public void setCourseStudyProgressCommonDao(
      CommonDao<CourseStudyProgress> courseStudyProgressCommonDao) {
    this.courseStudyProgressCommonDao = courseStudyProgressCommonDao;
  }

  @Autowired
  public void setSectionDao(CommonDao<CourseChapterSection> sectionDao) {
    this.sectionDao = sectionDao;
  }

  @Autowired
  public void setLogCommonDao(CommonDao<CourseSectionStudyLog> logCommonDao) {
    this.logCommonDao = logCommonDao;
  }

  @Autowired
  public void setMessageSender(MessageSender messageSender) {
    this.messageSender = messageSender;
  }

  @Autowired
  public void setProgressCommonDao(CommonDao<CourseSectionStudyProgress> progressCommonDao) {
    this.progressCommonDao = progressCommonDao;
  }

  @Autowired
  public void setSubjectLogDao(CommonDao<SubjectSectionStudyLog> subjectLogDao) {
    this.subjectLogDao = subjectLogDao;
  }

  @Autowired
  public void setConfigDao(CommonDao<SplitLogConfig> configDao) {
    this.configDao = configDao;
  }

  @Autowired
  public void setOrgDao(CommonDao<Organization> orgDao) {
    this.orgDao = orgDao;
  }
  @Autowired
  public void setCourseCacheService(CourseCacheService courseCacheService) {
    this.courseCacheService = courseCacheService;
  }

  @Override
  public CourseStudyLog startSectionLog(String memberId, String sectionId, Integer clientType) {
    String path = orgDao.execute(
        x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).leftJoin(MEMBER)
            .on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(MEMBER.ID.eq(memberId))
            .fetchOne(ORGANIZATION.PATH));
    return this.startSectionLog(memberId, sectionId, clientType, System.currentTimeMillis(), path);
  }

  @Override
  public CourseStudyLog startSectionLog(String memberId, String sectionId, Integer clientType,
      Long createTime, String path) {
//        courseException
    String key = new StringBuffer("startSectionLog").append(memberId).append(sectionId)
        .append(clientType).toString();
    frequentCheck(key);
    if(clientType == 2) {
      clientType = CourseSectionStudyLog.CLIENT_TYPE_APP;
    }
    // 初始化log
    CourseChapterSection section = courseCacheServiceSupport.getSection(sectionId);

    // updated by wangdongyan 学习时长第二阶段优化时，先保留分表的数据更新，汇总log停止更新
    CourseStudyLog log = insertSubLog(memberId, clientType, section, path);
    // 创建分表log同时创建人-课-天分表数据
//        String currentDate = this.changeTimeStyle(log.getCreateTime());
//        insertOrUpdateCourseSectionLogDay(memberId, section.getCourseId(), currentDate, clientType, 0);

    // 单窗口播放，进入课程时，将logId放入缓存
    String limitKey = String.join("_", "onlyOneVideoPlay", "logId", memberId);
    cache.set(limitKey, log.getId(), 60 * 60 * 24);

    //初始化progress
    CourseSectionStudyProgress progress = courseCacheServiceSupport
        .getProgress(memberId, section.getReferenceId());
    logger.info("从缓存中取出来的值，progress={}, memberId={}, sectionId={}", progress, memberId,
        section.getReferenceId());
    if(progress == null) {
      progress = new CourseSectionStudyProgress();
      progress.forInsert();
      progress.setCreateTime(createTime);
      progress.setCourseId(section.getCourseId());
      progress.setSectionId(section.getReferenceId());
      progress.setMemberId(memberId);
      progress.setBeginTime(progress.getCreateTime());
      progress.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_STUDY);
      progress.setLastAccessTime(progress.getCreateTime());
      progress.setStudyTotalTime(0);
      // update for xdn
//      TableImpl<?> csspTable = shardingConfigService.getTableName(section.getCourseId(),
//              ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//              ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
      TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, section.getCourseId());
      CourseSectionStudyProgress cssp = progress;
      progressCommonDao.execute(dslContext -> cssp.insert(csspTable, dslContext)).execute();
    }

    String setKey = log.getMemberId() + "," + log.getCourseId();
    redis.process(
        x -> x.hset(COURSE_PROGRESS_PRE_UPDATE_MAP, setKey, System.currentTimeMillis() + ""));
    return log;
  }

  @Override
  public CourseSectionStudyProgress docProcess(CourseStudyLog log, Optional<String> location,
      Optional<Integer> optionalStudyTime, Integer configMaxTime, String path) {
    String key = new StringBuffer("docProcess").append(log.getId()).toString();
    frequentCheck(key);

    logger.info("开始更新doc文档类型，logId = {}, 学习客户端clientType={}", log.getId(), log.getClientType());
    int systemTime =
        Long.valueOf(System.currentTimeMillis() - log.getCreateTime()).intValue() / 1000;
    logger.info("studyTime = {}", systemTime);
    int studyTime = optionalStudyTime.orElse(systemTime);
    if(studyTime == 0) {
      return null;
    }
    // 超過10秒偏差就是异常 updated by wdy 10秒偏差去掉，防止正常学习因为时间问题导致记录无法保存
//    if(studyTime > systemTime + 10) {
////            throw new CourseStudyException(CourseExceptionService.REMARK_STUDY_OVER);
//      return null;
//    }
    if(studyTime > systemTime) {
      studyTime = systemTime;
    }
    final int finalStudyTime = studyTime;
    // 时间过滤后返回的是单次请求新增的学习时长
    TableImpl<?> table = courseCacheServiceSupport.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
    studyTime = filterStudyTime(log, studyTime, Optional.empty(), configMaxTime, table);
    // add by wangdongyan 2018-12-11 如果单次新增的时长为0只需要更新最后学习章节即可
    if(studyTime == 0) {
      this.updateLastStudy(log);
      return null;
    }
    return this.defaultProcess(table, log, studyTime, location.orElse("0"), progress -> {
      if(( progress.getStudyTotalTime() >= 10 || finalStudyTime >= 10 )
          && progress.getFinishStatus() == CourseSectionStudyLog.FINISH_STATUS_STUDY) {
        progress.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_FINISH);
        progress.setCompletedRate(100);
        progress.setFinishTime(System.currentTimeMillis());
      }
    });
  }

  @Override
  public CourseSectionStudyProgress mediaProcess(CourseStudyLog log, Integer studyTime,
      Integer videoTotalTime, Integer location, Integer configProcess, Integer configPlay,
      Integer configMaxTime, String path) {
    String key = new StringBuffer("mediaProcess").append(log.getId()).toString();
    frequentCheck(key);

    if(videoTotalTime == null || videoTotalTime == 0 || studyTime == 0 || studyTime == null) {
      logger.error("学习时长或者视频总时长数值异常， videoTotalTime={}， studyTime={}", videoTotalTime, studyTime);
      return null;
    }

    int systemTime =
        Long.valueOf(System.currentTimeMillis() - log.getCreateTime()).intValue() / 1000;
    logger.info("开始更新media类型课件，系统当前时间，systemTime={}， log对应的创建时间createTime={},clientType={}",
        System.currentTimeMillis(), log.getCreateTime(), log.getClientType());
    logger.info("当前计算的时长为，systemTime={}, memberId={}, logId={},前台传递的时长studyTime={}", systemTime,
        log.getMemberId(), log.getId(), studyTime);
    // 超過10秒偏差就是異常updated by wdy 10秒偏差去掉，防止正常学习因为时间问题导致记录无法保存
//    if(studyTime.intValue() > systemTime + 10) {
////            throw new CourseStudyException(CourseExceptionService.REMARK_STUDY_OVER);
//      return null;
//    }
    if(studyTime.intValue() > systemTime) {
      studyTime = systemTime;
    }

    TableImpl<?> table = courseCacheServiceSupport.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
    // 12-10学习时长优化后过滤时长改为单次请求后新加的时长
    studyTime = filterStudyTime(log, studyTime, Optional.of(videoTotalTime), configMaxTime, table);
    // add 12-10 学习时长为0时不需要在更新其他地方
//        if (studyTime == 0) {
//        	this.updateLastStudy(log);
//        	return null;
//        }

    return this.defaultProcess(table, log, studyTime, location.toString(), progress -> {
      if(progress.getFinishStatus() != CourseSectionStudyLog.FINISH_STATUS_STUDY) {
        return;
      }

      int total = Optional.ofNullable(progress.getStudyTotalTime()).orElse(0);
      int oldRate = Optional.ofNullable(progress.getCompletedRate()).orElse(0);
      int processRate = total * 100 / videoTotalTime; // 学习时长百分比
      int localRate = location * 100 / videoTotalTime; // 最后播放时间占百分比
      oldRate = oldRate > localRate ? oldRate : localRate;
      progress.setCompletedRate(oldRate);
      logger.info(
          "更新时章节的完成比例，oldRate={}，localRate={}，configProcess={}，processRate={}, configPaly={} ",
          oldRate, localRate, configProcess, processRate, configPlay);
      if(localRate >= configProcess.intValue() && processRate >= configPlay.intValue()) {
        progress.setFinishStatus(CourseSectionStudyLog.FINISH_STATUS_FINISH);
        progress.setCompletedRate(100);
        progress.setFinishTime(System.currentTimeMillis());
      } else {
        if(oldRate >= 100) {
          progress.setCompletedRate(99);
        }
      }
    });
  }

  @Override
  public CourseSectionStudyProgress scormProcess(String scormLogId) {
    long now = System.currentTimeMillis();
    CourseSectionScormProgress log = courseSectionScormLogCommonDao.get(scormLogId);
    List<CourseSectionScormProgress> scormLogs = courseSectionScormLogCommonDao.fetch(
        COURSE_SECTION_SCORM_PROGRESS.SCORM_ID.eq(log.getScormId()),
        COURSE_SECTION_SCORM_PROGRESS.MEMBER_ID.eq(log.getMemberId()));
    int studyTime = scormLogs.stream().map(x -> x.getStudyTime()).reduce(0, (a, b) -> a + b);
    TableImpl<?> cacheTable = courseCacheService.getCacheTable(log.getMemberId(), SplitTableConfig.COURSE_STUDY_PROGRESS);
    CourseStudyProgress courseStudyProgress = courseStudyProgressCommonDao.execute(context -> context.select(cacheTable.fields()).from(cacheTable).where(cacheTable.field("f_course_id",String.class).eq(log.getCourseId()),
            cacheTable.field("f_member_id",String.class).eq(log.getMemberId())).limit(1).fetchOne(r->r.into(CourseStudyProgress.class)));
    // 如果不存在注册记录，不更新
    if(courseStudyProgress == null) {
      logger.info("课程进度找不到 未开始更新,memberId = {}, courseId = {} ", log.getMemberId(),
          log.getCourseId());
      return null;
    }
    ;
    CourseSectionStudyProgress progress = courseCacheServiceSupport
        .getProgress(log.getMemberId(), log.getScormId());
    if(progress == null) {
      progress = new CourseSectionStudyProgress();
      progress.forInsert();
      progress.setCourseId(log.getCourseId());
      progress.setSectionId(log.getScormId());
      progress.setMemberId(log.getMemberId());
      progress.setBeginTime(progress.getCreateTime());
      progress.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_STUDY);
      progress.setStudyTotalTime(0);
      // update for xdn
      TableImpl<?> csspTable = CourseSectionStudyProgressUtil
              .getTable(log.getMemberId(),log.getCourseId());
      CourseSectionStudyProgress cssp = progress;
      progressCommonDao.execute(dslContext -> cssp.insert(csspTable, dslContext)).execute();
      return progress;
      // return progressCommonDao.insert(progress);
    }
    progress.setAuditPass(CourseSectionStudyProgress.FINISH_STATUS_AUDIT);
    progress.setLastAccessTime(now);
    progress.setLessonLocation(log.getScormItemId());
    if(scormLogs.stream()
        .allMatch(x -> x.getFinishStatus() == CourseSectionScormProgress.STATUS_FINISH)) {
      progress.setFinishStatus(log.getFinishStatus());
    }
    progress.setStudyTotalTime(studyTime);
    courseCacheServiceSupport.updateProgress(progress);
    // 更新章节进度表时同时更新课程总进度表的currentSectionId
    courseStudyProgress.setCurrentSectionId(progress.getSectionId());
    courseStudyProgressCommonDao.execute(dsl -> dsl.update(cacheTable)
        .set(cacheTable.field("f_current_section_id",String.class), courseStudyProgress.getCurrentSectionId())
        .where(cacheTable.field("f_id",String.class).eq(courseStudyProgress.getId()))
        .execute());

    // add 2020-4-24 异步更新studyProgress分表数据
    messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
            MessageHeaderContent.ID, courseStudyProgress.getId(),MessageHeaderContent.MEMBER_ID, courseStudyProgress.getMemberId());

    this.deleteCourseStudyRedis(progress.getMemberId(), progress.getCourseId());
    messageSender.send(MessageTypeContent.COURSE_PROGRESS_UPDATE_NEW,
        MessageHeaderContent.MEMBER_ID, progress.getMemberId(),
        MessageHeaderContent.COURSE_ID, progress.getCourseId(),
        MessageHeaderContent.FINISHSTIME, System.currentTimeMillis() + "");
    return progress;
  }

  private CourseSectionStudyProgress defaultProcess(TableImpl<?> table, CourseStudyLog log,
      Integer studyTime, String location,
      Consumer<CourseSectionStudyProgress> consumer) {

    long now = System.currentTimeMillis();
    TableImpl<?> cacheTable = courseCacheServiceSupport.getCacheTable(log.getMemberId(), SplitTableConfig.COURSE_STUDY_PROGRESS);
    CourseStudyProgress courseStudyProgress = courseStudyProgressCommonDao.execute(dslContext ->
            dslContext.select(cacheTable.fields())
                    .from(cacheTable)
                    .where(cacheTable.field("f_course_id", String.class).eq(log.getCourseId()), cacheTable.field("f_member_id", String.class).eq(log.getMemberId()))
                    .fetchOne(r->r.into(CourseStudyProgress.class)));
    if (courseStudyProgress == null) {
      logger.info("课程进度找不到 未开始更新,memberId = {}, courseId = {} ", log.getMemberId(), log.getCourseId());
      return null;
    }
    // 获取当前章节
    CourseChapterSection section = courseCacheServiceSupport.getSection(log.getSectionId());
    CourseSectionStudyProgress progress = courseCacheServiceSupport.getProgress(log.getMemberId(),section.getReferenceId());
    logger.info("更新的章节引用id： referenceId = {}", section.getReferenceId());
    if(progress ==null) {
      throw new UnprocessableException(ErrorCode.NoRegister);
    }
    // 用log的提交时间来计算提交日期
    String date = changeTimeStyle(now);
    logger.info("logId:{},又学习了{}秒",log.getId(), studyTime);
    // 直接修改分表流水记录
    progressCommonDao.execute(x -> x.update(table)
            .set(table.field("f_study_time", Integer.class), DSL.nvl(table.field("f_study_time", Integer.class), 0).add(studyTime))
            .set(table.field("f_commit_time", Long.class), now)
            .set(table.field("f_lesson_location", String.class), location)
            .where(table.field("f_id", String.class).eq(log.getId())).execute());
    String flag = Optional.ofNullable(cache.get(CacheKeyConstant.CORSE_MEMBER_DAY_STUDY_UPDATE_MESSAGE_TRIGGER, String.class)).orElse("1");
    // add 2021-06-03  log的时长的同时更新章节进度的时长
/*
    TableImpl<?> csspTable = shardingConfigService.getTableName(progress.getCourseId(),
            ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
            ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
*/
    TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(log.getMemberId(), progress.getCourseId());
    progressCommonDao.execute(x -> x.update(csspTable)
            .set(csspTable.field("f_study_total_time", Integer.class), DSL.nvl(csspTable.field("f_study_total_time", Integer.class), 0).add(studyTime))
            .set(csspTable.field("f_modify_date", Timestamp.class), new Timestamp(System.currentTimeMillis()))
            .where(csspTable.field("f_id", String.class).eq(progress.getId()))
            .execute()
    );
    // 清除缓存
    courseCacheServiceSupport.clearProgress(log.getMemberId()+section.getReferenceId());
    // 重新获取新的章节进度数据
    CourseSectionStudyProgress newProgress = courseCacheServiceSupport.getProgress(log.getMemberId(), section.getReferenceId());

    //如果缓存中是1则发消息，反之不发
    if (flag.equals(CacheKeyConstant.CORSE_MEMBER_DAY_STUDY_UPDATE_MESSAGE_TRIGGER_TRUE)) {
      logger.info("发送人-课-天消息");
      // add by wangdongyan 异步更新人-课-天分表数据
        messageSender.send(MessageTypeContent.COURSE_STUDY_LOG_DAY_SPLIT_TIME_UPDATE,
                MessageHeaderContent.MEMBER_ID, log.getMemberId(),
                MessageHeaderContent.COURSE_ID, log.getCourseId(),
                MessageHeaderContent.STUDYDATE, date,
                MessageHeaderContent.STUDYCLIENTTYPE, log.getClientType() + "",
                MessageHeaderContent.STUDYTIME, studyTime + "");
    }
    logger.info("新增章节进度时长： memberId = {}, currentStudyTime={}", log.getMemberId(), studyTime);

    newProgress.setLessonLocation(location);
    newProgress.setLastAccessTime(now);
    if (consumer != null) {
      consumer.accept(newProgress);
    }

    progressCommonDao.execute(dslContext -> dslContext.update(csspTable)
            .set(csspTable.field("f_lesson_location", String.class), newProgress.getLessonLocation())
            .set(csspTable.field("f_last_access_time", Long.class), newProgress.getLastAccessTime())
            .set(csspTable.field("f_finish_status", Integer.class), newProgress.getFinishStatus())
            .set(csspTable.field("f_completed_rate", Integer.class), newProgress.getCompletedRate())
            .set(csspTable.field("f_modify_date", Timestamp.class), new Timestamp(System.currentTimeMillis()))
            .where(csspTable.field("f_id", String.class).eq(newProgress.getId())).execute()
    );
    courseCacheServiceSupport.clearProgress(log.getMemberId() + section.getReferenceId());
    // 每5分钟一提交时更新t_course_study_progress表的当前学习章节id
    if (null == courseStudyProgress.getCurrentSectionId() || !courseStudyProgress.getCurrentSectionId().equals(newProgress.getSectionId())){
      courseStudyProgressCommonDao.execute(dsl -> dsl.update(cacheTable)
              .set(cacheTable.field("f_current_section_id",String.class), newProgress.getSectionId())
              .where(cacheTable.field("f_id",String.class).eq(courseStudyProgress.getId()))
              .execute());
      // add 2020-4-24 异步更新studyProgress分表数据
      messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
              MessageHeaderContent.ID, courseStudyProgress.getId(),MessageHeaderContent.MEMBER_ID, courseStudyProgress.getMemberId());
    }

//    //更新最近学习课程  --  LJY
//    messageSender.send(MessageTypeContent.STUDY_CARD_UPDATE,
//        MessageHeaderContent.MEMBER_ID, log.getMemberId(),
//        MessageHeaderContent.COURSE_ID, log.getCourseId());
    String setKey = log.getMemberId() + ","+log.getCourseId();
    redis.process(x->x.hset(COURSE_PROGRESS_PRE_UPDATE_MAP, setKey, System.currentTimeMillis()+""));
    return newProgress;
  }

  @Override
  public void updateCourseStudyAsyn(String courseId, String memberId) {
	  this.deleteCourseStudyRedis(memberId, courseId);
    messageSender.send(MessageTypeContent.COURSE_PROGRESS_UPDATE_NEW,
        MessageHeaderContent.MEMBER_ID, memberId,
        MessageHeaderContent.COURSE_ID, courseId,
        MessageHeaderContent.FINISHSTIME, System.currentTimeMillis() + "");
  }

  @Override
  public Integer filterStudyTime(CourseStudyLog log, Integer studyTime,
      Optional<Integer> resourceTimeOptional, Integer configMaxTime, TableImpl<?> table) {

    if(studyTime == 0)
      return 0;
    logger.info("传参的学习时长， studyTime={}", studyTime);
    // updated by wangdongyan log分表中的流水记录
    CourseChapterSection section = courseCacheServiceSupport.getSection(log.getSectionId());

    // add by wangdongyan 12-10 单次增加的学习时长，以及当前学习章节的章节表总时长
    Integer singleStudyTime = studyTime - Optional.ofNullable(log.getStudyTime()).orElse(0);
    if(singleStudyTime < 0)
      return 0;
    // update 2019-11-29 优化查询，log查询时间太长，改为查询sectionProgress
//    Integer sectionStudyTotalTime = progressCommonDao
//        .execute(x -> x.select(table.field("f_study_time", Integer.class))).from(table)
//        .leftJoin(COURSE_CHAPTER_SECTION)
//        .on(COURSE_CHAPTER_SECTION.ID.eq(table.field("f_section_id", String.class)))
//        .where(table.field("f_member_id", String.class).eq(log.getMemberId()))
//        .and(COURSE_CHAPTER_SECTION.REFERENCE_ID.eq(section.getReferenceId()))
//        .and(table.field("f_study_time", Integer.class).gt(0))
//        .fetch(r -> {
//          CourseSectionStudyLog l = new CourseSectionStudyLog();
//          l.setStudyTime(Integer.valueOf(r.getValue("f_study_time").toString()));
//          return l;
//        }).stream().map(x -> x.getStudyTime()).reduce(0, (a, b) -> a + b);
/*
    TableImpl<?> csspTable = shardingConfigService.getTableName(log.getCourseId(), ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS, ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
*/
    TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(log.getMemberId(), section.getCourseId());
    Integer sectionStudyTotalTime = progressCommonDao.execute(x -> x.select(csspTable.field("f_study_total_time", Integer.class))
            .from(csspTable)
            .where(csspTable.field("f_section_id", String.class).eq(section.getReferenceId())
                    .and(csspTable.field("f_member_id", String.class).eq(log.getMemberId()))
            )).fetchOptional(csspTable.field("f_study_total_time", Integer.class)).orElse(0);
    // 不超过资源10倍
    int resourceTime = resourceTimeOptional
        .orElse(section.getTimeMinute() * 60 + section.getTimeSecond());
    if(sectionStudyTotalTime >= resourceTime * 10) {
      logger.info("filterStudyTime: studyTotalTime:{},maxResourceTime:maxResourceTime", studyTime,
          resourceTime * 10);
      return 0;
    }

    if(sectionStudyTotalTime + singleStudyTime > resourceTime * 10) {
      singleStudyTime = resourceTime * 10 - sectionStudyTotalTime;
    }
    logger.info(
        "当前更新资源的总时长： resourceTime={}, 当前更新资源的学习总时长，studyTotalTime={}, 每次请求后的实际学习时长，studyTime={}",
        resourceTime, sectionStudyTotalTime, singleStudyTime);

    // 课程的当天学习时间
    if(configMaxTime > 0) {
      // 查询log分表中的数据
      Integer dayTime = progressCommonDao.execute(x ->
          x.select(table.field("f_study_time", Integer.class)))
          .from(table)
          .where(table.field("f_member_id", String.class).eq(log.getMemberId()))
          .and(table.field("f_client_type", Integer.class).eq(log.getClientType()))
          .and(table.field("f_create_time", Long.class)
              .between(DateUtil.getTimesmorningByLong(log.getCreateTime()),
                  DateUtil.getTimesnightByLong(log.getCreateTime())))
          .and(table.field("f_study_time", Integer.class).gt(0))
          .fetch(r -> {
            CourseSectionStudyLog l = new CourseSectionStudyLog();
            l.setStudyTime(Integer.valueOf(r.getValue("f_study_time").toString()));
            return l;
          }).stream().map(x -> x.getStudyTime()).reduce(0, (a, b) -> a + b);
      logger.info("当天已学习的时长， dayTime={}， memberId={}, maxDayTime={},tableName={},clientType={}",
          dayTime, log.getMemberId(), configMaxTime, table, log.getClientType());
      if(dayTime > configMaxTime * 3600) {
        logger.info("filterStudyTime: dayTime:{},maxDayTime:{}", dayTime, configMaxTime);
        return 0;
      }
      if(dayTime + singleStudyTime > configMaxTime * 3600) {
        return configMaxTime * 3600 - dayTime;
      }
    }
    return singleStudyTime;
  }

  /**
   * MessageTypeContent.COURSE_PROGRESS_UPDATE 消息调用的方法，记录流水，计算进度
   *
   * 更新课程进度
   */
  @Deprecated
  @Override
  public CourseStudyProgress updateCourseStudyProgress(String memberId, String courseId,
      Boolean isCourse) {
    logger.debug("开始异步计算课程学习进度,memberId = {},courseId = {} ", memberId, courseId);
    TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
    this.deleteCourseStudyRedis(memberId, courseId);
    /**
     * 目的 修改时长，修改状态
     */
    long nowTime = System.currentTimeMillis();
    int oldStudyTime = 0;
    int currentStudyTime = 0;
    // 查询出用户注册记录
    CourseStudyProgress courseStudyProgress =courseStudyProgressCommonDao.execute(dslContext -> dslContext.select(cacheTable.fields()).from(cacheTable).where(cacheTable.field("f_course_id",String.class).eq(courseId),
            cacheTable.field("f_member_id",String.class).eq(memberId)).limit(1).fetchOne(r->r.into(CourseStudyProgress.class)));
    if(courseStudyProgress == null) {
      logger.debug("课程进度找不到 未开始更新,memberId = {}, courseId = {} ", memberId, courseId);
      return null;
    }
    logger.debug("开始异步更新课程进,memberId = {}, courseId = {} , courseStudeyProcess[id={}]", memberId,
        courseId,
        courseStudyProgress.getId());
    // 学习时长 = 所有版本的章节的总学习的时长(不过滤以前删除的章节的学习时长)
//        BigDecimal studentTimes = progressCommonDao.execute(
//                x -> x.select(COURSE_SECTION_STUDY_PROGRESS.STUDY_TOTAL_TIME.sum()).from(COURSE_SECTION_STUDY_PROGRESS)
//                        .where(COURSE_SECTION_STUDY_PROGRESS.COURSE_ID.eq(courseId)
//                                .and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId)))
//                        .fetchOne(COURSE_SECTION_STUDY_PROGRESS.STUDY_TOTAL_TIME.sum()));

    // updated by wangdongyan 直接sum求和比较慢
    // update for xdn
/*    TableImpl<?> csspTable = shardingConfigService.getTableName(courseId,
    // update for xdn   不用了好像
    TableImpl<?> csspTable = shardingConfigService.getTableName(courseId,
            ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
            ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);*/
    TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, courseId);
    Integer studentTimes = progressCommonDao.execute(
        x -> x.select(csspTable.field("f_study_total_time"))
            .from(csspTable)
            .where(csspTable.field("f_course_id", String.class).eq(courseId)
                .and(csspTable.field("f_member_id", String.class).eq(memberId)))
            .fetch(r -> r.getValue("f_study_total_time", Integer.class)))
        .stream().reduce(0, (a, b) -> a + b);
    if(courseStudyProgress.getBeginTime() == null) {
      progressCommonDao.execute(dslContext ->
          dslContext.select(csspTable.field("f_begin_time"))
              .from(csspTable)
              .where(csspTable.field("f_course_id", String.class).eq(courseId),
                      csspTable.field("f_member_id", String.class).eq(memberId))
              .orderBy(csspTable.field("f_begin_time")).limit(1)
              .fetchOptional(csspTable.field("f_begin_time", Long.class)))
          .ifPresent(courseStudyProgress::setBeginTime);
    }
    if(( courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_DEFAULT
        || courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_GIVEUP )
        && courseStudyProgress.getBeginTime() != null) {
      courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
    }

    // 修改前学习时长
    oldStudyTime = courseStudyProgress.getStudyTotalTime() == null ? 0
        : courseStudyProgress.getStudyTotalTime();
    // 当前学习时长
    currentStudyTime = studentTimes == null ? 0 : studentTimes;
    courseStudyProgress.setStudyTotalTime(currentStudyTime);
    // update for xdn
    String lastSectionId = progressCommonDao.execute(
        x -> x.select(csspTable.field("f_section_id")).from(csspTable)
            .where(csspTable.field("f_course_id", String.class).eq(courseId)
                .and(csspTable.field("f_member_id", String.class).eq(memberId)))
            .orderBy(csspTable.field("f_last_access_time").desc()).limit(1)
    ).fetchOne(csspTable.field("f_section_id", String.class));

    courseStudyProgress.setCurrentSectionId(lastSectionId);
    // 如果状态为未开始并且开始学习时间为null时没有最后访问时间
    if(( courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_DEFAULT )
        && courseStudyProgress.getBeginTime() == null) {
      courseStudyProgress.setLastAccessTime(null);
    } else {
      courseStudyProgress.setLastAccessTime(System.currentTimeMillis());
    }

    if(!courseStudyProgress.isFinish()) {
      // 1 用户需要必修的进度 = 必修的数量*100
      List<String> referenceIds = sectionDao
          .execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
              .innerJoin(COURSE_CHAPTER)
              .on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
          .where(COURSE_CHAPTER.COURSE_ID.eq(courseId)
              .and(COURSE_CHAPTER.VERSION_ID.eq(courseStudyProgress.getCourseVersionId()))
              .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED)))
          .fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID);

      // update for xdn
      // 2 用户当前学习版本必修的章节学习进度百分比
      Map<String, String> finishSectionIdsMap = progressCommonDao.execute(x ->
              x.select(csspTable.field("f_section_id")).from(csspTable)
                      .where(csspTable.field("f_member_id", String.class).eq(memberId))
                      .and(csspTable.field("f_course_id", String.class).eq(courseId))
                      .and(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
                              .or(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                      .fetch(csspTable.field("f_section_id", String.class)))
              .stream().collect(Collectors.toMap(r -> r, r -> r));
      List<String> finishList = new ArrayList<>();
      referenceIds.forEach(r -> {
        if (!org.jooq.tools.StringUtils.isEmpty(finishSectionIdsMap.get(r))) {
          finishList.add(r);
        }
      });

//      int finishNum = progressCommonDao.execute(x ->
//          x.select(COURSE_SECTION_STUDY_PROGRESS.ID.count()).from(COURSE_SECTION_STUDY_PROGRESS)
//              .where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.in(referenceIds))
//              .and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//              .and(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS
//                  .eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
//                  .or(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS
//                      .eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
//              .fetchOptional(COURSE_SECTION_STUDY_PROGRESS.ID.count()).orElse(0));
      int finishNum = finishList.size() > 0 ? finishList.size() : 0;
      if(referenceIds.size() == finishNum) {
        courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
        courseStudyProgress.setFinishTime(nowTime);
        courseStudyProgress.setCompletedRate(100);
      } else {
        courseStudyProgress.setCompletedRate(50);
      }
    }
    // add by wangdongyan 最后一次更新时间，分表使用
    courseStudyProgress.setLastModifyTime(nowTime);

    courseStudyProgressCommonDao.execute(dsl -> dsl.update(cacheTable).set(CourseStudyProgress.getUpdateMap(cacheTable,courseStudyProgress)).where(cacheTable.field("f_id",String.class).eq(courseStudyProgress.getId())).execute());

    messageSender.send(MessageTypeContent.COURSE_STUDY_PROGRESS_UPDATE,
        MessageHeaderContent.ID, courseStudyProgress.getId(),
        MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId(),MessageHeaderContent.MEMBER_ID,memberId);
//此函数未使用
//    //更新最近学习--LJY
//    logger.error("GET INTO###更新最近学习 updateCourseStudyProgress异步更新,MemberId={},courseId={} -- LJY", courseStudyProgress.getMemberId(),courseStudyProgress.getCourseId());
//    messageSender.send(MessageTypeContent.STUDY_CARD_UPDATE,
////            MessageHeaderContent.ID, courseStudyProgress.getId(),
//            MessageHeaderContent.MEMBER_ID, courseStudyProgress.getMemberId(),
//            MessageHeaderContent.COURSE_ID, courseStudyProgress.getCourseId());

    if(isCourse) {
      int studyTime = currentStudyTime - oldStudyTime;
      messageSender.send(MessageTypeContent.SUBJECT_PROGRESS_UPDATE_NEW,
          MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getId(),
          MessageHeaderContent.STUDYTIME, studyTime + "",MessageHeaderContent.MEMBER_ID,memberId);
    }
    return courseStudyProgress;
  }

  @Override
  public void updateMediaLength(String sectionId, Integer videoTotalTime) {
    CourseChapterSection section = courseCacheServiceSupport.getSection(sectionId);
    if(videoTotalTime > 0) {
      sectionDao.execute(x -> x.update(COURSE_CHAPTER_SECTION)
          .set(COURSE_CHAPTER_SECTION.TIME_SECOND, videoTotalTime)
          .set(COURSE_CHAPTER_SECTION.TIME_MINUTE, 0)
          .where(
              COURSE_CHAPTER_SECTION.REFERENCE_ID.eq(section.getReferenceId())
                  .or(COURSE_CHAPTER_SECTION.ID.eq(sectionId)))
          .execute());
      courseCacheServiceSupport.clearChapterSection(sectionId);
    }

  }

  private void frequentCheck(String key) {
    //验证频繁
    Long visitCount = cache.increment(key);
    if(visitCount.equals(1L)) {
      cache.expire(key, 3);
    }
    if(visitCount.equals(10L)) {
      throw new CourseStudyException(CourseExceptionService.REMARK_STUDY_OFTEN);
    }
  }

  @Override
  public CourseStudyLog startScormLog(String memberId, String sectionId, String scormId,
      Integer clientType, String path) {

    if(clientType == 2) {
      clientType = CourseSectionStudyLog.CLIENT_TYPE_APP;
    }
    // 初始化log
    CourseChapterSection section = courseCacheServiceSupport.getSection(sectionId);

    // updated by wangdongyan 2018-12-11 直接新增分表数据，不新增log汇总
    CourseStudyLog log = insertSubLog(memberId, clientType, section, path);
//        // 新增log分表数据的同时新增人-课-天分表的数据
//        String currentDate = this.changeTimeStyle(log.getCreateTime());
//        insertOrUpdateCourseSectionLogDay(memberId, section.getCourseId(), currentDate, clientType, 0);
    //初始化progress
    CourseSectionStudyProgress progress = courseCacheServiceSupport
        .getProgress(memberId, section.getReferenceId());
    if(progress == null) {
      progress = new CourseSectionStudyProgress();
      progress.forInsert();
      progress.setCourseId(section.getCourseId());
      progress.setSectionId(section.getReferenceId());
      progress.setMemberId(memberId);
      progress.setBeginTime(progress.getCreateTime());
      progress.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_STUDY);
      progress.setLastAccessTime(progress.getCreateTime());
      progress.setStudyTotalTime(0);
      // update for xdn
    /*  TableImpl<?> csspTable = shardingConfigService.getTableName(section.getCourseId(),
              ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
              ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);*/
      TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, section.getCourseId());
      CourseSectionStudyProgress cssp = progress;
      progressCommonDao.execute(dslContext -> cssp.insert(csspTable, dslContext)).execute();
      // progressCommonDao.insert(progress);
    }
    scormService.getScoreLogByMember(memberId, scormId).orElseGet(() -> {
      CourseSectionScormProgress courseSectionScormLog = new CourseSectionScormProgress();
      courseSectionScormLog.forInsert();
      courseSectionScormLog.setFinishStatus(1);
      courseSectionScormLog.setMemberId(memberId);
      courseSectionScormLog.setCourseId(section.getCourseId());
      courseSectionScormLog.setScormId(sectionId);
      courseSectionScormLog.setScormItemId(scormId);
      courseSectionScormLog.setStudyTime(0);
      return courseSectionScormLogCommonDao.insert(courseSectionScormLog);
    });
    return log;
  }

  @Override
  public int batchStudyProgress(List<CourseStudyLog> items, String organizationId) {
    messageSender
        .send(MessageTypeContent.COURSE_BATCH_STUDY, items, MessageHeaderContent.ORGANIZATION_ID,
            organizationId);
    return items.size();
  }

  /**
   * 定时更新studyProgress状态
   */
  @Override
  public void updateCourseStudyCache() {
    logger.error("开始从redis中取数据时间， time={}", System.currentTimeMillis());
    Map<String, String> maps = redis
        .process(x -> x.hgetAll(CourseProcessService.COURSE_PROGRESS_PRE_UPDATE_MAP));
    //logger.info("updateCourseStudyCache, "+ maps.size());
    logger.error("从redis中取出数据结束时间， time={}", System.currentTimeMillis());
    long now = System.currentTimeMillis();
    // 2024反腐 因为提交时间改成10分钟，这里需要容错，SO 暂定10+2
    long timeout = 12 * 60 * 1000;

    maps.forEach((k, timeStr) -> {
      long cacheTime = Long.parseLong(timeStr);
      if(now - cacheTime > timeout) {
        String[] keyList = k.split(",");
        if(keyList.length == 2) {
          redis.process(x -> x.hdel(CourseProcessService.COURSE_PROGRESS_PRE_UPDATE_MAP, k));
          logger.info("实施监听发送的消息，memberId={}, courseId={}", keyList[0], keyList[1]);
          messageSender.send(MessageTypeContent.COURSE_PROGRESS_UPDATE_NEW,
                  MessageHeaderContent.MEMBER_ID, keyList[0],
                  MessageHeaderContent.COURSE_ID, keyList[1],
                  MessageHeaderContent.FINISHSTIME, System.currentTimeMillis() + "");
        }
      }
    });
  }

  @Override
  public void deleteCourseStudyRedis(String memberId, String courseId) {
    redis.process(x -> x
        .hdel(CourseProcessService.COURSE_PROGRESS_PRE_UPDATE_MAP, memberId + "," + courseId));
  }


  /**
   * 新增专题log（已弃用）
   */
  @Deprecated
  @Override
  public void insertSubjectLog(String memberId, String subjectId, String sectionId,
      int finishStatus, int studyTime, Long createTime) {
    courseInfoDao.getOptional(subjectId)
        .filter(c -> CourseInfo.BUSINESS_TYPE_SUBJECT.equals(c.getBusinessType()))
        .ifPresent(c -> {
          // update 2019-11-29 专题log已无用
//          SubjectSectionStudyLog log = new SubjectSectionStudyLog();
//          log.forInsert();
//          log.setMemberId(memberId);
//          log.setSubjectId(subjectId);
//          log.setSectionId(sectionId);
//          log.setFinishStatus(finishStatus);
//          log.setStudyTime(studyTime);
//          log.setCreateTime(createTime);
//          subjectLogDao.insert(log);
//          logger.info("本次专题流水新加的时长， studyTime={}, memberId={}, subjectId= {}", studyTime, memberId,
//              subjectId);
//          // add by Acong 发送subjectLog分表同步消息
//          messageSender.send(MessageTypeContent.SPLIT_SUBJECT_SECTION_STUDY_LOG_INSERT,
//              MessageHeaderContent.ID, log.getId());
          insertOrUpdateSubjectStudyLogDay(memberId, subjectId, studyTime, createTime == null ? System.currentTimeMillis() : createTime);
        });

  }

  /**
   * add by wangdongyan 2019-01-23 新增人-专题-天分表，维护该表数据
   */
  private void insertOrUpdateSubjectStudyLogDay(String memberId, String subjectId, int studyTime,
      Long createTime) {
    // 如果时长为0 不修改数据
    logger
        .info("本次专题更新人-专题-天时长为，studyTime={}, subjectId={}, memberId={}, currentTime={}", studyTime,
            subjectId, memberId, System.currentTimeMillis());
//		if (studyTime == 0) return ;
    TableImpl<?> table = courseCacheServiceSupport
        .getCacheTable(memberId, SplitTableConfig.SUBJECT_STUDY_LOG_DAY);
    String date = this.changeTimeStyle(createTime);
    // 将date转换为年  月  日
    int year = Integer.parseInt(date.substring(0, 4));// 只有年
    int month = Integer.parseInt(date.substring(0, 6)); // 包括年月
    int day = Integer.parseInt(date);// 包括年月日
    SubjectSectionStudyLog subjectLog = subjectLogDao
        .execute(x -> x.select(table.field("f_id", String.class))
            .from(table)
            .where(table.field("f_subject_id", String.class).eq(subjectId)
                .and(table.field("f_member_id", String.class).eq(memberId))
                .and(table.field("f_day", Integer.class).eq(day)))
            .limit(1))
        .fetchOptional(r -> {
          SubjectSectionStudyLog log = new SubjectSectionStudyLog();
          log.setId(r.getValue("f_id").toString());
          return log;
        }).orElseGet(() -> {
          SubjectSectionStudyLog log = new SubjectSectionStudyLog();
          log.setMemberId(memberId);
          log.setSubjectId(subjectId);

          return log;
        });

    if(subjectLog.getId() == null) {
      // 赋值年月日
      subjectLog.setYear(year);
      subjectLog.setMonth(month);
      subjectLog.setDay(day);
      subjectLog.forInsert();
      subjectLogDao.execute(x ->
          x.insertInto(table, table.field("f_id", String.class),
              table.field("f_member_id", String.class),
              table.field("f_subject_id", String.class),
              table.field("f_study_time", Integer.class),
              table.field("f_day", Integer.class),
              table.field("f_month", Integer.class),
              table.field("f_year", Integer.class),
              table.field("f_create_time", Long.class))
              .values(subjectLog.getId(),
                  subjectLog.getMemberId(),
                  subjectLog.getSubjectId(),
                  studyTime,
                  subjectLog.getDay(),
                  subjectLog.getMonth(),
                  subjectLog.getYear(),
                  subjectLog.getCreateTime())
              .execute()
      );
    } else {
      subjectLogDao.execute(x ->
          x.update(table)
              .set(table.field("f_study_time", Integer.class),
                  DSL.nvl(table.field("f_study_time", Integer.class), 0).add(studyTime))
              .where(table.field("f_id", String.class).eq(subjectLog.getId())).execute()
      );
    }

  }


  @Override
  public CourseStudyLog insertSubLog(String memberId, Integer clientType,
      CourseChapterSection section, String path) {
    TableImpl<?> table = courseCacheServiceSupport
        .getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
    Field<?>[] fileds = {
        table.field("f_id", String.class),
        table.field("f_member_id", String.class),
        table.field("f_course_id", String.class),
        table.field("f_section_id", String.class),
        table.field("f_client_type", Integer.class),
        table.field("f_finish_status", Integer.class),
        table.field("f_create_time", Long.class),
    };
    CourseStudyLog log = new CourseStudyLog();
    log.forInsert();
    log.setMemberId(memberId);
    log.setCourseId(section.getCourseId());
    log.setSectionId(section.getId());
    log.setClientType(clientType);
    log.setFinishStatus(CourseSectionStudyLog.FINISH_STATUS_UNSTART);
    Object[] values = {
        log.getId(),
        log.getMemberId(),
        log.getCourseId(),
        log.getSectionId(),
        log.getClientType(),
        log.getFinishStatus(),
        log.getCreateTime()
    };
    logCommonDao.execute(e ->
        e.insertInto(table, fileds).values(values).execute()
    );
    return log;
  }


  /**
   * 仅仅更新最后访问的位子 <br/>
   *
   * @param log 学习日志对象
   */
  @Override
  public void updateLastStudy(CourseStudyLog log) {
    // @TODO 兼容原来的 referenceId
    TableImpl<?> cacheTable = courseCacheService.getCacheTable(log.getMemberId(), SplitTableConfig.COURSE_STUDY_PROGRESS);
    courseStudyProgressCommonDao.execute(dsl ->
        dsl.update(cacheTable)
            .set(cacheTable.field("f_current_section_id",String.class), log.getReferenceId())
            .where(cacheTable.field("f_course_id",String.class).eq(log.getCourseId()),
                    cacheTable.field("f_member_id",String.class).eq(log.getMemberId()))
            .execute());
    // add 2020-4-24 异步更新studyProgress分表数据
    Optional<String> progressId = courseStudyProgressCommonDao.execute(x -> x.select(cacheTable.field("f_id",String.class)).from(cacheTable)
            .where(cacheTable.field("f_course_id",String.class).eq(log.getCourseId()).and(cacheTable.field("f_member_id",String.class).eq(log.getMemberId())))
            .limit(1)
            .fetchOptional(cacheTable.field("f_id",String.class))
    );
    progressId.ifPresent(id -> {
      messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
              MessageHeaderContent.ID, id,MessageHeaderContent.MEMBER_ID, log.getMemberId());
    });
  }


  /**
   * 新增或者修改人-课-天数据（已弃用）
   */
  @Deprecated
  @Override
  public void insertOrUpdateCourseSectionLogDay(String memberId, String courseId, String date,
      Integer clientType,
      int studyTotalTime) {
    // 如果单次学习时长为0，不进行操作
    if(studyTotalTime == 0) {
      return;
    }
    // 查询当前更新人员属于哪个分表
    String path = orgDao.execute(
        x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).leftJoin(MEMBER)
            .on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(MEMBER.ID.eq(memberId))
            .fetchOne(ORGANIZATION.PATH));
    TableImpl<?> table = courseCacheServiceSupport
        .getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG_DAY);
    // 将date转换为年  月  日
    int year = Integer.parseInt(date.substring(0, 4));// 只有年
    int month = Integer.parseInt(date.substring(0, 6)); // 包括年月
    int day = Integer.parseInt(date);// 包括年月日

    // 查询人-课-天分表中的是否存在当天的数据如果存在则修改不存在则新增(一天只能有一天数据)
    CourseStudyLog studyDayLog = progressCommonDao
        .execute(x -> x.select(table.field("f_id", String.class))
            .from(table)
            .where(table.field("f_course_id", String.class).eq(courseId)
                .and(table.field("f_member_id", String.class).eq(memberId))
                .and(table.field("f_day", Integer.class).eq(day)))
            .limit(1))
        .fetchOptional(r -> {
          CourseStudyLog log = new CourseStudyLog();
          log.setId(r.getValue("f_id").toString());
          return log;
        }).orElseGet(() -> {
          CourseStudyLog log = new CourseStudyLog();
          log.setMemberId(memberId);
          log.setCourseId(courseId);

          return log;
        });

    if(studyDayLog.getId() == null) {
      // 赋值年月日
      studyDayLog.setYear(year);
      studyDayLog.setMonth(month);
      studyDayLog.setDay(day);
      studyDayLog.forInsert();
      if(clientType == CourseSectionStudyLog.CLIENT_TYPE_APP) {
        studyDayLog.setAppStudyTime(studyTotalTime);
      } else {
        studyDayLog.setPcStudyTime(studyTotalTime);
      }
      progressCommonDao.execute(x ->
          x.insertInto(table, table.field("f_id", String.class),
              table.field("f_member_id", String.class),
              table.field("f_course_id", String.class),
              table.field("f_app_study_time", Integer.class),
              table.field("f_pc_study_time", Integer.class),
              table.field("f_study_time", Integer.class),
              table.field("f_day", Integer.class),
              table.field("f_month", Integer.class),
              table.field("f_year", Integer.class),
              table.field("f_create_time", Long.class))
              .values(studyDayLog.getId(),
                  studyDayLog.getMemberId(),
                  studyDayLog.getCourseId(),
                  studyDayLog.getAppStudyTime(),
                  studyDayLog.getPcStudyTime(),
                  studyTotalTime,
                  studyDayLog.getDay(),
                  studyDayLog.getMonth(),
                  studyDayLog.getYear(),
                  studyDayLog.getCreateTime())
              .execute()
      );
    } else {
      if(studyTotalTime == 0)
        return;
      if(clientType == CourseSectionStudyLog.CLIENT_TYPE_APP) {
        progressCommonDao.execute(x ->
            x.update(table).set(table.field("f_app_study_time", Integer.class),
                DSL.nvl(table.field("f_app_study_time", Integer.class), 0).add(studyTotalTime))
                .set(table.field("f_study_time", Integer.class),
                    DSL.nvl(table.field("f_study_time", Integer.class), 0).add(studyTotalTime))
                .where(table.field("f_id", String.class).eq(studyDayLog.getId())).execute()
        );
      } else {
        progressCommonDao.execute(x ->
            x.update(table).set(table.field("f_pc_study_time", Integer.class),
                DSL.nvl(table.field("f_pc_study_time", Integer.class), 0).add(studyTotalTime))
                .set(table.field("f_study_time", Integer.class),
                    DSL.nvl(table.field("f_study_time", Integer.class), 0).add(studyTotalTime))
                .where(table.field("f_id", String.class).eq(studyDayLog.getId())).execute()
        );
      }
    }
  }

  @Override
  public CourseStudyLog findCourseSectionLog(String logId, String path) {
    TableImpl<?> table = courseCacheServiceSupport.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
    return logCommonDao.execute(x -> x.select(table.field("f_id", String.class),
        table.field("f_member_id", String.class),
        table.field("f_course_id", String.class),
        table.field("f_section_id", String.class),
        table.field("f_client_type", Integer.class),
        table.field("f_study_time", Integer.class),
        table.field("f_create_time", Long.class)
    ).from(table).where(table.field("f_id", String.class).eq(logId)))
        .fetchOptional(r -> {
          CourseStudyLog log = new CourseStudyLog();
          log.setId(r.getValue("f_id").toString());
          log.setMemberId(r.getValue("f_member_id").toString());
          log.setCourseId(r.getValue("f_course_id").toString());
          log.setSectionId(r.getValue("f_section_id").toString());
          log.setClientType(Integer.valueOf(r.getValue("f_client_type").toString()));
          log.setStudyTime(Integer.valueOf(
              r.getValue("f_study_time") == null ? "0" : r.getValue("f_study_time").toString()));
          log.setCreateTime(Long.valueOf(r.getValue("f_create_time").toString()));
          return log;
        }).orElse(null);
  }

  private String changeTimeStyle(Long createTime) {
    return new SimpleDateFormat("yyyyMMdd").format(new Date(createTime));
  }

  /**
   * 课程时长统计表与流水表不统一
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void repairCourseProgressTotalTimeByLog() {
    logger.info("【课程时长统计表与流水表不统一】,START.");
    Date start = repairDataConfig.getCourseTime(0).get("start");
    Date end = repairDataConfig.getCourseTime(0).get("end");
    Date now = new Date();
    if(now.after(start) && now.before(end)) {
      List<TempMember> members = tempMemberCommonDao.fetch(TEMP_MEMBER.FLAG.eq(0));
      long startTime = 0;
      members.forEach(m -> {
        String memberId = m.getMemberId();
        logger.info("【课程时长统计表与流水表不统一】,开始处理人员信息:{}.", memberId);
        TableImpl<?> courseStudyProgressTable = courseCacheServiceSupport
            .getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);

        List<String> courseIds = courseStudyProgressCommonDao.execute(x -> x.select(
            courseStudyProgressTable.field("f_course_id", String.class)
        )
            .from(courseStudyProgressTable)
            .innerJoin(COURSE_INFO)
            .on(COURSE_INFO.ID.eq(courseStudyProgressTable.field("f_course_id", String.class)))
            .where(courseStudyProgressTable.field("f_member_id", String.class).eq(memberId)
                .and(COURSE_INFO.BUSINESS_TYPE.eq(0))
                .and(courseStudyProgressTable.field("f_member_id", String.class).eq(memberId)))
            //.and(courseStudyProgressTable.field("f_last_access_time", Long.class).gt(startTime)))
            .fetch(0, String.class));
        //获取对应人学习的课程进度记录
             /* List<CourseStudyProgress> courseStudyProgressList = courseStudyProgressCommonDao.execute(cp ->
                      cp.selectDistinct(Fields.start().add(COURSE_STUDY_PROGRESS.COURSE_ID, COURSE_STUDY_PROGRESS.COURSE_VERSION_ID).end())
                              .from(COURSE_STUDY_PROGRESS).innerJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
                              .where(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId)).and(COURSE_INFO.BUSINESS_TYPE.eq(0))
                              .and(COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME.gt(startTime)).fetchInto(CourseStudyProgress.class)
              );*/
        //遍历学习过的所有课程信息
        courseIds.stream().forEach(cs -> {
          courseRepairServiceSupport.repair(m, cs);
        });
        logger.info("【课程时长统计表与流水表不统一】,更新人员标记位.");
        m.setFlag(1);
        tempMemberCommonDao.update(m);
      });
      logger.info("【课程时长统计表与流水表不统一】,END.");
    } else {
      logger.info("【课程时长统计表与流水表不统一】,超出可执行时间范围,END.");
    }


  }


  /**
   * 专题中同一门课程时长重复记录问题(暂不使用,sharding未改)
   */
  @Transactional(rollbackFor = Exception.class)
  @Override
  @Deprecated
  public void repairSubjectRepeatCourseRecord() {
    Date start = repairDataConfig.getCourseTime(1).get("start");
    Date end = repairDataConfig.getCourseTime(1).get("end");
    Date now = new Date();
    if(now.after(start) && now.before(end)) {
      //获取指定标题的专题
      String title = "2018年浙江公司“六好”党支部建设党员网上学习专区";
      final Optional<CourseInfo> courseInfo = courseInfoDao.fetchOne(COURSE_INFO.NAME.eq(title));
      courseInfo.ifPresent(x -> {
        String courseId = x.getId();
        String orgIds = repairDataConfig.getDuplicateOrgIds();
        logger.info("【专题同一课程重复记录时长】,待处理组织ID为:{}", orgIds);
        if(org.springframework.util.StringUtils.isEmpty(orgIds)) {
          return;
        }
        String[] orgAry = orgIds.split(",");
        Arrays.stream(orgAry).forEach(org -> {
          Optional<SplitTableConfig> splitLogConfig = splitTableConfigDao.fetchOne
              (SPLIT_TABLE_CONFIG.ORGANIZATION_ID.eq(org)
                  .and(SPLIT_TABLE_CONFIG.SOURCE.eq(SplitTableConfig.COURSE_STUDY_PROGRESS)));
          splitLogConfig.ifPresent(sc -> {
            TableImpl<?> courseStudyProgressTable = getTable(sc.getTargetTable());
            //获取学习过该专题的所有人
            List<String> memberIds = courseStudyProgressCommonDao.execute(dao -> dao.
                selectDistinct(courseStudyProgressTable.field("f_member_id", String.class))
                .from(courseStudyProgressTable)
                .where(courseStudyProgressTable.field("f_course_id", String.class).eq(courseId))
                .fetch(0, String.class));
            logger.info("【专题同一课程重复记录时长】,待处理用户数量为:{}", memberIds.size());
            // 获取对应人的专题章节学习流水
            memberIds.forEach(memberId -> {
              //判断该人是否处理过
              String isDeal = cache.get(memberId, String.class);
              if(StringUtils.isEmpty(isDeal)) {
                logger.info("【专题同一课程重复记录时长】,开始处理用户:{}", memberId);
                TableImpl<?> courseSectionTable = CourseSectionStudyProgressUtil.getTable(memberId, courseId);
                CourseSectionStudyProgressUtil.TableField tableField = CourseSectionStudyProgressUtil.initTableField(courseSectionTable);
                List<CourseSectionStudyProgress> courseStudyProgressList =
                    courseSectionStudyProgressCommonDao.execute(
                        r -> r.select(Fields.start().add(courseSectionTable.fields()).end())
                            .from(courseSectionTable)
                            .innerJoin(COURSE_CHAPTER_SECTION)
                            .on(tableField.SECTION_ID()
                                .eq(COURSE_CHAPTER_SECTION.REFERENCE_ID))
                            .where(tableField.COURSE_ID().eq(courseId))
                            .and(tableField.MEMBER_ID().eq(memberId))
                            .groupBy(tableField.MEMBER_ID(),
                                COURSE_CHAPTER_SECTION.RESOURCE_ID)
                            .having(DSL.count(COURSE_CHAPTER_SECTION.RESOURCE_ID).ge(1))
                            .fetchInto(CourseSectionStudyProgress.class));
                //正常学习记录
                List<String> courseStudyIdList = Lists.newArrayList();
                courseStudyProgressList.forEach(cps -> courseStudyIdList.add(cps.getId()));
                //获取重复记录
                List<RepeatCourseSectionStudyProgress> repeatCourseSectionStudyProgressList =
                    courseSectionStudyProgressCommonDao.execute(
                        r -> r.select(Fields.start().add(courseSectionTable).end())
                            .from(courseSectionTable)
                            .where(tableField.COURSE_ID().eq(courseId))
                            .and(tableField.MEMBER_ID().eq(memberId))
                            .and(tableField.ID().notIn(courseStudyIdList))
                            .fetch().map(rcs -> {
                              RepeatCourseSectionStudyProgress repeatCourseSectionStudyProgress = new RepeatCourseSectionStudyProgress();
                              repeatCourseSectionStudyProgress.setId(rcs.get("f_id", String.class));
                              repeatCourseSectionStudyProgress
                                  .setCreateTime(rcs.getValue("f_create_time", Long.class));
                              repeatCourseSectionStudyProgress
                                  .setFinishStatus(rcs.getValue("f_finish_status", Integer.class));
                              repeatCourseSectionStudyProgress
                                  .setMemberId(rcs.getValue("f_member_id", String.class));
                              repeatCourseSectionStudyProgress
                                  .setSectionId(rcs.getValue("f_section_id", String.class));
                              repeatCourseSectionStudyProgress.setStudyTotalTime(
                                  rcs.getValue("f_study_total_time", Integer.class));
                              repeatCourseSectionStudyProgress
                                  .setCourseId(rcs.getValue("f_course_id", String.class));
                              return repeatCourseSectionStudyProgress;
                            }));

                if(!CollectionUtils.isEmpty(repeatCourseSectionStudyProgressList)) {
                  // 获取重复总时长
                  int repeatStudyTotalTime = repeatCourseSectionStudyProgressList.stream()
                      .mapToInt(r -> r.getStudyTotalTime()).sum();
                  logger
                      .info("【专题同一课程重复记录时长】,用户memberId={},重复总时长repeatStudyTotalTime={},", memberId,
                          repeatStudyTotalTime);
                  // 更新专题学习进度表、专题章节学习流水记录及其分表对应的时长
                  Optional<CourseStudyProgress> courseStudyProgress =
                      courseStudyProgressCommonDao.execute(cp ->
                          cp.select(Fields.start()
                              .add(courseStudyProgressTable.field("f_id", String.class),
                                  courseStudyProgressTable
                                      .field("f_study_total_time", Integer.class)).end())
                              .from(courseStudyProgressTable)
                              .where(courseStudyProgressTable.field("f_course_id", String.class)
                                  .eq(courseId)
                                  .and(courseStudyProgressTable.field("f_member_id", String.class)
                                      .eq(memberId)))
                              .fetchOptional()
                              .map(r -> {
                                String id = r.getValue(0, String.class);
                                Integer studyTime = r.getValue(1, Integer.class);
                                CourseStudyProgress courseStudyPro = new CourseStudyProgress();
                                courseStudyPro.setId(id);
                                courseStudyPro.setStudyTotalTime(studyTime);
                                return courseStudyPro;
                              })
                      );
                  // 更新专题总时长
                  courseStudyProgress.ifPresent(cs -> {
                    cs.setStudyTotalTime(
                        (int)( cs.getStudyTotalTime().longValue() - repeatStudyTotalTime ));
                    courseStudyProgressCommonDao.execute(cp -> cp.update(courseStudyProgressTable)
                        .set(courseStudyProgressTable.field("f_study_total_time", Integer.class),
                            cs.getStudyTotalTime() - repeatStudyTotalTime)
                        .where(courseStudyProgressTable.field("f_id", String.class).eq(cs.getId()))
                    );
                  });

                  //保存删除记录流水
                  repeatCourseSectionStudyProgressDao.insert(repeatCourseSectionStudyProgressList);
                  //待删除ID
                  List<String> repeatCourseSectionStudyProgressIds = Lists.newArrayList();
                  repeatCourseSectionStudyProgressList
                      .forEach(rcs -> repeatCourseSectionStudyProgressIds.add(rcs.getId()));
                  //删除章节信息
                  courseSectionStudyProgressCommonDao.delete(repeatCourseSectionStudyProgressIds);

                }
                //获取所有章节总数 >= 1的记录
                TableImpl<?> subjectSectionStudyLogTable = courseCacheServiceSupport
                    .getCacheTable(memberId, SplitTableConfig.SUBJECT_SECTION_STUDY_LOG);

                List<String> repeatSectionIds =
                    repeatCourseSectionStudyProgressList.stream()
                        .map(RepeatCourseSectionStudyProgress :: getSectionId)
                        .collect(Collectors.toList());

                List<TempSubjectSectionStudyLog> repeatSubjectSectionStudyLogList =
                    subjectLogDao.execute(
                        sj -> sj.select(Fields.start().add(subjectSectionStudyLogTable).end())
                            .from(subjectSectionStudyLogTable)
                            .where(subjectSectionStudyLogTable.field("f_member_id", String.class)
                                .eq(memberId)
                                .and(subjectSectionStudyLogTable.field("f_subject_id", String.class)
                                    .eq(courseId)))
                            .and(subjectSectionStudyLogTable.field("f_section_id", String.class)
                                .in(repeatSectionIds)).fetch().map(sb -> {
                              TempSubjectSectionStudyLog log = new TempSubjectSectionStudyLog();
                              log.setId(sb.getValue("f_id", String.class));
                              log.setClientType(sb.getValue("f_client_type", Integer.class));
                              log.setCreateTime(sb.getValue("f_create_time", Long.class));
                              log.setFinishStatus(sb.getValue("f_finish_status", Integer.class));
                              log.setMemberId(sb.getValue("f_member_id", String.class));
                              log.setSectionId(sb.getValue("f_section_id", String.class));
                              log.setStudyTime(sb.getValue("f_study_time", Integer.class));
                              log.setSubjectId(sb.getValue("f_subject_id", String.class));
                              return log;
                            }));

                if(!CollectionUtils.isEmpty(repeatSubjectSectionStudyLogList)) {

                  tempSubjectSectionStudyLogDao.insert(repeatSubjectSectionStudyLogList);
                  //待删除log ID
                  List<String> subjectSectionStudyLogSectionIdList =
                      repeatSubjectSectionStudyLogList.stream()
                          .map(TempSubjectSectionStudyLog :: getSectionId)
                          .collect(Collectors.toList());
                  //总表删除LOG记录
                  List<String> totalTableDeleteIds = subjectLogDao.execute(dsl -> dsl
                          .select(SUBJECT_SECTION_STUDY_LOG.ID)
                          .from(SUBJECT_SECTION_STUDY_LOG)
                          .where(
                                  SUBJECT_SECTION_STUDY_LOG.MEMBER_ID.eq(memberId)
                                  .and(SUBJECT_SECTION_STUDY_LOG.SECTION_ID.in(subjectSectionStudyLogSectionIdList))
                          )
                          .fetch(SUBJECT_SECTION_STUDY_LOG.ID));
                  subjectLogDao.delete(totalTableDeleteIds);
                  dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.getTableName(SUBJECT_SECTION_STUDY_LOG), totalTableDeleteIds,""));

                  //从分表删除LOG记录
                  List<String> ids = subjectLogDao.execute(dsl -> dsl
                          .select(subjectSectionStudyLogTable.field("f_id", String.class))
                          .from(subjectSectionStudyLogTable)
                          .where(
                                  subjectSectionStudyLogTable.field("f_member_id", String.class)
                                  .eq(memberId)).and(subjectSectionStudyLogTable.field("f_section_id", String.class)
                                  .in(subjectSectionStudyLogSectionIdList)
                          ).fetch(subjectSectionStudyLogTable.field("f_id", String.class)));
                  subjectLogDao.execute(sld -> sld.delete(subjectSectionStudyLogTable).where(subjectSectionStudyLogTable.field("f_id", String.class).in(ids)).execute());
                  dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.getTableName(subjectSectionStudyLogTable), ids,""));

                }

                //处理完成后入缓存
                cache.set(memberId, memberId);
              }
            });
            logger.info("【专题同一课程重复记录时长】,处理完成.");
          });
        });
      });
    } else {
      logger.info("【专题同一课程重复记录时长】,超出可执行时间限制,END.");
    }


  }


  /**
   * 根据分省处理
   */
  @Override
  public void repairCourseProgressTotalTimeBySplitLog() {

    logger.info("【课程时长统计表与流水表不统一,分省处理】,START.");
    Date start = repairDataConfig.getCourseTime(0).get("start");
    Date end = repairDataConfig.getCourseTime(0).get("end");
    Date now = new Date();
    if(now.after(start) && now.before(end)) {
      String orgIds = repairDataConfig.getOrgIds();
      logger.info("【课程时长统计表与流水表不统一,分省处理】,待处理组织ID为:{}", orgIds);
      if(org.springframework.util.StringUtils.isEmpty(orgIds)) {
        return;
      }
      String[] orgAry = orgIds.split(",");
      Arrays.stream(orgAry).forEach(org -> {
        Optional<SplitTableConfig> splitLogConfig = splitTableConfigDao.fetchOne
            (SPLIT_TABLE_CONFIG.ORGANIZATION_ID.eq(org)
                .and(SPLIT_TABLE_CONFIG.SOURCE.eq(SplitTableConfig.COURSE_STUDY_PROGRESS)));
        splitLogConfig.ifPresent(x -> {
          List<String> memberIds = memberCommonDao.execute(mc -> mc.select(MEMBER.ID).from(MEMBER).
              innerJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).
              innerJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
              .where(ORGANIZATION_DETAIL.ROOT.eq(org))
              .fetch(MEMBER.ID));
          long startTime = 0L;
          TableImpl<?> courseStudyProgressTable = getTable(x.getTargetTable());
          memberIds.forEach(mi -> {
            Optional<TempMember> optionalTempMember = tempMemberCommonDao
                .fetchOne(TEMP_MEMBER.MEMBER_ID.eq(mi).and(TEMP_MEMBER.FLAG.eq(0)));
            optionalTempMember.ifPresent(m -> {
              String memberId = m.getMemberId();
              logger.info("【课程时长统计表与流水表不统一,分省处理】,开始处理人员信息:{}.", memberId);
              //获取对应人学习的课程进度记录
              List<String> courseStudyProgressList = courseStudyProgressCommonDao.execute(cp ->
                  cp.selectDistinct(Fields.start()
                      .add(courseStudyProgressTable.field("f_course_id")).end())
                      .from(courseStudyProgressTable).innerJoin(COURSE_INFO)
                      .on(courseStudyProgressTable
                          .field("f_course_id", String.class).eq(COURSE_INFO.ID))
                      .where(courseStudyProgressTable.field("f_member_id", String.class)
                          .eq(memberId))
                      .and(COURSE_INFO.BUSINESS_TYPE.eq(0))
                      // .and(courseStudyProgressTable.field("f_last_access_time",Long.class).gt(startTime))
                      .fetch(0, String.class)
              );
              //遍历学习过的所有课程信息
              courseStudyProgressList.stream().forEach(cs -> {
                courseRepairServiceSupport.repair(m, cs);
              });
              logger.info("【课程时长统计表与流水表不统一,分省处理】,更新人员标记位.");
              m.setFlag(1);
              tempMemberCommonDao.update(m);
            });

          });
          logger.info("【课程时长统计表与流水表不统一,分省处理】,END.");
        });
      });

    } else {
      logger.info("【课程时长统计表与流水表不统一,分省处理】,超出可执行时间范围,END.");
    }
  }


  private TableImpl<?> getTable(String code) {
    switch (code) {
      case "t_course_study_progress_ah":
        return COURSE_STUDY_PROGRESS_AH;
      case "t_course_study_progress_bj":
        return COURSE_STUDY_PROGRESS_BJ;
      case "t_course_study_progress_cm":
        return COURSE_STUDY_PROGRESS_CM;
      case "t_course_study_progress_cq":
        return COURSE_STUDY_PROGRESS_CQ;
      case "t_course_study_progress_eb":
        return COURSE_STUDY_PROGRESS_EB;
      case "t_course_study_progress_fj":
        return COURSE_STUDY_PROGRESS_FJ;
      case "t_course_study_progress_gd":
        return COURSE_STUDY_PROGRESS_GD;
      case "t_course_study_progress_gs":
        return COURSE_STUDY_PROGRESS_GS;
      case "t_course_study_progress_gx":
        return COURSE_STUDY_PROGRESS_GX;
      case "t_course_study_progress_gz":
        return COURSE_STUDY_PROGRESS_GZ;
      case "t_course_study_progress_hb":
        return COURSE_STUDY_PROGRESS_HB;
      case "t_course_study_progress_hl":
        return COURSE_STUDY_PROGRESS_HL;
      case "t_course_study_progress_hn":
        return COURSE_STUDY_PROGRESS_HN;
      case "t_course_study_progress_jl":
        return COURSE_STUDY_PROGRESS_JL;
      case "t_course_study_progress_js":
        return COURSE_STUDY_PROGRESS_JS;
      case "t_course_study_progress_jx":
        return COURSE_STUDY_PROGRESS_JX;
      case "t_course_study_progress_ln":
        return COURSE_STUDY_PROGRESS_LN;
      case "t_course_study_progress_nm":
        return COURSE_STUDY_PROGRESS_NM;
      case "t_course_study_progress_nx":
        return COURSE_STUDY_PROGRESS_NX;
      case "t_course_study_progress_other":
        return COURSE_STUDY_PROGRESS_OTHER;
      case "t_course_study_progress_qh":
        return COURSE_STUDY_PROGRESS_QH;
      case "t_course_study_progress_qo":
        return COURSE_STUDY_PROGRESS_QO;
      case "t_course_study_progress_sc":
        return COURSE_STUDY_PROGRESS_SC;
      case "t_course_study_progress_sd":
        return COURSE_STUDY_PROGRESS_SD;
      case "t_course_study_progress_sh":
        return COURSE_STUDY_PROGRESS_SH;
      case "t_course_study_progress_sn":
        return COURSE_STUDY_PROGRESS_SN;
      case "t_course_study_progress_sx":
        return COURSE_STUDY_PROGRESS_SX;
      case "t_course_study_progress_tj":
        return COURSE_STUDY_PROGRESS_TJ;
      case "t_course_study_progress_xj":
        return COURSE_STUDY_PROGRESS_XJ;
      case "t_course_study_progress_xn":
        return COURSE_STUDY_PROGRESS_XN;
      case "t_course_study_progress_xz":
        return COURSE_STUDY_PROGRESS_XZ;
      case "t_course_study_progress_yn":
        return COURSE_STUDY_PROGRESS_YN;
      case "t_course_study_progress_zgtt":
        return COURSE_STUDY_PROGRESS_ZGTT;
      case "t_course_study_progress_zj":
        return COURSE_STUDY_PROGRESS_ZJ;
      case "t_course_study_progress_zx":
        return COURSE_STUDY_PROGRESS_ZX;
      default:
        return COURSE_STUDY_PROGRESS_OTHER;
    }
  }

  /**
   * 单窗口播放限制
   *
   * @param remove 1:清除缓存
   * @param type 1:课程ID 0:章节ID
   */
  @Override
  public Integer onlyOneVideoPlayLimit(String courseId, String currentUserId, Integer remove,
      Integer type) {
    String limitKey = String.join("_", "limit", currentUserId);
    String cacheCourseId = cache.get(limitKey, String.class);
    //根据章节ID获取课程ID
    if(Objects.equals(type, 0)) {
      final String[] cid = new String[1];
      sectionDao.getOptional(courseId).ifPresent(x -> {
        cid[0] = x.getCourseId();
      });
      courseId = cid[0];
    }
    logger.info("缓存中的courseId={}，提交请求的courseId={}, key={}", cacheCourseId, courseId, limitKey);
    //关闭课程需要移除当前播放的课程信息
    if(Objects.nonNull(cacheCourseId) && Objects.equals(courseId, cacheCourseId) && Objects
        .equals(remove, 1)) {
      cache.clear(limitKey);
      return 1;
    }
    //正常提交时长忽略设置
    if(!Objects.equals(remove, 1)) {
      // 如果缓存为空,缓存已过期
      if(Objects.isNull(cacheCourseId)) {
        cache.set(limitKey, courseId, 10 * 60);
        cacheCourseId = courseId;
      }
      // 如果缓存一致,继续播放
      if(Objects.equals(courseId, cacheCourseId)) {
        return 1;
      }
      //否则暂停
      return 0;
    }
    return 1;
  }


  /**
   * 单窗口播放限制，按照logId判断
   */
  @Override
  public Integer onlyOneVideoPlayLimitLog(String logId, String currentUserId) {
    // 取缓存中的logId
    String limitKey = String.join("_", "onlyOneVideoPlay", "logId", currentUserId);
    String cacheLogId = cache.get(limitKey, String.class);
    logger.info("缓存中的logId={}，提交请求的logId={}, key={}", cacheLogId, logId, limitKey);

    //  如果缓存为空，抛错误码
    if (cacheLogId == null) {
      throw new UnprocessableException(ErrorCode.NetworkFluctuations);
    }
    // 如果缓存与传入的logId一致,继续播放
    if(Objects.equals(logId, cacheLogId)) {
      return 1;
    }
    //否则暂停
    return 0;
  }

  /**
   * 播放视频重新设置缓存.
   */
  @Override
  public void onlyOneVideoPlaySetCache(String id, String currentUserId) {
    String limitKey = String.join("_", "limit", currentUserId);
    sectionDao.getOptional(id).ifPresent(x ->
        cache.set(limitKey, x.getCourseId(), 10 * 60)
    );
  }

  /**
   * app学习时长超过24小时时长交叉问题处理.
   */
  @Override
  public void repairAppStudyTotalTimeConfuse() {

    String sql = "select f_member_id,f_day from t_temp_app_study_time_24 where f_flag = 0 ";
    List<CourseSectionStudyLogAhDay> courseSectionStudyLogDayList = logCommonDao.execute(lcd -> lcd.fetch(sql).map(x -> {
      CourseSectionStudyLogAhDay courseSectionStudyLog = new CourseSectionStudyLogAhDay();
      courseSectionStudyLog.setMemberId(x.getValue(0, String.class));
      courseSectionStudyLog.setDay(x.getValue(1, Integer.class));
      return courseSectionStudyLog;
    }));
    //遍历处理当天的log流水
    courseSectionStudyLogDayList.stream().forEach( csd -> {
      String memberId = csd.getMemberId();
      int day = csd.getDay();
      logger.info("[app时长],memberId={},day={}",memberId,day);
      TableImpl<?> courseSectionStudyLogTable = courseCacheServiceSupport.
          getCacheTable(memberId, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
      long start = DateUtil.start(day);
      long end = DateUtil.end(day);
      long nextDay = DateUtil.nextDay(day);
      List<CourseSectionStudyLog> courseSectionStudyLogs = logCommonDao.execute(
          x ->
              x.selectDistinct(Fields.start()
                  .add(courseSectionStudyLogTable.field("f_id"))
                  .add(courseSectionStudyLogTable.field("f_create_time"))
                  .add(courseSectionStudyLogTable.field("f_member_id"))
                  .add(courseSectionStudyLogTable.field("f_study_time"))
                  .add(courseSectionStudyLogTable.field("f_section_id"))
                  .add(courseSectionStudyLogTable.field("f_commit_time"))
                  .add(courseSectionStudyLogTable.field("f_course_id")).end())
                  .from(courseSectionStudyLogTable)
                  .where(courseSectionStudyLogTable.field("f_member_id", String.class).eq(memberId))
                  .and(courseSectionStudyLogTable.field("f_client_type", Integer.class).eq(1))
                  .and(courseSectionStudyLogTable.field("f_study_time", Integer.class).gt(0))
                  .and(courseSectionStudyLogTable.field("f_create_time", Long.class).ge(start))
                  .and(courseSectionStudyLogTable.field("f_create_time", Long.class).le(end))
                  .orderBy(courseSectionStudyLogTable.field("f_create_time", Long.class).asc())
                  .fetch().map(csl -> {
                CourseSectionStudyLog courseSectionStudyLog = new CourseSectionStudyLog();
                courseSectionStudyLog.setId(csl.getValue("f_id", String.class));
                courseSectionStudyLog.setCreateTime(csl.getValue("f_create_time", Long.class));
                courseSectionStudyLog.setCommitTime(csl.getValue("f_commit_time", Long.class));
                courseSectionStudyLog.setMemberId(csl.getValue("f_member_id", String.class));
                courseSectionStudyLog.setSectionId(csl.getValue("f_section_id", String.class));
                courseSectionStudyLog.setStudyTime(csl.getValue("f_study_time", Integer.class));
                courseSectionStudyLog.setCourseId(csl.getValue("f_course_id", String.class));
                return courseSectionStudyLog;
              }));
      int len = courseSectionStudyLogs.size() - 1;
      //流水交叉问题处理
      for(int i = 0; i < len; i++) {
        CourseSectionStudyLog currentLog = courseSectionStudyLogs.get(i);
        CourseSectionStudyLog nextLog = courseSectionStudyLogs.get(i + 1);
        long currentCommitTime = currentLog.getCommitTime().longValue();
        long nextBeginTime = nextLog.getCreateTime().longValue();
        //时长跨天问题不做处理
        if(currentCommitTime > nextBeginTime && currentCommitTime < nextDay) {
          long currentBeginTime = currentLog.getCreateTime().longValue();
          int currentStudyTime = currentLog.getStudyTime().intValue();
          long currentStudyTimeMinus = ( nextBeginTime - currentBeginTime ) / 1000;
          short flag = 1;
          //差值超出原有时长,不处理
          if(currentStudyTime < currentStudyTimeMinus) {
            flag = 2;
            currentStudyTimeMinus = currentStudyTime;
          }
          //备份原数据
          TempSectionStudyLogGt24 tempSectionStudyLogGt24 = new TempSectionStudyLogGt24();
          tempSectionStudyLogGt24.forInsert();
          tempSectionStudyLogGt24.setLogId(currentLog.getId());
          tempSectionStudyLogGt24.setCommitTime(currentCommitTime);
          tempSectionStudyLogGt24.setMemberId(memberId);
          tempSectionStudyLogGt24.setSectionId(currentLog.getSectionId());
          tempSectionStudyLogGt24.setCourseId(currentLog.getCourseId());
          tempSectionStudyLogGt24.setStudyTime(currentLog.getStudyTime().longValue());
          tempSectionStudyLogGt24.setFlag(flag);
          tempSectionStudyLogGt24CommonDao.insert(tempSectionStudyLogGt24);
          //更新log表时长
          int finalCurrentStudyTimeMinus = (int)currentStudyTimeMinus;
          logCommonDao.execute(lg -> lg.update(courseSectionStudyLogTable)
              .set(courseSectionStudyLogTable.field("f_study_time", Integer.class),
                  finalCurrentStudyTimeMinus)
              .set(courseSectionStudyLogTable.field("f_commit_time", Long.class), nextBeginTime)
              .where(courseSectionStudyLogTable.field("f_id", String.class).eq(currentLog.getId()))
              .execute());
        }
      }
    });
    //更新记录
    String updateSql = "update t_temp_app_study_time_24 set f_flag = 1 ";
    logCommonDao.execute(x -> x.execute(updateSql));
  }

  /**
   * app学习时长超过24小时处理.
   */
  @Override
  public void repairAppStudyTotalTimeMoreThan24Hours() {
    List<TempSectionStudyLogGt24> tempSectionStudyLogGt24s = tempSectionStudyLogGt24CommonDao
        .fetch(TEMP_SECTION_STUDY_LOG_GT_24.FLAG.eq((short)1));
    tempSectionStudyLogGt24s.stream().forEach(ts ->
        courseRepairServiceSupport.repairAppStudyTotalTimeMoreThan24Hours(ts));

  }

  /**
   * APP学习时长超过24小时将修复后流水记录合并更新人课天时长信息.
   */
  @Override
  public void repairSomeDayStudyTimeAppStudyTotalTimeMoreThan24Hours() {
    List<TempSectionStudyLogGt24> tempSectionStudyLogGt24s = tempSectionStudyLogGt24CommonDao
        .fetch(TEMP_SECTION_STUDY_LOG_GT_24.FLAG.eq((short)3));
    tempSectionStudyLogGt24s.stream().forEach(ts ->
        courseRepairServiceSupport.repairSomeDayStudyTimeAppStudyTotalTimeMoreThan24Hours(ts));
  }
}
