package com.zxy.product.course.service.support;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.ThirdPartyCourseStudyProgressService;
import com.zxy.product.course.content.DataSource;
import com.zxy.product.course.content.DataSourceEnum;
import com.zxy.product.course.entity.ThirdPartyCourseStudyProgress;
import com.zxy.product.course.service.util.DateUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.THIRD_PARTY_COURSE_INFO;
import static com.zxy.product.course.jooq.Tables.THIRD_PARTY_COURSE_STUDY_PROGRESS;

@Service
public class ThirdPartyCourseStudyProgressServiceSupport implements ThirdPartyCourseStudyProgressService {

    private CommonDao<ThirdPartyCourseStudyProgress> courseStudyProgressCommonDao;

    @Autowired
    public void setCourseStudyProgressCommonDao(CommonDao<ThirdPartyCourseStudyProgress> courseStudyProgressCommonDao) {
        this.courseStudyProgressCommonDao = courseStudyProgressCommonDao;
    }

    @Override
    public void insert(ThirdPartyCourseStudyProgress progress) {
        courseStudyProgressCommonDao.insert(progress);
    }

    @Override
    public void batchInsert(List<ThirdPartyCourseStudyProgress> progressList) {
        if(progressList!=null&&!progressList.isEmpty()){
            ThirdPartyCourseStudyProgress progressOne=progressList.get(0);
            List<Condition> conditions=Stream.of(
                    THIRD_PARTY_COURSE_STUDY_PROGRESS.MEMBER_ID.eq(progressOne.getMemberId()),
                    THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY.eq(progressOne.getDay())
            ).collect(Collectors.toList());
            int count=courseStudyProgressCommonDao.execute(dslContext -> {
               return dslContext.selectCount()
                       .from(THIRD_PARTY_COURSE_STUDY_PROGRESS)
                       .where(conditions).fetchOne().value1();
            });

            if(count>0){
                courseStudyProgressCommonDao.delete(conditions.toArray(new Condition[progressList.size()]));
            }
            courseStudyProgressCommonDao.insert(progressList);
        }
    }

    @Override
    public Integer getStudyTime(String memberId, Optional<String> courseId, Optional<String> startTime, Optional<String> endTime) {
        return courseStudyProgressCommonDao.execute(dslContext -> {
            Field<BigDecimal> sumField=DSL.sum(THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_TIME);
            List<Condition> conditionList= Stream.of(
                    Optional.of(THIRD_PARTY_COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId)),
                    startTime.map(THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY::ge),
                    endTime.map(THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY::le)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            BigDecimal studyTime=dslContext.select(Fields.start().add(sumField).end())
                    .from(THIRD_PARTY_COURSE_STUDY_PROGRESS)
                    .where(conditionList).fetchOne(sumField);
            if(studyTime==null){
                return 0;
            }else{
                return studyTime.intValue();
            }
        });
    }

    @Override
    public PagedResult<ThirdPartyCourseStudyProgress> getListenProgressList(String memberId, Optional<Long> startTime, Optional<Long> endTime, Integer page, Integer pageSize) {
        return  courseStudyProgressCommonDao.execute(dslContext -> {

            List<Condition> conditionList= Stream.of(
                    Optional.of(THIRD_PARTY_COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId)),
                    startTime.map(e ->{return DateUtil.getStrFormat(e,"yyyy-MM-dd");}).map(THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY::ge),
                    endTime.map(e ->{return DateUtil.getStrFormat(e,"yyyy-MM-dd");}).map(THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY::le)

            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            Field<BigDecimal> sumField=DSL.sum(THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_TIME);
            SortField orderBy=THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY.desc();
            SelectSeekStep2 step = dslContext.select(Fields.start()
                    .add(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE,
                            THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_STATUS,
                            THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME,
                            THIRD_PARTY_COURSE_INFO.COVER,THIRD_PARTY_COURSE_INFO.TITLE,
                            THIRD_PARTY_COURSE_INFO.LINK_URL,THIRD_PARTY_COURSE_INFO.TYPE,
                            THIRD_PARTY_COURSE_INFO.SOURCE,sumField).end())
                    .from(THIRD_PARTY_COURSE_STUDY_PROGRESS)
                    .leftJoin(THIRD_PARTY_COURSE_INFO)
                    .on(THIRD_PARTY_COURSE_INFO.CODE.eq(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE))
                    .where(conditionList).groupBy(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE).orderBy(THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME.desc(),orderBy);
            Integer count = dslContext.fetchCount(step);
            List<ThirdPartyCourseStudyProgress> list=step.limit((page - 1) * pageSize, pageSize).fetch(r ->{
                ThirdPartyCourseStudyProgress progress=new ThirdPartyCourseStudyProgress();
                progress.setStudyStatus(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_STATUS));
                progress.setStartLearningTime(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME));
                progress.setCourseCode(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE));
                progress.setCover(r.get(THIRD_PARTY_COURSE_INFO.COVER));
                progress.setTitle(r.get(THIRD_PARTY_COURSE_INFO.TITLE));
                progress.setLinkUrl(r.get(THIRD_PARTY_COURSE_INFO.LINK_URL));
                progress.setType(r.get(THIRD_PARTY_COURSE_INFO.TYPE));
                progress.setSumStudyTime(r.get(sumField).intValue());
                progress.setSource(r.get(THIRD_PARTY_COURSE_INFO.SOURCE));
                return progress;
            });
            return PagedResult.create(count, list);
        });
    }

    @Override
    public Map<String,Object> getListenProgressListPageSwitch(String memberId, Optional<Long> startTime, Optional<Long> endTime, Integer page, Integer pageSize, boolean pageSwitch) {
        return  courseStudyProgressCommonDao.execute(dslContext -> {

            List<Condition> conditionList= Stream.of(
                    Optional.of(THIRD_PARTY_COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId)),
                    startTime.map(e ->{return DateUtil.getStrFormat(e,"yyyy-MM-dd");}).map(THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY::ge),
                    endTime.map(e ->{return DateUtil.getStrFormat(e,"yyyy-MM-dd");}).map(THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY::le)

            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            Field<BigDecimal> sumField=DSL.sum(THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_TIME);
            SortField orderBy=THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY.desc();
            SelectSeekStep2 step = dslContext.select(Fields.start()
                    .add(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE,
                            THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_STATUS,
                            THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME,
                            THIRD_PARTY_COURSE_INFO.COVER,THIRD_PARTY_COURSE_INFO.TITLE,
                            THIRD_PARTY_COURSE_INFO.LINK_URL,THIRD_PARTY_COURSE_INFO.TYPE,
                            THIRD_PARTY_COURSE_INFO.SOURCE,sumField).end())
                    .from(THIRD_PARTY_COURSE_STUDY_PROGRESS)
                    .leftJoin(THIRD_PARTY_COURSE_INFO)
                    .on(THIRD_PARTY_COURSE_INFO.CODE.eq(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE))
                    .where(conditionList).groupBy(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE).orderBy(THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME.desc(),orderBy);
            Integer count = 0;
            if (pageSwitch) {
                count = dslContext.fetchCount(step);
            }
            List<ThirdPartyCourseStudyProgress> list=step.limit((page - 1) * pageSize, pageSize + 1).fetch(r ->{
                ThirdPartyCourseStudyProgress progress=new ThirdPartyCourseStudyProgress();
                progress.setStudyStatus(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_STATUS));
                progress.setStartLearningTime(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME));
                progress.setCourseCode(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE));
                progress.setCover(r.get(THIRD_PARTY_COURSE_INFO.COVER));
                progress.setTitle(r.get(THIRD_PARTY_COURSE_INFO.TITLE));
                progress.setLinkUrl(r.get(THIRD_PARTY_COURSE_INFO.LINK_URL));
                progress.setType(r.get(THIRD_PARTY_COURSE_INFO.TYPE));
                progress.setSumStudyTime(r.get(sumField).intValue());
                progress.setSource(r.get(THIRD_PARTY_COURSE_INFO.SOURCE));
                return progress;
            });

            Map<String,Object> resultMap = new HashMap<>();
            resultMap.put("items",list);

            Integer more = 0;
            if(!CollectionUtils.isEmpty(list) && list.size() > pageSize) {
                list.remove(pageSize);
                    more = 1;
            }
            if (pageSwitch) {
                resultMap.put("recordCount", count);
            }
            resultMap.put("more",more);
            return resultMap;
        });
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public PagedResult<ThirdPartyCourseStudyProgress> getMyListenBookList(String memberId, Optional<Integer> status, Optional<String> orderByType, Integer page, Integer pageSize) {
        return  courseStudyProgressCommonDao.execute(dslContext -> {

            List<Condition> conditionList= Stream.of(
                    Optional.of(THIRD_PARTY_COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId)),
                    status.map(THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_STATUS::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            SortField orderBy=THIRD_PARTY_COURSE_STUDY_PROGRESS.DAY.desc();
            SortField learnStudyTimeOrder;
            if(!orderByType.isPresent()||"desc".equals(orderByType.get().toLowerCase())){
                learnStudyTimeOrder=THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME.desc();
            }else{
                learnStudyTimeOrder=THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME.asc();
            }
            SelectSeekStep2 step = dslContext.select(Fields.start()
                    .add(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE,
                            THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_STATUS,
                            THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME,
                            THIRD_PARTY_COURSE_INFO.COVER,THIRD_PARTY_COURSE_INFO.TITLE,
                            THIRD_PARTY_COURSE_INFO.LINK_URL,THIRD_PARTY_COURSE_INFO.TYPE,
                            THIRD_PARTY_COURSE_INFO.SOURCE).end())
                    .from(THIRD_PARTY_COURSE_STUDY_PROGRESS)
                    .leftJoin(THIRD_PARTY_COURSE_INFO)
                    .on(THIRD_PARTY_COURSE_INFO.CODE.eq(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE))
                    .where(conditionList).groupBy(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE).orderBy(learnStudyTimeOrder,orderBy);
            Integer count = dslContext.fetchCount(step);
            List<ThirdPartyCourseStudyProgress> list=step.limit((page - 1) * pageSize, pageSize).fetch(r ->{
                ThirdPartyCourseStudyProgress progress=new ThirdPartyCourseStudyProgress();
                progress.setStudyStatus(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.STUDY_STATUS));
                progress.setStartLearningTime(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.START_LEARNING_TIME));
                progress.setCourseCode(r.get(THIRD_PARTY_COURSE_STUDY_PROGRESS.COURSE_CODE));
                progress.setCover(r.get(THIRD_PARTY_COURSE_INFO.COVER));
                progress.setTitle(r.get(THIRD_PARTY_COURSE_INFO.TITLE));
                progress.setLinkUrl(r.get(THIRD_PARTY_COURSE_INFO.LINK_URL));
                progress.setType(r.get(THIRD_PARTY_COURSE_INFO.TYPE));
                progress.setSource(r.get(THIRD_PARTY_COURSE_INFO.SOURCE));
                return progress;
            });
            return PagedResult.create(count, list);
        });
    }



}
