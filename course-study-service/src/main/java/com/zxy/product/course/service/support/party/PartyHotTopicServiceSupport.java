package com.zxy.product.course.service.support.party;


import com.alibaba.fastjson.JSONArray;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.party.PartyHotTopicService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.BusinessTopic;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.PartyHotTopic;
import com.zxy.product.course.entity.Topic;
import org.jooq.Condition;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;

/**
 * 热门标题处理
 */
@Service
public class PartyHotTopicServiceSupport implements PartyHotTopicService {

    private CommonDao<PartyHotTopic> commonDao;

    private CommonDao<Topic> topicCommonDao;

    public static final Optional<Integer> TOPIC_MAX=Optional.ofNullable(20);

    private static final Logger logger = LoggerFactory.getLogger(PartyHotTopicServiceSupport.class);



    @Autowired
    public void setCommonDao(CommonDao<PartyHotTopic> commonDao) {
        this.commonDao = commonDao;
    }

    @Autowired
    public void setTopicCommonDao(CommonDao<Topic> topicCommonDao) {
        this.topicCommonDao = topicCommonDao;
    }

    @Override
    public List<PartyHotTopic> findList() {
        return commonDao.execute(dslContext -> {
            return dslContext.select(Fields.start().add(PARTY_HOT_TOPIC).add(TOPIC).end())
                    .from(PARTY_HOT_TOPIC)
                    .leftJoin(TOPIC).on(TOPIC.ID.eq(PARTY_HOT_TOPIC.TOPIC_ID))
                    .orderBy(PARTY_HOT_TOPIC.SORT.asc(),PARTY_HOT_TOPIC.CREATE_TIME.asc()).fetch(record ->{
                        PartyHotTopic topic=new PartyHotTopic();
                        topic.setTopicName(record.get(TOPIC.NAME));
                        topic.setTypeId(record.get(TOPIC.TYPE_ID));
                        topic.setTopicId(record.get(TOPIC.ID));
                        topic.setSort(record.get(PARTY_HOT_TOPIC.SORT));
                        topic.setCreateTime(record.get(PARTY_HOT_TOPIC.CREATE_TIME));
                        return topic;
                    });
        });
    }

    @Override
    public void batchUpdate(String data,String createMemberId) {
        List<PartyHotTopic> list;
        try{
            list= JSONArray.parseArray(data,PartyHotTopic.class);
        }catch(Exception e){
            logger.error("保存党建热门标签推荐异常，data={},createMember={},error={}",data,createMemberId,e.getMessage());
            throw new UnprocessableException(ErrorCode.DJDataError);
        }
        TOPIC_MAX.ifPresent(x ->{
            if(x<list.size()){
                throw new UnprocessableException(ErrorCode.DJYPExceedMax);
            }
        });
        for(PartyHotTopic topic:list){
            if(topic.getSort()==null||"".equals(topic.getTopicId())
                    ||topic.getTopicId()==null){
                throw new UnprocessableException(ErrorCode.DJDataNotVerify);
            }
            topic.forInsert();
            topic.setCreateMemberId(createMemberId);
        }
        commonDao.execute(dslContext -> dslContext.deleteFrom(PARTY_HOT_TOPIC).execute());
        commonDao.insert(list);

    }

    @Override
    public List<String> findTopicTypeList() {
        List<Condition> conditionList=new ArrayList<>();
        conditionList.add(COURSE_INFO.IS_PARTY.eq(CourseInfo.PARTY_BUILING_COURSE_TRUE));
        conditionList.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));
        conditionList.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
        conditionList.add(BUSINESS_TOPIC.BUSINESS_TYPE.in(BusinessTopic.BUSINESS_TYPE_COURSE,BusinessTopic.BUSINESS_TYPE_SUBJECT));
        return topicCommonDao.execute(dslContext -> {
            return dslContext.selectDistinct(TOPIC.TYPE_ID).from(TOPIC)
                    .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.TOPIC_ID.eq(TOPIC.ID))
                    .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(BUSINESS_TOPIC.BUSINESS_ID))
                    .where(conditionList).fetch(TOPIC.TYPE_ID);
        });
    }

    @Override
    public List<Topic> findTopicList(Optional<String> topicTypeId, Optional<String> topicName) {

        List<Condition> conditionList = Stream.of(
                topicTypeId.map(TOPIC.TYPE_ID::eq),
                topicName.map(p -> TOPIC.NAME.like(p + "%")))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditionList.add(COURSE_INFO.IS_PARTY.eq(CourseInfo.PARTY_BUILING_COURSE_TRUE));
        conditionList.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));
        conditionList.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
        conditionList.add(BUSINESS_TOPIC.BUSINESS_TYPE.in(BusinessTopic.BUSINESS_TYPE_COURSE,BusinessTopic.BUSINESS_TYPE_SUBJECT));
        conditionList.add(TOPIC.GROUP.eq(Topic.GROUP_STANDARD));
        List<String> topicIds= topicCommonDao.execute(dslContext -> {
            return dslContext.selectDistinct(TOPIC.ID)
                    .from(TOPIC)
                    .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.TOPIC_ID.eq(TOPIC.ID))
                    .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(BUSINESS_TOPIC.BUSINESS_ID))
                    .where(conditionList).fetch(TOPIC.ID);
        });
        return topicCommonDao.fetch(TOPIC.ID.in(topicIds));
    }
}
