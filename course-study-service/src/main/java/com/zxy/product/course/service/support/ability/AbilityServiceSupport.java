package com.zxy.product.course.service.support.ability;

import com.alibaba.fastjson.JSONArray;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.ability.AbilityService;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.service.util.OrgConditionUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Comparator;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.entity.Ability.PUBLISH_STATUS;
import static com.zxy.product.course.entity.Ability.STUDY_MAP_TYPE;
import static com.zxy.product.course.jooq.Tables.*;

@Service
public class AbilityServiceSupport implements AbilityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbilityServiceSupport.class);

    private static final int MAX_QUERY_COUNT = 1000;

    /**
     * 最少关联必修资源数量
     */
    private static final int MIN_REQUIRE_RESOURCE_COUNT = 1;

    private CommonDao<Ability> dao;
    private CommonDao<AbilityBusiness> businessCommonDao;

    private CommonDao<CourseChapterSection> courseChapterSectionCommonDao;

    @Autowired
    public void setBusinessDao(CommonDao<AbilityBusiness> businessCommonDao) {
        this.businessCommonDao = businessCommonDao;
    }

    @Autowired
    public void setDao(CommonDao<Ability> dao) {
        this.dao = dao;
    }
    @Autowired
    public void setCourseChapterSectionCommonDao(CommonDao<CourseChapterSection> courseChapterSectionCommonDao) {
        this.courseChapterSectionCommonDao = courseChapterSectionCommonDao;
    }


    @Override
    public Ability get(String id) {
        Ability abilityData = dao.execute(d -> d.select(Fields.start().add(ABILITY).add(ORGANIZATION.NAME, ORGANIZATION.PATH).end())
                .from(ABILITY).leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ABILITY.ORGANIZATION_ID))
                .where(ABILITY.ID.eq(id))
                .fetchOne(r -> {
                    Ability ability = r.into(ABILITY).into(Ability.class);
                    Organization organization = r.into(ORGANIZATION).into(Organization.class);
                    organization.setId(ability.getOrganizationId());
                    ability.setOrganization(organization);
                    return ability;
                }));
        Field<String> courseName = COURSE_INFO.NAME.as("courseName");
        List<AbilityBusiness> abilityBusinessList = businessCommonDao.execute(e -> e.select(Fields.start()
                                .add(ABILITY_BUSINESS.fields()).add(courseName).end())
                        .from(ABILITY_BUSINESS).leftJoin(COURSE_INFO)
                        .on(ABILITY_BUSINESS.BUSINESS_ID.eq(COURSE_INFO.ID)))
                .where(ABILITY_BUSINESS.ABILITY_ID.eq(id)).orderBy(ABILITY_BUSINESS.SEQUENCE.asc()).fetch(r -> {
                    AbilityBusiness business = r.into(ABILITY_BUSINESS).into(AbilityBusiness.class);
                    business.setBusinessName(r.getValue(courseName));
                    return business;
                });
        abilityData.setBusinesses(abilityBusinessList);
        return abilityData;
    }

    @Override
    public Ability insert(String name, String code, String organizationId, Optional<String> rootOrgId, Optional<String> desc, Optional<Integer> status, Optional<Integer> type, Optional<List<AbilityBusiness>> businesses) {
        Ability ability = new Ability();

        ability.forInsert();
        ability.setName(name);
        ability.setCode(code);
        ability.setOrganizationId(organizationId);
        desc.ifPresent(ability::setDescription);
        status.ifPresent(ability::setStatus);
        type.ifPresent(ability::setType);
        businesses.ifPresent(list -> {
            ability.setBusinesses(batchInsertBusiness(ability.getId(), list));
        });

        ErrorCode.CODE_ALREADY_EXISTS.throwIf(codeUsed(Optional.empty(), Optional.empty(), code));
        dao.insert(ability);
        return ability;
    }

    private List<AbilityBusiness> batchInsertBusiness(String abilityId, List<AbilityBusiness> businesses) {
        if (CollectionUtils.isEmpty(businesses)) {
            // 能力至少需要关联一条必修内容，所以不可能为空
            return businesses;
        }
        //先删除
        businessCommonDao.delete(ABILITY_BUSINESS.ABILITY_ID.eq(abilityId));
        AtomicInteger order = new AtomicInteger();

        businesses.forEach(business -> {
            if(Objects.isNull(business.getId())){
                business.forInsert();
            }
            if(Objects.isNull(business.getSequence())){
                business.setSequence(order.getAndIncrement());
            }
            business.setAbilityId(abilityId);
        });
        // 后全量新增
        businessCommonDao.insert(businesses);

        updateChapterSections(abilityId, businesses);

        return businesses;
    }

    private void updateChapterSections(String abilityId, List<AbilityBusiness> businesses) {

        // 新增章节
        List<CourseChapterSection> executed = businessCommonDao.execute(dslContext -> dslContext
                .select(Fields.start()
                        .add(COURSE_CHAPTER_SECTION.fields())
                        .add(DSL.max(COURSE_CHAPTER_SECTION.SEQUENCE).as("maxSeq"))
                        .add(DSL.groupConcat(COURSE_CHAPTER_SECTION.RESOURCE_ID).as("resourceIds"))
                        .end())
                .from(COURSE_CHAPTER_SECTION)
                .join(COURSE_CHAPTER).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .leftJoin(COURSE_INFO).on(COURSE_CHAPTER.COURSE_ID.eq(COURSE_INFO.ID))
                .where(
                        COURSE_CHAPTER_SECTION.ABILITY_ID.eq(abilityId))
                .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID).or(COURSE_CHAPTER.VERSION_ID.isNull()))
                .groupBy(COURSE_CHAPTER_SECTION.COURSE_ID)
                .fetch(r -> {
                    CourseChapterSection into = r.into(CourseChapterSection.class);
                    into.setResourceId(Optional.ofNullable(r.getValue("resourceIds",String.class)).orElse(""));
                    into.setSequence(Optional.ofNullable(r.getValue("maxSeq",Integer.class)).orElse(0));
                    return into;
                })
        );
        List<CourseChapterSection> newCourseChapterSectionList = new ArrayList<>();
        for (AbilityBusiness business : businesses) {
            for (CourseChapterSection courseChapterSection : executed) {
                String resourceIdsString = courseChapterSection.getResourceId();
                if (!resourceIdsString.contains(business.getBusinessId())){
                    CourseChapterSection newcourseChapterSection = new CourseChapterSection();
                    BeanUtils.copyProperties(courseChapterSection,newcourseChapterSection);
                    newcourseChapterSection.forInsert();
                    newcourseChapterSection.setSequence(business.getSequence());
                    newcourseChapterSection.setSectionType(business.getBusinessType());
                    newcourseChapterSection.setResourceId(business.getBusinessId());
                    newcourseChapterSection.setRequired(business.getIsRequire());
                    newcourseChapterSection.setName(business.getBusinessName());
                    newCourseChapterSectionList.add(newcourseChapterSection);
                    courseChapterSection.setSequence(courseChapterSection.getSequence()+1);
                }
            }
        }

        // 删除章节进肚中的内容
        List<Param<String>> collect = businesses.stream().map(c -> DSL.val(c.getBusinessId())).collect(Collectors.toList());
        String sql = "delete from t_course_chapter_section where f_resource_id not in ({0}) and f_ability_id={1}";
        businessCommonDao.execute(dslContext -> dslContext.execute(sql, DSL.list(collect), abilityId));
        courseChapterSectionCommonDao.insert(newCourseChapterSectionList);

    }

    @Override
    public Ability update(String id, Optional<String> name, Optional<String> code, Optional<String> organizationId, Optional<String> desc, Optional<Integer> status, Optional<List<AbilityBusiness>> businesses) {
        Ability ability = dao.get(id);

        AtomicBoolean isNewName = new AtomicBoolean(false);
        String oldName = ability.getName();
        name.ifPresent(n -> {
            if (!ability.getName().equals(n)) {
                isNewName.set(true);
            }
        });
        organizationId.ifPresent(ability::setOrganizationId);
        desc.ifPresent(ability::setDescription);
        name.ifPresent(ability::setName);
        status.ifPresent(ability::setStatus);
        code.ifPresent(c -> {
            ability.setCode(c);
            ErrorCode.CODE_ALREADY_EXISTS.throwIf(codeUsed(Optional.of(id), Optional.empty(), c));
        });
        businesses.ifPresent(list -> {
            ability.setBusinesses(batchInsertBusiness(id, list));
        });
        dao.update(ability);
        updateChapterName(id, isNewName, oldName, ability);
        return ability;
    }

    private void updateChapterName(String id, AtomicBoolean isNewName, String oldName, Ability ability) {
        // 更新关联的主题名称
        if (isNewName.get()) {
            String selectChapterIdSql = "select t_course_chapter.f_id as id  " +
                    "from t_course_chapter  " +
                    "         join t_course_info on t_course_chapter.f_course_id = t_course_info.f_id  " +
                    "  and (t_course_chapter.f_version_id = t_course_info.f_version_id  or t_course_chapter.f_version_id  is null) " +
                    "  where t_course_info.f_business_type = 3  " +
                    "  and f_ability_id = {0}  " +
                    "  and t_course_chapter.f_name != {1}";
            List<Param<String>> courseChapterIds = Optional.ofNullable(dao.execute(dsl -> dsl.fetch(selectChapterIdSql, DSL.val(id), DSL.val(ability.getName())).map(r -> DSL.val(r.getValue("id", String.class))))).orElse(new ArrayList<>());
            courseChapterIds.add(DSL.val(UUID.randomUUID().toString()));
            String updateChapterSql = "update t_course_chapter set f_name = {0} where f_id in ({1});";
            dao.execute(dsl -> dsl.execute(updateChapterSql, DSL.val(ability.getName()), DSL.list(courseChapterIds)));
        }
    }


    @Override
    public Integer delete(String id) {
        int count = businessCommonDao.execute(dsl -> dsl.fetchCount(COURSE_CHAPTER,COURSE_CHAPTER.ABILITY_ID.eq(id)));
        ErrorCode.ABILITY_TO_APPLY_CANNOT_BE_DELETED.throwIf(count > 0);

        // 删除关联的内容
        businessCommonDao.execute(d -> d.delete(ABILITY_BUSINESS).where(ABILITY_BUSINESS.ABILITY_ID.eq(id)).execute());
        return dao.delete(id);
    }

    @Override
    public Ability changeStatus(String id, Integer status) {
        Ability ability = dao.get(id);
        // 没有变化
        Integer oldStatus = ability.getStatus();
        if (oldStatus.equals(status)) {
            return ability;
        }
        boolean isPublish = PUBLISH_STATUS.equals(status);
        if (isPublish) {
            // 发布 检查是否有关联必修内容
            Integer count = dao.execute(e -> e.select(Fields.start().add(DSL.count(ABILITY_BUSINESS.ID)).end()).from(ABILITY)
                    .innerJoin(ABILITY_BUSINESS).on(ABILITY_BUSINESS.ABILITY_ID.eq(ABILITY.ID))
                    .where(ABILITY.ID.eq(id), ABILITY_BUSINESS.IS_REQUIRE.eq(AbilityBusiness.REQUIRE)).fetchOne(DSL.count(ABILITY_BUSINESS.ID)));

            // 必修数不达标
            ErrorCode.ABILITY_NO_RELATION_BUSINESS.throwIf(count == null || count < MIN_REQUIRE_RESOURCE_COUNT);
        } else {
            // 撤销之前的状态 不是 发布状态
            if (!PUBLISH_STATUS.equals(oldStatus)) {
                return ability;
            }
        }
        ability.setStatus(isPublish ? PUBLISH_STATUS : Ability.UN_PUBLISH_STATUS);
        dao.execute(e -> e.update(ABILITY)
                .set(ABILITY.STATUS, ability.getStatus())
                .where(ABILITY.ID.eq(id)).execute());
        return ability;
    }

    @Override
    public Map<String, String> batchChangeStatus(Collection<String> ids, Integer status) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        return dao.execute(e -> {
            Map<String, String> resultMap = new HashMap<>(CommonConstant.FOUR);
            Collection<String> updateIds = new HashSet<>(ids);
            List<Ability> abilities = e.select(ABILITY.ID, ABILITY.STATUS).from(ABILITY).where(ABILITY.ID.in(updateIds)).fetchInto(Ability.class);
            boolean isPublish = PUBLISH_STATUS.equals(status);
            Set<String> okIds;
            if (isPublish) {
                // 查询必修数量，key-能力主键id value-关联的必修内容数量
                Map<String, Integer> abilityBusinessCountMap = e.select(Fields.start().add(ABILITY.ID).add(ABILITY_BUSINESS.ID).end()).from(ABILITY)
                        .innerJoin(ABILITY_BUSINESS).on(ABILITY_BUSINESS.ABILITY_ID.eq(ABILITY.ID))
                        .where(ABILITY.ID.in(updateIds), ABILITY_BUSINESS.IS_REQUIRE.eq(AbilityBusiness.REQUIRE))
                        .fetchGroups(ABILITY.ID, ABILITY_BUSINESS.ID).entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, a -> a.getValue().size()));
                // 过滤掉关联的必修资源数不达标的。能发布的能力集
                okIds = abilities.stream().filter(a -> abilityBusinessCountMap.getOrDefault(a.getId(), 0) >= MIN_REQUIRE_RESOURCE_COUNT).map(Ability::getId).collect(Collectors.toSet());
            } else {
                // 过滤掉草稿状态能力。能取消发布的能力集
                okIds = abilities.stream().filter(a -> !Ability.DRAFT_STATUS.equals(a.getStatus())).map(Ability::getId).collect(Collectors.toSet());
            }
            int total = updateIds.size();
            updateIds.removeAll(okIds);
            e.update(ABILITY).set(ABILITY.STATUS, isPublish ? PUBLISH_STATUS : Ability.UN_PUBLISH_STATUS).where(ABILITY.ID.in(okIds)).execute();
            int successCount = okIds.size();
            resultMap.put("successCount", successCount + "");
            resultMap.put("failCount", total - successCount + "");
            resultMap.put("failMsg", isPublish ? "请检查是否有必修内容！" : "请检查能力状态！");
            resultMap.put("failIds", String.join(CommonConstant.SEPARATOR_COMMA, updateIds));
            return resultMap;
        });
    }

    @Override
    public PagedResult<Ability> findPage(int page, int pageSize, Optional<String> name, Optional<String> code, Optional<Integer> status, Set<String> orgIds, Optional<String[]> selectIds) {
        List<Condition> conditions = Stream.of(
                status.map(ABILITY.STATUS::eq),
                Optional.of(orgIds).map(ORGANIZATION.ID::in),
                code.map(ABILITY.CODE::contains),
                name.map(ABILITY.NAME::contains),
                selectIds.map(ABILITY.ID::notIn),
                Optional.of(Ability.PUBLISH_STATUS).map(ABILITY.STATUS::eq)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        SelectOnConditionStep<Record> selectStep = dao.execute(e -> e.select(Fields.start()
                .add(ABILITY.fields()).end()).from(ABILITY).leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ABILITY.ORGANIZATION_ID)));

        Integer count = dao.execute(e -> e.fetchCount(selectStep.where(conditions)));
        selectStep.orderBy(ABILITY.CREATE_TIME.desc());
        int firstResult = (page - 1) * pageSize;

        List<Ability> data = selectStep.limit(firstResult, pageSize).fetch(r -> {
            Ability ability = r.into(ABILITY).into(Ability.class);
            return ability;
        });
        return PagedResult.create(count, data);
    }

    @Override
    public PagedResult<Ability> findPageNew(int page, int pageSize, Optional<String> organizationIdOptional, Optional<String> name, Optional<String> code, Optional<Integer> status, Optional<String> organizationId, Optional<Integer> level, Map<String, Set<String>> orgMap, Integer contain, Boolean lastPageFlag) {
        List<Condition> conditions = Stream.of(
                status.map(ABILITY.STATUS::eq),
                code.map(ABILITY.CODE::contains),
                name.map(ABILITY.NAME::contains),
                level.map(ABILITY.LEVEL::eq)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        generateOrganizationConditions(orgMap,conditions);

        if (organizationIdOptional.isPresent()) {
            if (contain.equals(Ability.ORG_NOT_CONTAIN)) {
                conditions.add(ORGANIZATION.ID.eq(organizationId.get()));
            } else {
                conditions.add(ORGANIZATION.PATH.startsWith(organizationIdOptional.get()));
            }
        }
        Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");

        SelectOnConditionStep<Record> selectStep = dao.execute(e -> e.select(Fields.start()
                .add(ABILITY.fields()).add(organizationName).end()).from(ABILITY).leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ABILITY.ORGANIZATION_ID)));

        Integer count = dao.execute(e -> e.fetchCount(selectStep.where(conditions)));
        selectStep.orderBy(ABILITY.CREATE_TIME.desc());
        int firstResult = (page - 1) * pageSize;

        List<Ability> data = selectStep.limit(firstResult, pageSize).fetch(r -> {
            Ability ability = r.into(ABILITY).into(Ability.class);
            Organization organization = r.into(ORGANIZATION).into(Organization.class);
            organization.setId(ability.getOrganizationId());
            organization.setName(r.get(organizationName));
            ability.setOrganization(organization);
            return ability;
        });
        return PagedResult.create(count, data);
    }

    @Override
    public Boolean codeUsed(Optional<String> id, Optional<String> companyId, String code) {
        return dao.fetchOne(id.map(ABILITY.ID::ne).orElse(DSL.trueCondition()), ABILITY.CODE.eq(code)).isPresent();
    }

    @Override
    public List<Ability> findByCodes(Optional<String> companyId, Collection<String> codes, Map<String, Set<String>> orgMap) {
        return dao.execute(d -> {
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunction = s -> {
                SelectJoinStep<Record> step = s.from(ABILITY);
                if (!CollectionUtils.isEmpty(orgMap)) {
                    step = step.leftJoin(ORGANIZATION).on(ABILITY.ORGANIZATION_ID.eq(ORGANIZATION.ID)).and(OrgConditionUtil.grantOrgCondition(orgMap, ORGANIZATION, Optional.empty()));
                }
                return step.where(ABILITY.CODE.in(codes));
            };
            return stepFunction.apply(d.select(ABILITY.fields())).fetchInto(Ability.class);
        });
    }

    @Override
    public void addRelationCount(String id, Optional<Integer> positionIncrement, Optional<Integer> businessIncrement) {

    }


    @Override
    public Integer batchInsert(Collection<Ability> abilities) {
        abilities.forEach(Ability::forInsert);
        dao.insert(abilities);
        return abilities.size();
    }

    @Override
    public List<Ability> getCodeAndNameList(Map<String, Set<String>> orgMap) {
        List<Condition> conditions = new ArrayList();
        conditions.add(OrgConditionUtil.grantOrgCondition(orgMap, ORGANIZATION));
        conditions.add(ABILITY.STATUS.ne(Ability.DRAFT_STATUS));
        return dao.execute(d ->
                d.select(ABILITY.NAME, ABILITY.CODE, ABILITY.ID)
                        .from(ABILITY)
                        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ABILITY.ORGANIZATION_ID))
                        .where(conditions)
                        .fetchInto(Ability.class));
    }

    @Override
    public List<Ability> findBusinessByIds(Collection<String> ids) {
        return dao.execute(e -> {
            Map<String, Ability> abilityMap = new HashMap<>();
            List<Ability> abilities = e.select(Fields.start()
                            .add(ABILITY.ID, ABILITY.CODE, ABILITY.NAME, ABILITY.STATUS)
                            .add(ABILITY_BUSINESS.ID, ABILITY_BUSINESS.SEQUENCE, ABILITY_BUSINESS.IS_REQUIRE, ABILITY_BUSINESS.BUSINESS_TYPE)
                            .add(COURSE_INFO.ID, COURSE_INFO.BUSINESS_TYPE, COURSE_INFO.NAME, COURSE_INFO.CODE)
                            .end()).from(ABILITY)
                    .leftJoin(ABILITY_BUSINESS).on(ABILITY_BUSINESS.ABILITY_ID.eq(ABILITY.ID))
                    .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(ABILITY_BUSINESS.BUSINESS_ID))
                    .where(ABILITY.ID.in(ids))
                    .orderBy(ABILITY.ID, ABILITY_BUSINESS.SEQUENCE)
                    .fetch(r -> {
                        AbilityBusiness business = r.into(ABILITY_BUSINESS).into(AbilityBusiness.class);
                        if (business != null) {
                            business.setCourseInfo(r.into(COURSE_INFO).into(CourseInfo.class));
                        }
                        String id = r.getValue(ABILITY.ID);
                        Ability ability = abilityMap.get(id);
                        if (ability == null) {
                            ability = r.into(ABILITY).into(Ability.class);
                            abilityMap.put(id, ability);
                        }
                        List<AbilityBusiness> businesses = ability.getBusinesses();
                        if (businesses == null) {
                            businesses = new ArrayList<>();
                        }
                        businesses.add(business);
                        return ability;
                    });
            abilityMap.clear();
            return abilities;
        });
    }

    @Override
    public List<Ability> getBusinessByIds(Collection<String> ids) {
        return dao.execute(e -> {
            List<Ability> abilities = e.select(Fields.start()
                            .add(ABILITY.ID, ABILITY.CODE, ABILITY.NAME, ABILITY.STATUS)
                            .add(ABILITY_BUSINESS.ID, ABILITY_BUSINESS.SEQUENCE, ABILITY_BUSINESS.IS_REQUIRE, ABILITY_BUSINESS.BUSINESS_TYPE, ABILITY_BUSINESS.BUSINESS_ID)
                            .add(COURSE_INFO.ID, COURSE_INFO.BUSINESS_TYPE, COURSE_INFO.NAME, COURSE_INFO.CODE, COURSE_INFO.STATUS, COURSE_INFO.URL)
                            .add(ORGANIZATION.NAME)
                            .end()).from(ABILITY)
                    .leftJoin(ABILITY_BUSINESS).on(ABILITY_BUSINESS.ABILITY_ID.eq(ABILITY.ID))
                    .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(ABILITY_BUSINESS.BUSINESS_ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                    .where(ABILITY.ID.in(ids))
                    .orderBy(ABILITY.ID, ABILITY_BUSINESS.SEQUENCE)
                    .fetch(r -> {
                        Ability ability = r.into(ABILITY).into(Ability.class);
                        AbilityBusiness business = r.into(ABILITY_BUSINESS).into(AbilityBusiness.class);
                        CourseInfo courseInfo = r.into(COURSE_INFO).into(CourseInfo.class);
                        String organizationName = r.getValue(ORGANIZATION.NAME);
                        courseInfo.setOrganizationName(organizationName);

                        business.setCourseInfo(courseInfo);
                        List<AbilityBusiness> businesses = new ArrayList();
                        businesses.add(business);

                        ability.setBusinesses(businesses);

                        return ability;
                    });
            Map<String, List<Ability>> abilitys = abilities.stream().collect(Collectors.groupingBy(Ability::getId));
            List<Ability> arrayList = new ArrayList();
            abilitys.forEach((key, value) -> {
                Ability ability = value.get(0);
                List<AbilityBusiness> abilityBusinesses = new ArrayList<>();

                value.stream().map(Ability::getBusinesses).forEach(x -> abilityBusinesses.addAll(x));
                ability.setBusinesses(abilityBusinesses);
                arrayList.add(ability);
            });
            return arrayList;
        });
    }

    @Override
    public String getOrgPathById(String id) {
        return dao.execute(e -> e.select(ORGANIZATION.PATH).from(ABILITY).innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ABILITY.ORGANIZATION_ID))
                .where(ABILITY.ID.eq(id)).fetchOne(ORGANIZATION.PATH));
    }

    @Override
    public String getOrgPathByAbilityBusinessId(String abilityBusinessId) {
        return dao.execute(e -> e.select(ORGANIZATION.PATH).from(ABILITY).innerJoin(ABILITY_BUSINESS).on(ABILITY.ID.eq(ABILITY_BUSINESS.ABILITY_ID))
                .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ABILITY.ORGANIZATION_ID))
                .where(ABILITY_BUSINESS.ID.eq(abilityBusinessId)).fetchOne(ORGANIZATION.PATH));
    }

    @Override
    public List<String> getBatchOrgPathByIds(String[] ids) {
        return dao.execute(e -> e.select(ORGANIZATION.PATH).from(ABILITY).innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ABILITY.ORGANIZATION_ID))
                .where(ABILITY.ID.in(ids)).fetch(ORGANIZATION.PATH));
    }

    @Override
    public Boolean updatePositionNum(List<String> abilityIds) {
        return null;
    }

    @Override
    public List<Ability> getByIds(String abilityIds) {
        List<Ability> abilityList = dao.execute(d -> d.select(Fields.start().add(ABILITY).add(ORGANIZATION.NAME, ORGANIZATION.PATH).end())
                .from(ABILITY).leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ABILITY.ORGANIZATION_ID))
                .where(ABILITY.ID.in(abilityIds.split(",")))
                .fetchInto(Ability.class));
        Field<String> courseName = COURSE_INFO.NAME.as("courseName");
        List<AbilityBusiness> abilityBusinessList = businessCommonDao.execute(e -> e.select(Fields.start()
                                .add(ABILITY_BUSINESS.fields()).add(courseName).end())
                        .from(ABILITY_BUSINESS).leftJoin(COURSE_INFO)
                        .on(ABILITY_BUSINESS.BUSINESS_ID.eq(COURSE_INFO.ID)))
                .where(ABILITY_BUSINESS.ABILITY_ID.in(abilityIds.split(","))).orderBy(ABILITY_BUSINESS.SEQUENCE.asc()).fetch(r -> {
                    AbilityBusiness business = r.into(ABILITY_BUSINESS).into(AbilityBusiness.class);
                    business.setBusinessName(r.getValue(courseName));
                    return business;
                });
        abilityBusinessList.forEach(e -> {
            e.setName(e.getBusinessName());
            e.setSectionType(e.getBusinessType());
            e.setRequired(e.getIsRequire());
        });
        Map<String, List<AbilityBusiness>> abilityMap = abilityBusinessList.stream().collect(Collectors.groupingBy(
                AbilityBusiness::getAbilityId, Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> list.stream()
                                .sorted(Comparator.comparing(AbilityBusiness::getSequence))
                                .collect(Collectors.toList())
                )));
        abilityList.stream().forEach(e -> e.setBusinesses(abilityMap.get(e.getId())));
        return abilityList;
    }

    @Override
    public List<Ability> batchAdd(String abilityJson) {
        List<Ability> abilities = JSONArray.parseArray(abilityJson, Ability.class);
        abilities.forEach(e -> {
            e.setCreateTime(System.currentTimeMillis());
            e.setType(STUDY_MAP_TYPE);
            e.setStatus(PUBLISH_STATUS);
            e.getCourseChapterSections().forEach(b -> {
                        b.forInsert();
                        b.setAbilityId(e.getId());
                    }
            );
        });

        List<AbilityBusiness> abilityBusinessList = abilities.stream().flatMap(e -> e.getCourseChapterSections().stream().peek(c->c.setIsRequire(c.getRequired()))).collect(Collectors.toList());
        dao.insert(abilities);
        businessCommonDao.insert(abilityBusinessList);
        return abilities;
    }

    /**
     * 拼装组织条件
     *
     * @param grantOrganizationMap 组织Map
     * @param conditions
     * return 组织条件
     */
    private void generateOrganizationConditions(Map<String, Set<String>> grantOrganizationMap, List<Condition> conditions) {
        Set<String> organizationIdSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.NOT_INCLUDE_KEY);
        Set<String> pathSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.INCLUDE_KEY);
        if (!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(pathSet) || !com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(organizationIdSet)) {
            Condition condition;
            if (pathSet.isEmpty()) {
                condition = Optional.of(organizationIdSet).map(ORGANIZATION.ID::in).orElse(DSL.trueCondition());
            } else {
                condition = pathSet.stream().map(ORGANIZATION.PATH::startsWith).reduce(DSL::or)
                        .orElse(DSL.trueCondition());
                if (!organizationIdSet.isEmpty()) {
                    condition = condition.or(ORGANIZATION.ID.in(organizationIdSet));
                }
            }
            conditions.add(condition);
        }
    }
}
