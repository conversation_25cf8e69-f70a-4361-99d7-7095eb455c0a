package com.zxy.product.course.service.support.other;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.other.KnowledgeViewRecordService;
import com.zxy.product.course.entity.KnowledgeViewRecord;
import com.zxy.product.course.jooq.tables.KnowledgeInfo;
import com.zxy.product.course.service.util.DateUtil;
import org.jooq.Record2;
import org.jooq.Table;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.zxy.product.course.jooq.tables.KnowledgeInfo.KNOWLEDGE_INFO;
import static com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD;

/**
 * Created by keeley on 2017/10/25.
 */
@Service
public class KnowledgeViewRecordServiceSupport implements KnowledgeViewRecordService {
    private CommonDao<KnowledgeViewRecord> knowledgeViewRecordCommonDao;
    @Autowired
    public void setKnowledgeViewRecordCommonDao(CommonDao<KnowledgeViewRecord> knowledgeViewRecordCommonDao) {
        this.knowledgeViewRecordCommonDao = knowledgeViewRecordCommonDao;
    }

    @Override
    public List<Map<String, Object>> totalMonth(int page, Optional<String> month) {
        long start, end;
        if(month.isPresent() && month.get().length() == 6) {
            start = getMonthStart(month.get(), 0);
            end = getMonthStart(month.get(), 1);
        } else {
            start = DateUtil.getMonthStart();
            end = DateUtil.getMonthStart(1);
        }

        return knowledgeViewRecordCommonDao.execute(dslContext -> {
            Table<Record2<String, Integer>> table1 =  dslContext.select(KNOWLEDGE_VIEW_RECORD.KNOWLEDGE_ID,
                KNOWLEDGE_VIEW_RECORD.KNOWLEDGE_ID.count().as("visits")).from(KNOWLEDGE_VIEW_RECORD)
                .where(KNOWLEDGE_VIEW_RECORD.CREATE_TIME.between(start,end))
                .groupBy(KNOWLEDGE_VIEW_RECORD.KNOWLEDGE_ID).limit(page).asTable();

            return dslContext.select(KNOWLEDGE_INFO.NAME.as("name"),table1.field(1)).from(table1).leftJoin(KNOWLEDGE_INFO)
                    .on(KNOWLEDGE_INFO.ID.eq(table1.field(0, String.class))).orderBy(table1.field(1).desc()).fetchMaps();
        });

    }

    private static long getMonthStart(String month,int monthAdd){
        Integer year = Integer.valueOf(month.substring(0,4));
        Integer mm = Integer.valueOf(month.substring(4));
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR,year);
        cal.set(Calendar.MONTH,(mm.intValue() - 1));
        cal.set(Calendar.DAY_OF_MONTH,1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);

        cal.add(Calendar.MONTH, monthAdd);
        return cal.getTimeInMillis();
    }
}
