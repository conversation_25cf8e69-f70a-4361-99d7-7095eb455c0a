package com.zxy.product.course.service.support.sharding;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.sharding.ShardingConfigService;
import com.zxy.product.course.content.CacheKeyConstant;
import com.zxy.product.course.entity.ShardingConfig;
import com.zxy.product.course.service.util.SplitTableName;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

import static com.zxy.product.course.jooq.Tables.SHARDING_CONFIG;

@Service
public class ShardingConfigServiceSupport implements ShardingConfigService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShardingConfigServiceSupport.class);

    private CommonDao<ShardingConfig> dao;
    private Cache cache;

    @Autowired
    public void setDao(CommonDao<ShardingConfig> dao) {
        this.dao = dao;
    }
    @Autowired
    public void setCache(Cache cache) {
        this.cache = cache;
    }

    @Override
    public TableImpl<?> getTableName(String businessId, Integer logicTable, String defaultTable) {
        return SplitTableName.getTableNameByCode(getTableStringName(businessId,logicTable,defaultTable));
    }

    @Override
    public String getTableStringName(String businessId, Integer logicTable, String defaultTable) {
        return  cache.get(CacheKeyConstant.SHARDING_CONFIG + businessId + "-" + logicTable, ()->{
            return dao.execute(e->e.select(SHARDING_CONFIG.REAL_TABLE).from(SHARDING_CONFIG)
                            .where(SHARDING_CONFIG.BUSINESS_ID.eq(businessId),SHARDING_CONFIG.LOGIC_TABLE.eq(logicTable)))
                    .limit(1).fetchOptional(SHARDING_CONFIG.REAL_TABLE).orElse(defaultTable);
        }, 60 * 60 * 24);
    }

    public void clearCache(String businessId, Integer logicTable) {
        cache.clear(CacheKeyConstant.SHARDING_CONFIG + businessId + "-" + logicTable);
        LOGGER.info("【清除获取表名的缓存,业务id:{},逻辑表:{}】", businessId, logicTable);
    }

    public void clearCache(String businessId) {
        Set<Integer> logicTables = dao.execute(e->e.selectDistinct(SHARDING_CONFIG.LOGIC_TABLE).from(SHARDING_CONFIG))
                .fetchSet(SHARDING_CONFIG.LOGIC_TABLE);
        for (Integer logicTable : logicTables) {
            this.clearCache(businessId, logicTable);
        }
    }

    public void clearCache() {
        cache.clearByPattern(CacheKeyConstant.SHARDING_CONFIG);
        LOGGER.info("【清除所有获取表名的缓存】");
    }

}
