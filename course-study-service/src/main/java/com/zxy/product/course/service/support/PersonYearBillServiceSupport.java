package com.zxy.product.course.service.support;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.PersonYearBillService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.PersonYearBill;
import com.zxy.product.course.entity.SubjectYearBill;

import static com.zxy.product.course.jooq.Tables.PERSON_YEAR_BILL;
import static com.zxy.product.course.jooq.Tables.SUBJECT_YEAR_BILL;

import java.util.Optional;

@Service
public class PersonYearBillServiceSupport implements PersonYearBillService {

	private CommonDao<PersonYearBill> dao;
	private CommonDao<SubjectYearBill> SubjectYearBillDao;
    private Cache cache;
	
	@Autowired
	public void setDao(CommonDao<PersonYearBill> dao) {
		this.dao = dao;
	}
	
	@Autowired
	public void setSubjectYearBillDao(CommonDao<SubjectYearBill> subjectYearBillDao) {
		SubjectYearBillDao = subjectYearBillDao;
	}

    @Autowired
    public void setCache(Cache cache) {
    	this.cache = cache;
    }
    
	@Override
	public PersonYearBill getByMemberId(String memberId) {
		return cache.get(PersonYearBill.CACHE_KEY + memberId, ()->{
			PersonYearBill personBill = dao.fetchOne(PERSON_YEAR_BILL.MEMBER_ID.eq(memberId)).orElseThrow(()->new UnprocessableException(ErrorCode.CourseNotAudience));
			if (personBill.getFavoriteSubjectId() != null) {
				Optional<SubjectYearBill> subjectBill = SubjectYearBillDao.fetchOne(SUBJECT_YEAR_BILL.SUBJECT_ID.eq(personBill.getFavoriteSubjectId()));
				subjectBill.ifPresent(x->{
					personBill.setFavoriteSubject(x.getSubjectName());
					personBill.setFavoriteSubjectMemberCount(x.getStudyMemberCount());
					personBill.setGroupMemberNum(x.getGroupMemberNum());
				});
			}
			return personBill;
		}, 60*60*24);
	}

}
