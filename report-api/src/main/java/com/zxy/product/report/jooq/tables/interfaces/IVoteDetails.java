/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 投票描述
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IVoteDetails extends Serializable {

    /**
     * Setter for <code>report.t_vote_details.f_id</code>. 投票ID
     */
    public void setId(String value);

    /**
     * Getter for <code>report.t_vote_details.f_id</code>. 投票ID
     */
    public String getId();

    /**
     * Setter for <code>report.t_vote_details.f_vote_name</code>. 投票活动名称
     */
    public void setVoteName(String value);

    /**
     * Getter for <code>report.t_vote_details.f_vote_name</code>. 投票活动名称
     */
    public String getVoteName();

    /**
     * Setter for <code>report.t_vote_details.f_number</code>. 每人投票个数
     */
    public void setNumber(Integer value);

    /**
     * Getter for <code>report.t_vote_details.f_number</code>. 每人投票个数
     */
    public Integer getNumber();

    /**
     * Setter for <code>report.t_vote_details.f_frequency</code>. 每人投票次数
     */
    public void setFrequency(Integer value);

    /**
     * Getter for <code>report.t_vote_details.f_frequency</code>. 每人投票次数
     */
    public Integer getFrequency();

    /**
     * Setter for <code>report.t_vote_details.f_start_time</code>. 投票活动起始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>report.t_vote_details.f_start_time</code>. 投票活动起始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>report.t_vote_details.f_end_time</code>. 投票活动结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>report.t_vote_details.f_end_time</code>. 投票活动结束时间
     */
    public Long getEndTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IVoteDetails
     */
    public void from(com.zxy.product.report.jooq.tables.interfaces.IVoteDetails from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IVoteDetails
     */
    public <E extends com.zxy.product.report.jooq.tables.interfaces.IVoteDetails> E into(E into);
}
