/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.report.jooq.tables.interfaces.IIhrDict;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IhrDictEntity extends BaseEntity implements IIhrDict {

    private static final long serialVersionUID = 1L;

    private Integer key;
    private String  code;
    private String  value;
    private String  organizationId;

    public IhrDictEntity() {}

    public IhrDictEntity(IhrDictEntity value) {
        this.key = value.key;
        this.code = value.code;
        this.value = value.value;
        this.organizationId = value.organizationId;
    }

    public IhrDictEntity(
        String  id,
        Long    createTime,
        Integer key,
        String  code,
        String  value,
        String  organizationId
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.key = key;
        this.code = code;
        this.value = value;
        this.organizationId = organizationId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Integer getKey() {
        return this.key;
    }

    @Override
    public void setKey(Integer key) {
        this.key = key;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("IhrDictEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(key);
        sb.append(", ").append(code);
        sb.append(", ").append(value);
        sb.append(", ").append(organizationId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IIhrDict from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setKey(from.getKey());
        setCode(from.getCode());
        setValue(from.getValue());
        setOrganizationId(from.getOrganizationId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IIhrDict> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends IhrDictEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.report.jooq.tables.records.IhrDictRecord r = new com.zxy.product.report.jooq.tables.records.IhrDictRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.ID, record.getValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.CREATE_TIME, record.getValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.KEY.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.KEY, record.getValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.KEY));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.CODE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.CODE, record.getValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.CODE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.VALUE, record.getValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.ORGANIZATION_ID, record.getValue(com.zxy.product.report.jooq.tables.IhrDict.IHR_DICT.ORGANIZATION_ID));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
