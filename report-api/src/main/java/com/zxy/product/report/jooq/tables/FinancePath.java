/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables;


import com.zxy.product.report.jooq.Keys;
import com.zxy.product.report.jooq.Report;
import com.zxy.product.report.jooq.tables.records.FinancePathRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 财务部考试文件表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FinancePath extends TableImpl<FinancePathRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>report.t_finance_path</code>
     */
    public static final FinancePath FINANCE_PATH = new FinancePath();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<FinancePathRecord> getRecordType() {
        return FinancePathRecord.class;
    }

    /**
     * The column <code>report.t_finance_path.f_id</code>.
     */
    public final TableField<FinancePathRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>report.t_finance_path.f_create_time</code>. 创建时间
     */
    public final TableField<FinancePathRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>report.t_finance_path.f_type</code>. 文件类型：1：word ，2: excel，3: ppt ，4: ptf，5：压缩包
     */
    public final TableField<FinancePathRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "文件类型：1：word ，2: excel，3: ppt ，4: ptf，5：压缩包");

    /**
     * The column <code>report.t_finance_path.f_group_name</code>. 组名
     */
    public final TableField<FinancePathRecord, String> GROUP_NAME = createField("f_group_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组名");

    /**
     * The column <code>report.t_finance_path.f_file_id</code>. 文件id
     */
    public final TableField<FinancePathRecord, String> FILE_ID = createField("f_file_id", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "文件id");

    /**
     * The column <code>report.t_finance_path.f_file_name</code>. 文件名称
     */
    public final TableField<FinancePathRecord, String> FILE_NAME = createField("f_file_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "文件名称");

    /**
     * Create a <code>report.t_finance_path</code> table reference
     */
    public FinancePath() {
        this("t_finance_path", null);
    }

    /**
     * Create an aliased <code>report.t_finance_path</code> table reference
     */
    public FinancePath(String alias) {
        this(alias, FINANCE_PATH);
    }

    private FinancePath(String alias, Table<FinancePathRecord> aliased) {
        this(alias, aliased, null);
    }

    private FinancePath(String alias, Table<FinancePathRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "财务部考试文件表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Report.REPORT_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<FinancePathRecord> getPrimaryKey() {
        return Keys.KEY_T_FINANCE_PATH_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<FinancePathRecord>> getKeys() {
        return Arrays.<UniqueKey<FinancePathRecord>>asList(Keys.KEY_T_FINANCE_PATH_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public FinancePath as(String alias) {
        return new FinancePath(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public FinancePath rename(String name) {
        return new FinancePath(name, null);
    }
}
