package com.zxy.product.report.api;

import java.util.List;
import java.util.Optional;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.report.entity.Practice;

@RemoteService
public interface PracticeService {
	
	/**
	 * 投票页列表
	 * @param page		页数
	 * @param pageSize	每页行数
	 * @param type		案例类型
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	PagedResult<Practice> find(int page, int pageSize, String type);

	/**
	 * 排行页列表
	 * @param page		页数
	 * @param pageSize	每页行数
	 * @param date		日期
	 * @param type		案例类型
	 * @return
	 */
	@Transactional(propagation = Propagation.SUPPORTS)
	PagedResult<Practice> findSort(int page, int pageSize, String date, Optional<String> type);
	
	/**
	 * 案例详情
	 * @param id		案例ID
	 * @return
	 */
	@Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	Practice get(String id,String voteDate);

	/**
	 * 投票
	 * @param memberId	用户ID
	 * @param voteDate	投票日期
	 * @param voteIds	要投票的案例ID集合
	 * @return
	 */
	@Transactional(propagation = Propagation.SUPPORTS)
	int vote(String memberId, String voteDate, List<String> voteIds);

	/**
	 * 初始化当天所有案例的统计数据
	 * @param voteDate
	 * @return
	 */
	int initPracticeVoteStatistics(String voteDate);
}
