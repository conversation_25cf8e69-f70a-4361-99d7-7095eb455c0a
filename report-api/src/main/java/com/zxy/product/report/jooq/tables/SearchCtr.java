/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables;


import com.zxy.product.report.jooq.Keys;
import com.zxy.product.report.jooq.Report;
import com.zxy.product.report.jooq.tables.records.SearchCtrRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 搜索结果点击率
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SearchCtr extends TableImpl<SearchCtrRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>report.t_search_ctr</code>
     */
    public static final SearchCtr SEARCH_CTR = new SearchCtr();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SearchCtrRecord> getRecordType() {
        return SearchCtrRecord.class;
    }

    /**
     * The column <code>report.t_search_ctr.f_id</code>. 主键
     */
    public final TableField<SearchCtrRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>report.t_search_ctr.f_first_page</code>. 第一页点击率
     */
    public final TableField<SearchCtrRecord, Long> FIRST_PAGE = createField("f_first_page", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "第一页点击率");

    /**
     * The column <code>report.t_search_ctr.f_article_one</code>. 第一条点击率
     */
    public final TableField<SearchCtrRecord, Long> ARTICLE_ONE = createField("f_article_one", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "第一条点击率");

    /**
     * The column <code>report.t_search_ctr.f_search_total</code>. 搜索总次数
     */
    public final TableField<SearchCtrRecord, Long> SEARCH_TOTAL = createField("f_search_total", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "搜索总次数");

    /**
     * The column <code>report.t_search_ctr.f_day</code>. 天,年月日
     */
    public final TableField<SearchCtrRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "天,年月日");

    /**
     * The column <code>report.t_search_ctr.f_create_time</code>. 创建时间
     */
    public final TableField<SearchCtrRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>report.t_search_ctr</code> table reference
     */
    public SearchCtr() {
        this("t_search_ctr", null);
    }

    /**
     * Create an aliased <code>report.t_search_ctr</code> table reference
     */
    public SearchCtr(String alias) {
        this(alias, SEARCH_CTR);
    }

    private SearchCtr(String alias, Table<SearchCtrRecord> aliased) {
        this(alias, aliased, null);
    }

    private SearchCtr(String alias, Table<SearchCtrRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "搜索结果点击率");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Report.REPORT_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SearchCtrRecord> getPrimaryKey() {
        return Keys.KEY_T_SEARCH_CTR_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SearchCtrRecord>> getKeys() {
        return Arrays.<UniqueKey<SearchCtrRecord>>asList(Keys.KEY_T_SEARCH_CTR_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SearchCtr as(String alias) {
        return new SearchCtr(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SearchCtr rename(String name) {
        return new SearchCtr(name, null);
    }
}
