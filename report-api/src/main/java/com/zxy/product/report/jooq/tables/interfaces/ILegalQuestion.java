/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 法律部评价题目表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILegalQuestion extends Serializable {

    /**
     * Setter for <code>report.t_legal_question.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>report.t_legal_question.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>report.t_legal_question.f_content</code>. 内容
     */
    public void setContent(String value);

    /**
     * Getter for <code>report.t_legal_question.f_content</code>. 内容
     */
    public String getContent();

    /**
     * Setter for <code>report.t_legal_question.f_company_type</code>. 公司类型 1.专业公司和直属单位 2.重要子企业
     */
    public void setCompanyType(Integer value);

    /**
     * Getter for <code>report.t_legal_question.f_company_type</code>. 公司类型 1.专业公司和直属单位 2.重要子企业
     */
    public Integer getCompanyType();

    /**
     * Setter for <code>report.t_legal_question.f_type</code>. 分类 1.基础数据 2.完善法律风险防范机制 3.完善企业法律顾问制度 4.完善企业法律工作体系 5.推动合规管理体系建设 6.提升依法治企能力 7.法治建设推进第一责任人履职 8.工作亮点
     */
    public void setType(Integer value);

    /**
     * Getter for <code>report.t_legal_question.f_type</code>. 分类 1.基础数据 2.完善法律风险防范机制 3.完善企业法律顾问制度 4.完善企业法律工作体系 5.推动合规管理体系建设 6.提升依法治企能力 7.法治建设推进第一责任人履职 8.工作亮点
     */
    public Integer getType();

    /**
     * Setter for <code>report.t_legal_question.f_target_type</code>. 指标分类 1.非评价指标 2.分步达成指标 3.硬性指标 4.鼓励性指标
     */
    public void setTargetType(Integer value);

    /**
     * Getter for <code>report.t_legal_question.f_target_type</code>. 指标分类 1.非评价指标 2.分步达成指标 3.硬性指标 4.鼓励性指标
     */
    public Integer getTargetType();

    /**
     * Setter for <code>report.t_legal_question.f_basic_requirement</code>. 基本要求
     */
    public void setBasicRequirement(String value);

    /**
     * Getter for <code>report.t_legal_question.f_basic_requirement</code>. 基本要求
     */
    public String getBasicRequirement();

    /**
     * Setter for <code>report.t_legal_question.f_file_requirement</code>. 数据及证明文件要求
     */
    public void setFileRequirement(String value);

    /**
     * Getter for <code>report.t_legal_question.f_file_requirement</code>. 数据及证明文件要求
     */
    public String getFileRequirement();

    /**
     * Setter for <code>report.t_legal_question.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>report.t_legal_question.f_order</code>. 排序
     */
    public Integer getOrder();

    /**
     * Setter for <code>report.t_legal_question.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>report.t_legal_question.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>report.t_legal_question.f_score</code>. 23年法制评分默认分数
     */
    public void setScore(Double value);

    /**
     * Getter for <code>report.t_legal_question.f_score</code>. 23年法制评分默认分数
     */
    public Double getScore();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILegalQuestion
     */
    public void from(ILegalQuestion from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILegalQuestion
     */
    public <E extends ILegalQuestion> E into(E into);
}
