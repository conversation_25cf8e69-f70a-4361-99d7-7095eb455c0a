/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables;


import com.zxy.product.report.jooq.Keys;
import com.zxy.product.report.jooq.Report;
import com.zxy.product.report.jooq.tables.records.PracticeHistoryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 实践案例归档表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PracticeHistory extends TableImpl<PracticeHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>report.t_practice_history</code>
     */
    public static final PracticeHistory PRACTICE_HISTORY = new PracticeHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PracticeHistoryRecord> getRecordType() {
        return PracticeHistoryRecord.class;
    }

    /**
     * The column <code>report.t_practice_history.f_id</code>.
     */
    public final TableField<PracticeHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>report.t_practice_history.f_name</code>. 案例名称
     */
    public final TableField<PracticeHistoryRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(255), this, "案例名称");

    /**
     * The column <code>report.t_practice_history.f_img_path</code>. 案例封面图路径
     */
    public final TableField<PracticeHistoryRecord, String> IMG_PATH = createField("f_img_path", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "案例封面图路径");

    /**
     * The column <code>report.t_practice_history.f_type</code>. 类型
     */
    public final TableField<PracticeHistoryRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "类型");

    /**
     * The column <code>report.t_practice_history.f_organization_id</code>. 归属部门id
     */
    public final TableField<PracticeHistoryRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "归属部门id");

    /**
     * The column <code>report.t_practice_history.f_organization_name</code>. 归属部门名称
     */
    public final TableField<PracticeHistoryRecord, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "归属部门名称");

    /**
     * The column <code>report.t_practice_history.f_upper_number</code>. 案例单位有效票数上限值
     */
    public final TableField<PracticeHistoryRecord, Integer> UPPER_NUMBER = createField("f_upper_number", org.jooq.impl.SQLDataType.INTEGER, this, "案例单位有效票数上限值");

    /**
     * The column <code>report.t_practice_history.f_total_votes</code>. 总票数
     */
    public final TableField<PracticeHistoryRecord, Integer> TOTAL_VOTES = createField("f_total_votes", org.jooq.impl.SQLDataType.INTEGER, this, "总票数");

    /**
     * The column <code>report.t_practice_history.f_own_votes</code>. 本单位人员投票数
     */
    public final TableField<PracticeHistoryRecord, Integer> OWN_VOTES = createField("f_own_votes", org.jooq.impl.SQLDataType.INTEGER, this, "本单位人员投票数");

    /**
     * The column <code>report.t_practice_history.f_valid_votes</code>. 有效票数
     */
    public final TableField<PracticeHistoryRecord, Integer> VALID_VOTES = createField("f_valid_votes", org.jooq.impl.SQLDataType.INTEGER, this, "有效票数");

    /**
     * The column <code>report.t_practice_history.f_last_vote_time</code>. 最后投票时间
     */
    public final TableField<PracticeHistoryRecord, Long> LAST_VOTE_TIME = createField("f_last_vote_time", org.jooq.impl.SQLDataType.BIGINT, this, "最后投票时间");

    /**
     * The column <code>report.t_practice_history.f_order</code>. 排序
     */
    public final TableField<PracticeHistoryRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>report.t_practice_history.f_create_time</code>. 创建时间
     */
    public final TableField<PracticeHistoryRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>report.t_practice_history.f_date</code>. 历史数据日期
     */
    public final TableField<PracticeHistoryRecord, Integer> DATE = createField("f_date", org.jooq.impl.SQLDataType.INTEGER, this, "历史数据日期");

    /**
     * Create a <code>report.t_practice_history</code> table reference
     */
    public PracticeHistory() {
        this("t_practice_history", null);
    }

    /**
     * Create an aliased <code>report.t_practice_history</code> table reference
     */
    public PracticeHistory(String alias) {
        this(alias, PRACTICE_HISTORY);
    }

    private PracticeHistory(String alias, Table<PracticeHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private PracticeHistory(String alias, Table<PracticeHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "实践案例归档表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Report.REPORT_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PracticeHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_PRACTICE_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PracticeHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<PracticeHistoryRecord>>asList(Keys.KEY_T_PRACTICE_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PracticeHistory as(String alias) {
        return new PracticeHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PracticeHistory rename(String name) {
        return new PracticeHistory(name, null);
    }
}
