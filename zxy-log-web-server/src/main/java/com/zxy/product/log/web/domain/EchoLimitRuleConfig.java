package com.zxy.product.log.web.domain;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年01月09日 16:41
 */
public class EchoLimitRuleConfig {
    private static final long serialVersionUID = 5386904533107466130L;

    /**限流规则集合*/
    private List<FlowRuleConfig> rules;

    /**主开关 0关闭 1开启*/
    private Integer mainSwitch;

    public List<FlowRuleConfig> getRules() { return rules; }

    public void setRules(List<FlowRuleConfig> rules) { this.rules = rules; }

    public Integer getMainSwitch() { return mainSwitch; }

    public void setMainSwitch(Integer mainSwitch) { this.mainSwitch = mainSwitch; }

    @Override
    public String toString() {
        return "EchoLimitRuleConfig{" +
                "rules=" + rules +
                ", mainSwitch=" + mainSwitch +
                '}';
    }
}
