package com.zxy.product.log.web.controller;

import java.sql.Date;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Optional;

import com.zxy.common.restful.security.Permitted;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.product.log.api.AuditLogService;
import com.zxy.product.log.entity.AuditLog;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/audit-log")
public class AuditLogController {

    private AuditLogService auditLogService;

    @Autowired
    public void setAuditLogService(AuditLogService auditLogService) {
        this.auditLogService = auditLogService;
    }

    @RequestMapping(value = "/page", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @Param(name = "desc", type = String.class)
    @Param(name = "module", type = String.class)
    @Param(name = "memberFullName", type = String.class)
    @Param(name = "action", type = Integer.class)
    @Param(name = "browser", type = String.class)
    @Param(name = "start", type = String.class)
    @Param(name = "end", type = String.class)
    @JSON("items.(id, desc, module, action, memberFullName, browser, ip, logTime)")
    @JSON("recordCount")
    @Permitted(perms = {"log/audit-log"})
    public PagedResult<AuditLog> list(RequestContext requestContext) {
        String rootOrganizationId = "1";
        return auditLogService.list(
                requestContext.getOptionalInteger("page").orElse(1),
                requestContext.getOptionalInteger("pageSize").orElse(10),
                rootOrganizationId,
                requestContext.getOptionalString("desc"),
                requestContext.getOptionalString("module"),
                requestContext.getOptionalString("memberFullName"),
                requestContext.getOptionalInteger("action"),
                requestContext.getOptionalString("browser"),
                dateString2OptionalLong(requestContext.getOptionalString("start")),
                dateString2OptionalLong(requestContext.getOptionalString("end")));
    }

    private Optional<Long> dateString2OptionalLong(Optional<String> value) {
        return value.map(t -> Date.from(LocalDate.parse(t).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime());
    }
}
