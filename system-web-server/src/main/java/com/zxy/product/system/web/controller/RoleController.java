package com.zxy.product.system.web.controller;

import com.alibaba.dubbo.common.URL;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.system.content.ErrorCode;
import com.zxy.product.system.entity.Grant;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Date;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.zxy.product.system.web.util.BrowserUtil;
import com.zxy.product.system.web.util.DateUtil;
import com.zxy.product.system.web.util.ImportExportUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.system.api.permission.RoleService;
import com.zxy.product.system.entity.Member;
import com.zxy.product.system.entity.Role;
import com.zxy.product.system.jooq.tables.pojos.MemberEntity;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/role")
public class RoleController {

    private RoleService roleService;
    private MemberService memberService;
    private final static Logger LOGGER = LoggerFactory.getLogger(RoleController.class);



    @Autowired
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @RequestMapping(method = RequestMethod.POST)
    @Permitted(perms = {"system/role"})
    @Param(name = "name", type = String.class, required = true)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "parentId", type = String.class, required = true)
    @Param(name = "menuIds", type = String.class, required = true)
    @Param(name = "desc")
    @Param(name = "childFlag", type = Integer.class)
    @JSON("id,name,organizationId,parentId,createTime")
    @Audit(module = "系统管理", subModule = "权限管理-角色管理", action = Audit.Action.INSERT, fisrtAction = "新增", desc = "新增角色{0}", params = {"name"})
    public Role insert(RequestContext requestContext, Subject<Member> subject) {
        return roleService.insert(
        		subject.getCurrentUserId(),
        		requestContext.get("name", String.class),
        		requestContext.get("organizationId", String.class),
	            requestContext.get("parentId", String.class),
	            requestContext.get("menuIds", String.class).split(","),
	            requestContext.getOptional("desc", String.class),
	            requestContext.getOptional("childFlag", Integer.class)
        );
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Permitted(perms = {"system/role"})
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "name", type = String.class)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "parentId", type = String.class)
    @Param(name = "menuIds", type = String.class, required = true)
    @Param(name = "indeterminates", type = String.class)
    @Param(name = "desc")
    @Param(name = "childFlag", type = Integer.class)
    @JSON("id,name,organizationId,parentId,createTime")
    @JSON("menus.(id)")
    @Audit(module = "系统管理", subModule = "权限管理-角色管理", action = Audit.Action.UPDATE, fisrtAction = "编辑", desc = "编辑角色{0}", params = {"name"})
    public Role update(RequestContext requestContext, Subject<Member> subject) {
        return roleService.update(
        		subject.getCurrentUserId(),
        		requestContext.get("id", String.class),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("organizationId", String.class),
                requestContext.getOptional("parentId", String.class),
                requestContext.get("menuIds", String.class).split(","),
                subject.getRootOrganizationId(),
                requestContext.getOptionalInteger("order"),
                requestContext.getOptional("desc", String.class),
                requestContext.getOptional("childFlag", Integer.class));
    }


    @RequestMapping(value = "/update-order/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "order", type = Integer.class)
    @JSON("id")
    public Role updateOrder(RequestContext requestContext) {
        return roleService.updateOrder(requestContext.get("id", String.class),
            requestContext.getInteger("order"));
    }

    @RequestMapping(value = "/update-child-flag/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "childFlag", type = Integer.class)
    @JSON("result")
    public Map<String, Boolean> updateChildFlag(RequestContext rc) {
        return ImmutableMap.of("result", roleService.updateChildFlag(rc.get("id", String.class), rc.getInteger("childFlag")));
    }

    @RequestMapping(value = "/{id}" , method = RequestMethod.DELETE)
    @Permitted(perms = {"system/role"})
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "name", type = String.class, required = true)
    @JSON("*")
    @Audit(module = "系统管理", subModule = "权限管理-角色管理", action = Audit.Action.DELETE, fisrtAction = "删除", desc = "删除角色{0}", params = {"name"})
    public Map<String,Object> delete(RequestContext requestContext) {
        return ImmutableMap.of("count",roleService.delete(requestContext.getString("id")));
    }

    @RequestMapping(value = "/{id}" , method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "grantId", type = String.class)
    @Param(name = "memberId", type = String.class)
    @JSON("id,name,organizationId,parentId,parentName,createTime,desc,type,childFlag,grantOrganizationName,memberCount")
    @JSON("menus.(id,name,parentId)")
    @JSON("organization.(id,name)")
    public Role get(RequestContext requestContext, Subject<Member> subject, @RequestHeader(required = false) String uri) {
        return roleService.get(requestContext.get("id", String.class), requestContext.getOptionalString("grantId"), subject.getCurrentUserId(), uri,
            requestContext.getOptionalString("memberId"));
    }

    @RequestMapping(method = RequestMethod.GET)
    @Permitted(perms = {"system/role", Grant.URI})
    @Param(name = "page", type = Integer.class , required = true)
    @Param(name = "pageSize", type = Integer.class , required = true)
    @Param(name = "name", type = String.class)
    @Param(name = "memberId", type = String.class)
    @Param(name = "type", type = Integer.class)
    @Param(name = "childFlag", type = Integer.class)
    @Param(name = "parentName", type = String.class)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "createTimeStart", type = String.class)
    @Param(name = "createTimeEnd", type = String.class)
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】
    @JSON("recordCount")
    @JSON("items.(id,name,parentId,parentName,createTime,owned,init,memberCount,desc,type,childFlag,order,grantOrganizationName,grantId)")
    @JSON("items.organization.(id,name)")
    public PagedResult<Role> findPage(RequestContext requestContext, Subject<Member> subject, @RequestHeader String uri) {
        return roleService.findPage(
                                 requestContext.get("page", Integer.class),
                                 requestContext.get("pageSize", Integer.class),
                                 subject.getCurrentUserId(),
                                 requestContext.getOptional("name", String.class),
                                 requestContext.getOptional("parentName", String.class),
                                 requestContext.getOptional("organizationId", String.class),
                                 dateString2OptionalLong(requestContext.getOptional("createTimeStart", String.class)),
                                 dateString2OptionalLongEndOfDay(requestContext.getOptional("createTimeEnd", String.class)),
                                 requestContext.getOptionalInteger("contain").orElse(0),
                                 Optional.of(requestContext.getOptionalInteger("type").orElse(Role.TYPE_STANDARD)),
                                 requestContext.getOptional("memberId", String.class),
                                 requestContext.getOptionalInteger("childFlag"),uri);
    }

    /** 查询当前用户能够看到的角色列表,也即:当前用户被授权的角色,以及此角色的子角色(这里,'子'指菜单比我少),并且该子角色在我的所属组织范围内*/
    @RequestMapping(value="/find-grant-roles", method = RequestMethod.GET)
    @Permitted
    @JSON("id,name,organizationId,parentId,createTime)")
    public List<Role> findGrantRoles(Subject<MemberEntity> subject, @RequestHeader String uri){
    	return roleService.findGrantRoles(subject.getCurrentUserId(), uri);
    }

    /**
     * 查询当前用户能够看到的角色列表,也即:当前用户被授权的角色,以及此角色的子角色(这里,'子'指菜单比我少),并且该子角色在我的所属组织范围内
     * 以及元角色
     * */
    @RequestMapping(value="/find-grant-role-page", method = RequestMethod.GET)
    @Permitted
    @Param(name = "content")
    @Param(name = "type", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,name,organizationId,parentId,createTime,childFlag,desc)")
    public PagedResult<Role> findGrantRolePage(RequestContext rc, Subject<MemberEntity> subject, @RequestHeader String uri){
    	return roleService.findGrantRolePage(subject.getCurrentUserId(), uri, rc.getOptionalString("content"), rc.getOptionalInteger("type"));
    }

    /**
     * 导出当前角色的所有子角色
     */
    @RequestMapping(value = "/sub-role-export", method = RequestMethod.GET)
    @Permitted
    @Param(name = "roleId",type = String.class)
    public void subRoleExport(RequestContext requestContext, Subject<Member> subject) throws IOException {

        HttpServletResponse response = requestContext.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition",  "attachment;filename="
                + parseFileName("子角色列表", requestContext.getRequest().getHeader("User-Agent")) + ".xlsx");

        List<Role> items = roleService.listsubRole(requestContext.getString("roleId"));

        if (CollectionUtils.isEmpty(items)){
            //子角色不存在
            throw new UnprocessableException(ErrorCode.SubRoleNotExists);
        }

        Writer writer = new ExcelWriter();

        writer.sheet("子角色列表", items)
                .field("序号", Role::getId)
                .field("子角色名称", Role::getName);

        OutputStream os = null;
        String date = DateUtil.dateLongToString(System.currentTimeMillis(), DateUtil.YYYYMMDD);
        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(subject.getCurrentUserId());
        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
        try {
            os = response.getOutputStream();
            writer.write(os, wookBook -> {
                try {
                    ImportExportUtil.putWaterRemarkToExcel(wookBook, wookBook.getSheetAt(0), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
                } catch (IOException e) {
                    LOGGER.error("角色管理-子角色导出出错", e);
                }
            });
        } catch (IOException e) {
            LOGGER.error("角色管理-子角色导出出错", e);
        } finally {
            if (os != null) {
                os.close();
            }
            LOGGER.info("【角色管理-子角色导出写入excel结束】：{}", System.currentTimeMillis());
        }
    }

    /**
     * 导出当前角色信息
     */
    @RequestMapping(value = "/role-export", method = RequestMethod.GET)
    @Permitted
    @Param(name = "name", type = String.class)
    @Param(name = "memberId", type = String.class)
    @Param(name = "type", type = Integer.class)
    @Param(name = "childFlag", type = Integer.class)
    @Param(name = "parentName", type = String.class)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "createTimeStart", type = String.class)
    @Param(name = "createTimeEnd", type = String.class)
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】
    @Audit(module = "系统管理", subModule = "权限管理-角色管理", action = Audit.Action.EXPORT, fisrtAction = "导出", desc = "导出角色清单")
    public void roleExport(RequestContext requestContext, Subject<Member> subject) throws IOException {

        HttpServletResponse response = requestContext.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition",  "attachment;filename="
                + parseFileName("角色清单", requestContext.getRequest().getHeader("User-Agent")) + ".xlsx");

        PagedResult<Role> result = roleService.findPage(
                1,
                5000,
                subject.getCurrentUserId(),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("parentName", String.class),
                requestContext.getOptional("organizationId", String.class),
                dateString2OptionalLong(requestContext.getOptional("createTimeStart", String.class)),
                dateString2OptionalLongEndOfDay(requestContext.getOptional("createTimeEnd", String.class)),
                requestContext.getOptionalInteger("contain").orElse(0),
                Optional.of(requestContext.getOptionalInteger("type").orElse(Role.TYPE_STANDARD)),
                requestContext.getOptional("memberId", String.class),
                requestContext.getOptionalInteger("childFlag"), "system/role");

        PagedResult<Role> resultY = roleService.findPage(
                1,
                5000,
                subject.getCurrentUserId(),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("parentName", String.class),
                requestContext.getOptional("organizationId", String.class),
                dateString2OptionalLong(requestContext.getOptional("createTimeStart", String.class)),
                dateString2OptionalLongEndOfDay(requestContext.getOptional("createTimeEnd", String.class)),
                requestContext.getOptionalInteger("contain").orElse(0),
                Optional.of(requestContext.getOptionalInteger("type").orElse(Role.TYPE_ELEMENT)),
                requestContext.getOptional("memberId", String.class),
                requestContext.getOptionalInteger("childFlag"), "system/role");

        List<Role> roles = result.getItems();
        List<Role> rolesY = resultY.getItems();
        roles.addAll(rolesY);

        Writer writer = new ExcelWriter();

        writer.sheet("角色清单", roles)
                .indexColumn(Optional.of("序号"))
                .field("角色名称", Role::getName)
                .field("角色类型", w -> {
                    Integer flag = w.getType();
                    return flag == null ? "-" : (flag == 0 ? "标准角色" : "元角色");
                })
                .field("角色人数", Role::getMemberCount)
                .field("是否授予子节点", w -> {
                    Integer flag = w.getChildFlag();
                    return flag == null ? "-" : (flag == 0 ? "否" : "是");
                })
                .field("父角色", w -> {
                    Integer flag = w.getType();
                    return flag == null ? "-" : (flag == 0 ? w.getParentName() : "-");
                });

        OutputStream os = null;
        String date = DateUtil.dateLongToString(System.currentTimeMillis(), DateUtil.YYYYMMDD);
        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(subject.getCurrentUserId());
        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
        try {
            os = response.getOutputStream();
            writer.write(os, wookBook -> {
                try {
                    ImportExportUtil.putWaterRemarkToExcel(wookBook, wookBook.getSheetAt(0), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
                } catch (IOException e) {
                    LOGGER.error("角色管理-导出出错", e);
                }
            });
        } catch (IOException e) {
            LOGGER.error("角色管理-导出出错", e);
        } finally {
            if (os != null) {
                os.close();
            }
            LOGGER.info("【角色管理-写入excel结束】：{}", System.currentTimeMillis());
        }
    }

    private String parseFileName(String fileName, String agent){
        if (BrowserUtil.isMSBrowser(agent)) {
            return URL.encode(fileName);
        }
        return new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
    }

    private static Optional<Long> dateString2OptionalLong(Optional<String> value) {
        return value.map(t -> Date.from(LocalDate.parse(t).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime());
    }
    private static Optional<Long> dateString2OptionalLongEndOfDay(Optional<String> value) {
    	return value.map(t -> Date.from(LocalDate.parse(t).atStartOfDay().plusDays(1L).atZone(ZoneId.systemDefault()).toInstant()).getTime());
    }
}

