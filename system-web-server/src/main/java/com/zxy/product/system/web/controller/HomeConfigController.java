package com.zxy.product.system.web.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.AuthenticationException;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.SecurityManager;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.security.interceptor.CachedResultInterceptor;
import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.content.ErrorCode;
import com.zxy.product.system.api.homeconfig.*;
import com.zxy.product.system.api.permission.OrganizationService;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.entity.*;
import com.zxy.product.system.util.DateUtil;
import com.zxy.product.system.util.HomeCfgUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/home-config")
public class HomeConfigController {

	private final static Logger LOGGER = LoggerFactory.getLogger(HomeConfigController.class);

    HomeConfigService homeConfigService;

    OrganizationService organizationService;

    SecurityManager<Member> securityManager;

    HomeNavService homeNavService;

    HomeFooterService homeFooterService;

    HomeAdvertisementService homeAdvertisementService;

    HomeModuleService homeModuleService;

    HomeNewsService homeNewsService;

    HomeContentService homeContentService;

    HomeContentTreeService homeContentTreeService;

    SkinConfigService skinConfigService;

    HomePersonPanelService homePersonPanelService;

    HomeModuleConfigService homeModuleConfigService;

    CachedResultInterceptor cachedResultInterceptor;

    HomeCertifyService homeCertifyService;

	private MemberService memberService;

	private Cache cache;
	private Cache cacheTree;
	private MessageSender messageSender;


    private static final String CACHE_KEY_PREFIX = "home-config";
    private static final String URI = "system/home-config";
    private static final String ORG_ID = "orgId#";

    private static final Object LOCK = new Object();

    @Autowired
    public void setMessageSender(MessageSender messageSender){this.messageSender=messageSender;}

    @Autowired
	public void setHomeAdvertisementService(HomeAdvertisementService homeAdvertisementService) {
		this.homeAdvertisementService = homeAdvertisementService;
	}

	@Autowired
	public void setHomeModuleService(HomeModuleService homeModuleService) {
		this.homeModuleService = homeModuleService;
	}

	@Autowired
	public void setHomeNewsService(HomeNewsService homeNewsService) {
		this.homeNewsService = homeNewsService;
	}

	@Autowired
	public void setHomeContentService(HomeContentService homeContentService) {
		this.homeContentService = homeContentService;
	}

	@Autowired
    public void setHomeContentTreeService(HomeContentTreeService homeContentTreeService) {
        this.homeContentTreeService = homeContentTreeService;
    }

    @Autowired
	public void setSkinConfigService(SkinConfigService skinConfigService) {
		this.skinConfigService = skinConfigService;
	}

	@Autowired
	public void setHomePersonPanelService(HomePersonPanelService homePersonPanelService) {
		this.homePersonPanelService = homePersonPanelService;
	}

	@Autowired
	public void setHomeModuleConfigService(HomeModuleConfigService homeModuleConfigService) {
		this.homeModuleConfigService = homeModuleConfigService;
	}

    @Lazy
	@Autowired
	public void setCachedResultInterceptor(CachedResultInterceptor cachedResultInterceptor) {
		this.cachedResultInterceptor = cachedResultInterceptor;
	}

	@Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setHomeConfigService(HomeConfigService homeConfigService) {
        this.homeConfigService = homeConfigService;
    }

    @Autowired
    public void setSecurityManager(SecurityManager<Member> securityManager) {
        this.securityManager = securityManager;
    }

    @Autowired
    public void setHomeNavService(HomeNavService homeNavService) {
        this.homeNavService = homeNavService;
    }

    @Autowired
    public void setHomeFooterService(HomeFooterService homeFooterService) {
        this.homeFooterService = homeFooterService;
    }

    @Autowired
	public void setMemberService(MemberService memberService) {
		this.memberService = memberService;
	}

	@Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create(CACHE_KEY_PREFIX);
    }
    @Autowired
    public void setCacheTreeService(CacheService cacheService) {
        this.cacheTree = cacheService.create("home-content-tree");
    }
    @Autowired
    public void setHomeCertifyService(HomeCertifyService homeCertifyService) {
        this.homeCertifyService = homeCertifyService;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("id,name,description,skinConfigId,logo,logoMini,deleteFlag,enableHomeBrowse,logoPath,logoMiniPath,organizationId,organizationName,state,createTime,platform,jumpHomePage")
    @Permitted
    public HomeConfig get(RequestContext requestContext) {
        return homeConfigService.get(requestContext.getString("id"));
    }

    @RequestMapping(value = "/enable/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "name", required = true)
    @Param(name = "type", required = true)//0=机构首页,1=虚拟空间
    @JSON("id")
    @Audit(module = "运营管理", subModule = "首页管理-首页配置", action = Audit.Action.MANAGE, fisrtAction = "激活首页", desc = "激活首页{0}", params = {"name"})
    public ImmutableMap<String, String> enable(RequestContext requestContext) {
        String id = homeConfigService.enable(requestContext.getString("id"),requestContext.getInteger("type"));
        messageSender.send(MessageTypeContent.HOME_SYSTEM, MessageHeaderContent.HOME_CFG_ID,requestContext.getString("id"));
        return ImmutableMap.of("id", id);
    }

    @RequestMapping(value = "/disable/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "name", required = true)
    @JSON("id")
    @Audit(module = "运营管理", subModule = "首页管理-首页配置", action = Audit.Action.MANAGE, fisrtAction = "禁用首页", desc = "禁用首页{0}", params = {"name"})
    public Map<String, String> disable(RequestContext requestContext) {
        String id = homeConfigService.disable(requestContext.getString("id"));
        return ImmutableMap.of("id", id);
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Param(name = "id", required = true)
    @Param(name = "name", required = true)
    @JSON("*")
    @Audit(module = "运营管理", subModule = "首页管理-首页配置", action = Audit.Action.DELETE, fisrtAction = "删除", desc = "删除首页{0}", params = {"name"})
    public Object delete(RequestContext requestContext) {
        homeConfigService.delete(requestContext.getString("id"));
        return new Object();
    }


    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "organizationId")
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @JSON("*")
    @JSON("items.(*)")
    @Permitted
    public PagedResult<HomeConfig> find(RequestContext requestContext, Subject<Member> subject) {
        return homeConfigService.find(
                requestContext.getOptionalInteger("page").orElse(1),
                requestContext.getOptionalInteger("pageSize").orElse(10),
                subject.getCurrentUserId(),
                requestContext.getOptionalString("organizationId"));
    }


    @RequestMapping(method = RequestMethod.GET , value = "/new")
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @Param(name = "organizationId", required = true)
    @JSON("*")
    @JSON("items.(*)")
    @Permitted
    public PagedResult<HomeConfig> findNew(RequestContext requestContext, Subject<Member> subject) {
        List<Organization> orgList = organizationService.findOrgList(
                subject.getCurrentUserId(),
                URI,
                Optional.empty(),
                Optional.of(Organization.LEVEL_BRANCH),
                Optional.empty(),
                Optional.empty(),
                Optional.empty(),
                Optional.of(Organization.TYPE_ALL),
                Optional.empty(),
                Optional.empty(),
                Optional.empty(),
                Optional.empty());

        LOGGER.info("组织信息：{}", orgList);
        if (CollectionUtils.isEmpty(orgList)){
            return null;
        }

        List<String> ids = orgList.stream().map(Organization::getId).collect(Collectors.toList());
        String organizationId = requestContext.getString("organizationId");
        if(!ids.contains(organizationId)){
            return null;
        }

        return homeConfigService.find(
                requestContext.getOptionalInteger("page").orElse(1),
                requestContext.getOptionalInteger("pageSize").orElse(10),
                subject.getCurrentUserId(),
                Optional.of(organizationId)
                );
    }

    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "logo")
    @Param(name = "logoMini")
    @Param(name = "logoPath")
    @Param(name = "logoMiniPath")
    @Param(name = "name", required = true)
    @Param(name = "organizationId", required = true)
    @Param(name = "description")
    @Param(name = "navJson", required = true)
    @Param(name = "footerContent", required = true)
    @Param(name = "homeModuleConfigJson", required = false)
    @Param(name = "version", required = false)
    @JSON("id,name")
    @Audit(module = "运营管理", subModule = "首页管理-首页配置", action = Audit.Action.INSERT, fisrtAction = "新增", desc = "新增首页{0}", params = {"name"})
    public HomeConfig save(RequestContext requestContext) {
        List<HomeNav> homeNavs = com.alibaba.fastjson.JSON.parseArray(requestContext.getString("navJson"), HomeNav.class);
        String homeModuleConfigJson = requestContext.getOptionalString("homeModuleConfigJson").orElse(null);
        List<HomeModuleConfig> homeModuleConfigs = null;
        if (!ObjectUtils.isEmpty(homeModuleConfigJson)) {
            homeModuleConfigs = com.alibaba.fastjson.JSON.parseArray(homeModuleConfigJson, HomeModuleConfig.class);
        }
        HomeConfig config = homeConfigService.insert(
                requestContext.getOptionalString("logo"),
                requestContext.getOptionalString("logoPath"),
                requestContext.getOptionalString("logoMini"),
                requestContext.getOptionalString("logoMiniPath"),
                requestContext.getOptionalString("description"),
                requestContext.getString("name"),
                requestContext.getString("organizationId"),
                requestContext.getOptionalString("version").orElse(HomeConfig.VERSION_OLD),
                homeModuleConfigs);
        homeNavService.save(config.getId(), homeNavs);
        homeFooterService.insert(config.getId(), requestContext.getString("footerContent"));
        return config;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Param(name = "id")
    @Param(name = "logo")
    @Param(name = "logoMini")
    @Param(name = "logoPath")
    @Param(name = "logoMiniPath")
    @Param(name = "name", required = true)
    @Param(name = "organizationId", required = true)
    @Param(name = "description")
    @Param(name = "version", required = false)
    @JSON("id,name,organizationId")
    @Audit(module = "运营管理", subModule = "首页管理-首页配置", action = Audit.Action.UPDATE, fisrtAction = "修改信息", desc = "修改首页{0}", params = {"name"})
    public HomeConfig update(RequestContext requestContext) {
        HomeConfig config = homeConfigService.update(
                requestContext.getString("id"),
                requestContext.getOptionalString("logo"),
                requestContext.getOptionalString("logoPath"),
                requestContext.getOptionalString("logoMini"),
                requestContext.getOptionalString("logoMiniPath"),
                requestContext.getOptionalString("description"),
                requestContext.getString("name"),
                requestContext.getString("organizationId"),
                requestContext.getOptionalString("version").orElse(HomeConfig.VERSION_OLD));
        return config;
    }

    @RequestMapping(value = "/config", method = RequestMethod.GET)
    @Permitted
    @Param(name = "configId")
    @Param(name = "orgId")
    @Param(name = "type" )//默认 0=机构首页,1=虚拟空间
    @Param(name = "virtualSpacesId" )//虚拟空间id
//    @CachedResult(params = {"type","configId","orgId","virtualSpacesId"},group = CACHE_KEY_PREFIX, expired = 60 * 60 * 12)
    @JSON("id,name,description,skinConfigId,logo,logoMini,deleteFlag,enableHomeBrowse,logoPath,logoMiniPath,organizationId,organizationName,state," +
            "createTime, version")
    public HomeConfig getConfig(RequestContext requestContext) {
        Integer type = requestContext.getOptionalInteger("type").orElse(HomeConfig.TYPE_INSTITUTIONS);
        if (Objects.equals(type, HomeConfig.TYPE_INSTITUTIONS)) {
            return getOrganizationConfig(requestContext);
        } else {
            return getVirtualSpacesConfig(requestContext);
        }

    }

    @RequestMapping(value = "/config-register", method = RequestMethod.GET)
    @Permitted
    @Param(name = "orgId")
    @JSON("id,name,description,skinConfigId,logo,logoMini,deleteFlag,enableHomeBrowse,logoPath,logoMiniPath,organizationId,organizationName,state," +
            "createTime, version")
    public HomeConfig getConfigRegister(RequestContext requestContext) {
        String orgId = requestContext.getString("orgId");
        return getVirtualSpacesConfig(orgId);

    }


    /**
     * 查询虚拟空间首页
     * @param requestContext
     * @return
     */
    private HomeConfig getVirtualSpacesConfig(RequestContext requestContext) {
        HomeConfig config = null;
        Optional<String> virtualSpacesId = requestContext.getOptionalString("virtualSpacesId");
        if (virtualSpacesId.isPresent()) {
            config = homeConfigService.get(virtualSpacesId.get());
        }  else {
            Optional<Organization> org = organizationService.getByDomain(requestContext.getDomain());
            if (org.isPresent()) {
                config = homeConfigService.getEnableHomeConfig(org.get().getId() ,HomeConfig.TYPE_VIRTUAL_SPACE)
                        .orElseThrow(() -> new ValidationException(ErrorCode.OrganizationDomainNotFound));
            }
        }
        return config;
    }

    private HomeConfig getVirtualSpacesConfig(String orgId) {
              return  homeConfigService.getEnableHomeConfig(orgId ,HomeConfig.TYPE_VIRTUAL_SPACE)
                        .orElseThrow(() -> new ValidationException(ErrorCode.OrganizationDomainNotFound));
    }


    /**
     * 查询机构首页
     * @param requestContext
     * @return
     */
    private HomeConfig getOrganizationConfig(RequestContext requestContext){
        LOGGER.info("getConfig  coming....");
        HomeConfig config = null;
        Optional<String> optionalOrgId = requestContext.getOptionalString("orgId");
        Optional<String> optionalId = requestContext.getOptionalString("configId");
        if (optionalId.isPresent()) {
            config = homeConfigService.get(requestContext.getString("configId"));
        } else if (optionalOrgId.isPresent()) {
            config = homeConfigService.getEnableHomeConfig(optionalOrgId.get() ,HomeConfig.TYPE_INSTITUTIONS)
                    .orElseThrow(() -> new ValidationException(ErrorCode.OrganizationDomainNotFound));
        } else {
            Optional<Organization> org = organizationService.getByDomain(requestContext.getDomain());
//            Optional<Organization> org = organizationService.getByDomain("dev9.zhixueyun.com");
            if (org.isPresent()) {
                config = homeConfigService.getEnableHomeConfig(org.get().getId() ,HomeConfig.TYPE_INSTITUTIONS)
                        .orElseThrow(() -> new ValidationException(ErrorCode.OrganizationDomainNotFound));
            }
        }
        if (config != null && config.getEnableHomeBrowse() != null && HomeConfig.DISABLE_BROWSE == config.getEnableHomeBrowse()) { // 不启用首页浏览权限，需要登录
            securityManager.getOptional(requestContext.getRequest()).orElseThrow(() -> new AuthenticationException());
        }
        return config;
    }

    @RequestMapping(value = "/OrgByConfig", method = RequestMethod.GET)
    @Param(name = "orgId", type = String.class, required = true)
    @Param(name = "type")//0=机构首页,1=虚拟空间
    @JSON("*")
    public HomeConfig getOrgByConfig(RequestContext requestContext) {
        return homeConfigService.getEnableHomeConfig(requestContext.getString("orgId"),
                requestContext.getOptionalInteger("type").orElse(HomeConfig.TYPE_INSTITUTIONS)
                ).orElse(new HomeConfig());
    }

    @RequestMapping(value = "/has-config-ids", method = RequestMethod.GET)
    @JSON("*")
    @Permitted
    public List<String> findHasConfigOrgId() {
        return homeConfigService.findEnableOrganizationIds();
    }

    @RequestMapping(value = "/update-config/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "name", required = true)
    @Param(name = "enableBrowse", type = Integer.class, required = true)
    @Param(name = "state", type = Integer.class)
    @JSON("id")
    @Audit(module = "运营管理", subModule = "首页管理-首页配置", action = Audit.Action.UPDATE, fisrtAction = "首页配置", desc = "配置首页{0}", params = {"name"})
    public HomeConfig updateConfig(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
    	saveAsFinal(subject.getCurrentUserId(), authorization, context.getString("id"));
        Optional<Integer> state = context.getOptionalInteger("state");
        state.ifPresent(s -> {
            if (s == HomeConfig.STATE_ENABLE) {
                homeConfigService.disableByOrganizationId(homeConfigService.get(context.getString("id")).getOrganizationId());
            }
        });
        HomeConfig update = homeConfigService.update(context.getString("id"), context.getInteger("enableBrowse"),
                context.getOptionalInteger("state"));
        messageSender.send(MessageTypeContent.HOME_SYSTEM, MessageHeaderContent.HOME_CFG_ID,context.getString("id"));
        return update;

    }

    @RequestMapping(value = "/exists-enable", method = RequestMethod.GET)
    @Param(name = "orgId", required = true)
    @JSON("count")
    public Map<String, Integer> existsEnable(RequestContext context) {
        int count = homeConfigService.existsEnableByOrgId(context.getString("orgId"));
        return ImmutableMap.of("count", count);
    }

    @RequestMapping(value = "/organization", method = RequestMethod.GET)
    @Permitted
    @Param(name = "")
    @JSON("id,name,shortName")
    public List<Organization> findOrgByEnableHome(RequestContext context, Subject<Member> subject) {

        String companyOrganizationId = subject.getCurrentUser().getCompanyOrganization().getId();
        List<Organization> orgList = cache.get(ORG_ID + companyOrganizationId, List.class);
        if (orgList == null) {
            synchronized (LOCK) {
                orgList = cache.get(ORG_ID + companyOrganizationId, List.class);
                if (orgList == null) {
                    orgList = homeConfigService.findOrgSubByEnableHome(companyOrganizationId);

                    if (orgList == null) {
                        orgList = new ArrayList<>();
                    }
                    cache.set(ORG_ID + companyOrganizationId, orgList, 60 * 60 * 2);
                }
            }
        }
        return orgList;
    }

    /**
     * 保存所有缓存的配置到数据库
     * @param userId
     * @param userToken
     * @param homeConfigId
     */
    private void saveAsFinal(String userId, String userToken, String homeConfigId) {
    	/*
        homeNavService.saveAsFinal(userId, userToken, homeConfigId);
        homeAdvertisementService.saveAsFinal(userId, userToken, homeConfigId);
        homeNewsService.saveAsFinal(userId, userToken, homeConfigId);
        homeContentService.saveAsFinal(userId, userToken, homeConfigId);
        homeFooterService.saveAsFinal(userId, userToken, homeConfigId);
        homePersonPanelService.saveAsFinal(userId, userToken, homeConfigId);
        homeModuleConfigService.saveAsFinal(userId, userToken, homeConfigId);
        skinConfigService.saveAsFinal(userId, userToken, homeConfigId);
        */
    	LOGGER.error("HomeConfig.invoke");
    	List<Consumer<Object>> consumers = new LinkedList<Consumer<Object>>();
    	consumers.add((o) -> {
            homeModuleConfigService.saveAsFinal(userId, userToken, homeConfigId);
    	});
    	LOGGER.error("HomeConfig.save.homeModuleConfig");
    	consumers.add((o) -> {
            homeNavService.saveAsFinal(userId, userToken, homeConfigId);
    	});
    	LOGGER.error("HomeConfig.save.homeNav");
    	consumers.add((o) -> {
            homeAdvertisementService.saveAsFinal(userId, userToken, homeConfigId);
    	});
    	LOGGER.error("HomeConfig.save.homeAdvertisement");
    	Optional<HomeModuleConfig> news = homeModuleConfigService.findByHomeConfigIdAndModuleCode(homeConfigId, "news");
    	news.ifPresent(x -> {
    		consumers.add((o) -> {
                homeNewsService.saveAsFinal(userId, userToken, x.getId());
        	});
    	});
    	LOGGER.error("HomeConfig.save.homeNews");
    	consumers.add((o) -> {
            homeContentService.saveAsFinal(userId, userToken, homeConfigId);
    	});
    	LOGGER.error("HomeConfig.save.homeContent");
    	consumers.add((o) -> {
            homeContentTreeService.saveAsFinal(userId, userToken, homeConfigId);
    	});
    	LOGGER.error("HomeConfig.save.homeContentTree");
    	consumers.add((o) -> {
            homeFooterService.saveAsFinal(userId, userToken, homeConfigId);
    	});
    	LOGGER.error("HomeConfig.save.homeFooter");
    	consumers.add((o) -> {
            homePersonPanelService.saveAsFinal(userId, userToken, homeConfigId);
    	});
    	LOGGER.error("HomeConfig.save.homePerson");
    	consumers.add((o) -> {
            skinConfigService.saveAsFinal(userId, userToken, homeConfigId);
    	});
    	LOGGER.error("HomeConfig.save.skinConfig");

        consumers.add((o) -> {
            homeCertifyService.saveAsFinal(userId, userToken, homeConfigId);
        });
        LOGGER.error("HomeConfig.save.homeCertify");
    	boolean completed = HomeCfgUtil.executeConcurrentSave(userId, homeConfigId, consumers);

        HomeConfig homeConfig = homeConfigService.get(homeConfigId);
        // 解决未激活的首页配置变更后 预览不生效的问题
        // if (homeConfig.getState() == HomeConfig.STATE_ENABLE) {
        	clearCacheByModuleHomeConfigId(homeConfigId);
        // }

        if (!completed) {
        	throw new RuntimeException("save home config failed.");
        }

    }

    private void clearCacheByModuleHomeConfigId(String moduleHomeConfigId){

        Optional<HomeModuleConfig> tab = homeModuleConfigService.findByHomeConfigIdAndModuleCode(moduleHomeConfigId, "tab");
        tab.ifPresent(x -> {
            cache.clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cache.clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cache.clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("more"), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cache.clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("more"), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_PC_STR));
            LOGGER.error("HomeConfig.clear cache.tab done.");
        });
    	Optional<HomeModuleConfig> bigBanner = homeModuleConfigService.findByHomeConfigIdAndModuleCode(moduleHomeConfigId, "big-banner");
    	bigBanner.ifPresent(x -> {
    		cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), null, HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), null, HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "5", HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "5", HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "20", HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "20", HomeModuleConfig.CLIENT_TYPE_PC_STR));
    	});
    	LOGGER.error("HomeConfig.clear cache.bigBanner done.");
    	Optional<HomeModuleConfig> smallBanner = homeModuleConfigService.findByHomeConfigIdAndModuleCode(moduleHomeConfigId, "small-banner");
    	smallBanner.ifPresent(x -> {
    		cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), null, HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), null, HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "5", HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "5", HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "20", HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "20", HomeModuleConfig.CLIENT_TYPE_PC_STR));
    	});
    	LOGGER.error("HomeConfig.clear cache.smallBanner done.");
        Optional<HomeModuleConfig> bigBannerNew = homeModuleConfigService.findByHomeConfigIdAndModuleCode(moduleHomeConfigId, "big-banner-new");
        bigBannerNew.ifPresent(x -> {
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), null, HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), null, HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "5", HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "5", HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "20", HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "20", HomeModuleConfig.CLIENT_TYPE_PC_STR));
        });
        LOGGER.error("HomeConfig.clear cache.bigBannerNew done.");
        Optional<HomeModuleConfig> smallBannerNew = homeModuleConfigService.findByHomeConfigIdAndModuleCode(moduleHomeConfigId, "small-banner-new");
        smallBannerNew.ifPresent(x -> {
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), null, HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), null, HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "5", HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "5", HomeModuleConfig.CLIENT_TYPE_PC_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "24", HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeAdvertisementController.CACHE_GROUP_KEY, Optional.empty(), x.getId(), "24", HomeModuleConfig.CLIENT_TYPE_PC_STR));
        });
        Optional<HomeModuleConfig> classifiedCourses = homeModuleConfigService.findByHomeConfigIdAndModuleCode(moduleHomeConfigId, "classified-courses");
        classifiedCourses.ifPresent(x -> cacheTree.clear("configId#" + x.getId()));

        LOGGER.error("HomeConfig.clear cache.smallBannerNew done.");
        Optional<HomeModuleConfig> partySchool = homeModuleConfigService.findByHomeConfigIdAndModuleCode(moduleHomeConfigId, "party-school");
        partySchool.ifPresent(x -> {
            cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId(), HomeModuleConfig.CLIENT_TYPE_APP_STR));
            cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId(), HomeModuleConfig.CLIENT_TYPE_PC_STR));
        });
        LOGGER.error("HomeConfig.clear cache.partySchool done.");
    	List<HomeModuleConfig> layoutList = homeModuleConfigService.findListByHomeConfigIdAndModuleCode(moduleHomeConfigId, "layout");
    	if(layoutList != null) {
    		layoutList.stream().forEach(x -> {
    			cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId(), HomeModuleConfig.CLIENT_TYPE_APP_STR));
    	        cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId(), HomeModuleConfig.CLIENT_TYPE_PC_STR));
    	        cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("more"), x.getId(), HomeModuleConfig.CLIENT_TYPE_APP_STR));
    	        cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("more"), x.getId(), HomeModuleConfig.CLIENT_TYPE_PC_STR));
    		});
    	}
    	LOGGER.error("HomeConfig.clear cache.layoutList done.");
        List<HomeModuleConfig> layoutNewList = homeModuleConfigService.findListByHomeConfigIdAndModuleCode(moduleHomeConfigId, "layout-new");
        if(layoutNewList != null) {
            layoutNewList.stream().forEach(x -> {
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId(), HomeModuleConfig.CLIENT_TYPE_APP_STR));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId(), HomeModuleConfig.CLIENT_TYPE_PC_STR));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("more"), x.getId(), HomeModuleConfig.CLIENT_TYPE_APP_STR));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("more"), x.getId(), HomeModuleConfig.CLIENT_TYPE_PC_STR));
            });
        }
        LOGGER.error("HomeConfig.clear cache.layoutNewList done.");
        List<HomeModuleConfig> interactiveLearningList = homeModuleConfigService.findListByHomeConfigIdAndModuleCode(moduleHomeConfigId, "interactive-learning");
        if(interactiveLearningList != null) {
            interactiveLearningList.stream().forEach(x -> {
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() ,HomeModuleConfig.CLIENT_TYPE_APP_STR,HomeModuleConfig.INTERACTIVE_LEARNING_DATA_TYPE));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() , HomeModuleConfig.CLIENT_TYPE_PC_STR,HomeModuleConfig.INTERACTIVE_LEARNING_DATA_TYPE));
            });
        }
        LOGGER.error("HomeConfig.clear cache.interactiveLearningList done.");
        List<HomeModuleConfig> picksList = homeModuleConfigService.findListByHomeConfigIdAndModuleCode(moduleHomeConfigId, "picks");
        if(picksList != null) {
            picksList.stream().forEach(x -> {
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() ,HomeModuleConfig.CLIENT_TYPE_APP_STR,HomeModuleConfig.COURSE_DATA_TYPE));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() , HomeModuleConfig.CLIENT_TYPE_PC_STR,HomeModuleConfig.COURSE_DATA_TYPE));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() ,HomeModuleConfig.CLIENT_TYPE_APP_STR,HomeModuleConfig.PROJECT_DATA_TYPE));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() , HomeModuleConfig.CLIENT_TYPE_PC_STR,HomeModuleConfig.PROJECT_DATA_TYPE));
            });
        }
        LOGGER.error("HomeConfig.clear cache.picksList done.");
        List<HomeModuleConfig> hotAppList = homeModuleConfigService.findListByHomeConfigIdAndModuleCode(moduleHomeConfigId, "hot-app");
        if(hotAppList != null) {
            hotAppList.stream().forEach(x -> {
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() ,HomeModuleConfig.CLIENT_TYPE_APP_STR,HomeModuleConfig.COURSE_DATA_TYPE));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() , HomeModuleConfig.CLIENT_TYPE_PC_STR,HomeModuleConfig.COURSE_DATA_TYPE));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() ,HomeModuleConfig.CLIENT_TYPE_APP_STR,HomeModuleConfig.PROJECT_DATA_TYPE));
                cachedResultInterceptor.getCache().clear(generateKey(HomeContentController.CACHE_GROUP_KEY, Optional.of("content"), x.getId() , HomeModuleConfig.CLIENT_TYPE_PC_STR,HomeModuleConfig.PROJECT_DATA_TYPE));
            });
        }
        LOGGER.error("HomeConfig.clear cache.hotAppList done.");
        cachedResultInterceptor.getCache().clear(generateKey(HomeFooterController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId));
        LOGGER.error("HomeConfig.clear cache.HomeFooter done.");
        cachedResultInterceptor.getCache().clear(generateKey(HomeModuleController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_PC_STR, HomeModuleConfig.IS_LOGIN_TRUE));
        cachedResultInterceptor.getCache().clear(generateKey(HomeModuleController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_PC_STR, HomeModuleConfig.IS_LOGIN_FALSE));
        cachedResultInterceptor.getCache().clear(generateKey(HomeModuleController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_PC_STR, null));
        cachedResultInterceptor.getCache().clear(generateKey(HomeModuleController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_APP_STR, HomeModuleConfig.IS_LOGIN_TRUE));
        cachedResultInterceptor.getCache().clear(generateKey(HomeModuleController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_APP_STR, HomeModuleConfig.IS_LOGIN_FALSE));
        cachedResultInterceptor.getCache().clear(generateKey(HomeModuleController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_APP_STR, null));
        LOGGER.error("HomeConfig.clear cache.HomeModule done.");
        cachedResultInterceptor.getCache().clear(generateKey(HomeNavController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId));
        cachedResultInterceptor.getCache().clear(generateKey(HomeNavController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_PC_STR));
        cachedResultInterceptor.getCache().clear(generateKey(HomeNavController.CACHE_GROUP_KEY, Optional.empty(), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_APP_STR));
        LOGGER.error("HomeConfig.clear cache.HomeNav done.");
        Optional<HomeModuleConfig> personPanel = homeModuleConfigService.findByHomeConfigIdAndModuleCode(moduleHomeConfigId, "person-panel");
        personPanel.ifPresent(x -> {
        	cachedResultInterceptor.getCache().clear(generateKey(HomePersonPanelController.CACHE_GROUP_KEY, Optional.empty(), x.getId()));
        });
        LOGGER.error("HomeConfig.clear cache.HomePersonPanel done.");
        //cachedResultInterceptor.getCache().clearByPattern(HomeNewsController.CACHE_GROUP_KEY);
        LOGGER.error("HomeConfig.clear cache.HomeNew done.");
    }

	private String generateKey(String cacheGroupKey, Optional<String> key, String...params){
        StringBuilder sbKey = new StringBuilder(key.orElse("result-"));
        Arrays.asList(params).stream().forEach(r -> sbKey.append(r));
    	return cacheGroupKey + "#" + DigestUtils.md5DigestAsHex(sbKey.toString().getBytes());
    }


    /**
     * 查询虚拟空间
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-virtual-space-list",method = RequestMethod.GET)
    @Param(name = "uri",required = true)//菜单路由
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @Param(name = "createTimeStart", type = String.class)
    @Param(name = "createTimeEnd", type = String.class)
    @Param(name = "name", type = String.class)//创建时间
    @Param(name = "id", type = String.class)//创建时间
    @Param(name = "organizationId", type = String.class)//组织部门
    @JSON("*")
    @JSON("items.(id,name,organizationName,createTime,state,organizationId)")
    @Permitted(perms ="system/home-config-virtual")
    public PagedResult<HomeConfig> findVirtualSpace(RequestContext requestContext, Subject<Member> subject) {

        List<Organization> orgList  =  organizationService.findOrgListVirtualSpace(
                subject.getCurrentUserId(),
                requestContext.get("uri", String.class),
                Optional.of(true),
                Optional.of(Organization.LEVEL_DEPARTMENT),
                requestContext.getOptional("asParent", Boolean.class),
                Optional.empty(),
                Optional.empty(),
                Optional.of(Organization.TYPE_ALL),
                Optional.empty(),
                Optional.of(false)
        );
        if (ObjectUtils.isEmpty(orgList)){
            return  PagedResult.create(0,new ArrayList<>());
        }

        List<String> ids = Lists.newArrayList();

        orgList.forEach(r->ids.add(r.getId()));

        return homeConfigService.findVirtualSpace(
                requestContext.getOptionalInteger("page").orElse(1),
                requestContext.getOptionalInteger("pageSize").orElse(10),
                subject.getCurrentUserId(),
                DateUtil.dateString2OptionalLong(requestContext.getOptional("createTimeStart", String.class)),
                DateUtil.dateString2OptionalLongEndOfDay(requestContext.getOptional("createTimeEnd", String.class)),
                requestContext.getOptionalString("name"),
                ids,
                requestContext.getOptionalString("organizationId"));
    }


    /**
     * 查询虚拟空间详情
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/find-virtual-space-detail",method = RequestMethod.GET)
    @Param(name = "id",required = true)//id
    @JSON("id,name,organizationName,description,organizationId")
    @Permitted(perms ="system/home-config-virtual")
    public HomeConfig getById(RequestContext requestContext){
       return homeConfigService.get(requestContext.getString("id"));
    }


    /**
     * 添加虚拟空间
     * @param requestContext
     * @return
     */
    @RequestMapping(method = RequestMethod.POST,value = "/save-virtual-space")
    @Param(name = "logo")
    @Param(name = "logoMini")
    @Param(name = "logoPath")
    @Param(name = "logoMiniPath")
    @Param(name = "name", required = true)
    @Param(name = "organizationId", required = true)
    @Param(name = "description")
    @Param(name = "navJson", required = true)
    @Param(name = "footerContent", required = true)
    @Param(name = "homeModuleConfigJson", required = false)
    @Param(name = "version", required = false)
    @Param(name = "pcCanvasPageConfigIds", value = "pc画布容器页面id(知识、课程等列表页面)")
    @Param(name = "appCanvasPageConfigIds", value = "app画布容器页面id(知识、课程等列表页面)")
    @Param(name = "homePageJson", value = "子首页容器页面json，包含id、type、extraParam")
    @Param(name = "enableHomeBrowse", required = true)
    @Param(name = "platform", required = true)
    @Param(name = "jumpHomePage", value = "是否开启中移网大和移动党校首页跳转 0 开启 1不开启",type = Integer.class)
    @JSON("id,name,organizationId")
    @Permitted(perms ="system/home-config-virtual")
    public HomeConfig saveVirtualSpace(RequestContext requestContext) {
        String organizationId = requestContext.getString("organizationId");
        haveOrDonTHave(organizationId,Optional.empty());
        List<HomeNav> homeNavs = com.alibaba.fastjson.JSON.parseArray(requestContext.getString("navJson"), HomeNav.class);
        String homeModuleConfigJson = requestContext.getOptionalString("homeModuleConfigJson").orElse(null);
        List<HomeModuleConfig> homeModuleConfigs = null;
        if (!ObjectUtils.isEmpty(homeModuleConfigJson)) {
            homeModuleConfigs = com.alibaba.fastjson.JSON.parseArray(homeModuleConfigJson, HomeModuleConfig.class);
        }

        HomeConfig config = homeConfigService.insertVirtualSpace(
                requestContext.getOptionalString("logo"),
                requestContext.getOptionalString("logoPath"),
                requestContext.getOptionalString("logoMini"),
                requestContext.getOptionalString("logoMiniPath"),
                requestContext.getOptionalString("description"),
                requestContext.getString("name"),
                organizationId,
                requestContext.getOptionalString("version").orElse(HomeConfig.VERSION_OLD),
                homeModuleConfigs,
                requestContext.getOptionalString("pcCanvasPageConfigIds"),
                requestContext.getOptionalString("appCanvasPageConfigIds"),
                requestContext.getOptionalString("homePageJson"),
                requestContext.getInteger("enableHomeBrowse"),
                requestContext.getInteger("platform"),
                requestContext.getOptionalInteger("jumpHomePage")
                );
        homeNavService.save(config.getId(), homeNavs);
        homeFooterService.insert(config.getId(), requestContext.getString("footerContent"));
        return config;
    }


    /**
     * 修改虚拟空间
     * @return
     */
    @RequestMapping(value = "/update-virtual-space/{id}" ,method = RequestMethod.PUT)
    @Param(name = "name" , required = true)
    @Param(name = "description")
    @Param(name = "organizationId" , required = true)
    @Param(name = "id" , required = true)
    @Param(name = "logo")
    @Param(name = "logoMini")
    @Param(name = "logoPath")
    @Param(name = "logoMiniPath")
    @Param(name = "version", required = false)
    @Param(name = "pcCanvasPageConfigIds", value = "pc画布容器页面id(知识、课程等列表页面)")
    @Param(name = "appCanvasPageConfigIds", value = "app画布容器页面id(知识、课程等列表页面)")
    @Param(name = "homePageJson", value = "子首页容器页面json，包含id、type、extraParam")
    @Param(name = "pcJson")
    @Param(name = "enableHomeBrowse", required = true)
    @Param(name = "platform", required = true)
    @Param(name = "jumpHomePage", value = "是否开启中移网大和移动党校首页跳转 0 开启 1不开启",type = Integer.class)
    @Permitted(perms ="system/home-config-virtual")
    @JSON("id,name,description,organizationId")
    public HomeConfig updateVirtualSpace(RequestContext requestContext){
        Optional<String> organizationId = requestContext.getOptionalString("organizationId");
        String id = requestContext.getString("id");
        organizationId.ifPresent(s -> haveOrDonTHave(s, Optional.of(id)));
        HomeConfig homeConfig = homeConfigService.updateVirtualSpace(
                requestContext.getOptionalString("name"),
                requestContext.getOptionalString("description"),
                organizationId,
                requestContext.getString("id"),
                requestContext.getOptionalString("logo"),
                requestContext.getOptionalString("logoMini"),
                requestContext.getOptionalString("logoPath"),
                requestContext.getOptionalString("logoMiniPath"),
                requestContext.getOptionalString("version"),
                requestContext.getOptionalString("pcJson"),
                requestContext.getInteger("enableHomeBrowse"),
                requestContext.getInteger("platform"),
                requestContext.getOptionalInteger("jumpHomePage")
        );
        homeConfigService.handHomePageRelation(requestContext.getOptionalString("pcCanvasPageConfigIds"), requestContext.getOptionalString("appCanvasPageConfigIds"), id);
        homeConfigService.handHomePageRelation(requestContext.getOptionalString("homePageJson"), id);
        return  homeConfig;
    }

    /**
     * //组织校验
     * @param orgId  组织id
     */
    private void haveOrDonTHave(String orgId , Optional<String> id){
        int count = homeConfigService.getVirtualSpaceByOrganizationId(orgId,id);
        if (0 != count){
            throw  new UnprocessableException(com.zxy.product.system.content.ErrorCode.RepeatAdd);
        }

        Optional<Organization> organization = organizationService.get(orgId);

        //判断是否是3,4级组织
        com.zxy.product.system.content.ErrorCode.OrganizationsAannotVirtualSpace.throwIf(organization.isPresent() && !(organization.get().getDepth().equals(Organization.LEVEL_BRANCH) || organization.get().getDepth().equals(Organization.LEVEL_DEPARTMENT)));
    }


    /**
     * 删除虚拟空间
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/del-virtual-space/{id}", method = RequestMethod.DELETE)
    @Param(name = "id", required = true)
    @Permitted(perms ="system/home-config-virtual")
    @JSON("id")
    public ImmutableMap<String,String> delVirtualSpace(RequestContext requestContext) {
        String id = homeConfigService.delete(requestContext.getString("id"));

        return ImmutableMap.of("id",id);
    }


    /**
     * 修改虚拟空间状态
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/update-virtual-state/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "state", required = true)
    @Permitted(perms ="system/home-config-virtual")
    @JSON("id")
    public ImmutableMap<String,String> updateVirtualSpaceState(RequestContext requestContext) {
        String id = homeConfigService.updateVirtualSpaceState(
                requestContext.getString("id"),
                requestContext.getInteger("state")
        );
       return ImmutableMap.of("id",id);
    }


    @RequestMapping(value = "/has-config-virtual-ids", method = RequestMethod.GET)
    @JSON("*")
    @Permitted(perms ="system/home-config-virtual")
    public List<String> hasConfigVirtualIds(){
        return homeConfigService.hasConfigVirtualIds();
    }

    /**
     * 虚拟空间添加配置列表(过滤已添加的)
     */
    @RequestMapping(value = "/find-add-list", method = RequestMethod.GET)
    @Param(name= "name")
    @Param(name = "page", required = true, type = Integer.class)
    @Param(name = "pageSize", required = true, type = Integer.class)
    @JSON("items.(id, name, organizationName)")
    @JSON("recordCount")
    @Permitted(perms = {"system/home-config-virtual","system/virtual-space-grouping"})
    public PagedResult<HomeConfig> findAddList(RequestContext requestContext){
        Integer page = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        Optional<String> optionalName = requestContext.getOptionalString("name");
        return homeConfigService.findAddList(page, pageSize, optionalName);
    }

    @RequestMapping(value = "/clearMongoCache", method = RequestMethod.DELETE)
    @Param(name = "homeConfigId", type = String.class, required = true)
    @JSON("count")
    public ImmutableMap<String,Integer> clearMongoCache(RequestContext requestContext, Subject<Member> subject, @RequestHeader String authorization){
        int count = homeConfigService.clearMongoCache(subject.getCurrentUserId(),
                authorization ,requestContext.getString("homeConfigId"));
        return ImmutableMap.of("count",count);
    }
}
