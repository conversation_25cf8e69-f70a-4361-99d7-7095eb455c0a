package com.zxy.product.system.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.system.api.homeconfig.HomeContentService;
import com.zxy.product.system.entity.HomeContent;
import com.zxy.product.system.entity.HomeModuleConfig;
import com.zxy.product.system.entity.Member;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/home-content-cfg")
public class HomeContentCfgController {


    private HomeContentService homeContentService;

    private Cache cache;

    public  static final String CACHE_GROUP_KEY = "home-content";
    public static final String CACHE_RESULT_PREFIX = "jsonResultValue";
    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create(CACHE_RESULT_PREFIX);
    }

    @Autowired
    public void setHomeContentService(HomeContentService homeContentService) {
        this.homeContentService = homeContentService;
    }

    @RequestMapping(value = "/pages", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "moduleHomeConfigId", required = true)
    @Param(name = "clientType", type = Integer.class, required = true)
    @JSON("*")
    @JSON("items.(*)")
    public PagedResult<HomeContent> findPages(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
    	return homeContentService.findPagesFromCache(subject.getCurrentUserId(), authorization
    			, context.getInteger("page"), context.getInteger("pageSize")
    			, context.getString("moduleHomeConfigId"), context.getInteger("clientType"));
    }

    @RequestMapping(value = "/insert-batch", method = RequestMethod.POST)
    @Permitted
    @Param(name = "moduleHomeConfigId", required = true)
    @Param(name = "contentJson", required = true)
    @JSON("*")
    public List<HomeContent> insertList(RequestContext context
    		, Subject<Member> subject, @RequestHeader String authorization) {
        List<HomeContent> homeContents = com.alibaba.fastjson.JSON.parseArray(context.getString("contentJson"), HomeContent.class);
        clearCacheByModuleHomeConfigId(context.getString("moduleHomeConfigId"));
        return homeContentService.insertBatchToCache(subject.getCurrentUserId()
        		, authorization, context.getString("moduleHomeConfigId"), homeContents);
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("*")
    public Map<String, String> deleteContent(RequestContext context
    		, Subject<Member> subject, @RequestHeader String authorization) {
        String id = homeContentService.deleteFromCache(subject.getCurrentUserId()
    			, authorization, context.getString("id"));
        return ImmutableMap.of("id", id);
    }


    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "moduleHomeConfigId", required = true)
    @Param(name = "sort", type = Integer.class, required = true)
    @Param(name = "clientType", type = Integer.class, required = true)
    @Permitted
    @JSON("*")
    public Map<String, String> updateSort(RequestContext context
    		, Subject<Member> subject, @RequestHeader String authorization) {
        String id = homeContentService.updateSortByIdToCache(subject.getCurrentUserId()
        		, authorization, context.getString("id"), context.getInteger("sort"), context.getInteger("clientType"),context.getString("moduleHomeConfigId"));
        return ImmutableMap.of("id", id);
    }

    @Permitted
    @RequestMapping(value = "/image/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "imageId", required = true)
    @Param(name = "imageIdPath", required = true)
    @Param(name = "clientType", type = Integer.class, required = true)
    @JSON("*")
    public Map<String, String> updateImage(RequestContext context
    		, Subject<Member> subject, @RequestHeader String authorization) {
        String id = homeContentService.updateImageToCache(subject.getCurrentUserId()
        		, authorization, context.getString("id"), context.getString("imageId")
        		, context.getString("imageIdPath"), context.getInteger("clientType"));
        return ImmutableMap.of("id", id);
    }

    @Permitted
    @RequestMapping(value = "/basic-info/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "dataExt")
    @Param(name = "dataName")
    @Param(name = "url")
    @JSON("*")
    public Map<String, String> updateBasicInfo(RequestContext context
    		, Subject<Member> subject, @RequestHeader String authorization) {
        String id = homeContentService.updateBasicInfoToCache(subject.getCurrentUserId()
        		, authorization, context.getString("id"),
        		context.getOptionalString("dataExt"),
        		context.getOptionalString("dataName"),
        		context.getOptionalString("url"));
        return ImmutableMap.of("id", id);
    }
    
    
    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "moduleHomeConfigId", required = true)
    @Param(name = "size", type = Integer.class, required = true)
    @Param(name = "clientType", type = Integer.class, required = true)
    @Param(name = "dataType", type = Integer.class, required = false)
    @JSON("*")
    public List<HomeContent> findList(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        return homeContentService.findListFromCache(subject.getCurrentUserId()
        		, authorization, context.getString("moduleHomeConfigId"),
                context.getInteger("size"), context.getInteger("clientType"), context.getOptionalInteger("dataType"));
    }

    /**
     * 保存前校验,图片类型强制配置图片
     * @param context
     * @param subject
     * @param authorization
     * @return
     */
    @RequestMapping(value = "/save-check",method = RequestMethod.GET)
    @Param(name = "moduleHomeConfigId", required = true)
    @Param(name = "clientType", type = Integer.class, required = true)
    @JSON("*")
    public Map<String,Object> saveCheck(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        String inCompleteDataName = homeContentService.saveCheck(subject.getCurrentUserId()
                , authorization, context.getString("moduleHomeConfigId"),
                 context.getInteger("clientType"));
        return ImmutableMap.of("success", "".equals(inCompleteDataName), "dataName", inCompleteDataName);
    }

    public void clearCacheByModuleHomeConfigId(String moduleHomeConfigId){
        cache.clear(generateKey(CACHE_GROUP_KEY, Optional.of("content"), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_APP_STR));
        cache.clear(generateKey(CACHE_GROUP_KEY, Optional.of("content"), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_PC_STR));
        cache.clear(generateKey(CACHE_GROUP_KEY, Optional.of("more"), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_APP_STR));
        cache.clear(generateKey(CACHE_GROUP_KEY, Optional.of("more"), moduleHomeConfigId, HomeModuleConfig.CLIENT_TYPE_PC_STR));
    }

    private String generateKey(String cacheGroupKey, Optional<String> key, String...params){
        StringBuilder sbKey = new StringBuilder(key.orElse("result-"));
        Arrays.asList(params).stream().forEach(r -> sbKey.append(r));
        return cacheGroupKey + "#" + DigestUtils.md5DigestAsHex(sbKey.toString().getBytes());
    }
}
