package com.zxy.product.system.web.controller;

import com.alibaba.dubbo.common.URL;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.Validator;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.office.excel.support.DefaultReader;
import com.zxy.common.office.excel.support.validator.RequiredValidator;
import com.zxy.common.office.excel.support.validator.TrueValidator;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.annotation.Params;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.audit.Audit.Action;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.entity.CertificateRecord;
import com.zxy.product.course.entity.OfflineCourseQuestionnaireChapter;
import com.zxy.product.system.content.ErrorCode;
import com.zxy.product.system.entity.RowError;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.entity.Attachment;
import com.zxy.product.system.api.permission.OrganizationService;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.entity.Grant;
import com.zxy.product.system.entity.Member;
import com.zxy.product.system.entity.Organization;
import com.zxy.product.system.web.audit.ImAudit;
import com.zxy.product.system.web.util.BrowserUtil;
import com.zxy.product.system.web.util.DateUtil;
import com.zxy.product.system.web.util.ImportExportUtil;
import com.zxy.product.system.web.util.UploadUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.ValidationException;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @user tianjun
 * @date 16/6/15
 */
@Controller
@RequestMapping("/grant")
public class GrantController {

    private GrantService grantService;

    private Cache cache;

    private static final String PAAS_OPERATOR_TYPE_KEY = "paas-operator-type-key";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private MemberService memberService;
    private OrganizationService organizationService;
    private FileService fileService;
    private AttachmentResolver attachmentResolver;
    private final static Logger LOGGER = LoggerFactory.getLogger(RoleController.class);


    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create(PAAS_OPERATOR_TYPE_KEY);
    }

    @Autowired
    public void setGrantService(GrantService grantService) {
        this.grantService = grantService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
        this.attachmentResolver = attachmentResolver;
    }

    @RequestMapping(method= RequestMethod.GET)
    @Permitted(perms = {"system/grant"})
    @Param(name="page", type=Integer.class)
    @Param(name="pageSize", type=Integer.class)
    @Param(name = "organizationId")
    @Param(name = "roleName", type = String.class)
    @Param(name = "createTimeStart", type = String.class)
    @Param(name = "createTimeEnd", type = String.class)
    @Param(name = "operatorType", type = String.class)
    @Param(name = "type", type = Integer.class)
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】
    @JSON("recordCount")
    @JSON("items.(id, createTime, owned, memberSize, operatorTypes, organizationSize)")
    @JSON("items.role.(id,name,parentId,parentName,memberCount,desc,type,childFlag,order)")
    @JSON("items.organization.(id, name)")
    public PagedResult<Grant> findPage(RequestContext context, Subject<Member> subject){
        return grantService.find(
                context.getOptionalInteger("page").orElse(1),
                context.getOptionalInteger("pageSize").orElse(10),
                context.getOptionalString("organizationId"),
                context.getOptionalString("roleName"),
                dateString2OptionalLong(context.getOptionalString("createTimeStart")),
                dateString2OptionalLongEndOfDay(context.getOptionalString("createTimeEnd")),
                context.getOptionalString("operatorType"),
                subject.getCurrentUserId(),
                context.getOptionalInteger("contain").orElse(0),
                context.getOptionalInteger("type"), subject.getRootOrganizationId());
    }

    /**
     * 授权管理
     */
    @RequestMapping(value = "/find-member", method= RequestMethod.GET)
    @Permitted(perms = {"system/grant"})
    @Param(name="page", type=Integer.class)
    @Param(name="pageSize", type=Integer.class)
    @Param(name = "organizationId")
    @Param(name = "authorizeOrgId")
    @Param(name = "roleId", type = String.class)
    @Param(name = "roleName", type = String.class)
    @Param(name = "createTimeStart", type = String.class)
    @Param(name = "createTimeEnd", type = String.class)
    @Param(name = "operatorType", type = String.class)
    @Param(name = "type", type = Integer.class)
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】（人员归属）
    @Param(name = "contains",type=Integer.class) //是否包含子级【0：不包含；1：包含】(授权归属)
    @Param(name = "memberId", type = String.class)
    @Param(name = "grantId", type = String.class)
    @Param(name = "content", type = String.class, value = "查询姓名、员工编号、归属部门")
    @Param(name = "memberName", type = String.class, value = "查询员工编号")
    @Param(name = "fullName", type = String.class, value = "查询姓名")
    @JSON("recordCount")
    @JSON("items.(id, createTime, owned, memberSize, operatorTypes, organizationSize, validDate)")
    @JSON("items.role.(id,name,parentId,parentName,memberCount,desc,type,childFlag,order)")
    @JSON("items.organization.(id, name)")
    @JSON("items.member.(id, name, fullName)")
    @JSON("items.member.organization.(id, name, path)")
    public PagedResult<Grant> findMemberPage(RequestContext context, Subject<Member> subject){
        return grantService.findMemberPage(
                context.getOptionalInteger("page").orElse(1),
                context.getOptionalInteger("pageSize").orElse(10),
                context.getOptionalString("organizationId"),
                context.getOptionalString("roleName"),
                dateString2OptionalLong(context.getOptionalString("createTimeStart")),
                dateString2OptionalLongEndOfDay(context.getOptionalString("createTimeEnd")),
                context.getOptionalString("operatorType"),
                subject.getCurrentUserId(),
                context.getOptionalInteger("contain").orElse(0),
                context.getOptionalInteger("type"),
                context.getOptionalString("roleId"),
                context.getOptionalString("memberId"),
                context.getOptionalString("content"),
                context.getOptionalString("grantId"),
                context.getOptionalString("authorizeOrgId"),
                context.getOptionalInteger("contains").orElse(0),
                context.getOptionalString("memberName"),
                context.getOptionalString("fullName")
        );
    }

    /**
     * 授权管理删除
     */
    @RequestMapping(value = "/find-member/{grantId}", method= RequestMethod.DELETE)
    @Permitted(perms = {"system/grant"})
    @Param(name = "grantId", type = String.class, required = true)
    @Param(name = "memberId", type = String.class, required = true)
    @JSON("result")
    public Map<String, Boolean> deleteGrantMemberByGrantId(RequestContext context, Subject<Member> subject){
        return ImmutableMap.of("result", grantService.deleteGrantMemberByGrantId(context.getString("grantId"), context.getString("memberId"), subject.getCurrentUser().getId(), subject.getCurrentUser().getFullName()));
    }

    @RequestMapping(value = "/{grantId}",method= RequestMethod.GET)
    @Permitted(perms = {"system/grant"})
    @Param(name = "grantId",required = true)
    @Param(name = "memberId")
    @JSON("id,createTime,memberSize,operatorTypes,organizationSize,validType,validDate,notifyFlag,notifyType")
    @JSON("members.(id,name,fullName)")
    @JSON("organizations.(id, name, parentId ,childFind)")
    @JSON("role.(id,name)")
    @JSON("role.organization.(id,name)")
    @JSON("organization.(id,name)")
    public Grant get(RequestContext context){
        String grantId = context.get("grantId",String.class);
        return grantService.getOptional(grantId, context.getOptionalString("memberId")).orElse(null);
    }

    /** 新增授权 */
    @RequestMapping(method=RequestMethod.POST)
    @Permitted(perms = {"system/grant"})
    @Param(name = "organizationId",required = true)
    @Param(name = "memberIds",required = true)
    @Param(name = "roleIds",required = true)
    @Param(name = "organizationIds",required = true)
    @Param(name = "chindFinds")
    @Param(name = "indeterminates")
    @Param(name = "operatorTypes",required = true)
    @Param(name = "memberFullName")
    @Param(name = "memberName", required = true)
    @Param(name = "roleName",required = true)
    @Param(name = "childFlag", type = Integer.class)
    @Param(name = "validType", type = Integer.class)
    @Param(name = "validDate", type = Long.class)
    @Param(name = "notifyFlag", type = Integer.class)
    @Param(name = "notifyType")
    @JSON("id,nodeId,createTime")
    @Audit(module = "系统管理", subModule = "权限管理-授权管理", action = Audit.Action.INSERT, fisrtAction = "新增", desc = "对{0}操作添加授权", params = {"roleName"})
    @ImAudit(module = "系统管理", subModule = "权限管理-授权管理", action = ImAudit.Action.INSERT, fisrtAction = "新增", desc = "对{0}进行了{1}的添加授权", params = {"memberFullName","roleName"}, businessType = "memberName", businessValue = "cmuonline")
    @ImAudit(module = "系统管理", subModule = "权限管理-授权管理", action = ImAudit.Action.INSERT, fisrtAction = "新增", desc = "对{0}进行了{1}的添加授权", params = {"memberFullName","roleName"}, businessType = "memberName", businessValue = "E0003881924")
    public List<Grant> insert(RequestContext context, Subject<Member> subject){
    	return grantService.insert(
    			subject.getCurrentUserId(),
    			context.get("organizationId", String.class),
    			context.get("roleIds",String.class),
    			context.get("memberIds", String.class),
    			context.get("organizationIds", String.class),
    			context.getOptional("indeterminates", String.class),
    			context.getOptional("chindFinds", String.class),
    			context.get("operatorTypes", String.class),
    			context.getOptional("validType", Integer.class),
    			context.getOptional("validDate", Long.class),
    			context.getOptional("notifyFlag", Integer.class),
    			context.getOptional("notifyType", String.class)
    			);
    }

    /** 新增授权 */
    @RequestMapping(method=RequestMethod.POST, value = "/members")
    @Permitted(perms = {"system/grant"})
    @Param(name = "organizationId",required = true)
    @Param(name = "memberIds",required = true)
    @Param(name = "roleIds",required = true)
    @Param(name = "organizationIds",required = true)
    @Param(name = "chindFinds")
    @Param(name = "indeterminates")
    @Param(name = "operatorTypes",required = true)
    @Param(name = "memberName", required = true)
    @Param(name = "memberFullNames")
    @Param(name = "roleName",required = true)
    @Param(name = "validType", type = Integer.class)
    @Param(name = "validDate", type = Long.class)
    @Param(name = "notifyFlag", type = Integer.class)
    @Param(name = "notifyType")
    @JSON("id,nodeId,createTime")
    @Audit(module = "系统管理", subModule = "权限管理-授权管理", action = Audit.Action.INSERT, fisrtAction = "新增", desc = "对{0}操作添加授权", params = {"roleName"})
    @ImAudit(module = "系统管理", subModule = "权限管理-授权管理", action = ImAudit.Action.INSERT, fisrtAction = "新增", desc = "对{0}进行了{1}的授权添加", params = {"roleName","memberFullNames"},  businessType="memberName", businessValue="cmuonline")
    @ImAudit(module = "系统管理", subModule = "权限管理-授权管理", action = ImAudit.Action.INSERT, fisrtAction = "新增", desc = "对{0}进行了{1}的授权添加", params = {"roleName","memberFullNames"}, businessType="memberName", businessValue="E0003881924")
    public List<Grant> insertMembers(RequestContext context, Subject<Member> subject){
    	return grantService.insert(
    			subject.getCurrentUserId(),
    			context.get("organizationId", String.class),
    			context.get("roleIds",String.class),
    			context.get("memberIds", String.class),
    			context.get("organizationIds", String.class),
    			context.getOptional("indeterminates", String.class),
    			context.getOptional("chindFinds", String.class),
    			context.get("operatorTypes", String.class),
                context.getOptional("validType", Integer.class),
                context.getOptional("validDate", Long.class),
                context.getOptional("notifyFlag", Integer.class),
                context.getOptional("notifyType", String.class)
    			);
    }

    /** 修改授权 */
    @RequestMapping(value = "/{grantId}", method=RequestMethod.PUT)
    @Permitted(perms = {"system/grant"})
    @Param(name = "id",required = true)
    @Param(name = "organizationId",required = true)
    @Param(name = "memberIds",required = true)
    @Param(name = "roleIds",required = true)
    @Param(name = "organizationIds",required = true)
    @Param(name = "chindFinds")
    @Param(name = "indeterminates")
    @Param(name = "operatorTypes",required = true)
    @Param(name = "memberFullNames",required = true)
    @Param(name = "memberName",required = true)
    @Param(name = "roleName",required = true)
    @Param(name = "validType", type = Integer.class)
    @Param(name = "validDate", type = Long.class)
    @Param(name = "notifyFlag", type = Integer.class)
    @Param(name = "notifyType")
    @JSON("id,nodeId,createTime")
    @Audit(module = "系统管理", subModule = "权限管理-授权管理", action = Audit.Action.UPDATE, fisrtAction = "编辑", desc = "对{0}操作编辑授权", params = {"roleName"})
    @ImAudit(module = "系统管理", subModule = "权限管理-授权管理", action = ImAudit.Action.UPDATE, fisrtAction = "编辑", desc = "对{0}进行了{1}的授权更改",  params = {"roleName","memberFullNames"}, businessType="memberName", businessValue="cmuonline")
    @ImAudit(module = "系统管理", subModule = "权限管理-授权管理", action = ImAudit.Action.UPDATE, fisrtAction = "编辑", desc = "对{0}进行了{1}的授权更改",  params = {"roleName","memberFullNames"}, businessType="memberName", businessValue="E0003881924")
    public Grant update(RequestContext context, Subject<Member> subject){
        return grantService.update(
                subject.getCurrentUserId(),
                context.get("id", String.class),
                context.get("organizationId", String.class),
                context.get("roleIds", String.class),
                context.get("memberIds", String.class),
                context.get("organizationIds", String.class),
                context.getOptional("indeterminates", String.class),
                context.getOptional("chindFinds", String.class),
                context.get("operatorTypes", String.class),
                context.getOptional("validType", Integer.class),
                context.getOptional("validDate", Long.class),
                context.getOptional("notifyFlag", Integer.class),
                context.getOptional("notifyType", String.class),
                subject.getCurrentUser().getFullName()
        );
    }

    /**
     * 授权管理新增成员
     */
    @RequestMapping(value = "/add-member", method=RequestMethod.POST)
    @Permitted(perms = {"system/grant"})
    // @Param(name = "grantId",required = true)
    @Param(name = "organizationId",required = true)
    @Param(name = "memberIds",required = true)
    @Param(name = "roleIds",required = true)
    @Param(name = "organizationIds",required = true)
    @Param(name = "chindFinds")
    @Param(name = "indeterminates")
    @Param(name = "operatorTypes",required = true)
    @Param(name = "memberFullNames",required = true)
    @Param(name = "memberName",required = true)
    @Param(name = "roleName",required = true)
    @Param(name = "validType", type = Integer.class)
    @Param(name = "validDate", type = Long.class)
    @Param(name = "notifyFlag", type = Integer.class)
    @Param(name = "notifyType")
    @JSON("id,nodeId,createTime")
    @Audit(module = "系统管理", subModule = "权限管理-授权管理", action = Action.INSERT, fisrtAction = "新增", desc = "对{0}操作新增授权", params = {"roleName"})
    public Grant addMember(RequestContext context, Subject<Member> subject){
        return grantService.addMember(
                subject.getCurrentUserId(),
                null,
                context.get("organizationId", String.class),
                context.get("roleIds", String.class),
                context.get("memberIds", String.class),
                context.get("organizationIds", String.class),
                context.getOptional("indeterminates", String.class),
                context.getOptional("chindFinds", String.class),
                context.get("operatorTypes", String.class),
                context.getOptional("validType", Integer.class),
                context.getOptional("validDate", Long.class),
                context.getOptional("notifyFlag", Integer.class),
                context.getOptional("notifyType", String.class),
                subject.getCurrentUser().getFullName()
        );
    }

    @RequestMapping(value="/member-full-names", method=RequestMethod.GET)
    @Param(name = "grantId",required = true)
    @JSON("memberFullNames")
    public ImmutableMap<String, String> getMemberFullNames(RequestContext requestContext){
        return ImmutableMap.of("memberFullNames", grantService.findGrantMemberFullNames(requestContext.getString("grantId")));
    }

    /** 删除授权记录(关联明细也将全部删除) */
    @RequestMapping(value="/{grantId}", method=RequestMethod.DELETE)
    @Permitted(perms = {"system/grant"})
    @Param(name = "grantId",required = true)
    @Param(name = "roleName", type = String.class, required = true) // 用于记录审计日志
    @Param(name = "memberFullNames")
    @Param(name = "memberName")
    @JSON("count")
    @Audit(module = "系统管理", subModule = "权限管理-授权管理", action = Audit.Action.DELETE, fisrtAction = "删除", desc = "对{0}操作清除授权", params = {"roleName"})
    @ImAudit(module = "系统管理", subModule = "权限管理-授权管理", action = ImAudit.Action.UPDATE, fisrtAction = "删除", desc = "对{0}的授权人员{1}进行了授权删除",  params = {"roleName","memberFullNames"}, businessType="memberName", businessValue="cmuonline")
    @ImAudit(module = "系统管理", subModule = "权限管理-授权管理", action = ImAudit.Action.UPDATE, fisrtAction = "删除", desc = "对{0}的授权人员{1}进行了授权删除",  params = {"roleName","memberFullNames"}, businessType="memberName", businessValue="E0003881924")
    public Map<String,Object> delete(RequestContext context, Subject<Member> subject){
        return ImmutableMap.of("count", grantService.delete(context.get("grantId",String.class), subject.getCurrentUserId(), subject.getCurrentUser().getFullName()));
    }

    /** 查询被授权组织列表 */
    @RequestMapping(value = "/granted-organization", method = RequestMethod.GET)
    @Param(name = "name", type = String.class)
    @Param(name = "code", type = String.class)
    @Param(name = "level", type = Integer.class)
    @Param(name = "excludeId", type = String.class)
    @Param(name = "operatorType", type = String.class)
    @Param(name = "nodeId", type = String.class)
    @Param(name = "allStatus", type = Boolean.class)
    @Param(name = "uri", type = String.class)
    @JSON("id, parentId, name, level, order, status, createTime")
    public List<Organization> listOrganization(RequestContext requestContext, Subject<Member> subject, @RequestHeader String uri){
        return grantService.findGrantedOrganization(
                subject.getCurrentUserId(),
                requestContext.getOptionalString("uri").orElse(uri),
                requestContext.getOptional("level", Integer.class),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("code", String.class),
                requestContext.getOptional("excludeId", String.class),
                requestContext.getOptional("nodeId", String.class),
                requestContext.getOptional("operatorType", String.class),
                requestContext.getOptional("allStatus", Boolean.class));
    }
    /** 查询被授权组织列表 */
    @RequestMapping(value = "/simple-granted-organization", method = RequestMethod.GET)
    @Param(name = "name", type = String.class)
    @Param(name = "code", type = String.class)
    @Param(name = "uri", type = String.class)
    @Param(name = "supportMore", type = Boolean.class)
    @JSON("id, parentId, isParent, name, level, order, status, createTime")
    public List<Organization> simpleGrantedOrganization(RequestContext requestContext, Subject<Member> subject){
    	return grantService.findSimpleGrantedOrganization(
    			subject.getCurrentUserId(),
    			requestContext.getString("uri"),
    			requestContext.getOptional("name", String.class),
    			requestContext.getOptional("code", String.class),
    			requestContext.getOptional("supportMore", Boolean.class).orElse(false));
    }

    /** 查询被授权组织列表(分页) */
    @RequestMapping(value = "/granted-organization/page", method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "name", type = String.class)
    @Param(name = "level", type = Integer.class)
    @Param(name = "specLevel", type = Integer.class)
    @Param(name = "code", type = String.class)
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id, parentId, name, level, createTime, status, order, organizationName)")
    public PagedResult<Organization> pagedOrganization(RequestContext requestContext, Subject<Member> subject, @RequestHeader String uri){
        return grantService.findGrantedOrganization(
                requestContext.getOptional("page", Integer.class).orElse(1),
                requestContext.getOptional("pageSize", Integer.class).orElse(10),
                subject.getCurrentUserId(),
                uri,
                requestContext.getOptional("organizationId", String.class),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("level", Integer.class),
                requestContext.getOptionalInteger("specLevel"),
                requestContext.getOptionalString("code"));
    }

    /** 查询授权的授权人 */
    @RequestMapping(value = "/member", method = RequestMethod.GET)
    @Param(name = "grantId", type = String.class, required = true)
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id, name, fullName, headPortrait, companyId, self)") // headPortrait为借用字段,存下grantMemberId
    @JSON("items.organization.(id, name)")
	public PagedResult<Member> findGrantMembers(RequestContext requestContext, Subject<Member> subject) {
		return grantService.findGrantMembers(
				subject.getCurrentUserId(),
				requestContext.getOptionalInteger("page").orElse(1),
				requestContext.getOptionalInteger("pageSize").orElse(10),
				requestContext.getString("grantId"));
	}

    /** 删除授权人 */
    @RequestMapping(value="/member/{grantMemberId}", method = RequestMethod.DELETE)
    @Permitted(perms = {"system/grant"})
    @Param(name = "grantMemberId", required = true)
    @JSON("*")
    public int deleteGrantMember(RequestContext context){
    	return grantService.deleteGrantMember(context.getString("grantMemberId"));
    }

    @RequestMapping(value="/find-by-role", method=RequestMethod.GET)
    @Permitted
    @Param(name="roleId", type=String.class, required=true)
    @JSON("id, parentId, name, level, createTime")
    public List<Organization> findOrganizationByMemberAndRole(RequestContext context,Subject<Member> subject){
        return grantService.findOrganizationByMemberAndRole(subject.getCurrentUserId(), context.getString("roleId"));
    }

	@RequestMapping(value = "/find-operator-type", method = RequestMethod.GET)
    @Permitted
//	@Param(name = "roleId", type = String.class, required = true)
//	@Param(name = "seconds", type = String.class)
//	@Param(name = "organizationIds", type = String.class, required = true)
	@JSON("operatorType")
	public Map<String, String> findOperatorType() {
//		String roleId = context.get("roleId", String.class);
//		String seconds = context.getOptionalString("seconds").orElse(null);
//		String organizationIds = context.get("organizationIds", String.class);
//		final String splitCharacter = ",";
//		String operatorType = grantService.findOperatorType(subject.getCurrentUserId(),
//				roleId,
//				organizationIds.split(splitCharacter),
//				seconds == null ? new String[0] : seconds.split(splitCharacter));
		// 2019-4-28修改
		//权限操作类型， 根据管理员所具有所有角色uri判定，新增的角色权限继承管理员哪一个角色的权限的操作类型
//		String operatorType = grantService.getOperatorTypes(subject.getCurrentUserId(), roleId);
        // 权限体系重构，授权类型不再看之前管理员所拥有的类型，可直接全授予。
        String operatorType ="0,1,2,3,4";

		Map<String, String> result = new HashMap<>();
		result.put("operatorType", operatorType);
		return result;
	}

    @RequestMapping(value="/find-organization-operatortype", method=RequestMethod.GET)
    @Permitted
    @JSON("*")
    public Map<String,String> findOrganizationOperatorType(Subject<Member> subject, @RequestHeader String uri){
        return grantService.findOrganizationOperatorType(subject.getCurrentUserId(), uri);
    }

    /** 判断当前登陆用户,对于某一菜单,是否拥有最高结点的权限 */
    @RequestMapping(value = "/judge-owned-company-node", method = RequestMethod.GET)
    @Permitted
    @Param(name = "uri", type = String.class, required = true)
    @JSON("*")
    public boolean judegOwnedCompanyNode(RequestContext requestContext, Subject<Member> subject){
    	return grantService.judegOwnedCompanyNode(
    			subject.getCurrentUserId(),
    			requestContext.get("uri", String.class));
    }

    /** 比较用户memberId和当前登陆用户的权限,如果比当前用户小,返回true,否则返回false  */
    @RequestMapping(value = "/check-grants", method = RequestMethod.GET)
    @Permitted
    @Param(name = "memberId", type = String.class, required = true)
    @JSON("*")
    public boolean checkGrants(RequestContext requestContext, Subject<Member> subject){
    	return grantService.checkGrants(requestContext.getString("memberId"), subject.getCurrentUserId());
    }

    /**
     * 当新增一菜单时,哪些角色需要这个新增的菜单,开发人员需要先根据需要查询出来,
     * 然后执行该方法,补全角色菜单以及用户授权
     * memberId 执行该方法时,请传入一个登陆用户的id,以做简单的认证
     * menuIds 新增的菜单id,传入时以","分隔
     * roleIds 哪些角色需要加入这些新增的菜单
     */
    @RequestMapping(value = "/full-role-grant", method = RequestMethod.GET)
    @Param(name = "memberId", required = true)
    @Param(name = "menuIds", required = true)
    @JSON("*")
    public Map<String, Object> fullAdminRoleGrantWhenMenuAdd(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");
        if (!subject.getCurrentUserId().equals(memberId) || !memberId.equals("1")) {
            return ImmutableMap.of("status", false, "auth", false);
        }
        return grantService.fullAdminRoleGrantWhenMenuAdd(requestContext.getString("menuIds"));
    }

    private static Optional<Long> dateString2OptionalLong(Optional<String> value) {
        return value.map(t -> Date.from(LocalDate.parse(t).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime());
    }
    private static Optional<Long> dateString2OptionalLongEndOfDay(Optional<String> value) {
    	return value.map(t -> Date.from(LocalDate.parse(t).atStartOfDay().plusDays(1L).atZone(ZoneId.systemDefault()).toInstant()).getTime());
    }

    /** 查询被授权组织列表 */
    @RequestMapping(value = "/granted-organization-all", method = RequestMethod.GET)
    @Param(name = "name", type = String.class)
    @Param(name = "code", type = String.class)
    @Param(name = "level", type = Integer.class)
    @Param(name = "excludeId", type = String.class)
    @Param(name = "operatorType", type = String.class)
    @Param(name = "nodeId", type = String.class)
    @Param(name = "allStatus", type = Boolean.class)
    @Param(name = "uri", type = String.class)
    @JSON("id, parentId, name, level, code")
    public List<Organization> listOrganizationAll(RequestContext requestContext){
        return grantService.findGrantedOrganizationAll(
                "1",
                requestContext.getOptional("uri", String.class),
                requestContext.getOptional("level", Integer.class),
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("code", String.class),
                requestContext.getOptional("excludeId", String.class),
                requestContext.getOptional("nodeId", String.class),
                requestContext.getOptional("operatorType", String.class),
                requestContext.getOptional("allStatus", Boolean.class));
    }


    @RequestMapping(value = "/find-paas-operator-type", method = RequestMethod.GET)
    @Param(name = "menuId", required = true)
    @Permitted
    @JSON("*")
    public Map<String, String> findPaasOperatorType(RequestContext rc, Subject<Member> subject) {
        String menuId = rc.getString("menuId");
        String memberId = subject.getCurrentUserId();
        String organizationId = subject.getRootOrganizationId();
        String key = concatString(PAAS_OPERATOR_TYPE_KEY, "#", memberId, "#", menuId, "#", organizationId);
        return cache.get(key, () -> grantService.findPaasOperatorType(memberId, menuId, organizationId), 5 * 60);
    }

    /**
     * 修复授权数据为以人为维度
     */
    @RequestMapping(value = "/fix-grant", method = RequestMethod.PUT)
    @Params
    @JSON("*")
    public Map<String, Boolean> fixGrant() {
        return ImmutableMap.of("result", grantService.fixGrant());
    }


    private static String concatString(Object... args) {
        Joiner joiner = Joiner.on("").useForNull("null");
        return joiner.join(args);
    }

    /**
     * 导出授权管理信息
     */
    @RequestMapping(value = "/grant-export", method = RequestMethod.GET)
    @Permitted
    @Param(name = "organizationId")
    @Param(name = "authorizeOrgId")
    @Param(name = "roleId", type = String.class)
    @Param(name = "roleName", type = String.class)
    @Param(name = "createTimeStart", type = String.class)
    @Param(name = "createTimeEnd", type = String.class)
    @Param(name = "operatorType", type = String.class)
    @Param(name = "type", type = Integer.class)
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】（人员归属）
    @Param(name = "contains",type=Integer.class) //是否包含子级【0：不包含；1：包含】(授权归属)
    @Param(name = "memberId", type = String.class)
    @Param(name = "grantId", type = String.class)
    @Param(name = "content", type = String.class, value = "查询姓名、员工编号、归属部门")
    @Param(name = "memberName", type = String.class, value = "查询员工编号")
    @Param(name = "fullName", type = String.class, value = "查询姓名")
    @Audit(module = "系统管理", subModule = "权限管理-授权管理", action = Audit.Action.EXPORT, fisrtAction = "导出", desc = "导出{0}角色授权清单", params = {"roleName"})
    public void grantExport(RequestContext context, Subject<Member> subject) throws IOException {

        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition",  "attachment;filename="
                + parseFileName("角色授权清单", context.getRequest().getHeader("User-Agent")) + ".xlsx");

        PagedResult<Grant> memberPage = grantService.findMemberPage(
                1,
                Grant.TEMPLATE_DATA_LIMIT,
                context.getOptionalString("organizationId"),
                context.getOptionalString("roleName"),
                dateString2OptionalLong(context.getOptionalString("createTimeStart")),
                dateString2OptionalLongEndOfDay(context.getOptionalString("createTimeEnd")),
                context.getOptionalString("operatorType"),
                subject.getCurrentUserId(),
                context.getOptionalInteger("contain").orElse(0),
                context.getOptionalInteger("type"),
                context.getOptionalString("roleId"),
                context.getOptionalString("memberId"),
                context.getOptionalString("content"),
                context.getOptionalString("grantId"),
                context.getOptionalString("authorizeOrgId"),
                context.getOptionalInteger("contains").orElse(0),
                context.getOptionalString("memberName"),
                context.getOptionalString("fullName")
        );

        List<Grant> pageItems = memberPage.getItems();


        Writer writer = new ExcelWriter();

        writer.sheet("角色授权清单", pageItems)
                .indexColumn(Optional.of("序号"))
                .field("角色名称", w -> w.getRole().getName())
                .field("授予人员姓名", w -> w.getMember().getFullName())
                .field("员工编码", w -> {
                    // 获取原始员工编码
                    String code = w.getMember().getName();

                    // 处理空值：若编码为空，直接返回"-"或空字符串
                    if (code == null || code.trim().isEmpty()) {
                        return "-";
                    }

                    // 校验是否为18位纯数字（正则匹配）
                    if (code.matches("\\d{18}")) {
                        // 对第7-14位（索引6-13，共8位）进行脱敏，替换为"******"
                        return code.substring(0, 6) + "******" + code.substring(14);
                    } else {
                        // 非18位纯数字，直接返回原始编码
                        return code;
                    }
                })
                .field("员工归属部门", w -> w.getMember().getOrganization().getName())
                .field("授权类型", w -> Grant.convertPermissionTypes(w.getOperatorTypes()))
                .field("授权归属部门", w -> w.getOrganization().getName())
                .field("授权有效期", w -> {
                    Long timestamp = w.getValidDate();
                    return timestamp == null ? "-" :
                            LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
                                    .format(DATE_FORMATTER);
                });

        OutputStream os = null;
        String date = DateUtil.dateLongToString(System.currentTimeMillis(), DateUtil.YYYYMMDD);
        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(subject.getCurrentUserId());
        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
        try {
            os = response.getOutputStream();
            writer.write(os, wookBook -> {
                try {
                    ImportExportUtil.putWaterRemarkToExcel(wookBook, wookBook.getSheetAt(0), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
                } catch (IOException e) {
                    LOGGER.error("授权管理-角色授权-授权管理-导出出错", e);
                }
            });
        } catch (IOException e) {
            LOGGER.error("授权管理-角色授权-授权管理-导出出错", e);
        } finally {
            if (os != null) {
                os.close();
            }
            LOGGER.info("【授权管理-角色授权-授权管理-写入excel结束】：{}", System.currentTimeMillis());
        }
    }

    /** 下载角色授权导入模板 */
    @RequestMapping(value = "/export-download" , method = RequestMethod.GET)
    @Param()
    @JSON("*")
    public void exportPointAdjustment(RequestContext requestContext, Subject<Member> subject) throws IOException {
        HttpServletResponse response = BrowserUtil.fileDownloadResponse(requestContext, "角色授权导入.xlsx");
        Writer writer = new ExcelWriter();
        // 导入模板
        writer.sheet("角色授权导入", new ArrayList<>())
                .field("员工编码（必填）", null)
                .field("授权类型（必填，完全控制或可查看、可编辑、可删除、其他）", null)
                .field("授权节点组织编码（必填）", null)
                .field("是否包含子节点（必填，是/否）", null)
                .field("授权归属部门组织编码（必填）", null)
                .field("授权有效期（非必填，yyyy-mm-dd）", null);

        //机构编码
        List<Organization> organization = organizationService.findGrantedOrganization(
                subject.getCurrentUserId(),
                "system/grant",
                Optional.empty(),
                Optional.empty(),
                Optional.empty(),
                Optional.of("2"),
                Optional.of(true));

        writer.sheet("归属部门编码", organization)
                .indexColumn(Optional.of("序号"))
                .field("名称", Organization::getName)
                .field("编码", Organization::getCode);

        writer.write(response.getOutputStream());
    }

    /** 角色授权导入*/
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @Param(name = "fileId", required = true)
    @Param(name = "roleId", required = true)
    @JSON("successCount,failCount,data,errorFileId")
    @JSON("errors.(column,row)")
    @JSON("errors.code.(code)")
    @Permitted
    @Audit(module = "系统管理", subModule = "权限管理-授权管理", action = Audit.Action.IMPORT, fisrtAction = "导入", desc = "导入角色授权")
    public Map<String, Object> importData(RequestContext requestContext, Subject<Member> subject) {

        Optional<Attachment> attachment = fileService.get(requestContext.getString("fileId"));
        return attachment.map(t -> {
            try {
                // 1. 解析Excel文件
                Reader reader = new DefaultReader()
                        .skipRows(1)
                        // 员工编码（必填）
                        .setColumn(0, String.class, new RequiredValidator<>())
                        // 授权类型（必填）
                        .setColumn(1, String.class, new RequiredValidator<>())
                        // 授权节点组织编码（必填）
                        .setColumn(2, String.class, new RequiredValidator<>())
                        // 是否包含子节点（必填）
                        .setColumn(3, String.class, new RequiredValidator<>())
                        // 授权归属部门（必填）
                        .setColumn(4, String.class, new RequiredValidator<>())
                        // 授权有效期（非必填，校验格式）
                        .setColumn(5, String.class, new TrueValidator<>());

                // 2. 读取Excel数据
                com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
                fastDFSAttachment.setPath(t.getPath());
                InputStream inputStream = attachmentResolver.resolveToRead(fastDFSAttachment);
                Map<String, Object> resultMap = new HashMap<>();
                Reader.Result result = reader.read(inputStream);

                // 判断是否超出每次允许导入的最大数量:暂定5000
                if ((result.getCorrectRows().size() + result.getErrorRows().size()) >= Grant.TEMPLATE_DATA_LIMIT) {
                    throw new UnprocessableException(ErrorCode.OrganizationImportExceedMax);
                }

                if (!result.isCellMatched()) { // 那么就是模板不匹配
                    throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
                }

                if (result.getCorrectRows().size() == 0 && result.getErrorRows().size() == 0){
                    throw new UnprocessableException(ErrorCode.ImportNullFile);
                }

                List<Grant> tempLists = readExcelResult(result);

                // 3. 准备校验所需数据
                String roleId = requestContext.getString("roleId");
                String currentUserId = subject.getCurrentUserId();

                // 3.1 员工编码→用户映射
                List<com.zxy.product.human.entity.Member> memberList = memberService.findByNames(tempLists.stream().map(Grant::getMemberName).collect(Collectors.toList()));
                Map<String, com.zxy.product.human.entity.Member> memberMap = memberList.stream()
                        .filter(member -> member.getName() != null) // 过滤姓名为空的无效数据
                        .collect(Collectors.toMap(com.zxy.product.human.entity.Member::getName, member -> member));

                // 3.2 查询用户被授权组织
                List<Organization> organization = organizationService.findGrantedOrganization(
                        currentUserId,
                        "system/grant",
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.of("2"),
                        Optional.of(true)
                );
                Set<String> authorizedOrgCodes = organization.stream().map(Organization::getCode).collect(Collectors.toSet());
                Set<String> authorizedOrgIds = organization.stream().map(Organization::getId).collect(Collectors.toSet());
                Map<String, Organization> authorizedOrMap = organization.stream().collect(Collectors.toMap(Organization::getCode, org -> org));


                // 3.3 查询角色授权列表
                PagedResult<Grant> memberPage = grantService.findMemberPage(
                        1,
                        500,
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        currentUserId,
                        0,
                        Optional.empty(),
                        Optional.ofNullable(roleId),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        Optional.empty(),
                        0,
                        Optional.empty(),
                        Optional.empty()
                );
                List<Member> members = memberPage.getItems().stream().map(Grant::getMember).collect(Collectors.toList());
                Set<String> existingGrantKeys = members.stream().map(Member::getId).collect(Collectors.toSet());

                // 格式错误数据
                List<Grant> errorList = getResultErrors(result);
                List<Grant> errorGrants;

                // 4. 业务数据校验
                List<Grant> correctInsertList = new ArrayList<>();
                List<Grant> businessErrorList = new ArrayList<>();

                tempLists.forEach(grant -> validateBusinessData(
                        grant, memberMap, authorizedOrgCodes, existingGrantKeys, correctInsertList, businessErrorList, authorizedOrMap, authorizedOrgIds));

                errorList.addAll(businessErrorList); // 合并所有错误数据

                // 5.调用现有批量新增接口
                if (!correctInsertList.isEmpty()) {
                    for (Grant group : correctInsertList) {
                        grantService.insert(
                                currentUserId,
                                group.getOrganizationCode(), // 归属部门
                                roleId,
                                group.getMemberId(),
                                group.getOrganizationCodes(),
                                Optional.empty(),
                                "是".equals(group.getChildFlag()) ? Optional.ofNullable(group.getOrganizationCodes()) : Optional.empty(),
                                group.getOperatorTypes(),
                                group.getValid() == null ? Optional.of(6) : Optional.of(5), // 有效期类型
                                Optional.ofNullable(convertToTimestamp(group.getValid())), // 有效期时间戳
                                Optional.of(1), // 通知标识
                                Optional.empty()
                        );
                    }
                }

                // 6. 生成错误文件并返回结果
                resultMap.put("successCount", correctInsertList.size());
                resultMap.put("failCount", errorList.size());
                errorGrants = CollectionUtils.isNotEmpty(errorList)?errorList.stream().sorted(Comparator.comparing(Grant::getRow)).collect(Collectors.toList()):errorList;
                // 失败记录下载文件id
                resultMap.put("errorFileId", createErrorTempFile(errorGrants));
                resultMap.put("errors", createErrorList(errorGrants)); // 错误信息：行-列-错误编码

                return resultMap;

            } catch (Exception e) {
                throw new UnprocessableException(ErrorCode.DATE_ERROR, e.getMessage());
            }
        }).orElseThrow(() -> new UnprocessableException(ErrorCode.DATE_TWO_ERROR));
    }

    private Long convertToTimestamp(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null; // 空值表示永久授权
        }

        try {
            // 解析日期字符串为LocalDate
            LocalDate date = LocalDate.parse(dateStr.trim(), DateTimeFormatter.ISO_LOCAL_DATE);
            // 转换为时间戳（毫秒），默认取当天的0点0分0秒
            return date.atStartOfDay(ZoneId.systemDefault())
                    .toInstant()
                    .toEpochMilli();
        } catch (Exception e) {
            // 日期格式错误时返回null（后续可在校验中处理）
            return null;
        }
    }

    private String parseFileName(String fileName, String agent){
        if (BrowserUtil.isMSBrowser(agent)) {
            return URL.encode(fileName);
        }
        return new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
    }

    // 获取输入流
    private InputStream getInputStream(Attachment attachment) throws IOException {
        com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
        fastDFSAttachment.setPath(attachment.getPath());
        return attachmentResolver.resolveToRead(fastDFSAttachment);
    }

    /**
     * 读取Excel数据并转换为Grant实体列表
     */
    private List<Grant> readExcelResult(Reader.Result result) {
        List<Grant> grantLists = result.getCorrectRows().stream().skip(0).map(row -> {
            Grant grant = new Grant();
            grant.forInsert();
            grant.setRow(row.getIndex()); // 记录行号，用于错误定位
            // 员工编码（第0列）
            grant.setMemberName(row.get(0, String.class) != null ? row.get(0, String.class).trim() : null);
            // 授权类型（第1列）
            grant.setOperatorTypes(row.get(1, String.class) != null ? row.get(1, String.class).trim() : null);
            // 授权节点组织编码（第2列）
            grant.setOrganizationCodes(row.get(2, String.class) != null ? row.get(2, String.class).trim() : null);
            // 是否包含子节点（第3列）
            grant.setChildFlag(row.get(3, String.class) != null ? row.get(3, String.class).trim() : null);
            // 授权归属部门组织编码（第4列）
            grant.setOrganizationCode(row.get(4, String.class) != null ? row.get(4, String.class).trim() : null);
            // 授权有效期（第5列）
            grant.setValid(row.get(5, String.class) != null ? row.get(5, String.class).trim() : null);
            return grant;
        }).collect(Collectors.toList());

        // 保持导入顺序（时间戳倒序）
        Long currentTime = System.currentTimeMillis();
        for (Grant grant : grantLists) {
            grant.setCreateTime(currentTime--);
        }
        return grantLists;
    }

    /**
     * 收集Reader校验阶段的错误数据（如必填项缺失、格式错误）
     */
    private List<Grant> getResultErrors(Reader.Result result) {
        List<Grant> list = new ArrayList<>();
        List<Reader.Row> errorRows = result.getErrorRows();
//                .stream()
//                .filter(row -> row.getIndex() > 0) // 只处理数据行（跳过表头行）
//                .collect(Collectors.toList());
        List<Validator.DataError> errors = result.getErrors();
        Map<Integer, List<Validator.DataError>> rowErrorsMap = errors.stream().collect(Collectors.groupingBy(Validator.DataError::getRow));
        if(errorRows != null && !errorRows.isEmpty()){
            for(int i = 0; i < errorRows.size(); i++ ){
                Grant grant = new Grant();
                // 错误记录
                Reader.Row item = errorRows.get(i);
                List<Validator.DataError> dataErrors = rowErrorsMap.get(item.getIndex());
                List<RowError> rowErrors = dataErrors.stream().map(dataError ->{
                    RowError error = new RowError();
                    if(!StringUtils.isEmpty(dataError.getCode())){
                        error.setCode(dataError.getCode().getCode());
                    }
                    if(dataError.getColumn() !=-1){
                        error.setColumn(dataError.getColumn());
                    }
                    if(dataError.getRow() !=-1){
                        error.setRow(dataError.getRow());
                    }
                    return error;
                }).collect(Collectors.toList());

                grant.setErrors(rowErrors);
                grant.setRow(item.getIndex());
                grant.setMemberName(item.get(0, String.class) != null
                        ? item.get(0, String.class).trim()
                        : item.get(0, String.class));
                grant.setOperatorTypes(item.get(1, String.class) != null
                        ? item.get(1, String.class).trim()
                        : item.get(1, String.class));
                grant.setOrganizationCodes(item.get(2, String.class) != null
                        ? item.get(2, String.class).trim()
                        : item.get(2, String.class));
                grant.setChildFlag(item.get(3, String.class) != null
                        ? item.get(3, String.class).trim()
                        : item.get(3, String.class));
                grant.setOrganizationCode(item.get(4, String.class) != null
                        ? item.get(4, String.class).trim()
                        : item.get(4, String.class));
                grant.setValid(item.get(5, String.class) != null
                        ? item.get(5, String.class).trim()
                        : item.get(5, String.class));
                list.add(grant);
            }
        }
        return list;
    }

    private static final Set<String> VALID_TYPES = new HashSet<>(Arrays.asList(
            "完全控制", "可查看", "可编辑", "可删除", "其他"
    ));

    /**
     * 业务数据校验（覆盖PRD的7项校验规则）
     */
    private void validateBusinessData(
            Grant grant,
            Map<String, com.zxy.product.human.entity.Member> memberMap,
            Set<String> authorizedOrgCodes,
            Set<String> existingGrantKeys,
            List<Grant> correctInsertList,
            List<Grant> errorList,
            Map<String, Organization> authorizedOrMap,
            Set<String> authorizedOrgIds) {

        List<RowError> errors = grant.getErrors();

        // 1. 员工编码校验（规则3）
        String memberCode = grant.getMemberName();
        com.zxy.product.human.entity.Member member = memberMap.get(memberCode);
        String CodesId = null;
        String CodeId = null;
        if (member == null) {
            errors.add(new RowError(grant.getRow(), 0, ErrorCode.MEMBER_ERROR.getCode()));
        }else {
            String organizationId = member.getOrganizationId();
            if (!authorizedOrgIds.contains(organizationId)) {
                errors.add(new RowError(grant.getRow(), 0, ErrorCode.MEMBER_ERROR.getCode()));
            }
        }

        // 2. 授权类型校验（规则2）
        String operatorType = grant.getOperatorTypes();
        if (operatorType != null) {
            String[] types = operatorType.split("、");
            for (String type : types) {
                if (!VALID_TYPES.contains(type)) {
                    errors.add(new RowError(grant.getRow(), 1, ErrorCode.OPERATOR_TYPE_ERROR.getCode()));
                    break;
                }
            }
        }

        // 3. 授权节点组织编码校验（规则5）
        String nodeOrgCode = grant.getOrganizationCodes();
        if (!authorizedOrgCodes.contains(nodeOrgCode)) {
            errors.add(new RowError(grant.getRow(), 2, ErrorCode.ORG_CODE_ERROR.getCode()));
        }else {
            Organization organization = authorizedOrMap.get(nodeOrgCode);
            if (organization == null) {
                errors.add(new RowError(grant.getRow(), 2, ErrorCode.ORG_CODE_ERROR.getCode()));
            } else {
                CodesId = organization.getId();
            }
        }

        // 4. 是否包含子节点校验（规则2）
        String childFlag = grant.getChildFlag();
        if (!"是".equals(childFlag) && !"否".equals(childFlag)) {
            errors.add(new RowError(grant.getRow(), 3, ErrorCode.OPERATOR_TYPE_ERROR.getCode()));
        }

        // 5. 授权归属部门校验（规则6）
        String belongOrgCode = grant.getOrganizationCode();
        if (!authorizedOrgCodes.contains(belongOrgCode)) {
            errors.add(new RowError(grant.getRow(), 4, ErrorCode.ORG_CODE_ERROR.getCode()));
        }else {
            Organization organization = authorizedOrMap.get(belongOrgCode);
            if (organization == null) {
                errors.add(new RowError(grant.getRow(), 2, ErrorCode.ORG_CODE_ERROR.getCode()));
            } else {
                CodeId = organization.getId();
            }
        }

        // 6. 授权有效期校验（规则7）
        String validDateStr = grant.getValid();
        if (validDateStr != null && !validDateStr.isEmpty()) {
            try {
                LocalDate date = LocalDate.parse(validDateStr);
                if (date.isBefore(LocalDate.now())) {
                    errors.add(new RowError(grant.getRow(), 5, ErrorCode.VALID_DATE_ERROR.getCode()));
                }
            } catch (DateTimeParseException e) {
                errors.add(new RowError(grant.getRow(), 5, ErrorCode.VALID_DATE_ERROR.getCode()));
            }
        }

        // 7. 授权重复校验（规则4）
        if (member != null && errors.isEmpty()) {
            String grantKey = generateGrantKey(member.getId());
            if (existingGrantKeys.contains(grantKey)) {
                errors.add(new RowError(grant.getRow(), 0, ErrorCode.GRANT_REPEAT.getCode()));
            }
        }

        // 校验结果处理
        if (errors.isEmpty()) {
            grant.setMemberId(member.getId()); // 关联员工ID
            grant.setOperatorTypes(convertOperatorType(operatorType)); // 转换为编码（如"可查看"→"1"）
            grant.setOrganizationCodes(CodesId);
            grant.setOrganizationCode(CodeId);
            correctInsertList.add(grant);
        } else {
            errorList.add(grant);
        }
    }

    // 辅助方法：生成授权唯一标识（用于重复校验）
    private String generateGrantKey(String memberId) {
        return memberId ;
    }

    // 辅助方法：将授权类型文本转为编码（如"完全控制"→"0"）
    private String convertOperatorType(String operatorType) {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("完全控制", "0");
        typeMap.put("可查看", "1");
        typeMap.put("可编辑", "2");
        typeMap.put("可删除", "3");
        typeMap.put("其他", "4");

        return Arrays.stream(operatorType.split("、"))
                .map(typeMap::get)
                .collect(Collectors.joining(","));
    }

    /** 错误信息：行-列-错误编码 */
    private List<Map<String, Object>> createErrorList(List<Grant> errorList) {
        List<Map<String, Object>> errors = new ArrayList<>();
        errorList.forEach(error -> {
            for(RowError e : error.getErrors() ){
                Map<String, Object> map = new HashMap<>();
                map.put("row", e.getRow());
                map.put("column", e.getColumn());
                map.put("code", e.getCode() == null ? "" : e.getCode());
                errors.add(map);
            }
        });
        return errors;
    }

    /** 生成导入的错误临时文件 */
    private String createErrorTempFile(List<Grant> errorList) throws IOException {
        if (CollectionUtils.isNotEmpty(errorList)) {
            Writer writer = new ExcelWriter();
            // 错误数据
            writer.sheet("导入有误的数据", errorList)
                    .field("员工编码（必填）", Grant::getMemberName)
                    .field("授权类型（必填，完全控制或可查看、可编辑、可删除、其他）", Grant::getOperatorTypes)
                    .field("授权节点组织编码（必填）", Grant::getOrganizationCodes)
                    .field("是否包含子节点（必填，是/否）", Grant::getChildFlag)
                    .field("授权归属部门组织编码（必填）", Grant::getOrganizationCode)
                    .field("授权有效期（非必填，yyyy-mm-dd）", Grant::getValid);
            // 将临时文件写入文件系统
            return uploadTempFile(writer);
        }
        return null;
    }

    /** 输出流转文件，再转输入流上传到文件系统，返回文件id  */
    private String uploadTempFile(Writer writer) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        writer.write(os);
        InputStream is = new ByteArrayInputStream(os.toByteArray());
        MultipartFile mfile = UploadUtil.transferTo(is, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "导出错误数据.xlsx", is.available());
        com.zxy.common.restful.multipart.Attachment restfulAttachment = attachmentResolver.store(null, mfile, Optional.empty());
        String[] filename = new String[]{ restfulAttachment.getFilename() };
        String[] contentType = new String[]{ restfulAttachment.getContentType() };
        String[] path = new String[]{ restfulAttachment.getPath() };
        Long[] size = new Long[]{ restfulAttachment.getSize() };
        List<Attachment> result = fileService.insert(filename, contentType, new String[]{"xlsx"}, path, size);
        return result.get(0).getId();
    }

}


