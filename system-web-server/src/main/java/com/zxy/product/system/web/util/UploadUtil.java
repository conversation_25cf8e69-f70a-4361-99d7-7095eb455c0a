package com.zxy.product.system.web.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

public class UploadUtil {

    /** 重写MultipartFile实现， 将文件流转换成MultipartFile */
    public static MultipartFile transferTo(final InputStream inputStream, String contentType, String filename, int size) {
        return new MultipartFile() {

            @Override
            public String getName() {
                return filename;
            }

            @Override
            public String getOriginalFilename() {
                if (filename == null) {
                    // Should never happen.
                    return "";
                }

                // Check for Unix-style path
                int unixSep = filename.lastIndexOf("/");
                // Check for Windows-style path
                int winSep = filename.lastIndexOf("\\");
                // Cut off at latest possible point
                int pos = (winSep > unixSep ? winSep : unixSep);
                if (pos != -1)  {
                    // Any sort of path separator found...
                    return filename.substring(pos + 1);
                }
                else {
                    // A plain name
                    return filename;
                }
            }

            @Override
            public String getContentType() {
                return contentType;
            }

            @Override
            public boolean isEmpty() {
                return size == 0;
            }

            @Override
            public long getSize() {
                return size;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return  inputStream;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };
    }
}
