package com.zxy.product.system.service.support.homeconfig;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.homeconfig.HomeConfigService;
import com.zxy.product.system.content.SystemConstant;
import com.zxy.product.system.entity.*;
import com.zxy.product.system.jooq.tables.pojos.VirtualSpaceGroupingDetailsEntity;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.system.jooq.Tables.*;

@Service
public class HomeConfigServiceSupport implements HomeConfigService {

    CommonDao<HomeConfig> homeConfigDao;

    CommonDao<HomeModuleConfig> homeModuleConfigDao;

    private CommonDao<PageRelation> pageRelationCommonDao;

    CommonDao<Organization> organizationDao;


    private MongoTemplate mongoTemplate;

    private CommonDao<VirtualSpaceGroupingDetails> virtualSpaceGroupingDetailsCommonDao;


    private static final String REMOVE = "remove";

    @Autowired
    public void setVirtualSpaceGroupingDetailsCommonDao(CommonDao<VirtualSpaceGroupingDetails> virtualSpaceGroupingDetailsCommonDao) {
        this.virtualSpaceGroupingDetailsCommonDao = virtualSpaceGroupingDetailsCommonDao;
    }

    @Autowired
    public void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }
    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
        this.organizationDao = organizationDao;
    }


    @Autowired
    public void setPageRelationCommonDao(CommonDao<PageRelation> pageRelationCommonDao) {
        this.pageRelationCommonDao = pageRelationCommonDao;
    }


    @Autowired
    public void setHomeModuleConfigDao(CommonDao<HomeModuleConfig> homeModuleConfigDao) {
        this.homeModuleConfigDao = homeModuleConfigDao;
    }

    @Autowired
    public void setHomeConfigDao(CommonDao<HomeConfig> homeConfigDao) {
        this.homeConfigDao = homeConfigDao;
    }



    @Override
    public HomeConfig insert(Optional<String> logo, Optional<String> logoPath, Optional<String> logoMini, Optional<String> logoMiniPath,
                             Optional<String> description, String name, String organizationId, String version, List<HomeModuleConfig> homeModuleConfigs) {
        HomeConfig config = new HomeConfig();
        config.forInsert();
        logo.ifPresent(config::setLogo);
        logoPath.ifPresent(config::setLogoPath);
        logoMini.ifPresent(config::setLogoMini);
        logoMiniPath.ifPresent(config::setLogoMiniPath);
        description.ifPresent(config::setDescription);
        config.setName(name);
        config.setOrganizationId(organizationId);
        config.setState(HomeConfig.STATE_DRAFT);
        config.setDeleteFlag(HomeConfig.DELETE_FLAG_NO);
        config.setEnableHomeBrowse(HomeConfig.ENABLE_BROWSE);
        config.setVersion(version);
        config.setType(HomeConfig.TYPE_INSTITUTIONS);
        homeConfigDao.insert(config);
        if (!ObjectUtils.isEmpty(homeModuleConfigs)) {
            homeModuleConfigs.forEach(homeModuleConfig -> {
                homeModuleConfig.forInsert();
                homeModuleConfig.setHomeConfigId(config.getId());
            });
            homeModuleConfigDao.insert(homeModuleConfigs);
        }
        return config;
    }

    @Override
    public HomeConfig update(String id, Optional<String> logo, Optional<String> logoPath, Optional<String> logoMini,Optional<String> logoMiniPath,
                             Optional<String> description, String name, String organizationId, String version) {
        HomeConfig config = homeConfigDao.get(id);
        logo.ifPresent(config::setLogo);
        logoPath.ifPresent(config::setLogoPath);
        logoMini.ifPresent(config::setLogoMini);
        logoMiniPath.ifPresent(config::setLogoMiniPath);
        description.ifPresent(config::setDescription);
        config.setName(name);
        config.setOrganizationId(organizationId);
        config.setVersion(version);
        homeConfigDao.update(config);
        return config;
    }

    @Override
    public PagedResult<HomeConfig> find(Integer page, Integer pageSize, String currentUserId, Optional<String> organizationId) {
        Field<String> orgName = ORGANIZATION.NAME.as("organizationName");
        return homeConfigDao.fetchPage(page, pageSize, e -> {
            return e.select(Fields.start().add(HOME_CONFIG.fields()).add(orgName).end())
                    .from(HOME_CONFIG)
                    .leftJoin(ORGANIZATION).on(HOME_CONFIG.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .where(organizationId.map(HOME_CONFIG.ORGANIZATION_ID::eq).orElse(DSL.trueCondition()))
                    .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))
                    .and(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_INSTITUTIONS))
                    .orderBy(HOME_CONFIG.CREATE_TIME.desc());
        }, r -> {
            HomeConfig config = r.into(HomeConfig.class);
            config.setOrganizationName(r.getValue(orgName));
            return config;
        });
    }

    @Override
    public HomeConfig get(String id) {
        return homeConfigDao.execute(e -> {
            return e.select(Fields.start().add(HOME_CONFIG.fields()).add(ORGANIZATION.NAME).end())
                    .from(HOME_CONFIG).leftJoin(ORGANIZATION)
                    .on(HOME_CONFIG.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .where(HOME_CONFIG.ID.eq(id))
                    .fetchOne(r -> {
                        HomeConfig config = r.into(HomeConfig.class);
                        config.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                        return config;
                    });
        });
    }

    @Override
    public HomeConfig getDetail(String id) {
        return homeConfigDao.execute(e -> e.select(Fields.start().add(HOME_CONFIG.fields())
                        .add(ORGANIZATION.NAME)
                        .add(ORGANIZATION.PATH).end())
                .from(HOME_CONFIG)
                .leftJoin(ORGANIZATION).on(HOME_CONFIG.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(HOME_CONFIG.ID.eq(id))
                .fetchOne(r -> {
                    HomeConfig config = r.into(HomeConfig.class);
                    config.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                    List<PageRelation> listPageRelation = pageRelationCommonDao.fetch(PAGE_RELATION.PAGE_PLAN_CONFIG_ID.eq(id),
                            PAGE_RELATION.TYPE.in(PageRelation.PC_TYPE, PageRelation.APP_TYPE),
                            PAGE_RELATION.BUSINESS_TYPE.isNull());
                    config.setPageRelation(listPageRelation);
                    // 子首页
                    List<PageRelation> homePageJson = pageRelationCommonDao.fetch(PAGE_RELATION.PAGE_PLAN_CONFIG_ID.eq(id),
                            PAGE_RELATION.TYPE.in(PageRelation.PC_TYPE, PageRelation.APP_TYPE),
                            PAGE_RELATION.BUSINESS_TYPE.eq(PageRelation.BUSINESS_TYPE_CHILD_PAGE));
                    config.setHomePageJson(homePageJson);
                    return config;
                }));
    }

    @Override
    public String enable(String id, Integer type) {
        HomeConfig homeConfig = homeConfigDao.get(id);
        homeConfigDao.execute(e -> e.update(HOME_CONFIG).set(HOME_CONFIG.STATE, HomeConfig.STATE_DISABLE)
                .where(HOME_CONFIG.ORGANIZATION_ID.eq(homeConfig.getOrganizationId()).and(HOME_CONFIG.TYPE.eq(type))).execute());
        homeConfig.setState(HomeConfig.STATE_ENABLE);
        homeConfigDao.update(homeConfig);
        return id;
    }

    @Override
    public String disable(String id) {
        HomeConfig homeConfig = homeConfigDao.get(id);
        homeConfig.setState(HomeConfig.STATE_DISABLE);
        homeConfigDao.update(homeConfig);
        return id;
    }

    @Override
    public String delete(String id) {
        homeConfigDao.execute(e -> {
            return e.update(HOME_CONFIG).set(HOME_CONFIG.DELETE_FLAG, HomeConfig.DELETE_FLAG_YES)
                    .where(HOME_CONFIG.ID.eq(id))
                    .execute();
        });
        return id;
    }

    @Override
    public Optional<HomeConfig> getEnableHomeConfig(String organizationId, Integer type) {
        Optional<HomeConfig> config = homeConfigDao.fetchOne(
                HOME_CONFIG.ORGANIZATION_ID.eq(organizationId).and(
                        HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE)).and(HOME_CONFIG.TYPE.eq(type)));
        return config;
    }

    @Override
    public List<String> findEnableOrganizationIds() {
        return homeConfigDao.execute(e -> {
            return e.select(HOME_CONFIG.ORGANIZATION_ID)
                    .from(HOME_CONFIG)
                    .where(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE))
                    .and(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_INSTITUTIONS))
                    .fetch(HOME_CONFIG.ORGANIZATION_ID);
        });
    }

    @Override
    public List<HomeModule> findModulesById(String id) {
        return homeConfigDao.execute(e -> e.select(
                        Fields.start().
                                add(HOME_MODULE_CONFIG.HOME_MODULE_ID,
                                        HOME_MODULE_CONFIG.NAME)
                                .end()))
                .from(HOME_MODULE_CONFIG)
                .where(HOME_MODULE_CONFIG.HOME_CONFIG_ID.eq(id))
                .fetch(r -> {
                    HomeModule homeModule = new HomeModule();
                    homeModule.setId(r.getValue(HOME_MODULE_CONFIG.HOME_MODULE_ID));
                    homeModule.setName(r.getValue(HOME_MODULE_CONFIG.NAME));
                    return homeModule;
                });
    }


    @Override
    public HomeConfig update(String id, Integer enableBrowse, Optional<Integer> state) {
        HomeConfig homeConfig = homeConfigDao.get(id);
        homeConfig.setEnableHomeBrowse(enableBrowse);
        state.ifPresent(homeConfig::setState);
        homeConfigDao.update(homeConfig);
        return homeConfig;
    }

    @Override
    public int existsEnableByOrgId(String orgId) {
        return homeConfigDao.execute(e -> e.fetchCount(e.select(HOME_CONFIG.ID).from(HOME_CONFIG)
                .where(HOME_CONFIG.ORGANIZATION_ID.eq(orgId))
                .and(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE))
                .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))));
    }

    @Override
    public List<Organization> findOrgByEnableHome(String organizationId) {
        return homeConfigDao.execute(e -> e.select(Fields.start().add(ORGANIZATION.ID,ORGANIZATION.NAME,ORGANIZATION.SHORT_NAME).end())
                .from(HOME_CONFIG)
                .leftJoin(ORGANIZATION).on(HOME_CONFIG.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE))
                .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))
                .and(HOME_CONFIG.ORGANIZATION_ID.eq(organizationId))
                .orderBy(HOME_CONFIG.CREATE_TIME.asc())
                .fetch()
                .into(Organization.class));
    }

    @Override
    public List<Organization> findOrgSubByEnableHome(String organizationId) {
        return homeConfigDao.execute(e -> e.select(Fields.start().add(ORGANIZATION.ID,ORGANIZATION.NAME,ORGANIZATION.SHORT_NAME).end())
                .from(ORGANIZATION_DETAIL)
                .leftJoin(ORGANIZATION).on(ORGANIZATION_DETAIL.ROOT.eq(ORGANIZATION.ID))
                .leftJoin(HOME_CONFIG).on(ORGANIZATION.ID.eq(HOME_CONFIG.ORGANIZATION_ID))
                .where(ORGANIZATION_DETAIL.SUB.eq(organizationId))
                .and(ORGANIZATION.LEVEL.eq(3))
                .and(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE))
                .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))
                .and(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_INSTITUTIONS))
                .orderBy(ORGANIZATION.CREATE_TIME.asc())
                .fetch()
                .into(Organization.class));
    }

    @Override
    public void disableByOrganizationId(String organizationId) {
        homeConfigDao.execute(e -> e.update(HOME_CONFIG).set(HOME_CONFIG.STATE, HomeConfig.STATE_DISABLE)
                .where(HOME_CONFIG.ORGANIZATION_ID.eq(organizationId)).execute());
    }



    @Override
    public PagedResult<HomeConfig> findVirtualSpace(Integer page, Integer pageSize, String currentUserId, Optional<Long> createTimeStart, Optional<Long> createTimeEnd, Optional<String> name, List<String> ids, Optional<String> organizationId) {
        List<Condition> conditions = Stream.of(
                        name.map(HOME_CONFIG.NAME::contains),
                        createTimeStart.map(HOME_CONFIG.CREATE_TIME::ge),
                        createTimeEnd.map(HOME_CONFIG.CREATE_TIME::lt))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(HOME_CONFIG.ORGANIZATION_ID.in(ids));

        Field<String> orgName = ORGANIZATION.NAME.as("organizationName");
        return homeConfigDao.fetchPage(page, pageSize, e -> e.select(Fields.start().add(HOME_CONFIG.fields()).add(orgName).end())
                .from(HOME_CONFIG)
                .leftJoin(ORGANIZATION).on(HOME_CONFIG.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .and(organizationId.map(HOME_CONFIG.ORGANIZATION_ID::eq).orElse(DSL.trueCondition()))
                .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))
                .and(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE))
                .orderBy(HOME_CONFIG.CREATE_TIME.desc()), r -> {
            HomeConfig config = r.into(HomeConfig.class);
            config.setOrganizationName(r.getValue(orgName));
            return config;
        });

    }

    @Override
    public List<Organization> findOrgVirtual(List<Organization> orgList) {
        List<String> ids = Lists.newArrayList();
        orgList.forEach(r->ids.add(r.getId()));
        List<String> virtualIds = homeConfigDao.execute(r -> r.select(HOME_CONFIG.ORGANIZATION_ID).
                from(HOME_CONFIG).
                where(HOME_CONFIG.ORGANIZATION_ID.in(ids)).
                and(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE)).
                fetch(Record1::value1)
        );
        orgList.forEach(r-> virtualIds.forEach(o->{
            if (r.getId().equals(o)){
                r.setVirtualStatus(Organization.VIRTUAL_STATUS_OK);
            }
        }));
        return  orgList;
    }


    @Override
    public HomeConfig insertVirtualSpace(Optional<String> logo, Optional<String> logoPath, Optional<String> logoMini, Optional<String> logoMiniPath,
                                         Optional<String> description, String name, String organizationId, String version, List<HomeModuleConfig> homeModuleConfigs, Optional<String> pcCanvasPageConfigIds, Optional<String> appCanvasPageConfigIds, Optional<String> homePageJson, Integer enableHomeBrowse, Integer platform, Optional<Integer> jumpHomePage) {

        HomeConfig config = new HomeConfig();
        config.forInsert();
        logo.ifPresent(config::setLogo);
        logoPath.ifPresent(config::setLogoPath);
        logoMini.ifPresent(config::setLogoMini);
        logoMiniPath.ifPresent(config::setLogoMiniPath);
        description.ifPresent(config::setDescription);
        jumpHomePage.ifPresent(config::setJumpHomePage);
        config.setName(name);
        config.setOrganizationId(organizationId);
        config.setState(HomeConfig.STATE_DISABLE);
        config.setDeleteFlag(HomeConfig.DELETE_FLAG_NO);
        config.setVersion(version);
        config.setType(HomeConfig.TYPE_VIRTUAL_SPACE);
        config.setPlatform(platform);
        config.setEnableHomeBrowse(enableHomeBrowse);
        homeConfigDao.insert(config);
        //保存首页和方案页面的关系
        handHomePageRelation(pcCanvasPageConfigIds, appCanvasPageConfigIds, config.getId());

        // 保存首页和子首页关系
        handHomePageRelation(homePageJson, config.getId());

        if (!ObjectUtils.isEmpty(homeModuleConfigs)) {
            homeModuleConfigs.forEach(homeModuleConfig -> {
                homeModuleConfig.forInsert();
                homeModuleConfig.setHomeConfigId(config.getId());
            });
            homeModuleConfigDao.insert(homeModuleConfigs);
        }
        return config;
    }


    @Override
    public void handHomePageRelation(Optional<String> homePageJson, String homeConfigId) {
        homePageJson.ifPresent(p -> {
            pageRelationCommonDao.delete(PAGE_RELATION.PAGE_PLAN_CONFIG_ID.eq(homeConfigId), PAGE_RELATION.TYPE.eq(PageRelation.BUSINESS_TYPE_CHILD_PAGE));
            List<PageRelation> pageRelations = JSON.parseArray(p, PageRelation.class);
            List<PageRelation> inserts = new ArrayList<>();
            pageRelations.forEach(relation -> inserts.add(doInsertPageRelation(homeConfigId, Optional.of(PageRelation.BUSINESS_TYPE_CHILD_PAGE), relation.getId(),
                    relation.getType(), Optional.ofNullable(relation.getExtraParam()))));
            pageRelationCommonDao.insert(inserts);
        });
    }

    @Override
    public void handHomePageRelation(Optional<String> pcCanvasPageConfigIds, Optional<String> appCanvasPageConfigIds, String homeConfigId) {
        handlePageRelation(pcCanvasPageConfigIds, appCanvasPageConfigIds, homeConfigId, Optional.empty());
    }


    //保存首页和方案页面的关系
    private void handlePageRelation(Optional<String> pcCanvasPageConfigIds, Optional<String> appCanvasPageConfigIds, String homeConfigId,
                                    Optional<String> childHomeConfigIds) {
        //保存首页和方案页面的关系
        pcCanvasPageConfigIds.ifPresent(p -> {
            pageRelationCommonDao.delete(PAGE_RELATION.PAGE_PLAN_CONFIG_ID.eq(homeConfigId), PAGE_RELATION.TYPE.eq(PageRelation.PC_TYPE));
            //前端把页面配置全删除时，传false,做删除用
            if (SystemConstant.FALSE.equals(p)) {
                return;
            }
            List<PageRelation> pageRelationList = Arrays.stream(p.split(","))
                    .map(v -> doInsertPageRelation(homeConfigId, Optional.empty(), v, PageRelation.PC_TYPE, Optional.empty()))
                    .collect(Collectors.toList());
            pageRelationCommonDao.insert(pageRelationList);
        });

        appCanvasPageConfigIds.ifPresent(a -> {
            pageRelationCommonDao.delete(PAGE_RELATION.PAGE_PLAN_CONFIG_ID.eq(homeConfigId), PAGE_RELATION.TYPE.eq(PageRelation.APP_TYPE));
            if (SystemConstant.FALSE.equals(a)) {
                return;
            }
            List<PageRelation> pageRelationList = Arrays.stream(a.split(","))
                    .map(v -> doInsertPageRelation(homeConfigId, Optional.empty(), v, PageRelation.APP_TYPE, Optional.empty()))
                    .collect(Collectors.toList());
            pageRelationCommonDao.insert(pageRelationList);
        });
        // 插入子首页
        childHomeConfigIds.ifPresent(a->{
            if (!StringUtils.hasText(a)) {
                return;
            }
            List<PageRelation> pageRelationList = Arrays.stream(a.split(","))
                    .map(v -> doInsertPageRelation(homeConfigId, Optional.of(PageRelation.BUSINESS_TYPE_CHILD_PAGE), v,
                            PageRelation.APP_TYPE, Optional.empty()))
                    .collect(Collectors.toList());
            pageRelationCommonDao.insert(pageRelationList);
        });
    }


    private PageRelation doInsertPageRelation(String homeConfigId, Optional<Integer> businessType, String canvasId, Integer type, Optional<String> extraParam) {
        PageRelation tmp = new PageRelation();
        tmp.forInsert();
        tmp.setCanvasPageConfigId(canvasId);
        tmp.setType(type);
        tmp.setPagePlanConfigId(homeConfigId);
        businessType.ifPresent(tmp::setBusinessType);
        extraParam.ifPresent(tmp::setExtraParam);
        return tmp;
    }


    @Override
    public int getVirtualSpaceByOrganizationId(String organizationId, Optional<String> id) {
        List<Condition> conditions = Stream.of(
                        id.map(HOME_CONFIG.ID::ne))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        conditions.add(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE));
        conditions.add(HOME_CONFIG.ORGANIZATION_ID.eq(organizationId));
        conditions.add(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO));

        return homeConfigDao.execute(r->
                r.selectCount()
                        .from(HOME_CONFIG)
                        .where(conditions)
                        .fetchOne().value1());
    }

    @Override
    public HomeConfig updateVirtualSpace(Optional<String> name,
                                         Optional<String> description,
                                         Optional<String> organizationId,
                                         String id,
                                         Optional<String> logo,
                                         Optional<String> logoMini,
                                         Optional<String> logoPath,
                                         Optional<String> logoMiniPath,
                                         Optional<String> version, Optional<String> pcJson,
                                         Integer enableHomeBrowse,
                                         Integer platform, Optional<Integer> jumpHomePage) {
        Optional<HomeConfig> optional = homeConfigDao.getOptional(id);
        if (optional.isPresent()){
            HomeConfig homeConfig = optional.get();
            name.ifPresent(homeConfig::setName);
            description.ifPresent(homeConfig::setDescription);
            organizationId.ifPresent(homeConfig::setOrganizationId);
            logo.ifPresent(homeConfig::setLogo);
            logoMini.ifPresent(homeConfig::setLogoMini);
            logoPath.ifPresent(homeConfig::setLogoPath);
            logoMiniPath.ifPresent(homeConfig::setLogoMiniPath);
            version.ifPresent(homeConfig::setVersion);
            pcJson.ifPresent(homeConfig::setPcCanvasJson);
            jumpHomePage.ifPresent(homeConfig::setJumpHomePage);
            homeConfig.setEnableHomeBrowse(enableHomeBrowse);
            homeConfig.setPlatform(platform);
            homeConfigDao.update(homeConfig);
            return homeConfig;
        }
        return new HomeConfig();
    }

    @Override
    public String updateVirtualSpaceState(String id, Integer state) {
        homeConfigDao.execute(e -> e.
                update(HOME_CONFIG)
                .set(HOME_CONFIG.STATE, state)
                .where(HOME_CONFIG.ID.eq(id)).execute());

        return id;
    }

    @Override
    public List<String> hasConfigVirtualIds() {
        return homeConfigDao.execute(e -> e.select(HOME_CONFIG.ORGANIZATION_ID)
                .from(HOME_CONFIG)
                .where(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE))
                .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))
                .fetch(HOME_CONFIG.ORGANIZATION_ID));
    }


    @Override
    public List<HomeConfig> findAll(int page, int pageSize, Integer clientType, Optional<Integer> paasFlag) {
        List<Condition> conditions = Stream.of(
                Optional.of(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO)),
                Optional.of(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE))
//                paasFlag.map(COMPANY.PAAS_TRAIN_SWITCH::eq)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        List<HomeConfig> homeConfigs = homeConfigDao.execute(d -> d.select(HOME_CONFIG.ID).from(HOME_CONFIG)
//                        .leftJoin(COMPANY).on(HOME_CONFIG.COMPANY_ID.eq(COMPANY.ID))
                        .where(conditions))
                .limit((page - 1) * pageSize, pageSize)
                .fetchInto(HomeConfig.class);
        Map<String, List<PageRelation>> pageRelations = pageRelationCommonDao
                .execute(d -> d.select(PAGE_RELATION.PAGE_PLAN_CONFIG_ID, PAGE_RELATION.CANVAS_PAGE_CONFIG_ID).from(PAGE_RELATION)
                        .where(PAGE_RELATION.PAGE_PLAN_CONFIG_ID.in(homeConfigs.stream().map(HomeConfig::getId).collect(Collectors.toList())),
                                PAGE_RELATION.TYPE.eq(clientType),
                                PAGE_RELATION.BUSINESS_TYPE.isNull()))
                .fetchInto(PageRelation.class).stream().collect(Collectors.groupingBy(PageRelation::getPagePlanConfigId));
        homeConfigs.forEach(h -> h.setPageRelation(pageRelations.get(h.getId())));
        return homeConfigs;
    }


    @Override
    public Boolean addPageRelations(Map<String, String> map, Integer type) {
        ArrayList<PageRelation> inserts = new ArrayList<>();
        map.forEach((homeConfigId, pageId) -> {
            PageRelation pageRelation = new PageRelation();
            pageRelation.forInsert();
            pageRelation.setPagePlanConfigId(homeConfigId);
            pageRelation.setCanvasPageConfigId(pageId);
            pageRelation.setType(type);
            pageRelation.setLabel("");
            pageRelation.setDescription("");
            inserts.add(pageRelation);
        });
        pageRelationCommonDao.insert(inserts);
        return null;
    }

    @Override
    public PagedResult<HomeConfig> findAddList(Integer page, Integer pageSize, Optional<String> optionalName) {

        List<Condition> conditionList = Stream.of(
                        optionalName.map(HOME_CONFIG.NAME::contains))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        conditionList.add(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE));
        conditionList.add(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE));
        List<String> ids = virtualSpaceGroupingDetailsCommonDao.fetch().stream().map(VirtualSpaceGroupingDetailsEntity::getHomeConfigId).collect(Collectors.toList());

        return homeConfigDao.fetchPage(page, pageSize,
                r -> r.select(HOME_CONFIG.ID,
                                HOME_CONFIG.NAME,
                                ORGANIZATION.NAME.as("orgName"))
                        .from(HOME_CONFIG)
                        .leftJoin(ORGANIZATION).on(HOME_CONFIG.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .where(conditionList)
                        .and(HOME_CONFIG.ID.notIn(ids))
                , o -> {
                    HomeConfig homeConfig = new HomeConfig();
                    homeConfig.setId(o.getValue(HOME_CONFIG.ID));
                    homeConfig.setName(o.getValue(HOME_CONFIG.NAME));
                    homeConfig.setOrganizationName(o.getValue(ORGANIZATION.NAME.as("orgName")));
                    return homeConfig;
                });
    }

    @Override
    public Optional<HomeConfig> getOptional(String id) {
        return homeConfigDao.getOptional(id);
    }

    @Override
    public String getVirtualSpaceIdByOrganizationId(String organizationId) {
        return homeConfigDao.execute(r->
                r.select(HOME_CONFIG.ID)
                        .from(HOME_CONFIG)
                        .where(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE))
                        .and(HOME_CONFIG.DELETE_FLAG.ne(HomeConfig.DELETE_FLAG_YES))
                        .and(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE))
                        .and(HOME_CONFIG.ORGANIZATION_ID.eq(organizationId))
                        .fetchOne(HOME_CONFIG.ID));
    }


    @Override
    public Map<String, HomeConfig> findByOrganizationId(List<String> list) {
        Map<String,HomeConfig> map = Maps.newHashMap();
        homeConfigDao.execute(e -> e.select()
                .from(HOME_CONFIG)
                .where(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE))
                .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))
                .and(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE))
                .and(HOME_CONFIG.ORGANIZATION_ID.in(list))
                .fetch(r->{
                    HomeConfig into = r.into(HomeConfig.class);
                    map.put(into.getOrganizationId(),into);
                    return null;
                }));
        return  map;

    }


    @Override
    public List<HomeConfig> findVirtualSpaceByOrganizationId(List<String> list) {
        return homeConfigDao.execute(o ->
                o.select(HOME_CONFIG.ID,HOME_CONFIG.CREATE_TIME,HOME_CONFIG.NAME,HOME_CONFIG.ORGANIZATION_ID,HOME_CONFIG.LOGO_PATH,HOME_CONFIG.LOGO,HOME_CONFIG.ENABLE_HOME_BROWSE)
                        .from(HOME_CONFIG)
                        .leftJoin(ORGANIZATION).on(HOME_CONFIG.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .where(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE))
                        .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))
                        .and(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE))
                      //  .and(HOME_CONFIG.ENABLE_HOME_BROWSE.eq(HomeConfig.ENABLE_BROWSE))
                        .and(HOME_CONFIG.ORGANIZATION_ID.in(list))
                        .orderBy(ORGANIZATION.DEPTH.asc(), ORGANIZATION.ORDER.asc(), ORGANIZATION.CREATE_TIME.desc())
                        .fetch(r->{
                            HomeConfig homeConfig = new HomeConfig();
                            homeConfig.setId(r.getValue(HOME_CONFIG.ID));
                            homeConfig.setCreateTime(r.getValue(HOME_CONFIG.CREATE_TIME));
                            homeConfig.setName(r.getValue(HOME_CONFIG.NAME));
                            homeConfig.setOrganizationId(r.getValue(HOME_CONFIG.ORGANIZATION_ID));
                            homeConfig.setLogoPath(r.getValue(HOME_CONFIG.LOGO_PATH));
                            homeConfig.setLogo(r.getValue(HOME_CONFIG.LOGO));
                            homeConfig.setEnableHomeBrowse(r.getValue(HOME_CONFIG.ENABLE_HOME_BROWSE));
                            return homeConfig;
                            //.stream().sorted(Comparator.comparing(HomeConfig::getCreateTime).reversed())).collect(Collectors.toList()
                        }));
    }

    @Override
    public List<HomeConfig> findVirtualSpaceByOrganizationNotInId(List<String> list) {
        return homeConfigDao.execute(o ->
                o.select(HOME_CONFIG.ID,HOME_CONFIG.CREATE_TIME,HOME_CONFIG.NAME,HOME_CONFIG.ORGANIZATION_ID,HOME_CONFIG.LOGO_PATH,HOME_CONFIG.LOGO,HOME_CONFIG.ENABLE_HOME_BROWSE)
                        .from(HOME_CONFIG)
                        .leftJoin(ORGANIZATION).on(HOME_CONFIG.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .where(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE))
                        .and(HOME_CONFIG.DELETE_FLAG.eq(HomeConfig.DELETE_FLAG_NO))
                        .and(HOME_CONFIG.STATE.eq(HomeConfig.STATE_ENABLE))
                        //.and(HOME_CONFIG.ENABLE_HOME_BROWSE.eq(HomeConfig.ENABLE_BROWSE))
                        .and(HOME_CONFIG.ORGANIZATION_ID.notIn(list))
                        .orderBy(ORGANIZATION.DEPTH.asc(), ORGANIZATION.ORDER.asc(), ORGANIZATION.CREATE_TIME.desc())
                        .fetch(r->{
                            HomeConfig homeConfig = new HomeConfig();
                            homeConfig.setId(r.getValue(HOME_CONFIG.ID));
                            homeConfig.setCreateTime(r.getValue(HOME_CONFIG.CREATE_TIME));
                            homeConfig.setName(r.getValue(HOME_CONFIG.NAME));
                            homeConfig.setOrganizationId(r.getValue(HOME_CONFIG.ORGANIZATION_ID));
                            homeConfig.setLogoPath(r.getValue(HOME_CONFIG.LOGO_PATH));
                            homeConfig.setLogo(r.getValue(HOME_CONFIG.LOGO));
                            homeConfig.setEnableHomeBrowse(r.getValue(HOME_CONFIG.ENABLE_HOME_BROWSE));
                            return homeConfig;
                            // 排序
                            //orgs.sort(OrganizationUtil.getOrgComparator());
                            //.stream().sorted(Comparator.comparing(HomeConfig::getCreateTime).reversed())).collect(Collectors.toList()
                        }));
    }


    @Override
    public int clearMongoCache(String userId, String userToken, String homeConfigId) {
        HomeConfig homeConfig = homeConfigDao.get(homeConfigId);
        ArrayList<Query> queries = new ArrayList<>();
        if (Objects.equals(HomeConfig.TYPE_INSTITUTIONS, homeConfig.getType())) {
            organizationDao.getOptional(homeConfig.getOrganizationId()).ifPresent(org -> {
                Query query = new Query();
                query.addCriteria(getCriteria(userId, userToken, org.getId()));
                queries.add(query);
            });
        }
        Query businessHomeQuery = new Query();
        businessHomeQuery.addCriteria(getCriteria(userId, userToken, homeConfigId));
        queries.add(businessHomeQuery);
        // 机构分院增加清除跟组件id关联的数据
        homeModuleConfigDao.execute(e -> e.select(HOME_MODULE_CONFIG.ID).from(HOME_MODULE_CONFIG)
                .where(HOME_MODULE_CONFIG.HOME_CONFIG_ID.eq(homeConfigId))).forEach(r -> {
            String moduleConfigId = r.getValue(HOME_MODULE_CONFIG.ID);
            Query query = new Query();
            query.addCriteria(getCriteria(userId, userToken, moduleConfigId));
            queries.add(query);
        });

        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, HomeCfgCacheItem.COLL_NAME);
        bulkOps.remove(queries);
        return bulkOps.execute().getRemovedCount();
    }

    @Override
    public void disableByOrganizationId(String organizationId, Integer homeType) {
        homeConfigDao.execute(e -> e.update(HOME_CONFIG).set(HOME_CONFIG.STATE, HomeConfig.STATE_DISABLE)
                .where(HOME_CONFIG.ORGANIZATION_ID.eq(organizationId), HOME_CONFIG.TYPE.eq(homeType)).execute());
    }

    @Override
    public HomeConfig update(String id, Integer enableBrowse, Optional<Integer> state, Optional<String> logoIdOptional, Optional<String> logoPathOptional,
                             String rootOrganizationId) {
        HomeConfig homeConfig = homeConfigDao.get(id);
        homeConfig.setEnableHomeBrowse(enableBrowse);
        state.ifPresent(homeConfig::setState);
        String logoId = homeConfig.getLogo();
        String logoPath = homeConfig.getLogoPath();
        if (logoPathOptional.isPresent() && logoIdOptional.isPresent()) {
            logoId = logoIdOptional.get();
            logoPath = logoPathOptional.get();
        }
        /*else if (logoId == null && logoPath == null) {
            //查询默认的根首页 替换logoPath
            Optional<HomeConfig> enableHomeConfig = getEnableHomeConfig(rootOrganizationId, homeConfig.getType());
            if (enableHomeConfig.isPresent()) {
                logoPath = enableHomeConfig.get().getLogoPath();
                logoId = enableHomeConfig.get().getLogo();
            }
        }*/
        // 约定remove清除首页logo
        if (Objects.equals(REMOVE, logoId) || Objects.equals(REMOVE, logoPath)) {
            logoId = null;
            logoPath = null;
        }
        homeConfig.setLogo(logoId);
        homeConfig.setLogoPath(logoPath);
        homeConfigDao.update(homeConfig);
        return homeConfig;
    }

    @Override
    public List<HomeConfig> findAll(List<String> list) {
        return homeConfigDao.fetch(HOME_CONFIG.ID.in(list).and(HOME_CONFIG.TYPE.eq(HomeConfig.TYPE_VIRTUAL_SPACE)));
    }


    private Criteria getCriteria(String userId, String userToken, String homeConfigId) {
        Criteria criteria = Criteria.where("userId").is(userId);
        if (!StringUtils.isEmpty(userToken)) {
            criteria = criteria.and("userToken").is(userToken);
        }
        if (!StringUtils.isEmpty(homeConfigId)) {
            criteria = criteria.and("homeCfgId").is(homeConfigId);
        }
        return criteria;
    }
}
