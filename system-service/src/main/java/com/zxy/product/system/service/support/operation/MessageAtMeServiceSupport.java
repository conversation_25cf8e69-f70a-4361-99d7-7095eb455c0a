package com.zxy.product.system.service.support.operation;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.operation.MessageAtMeService;
import com.zxy.product.system.entity.MessageAtMe;
import com.zxy.product.system.util.EncryptUtil;
import org.jooq.Condition;
import org.jooq.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zxy.product.system.jooq.Tables.MEMBER;
import static com.zxy.product.system.jooq.Tables.MESSAGE_AT_ME;


@Service
public class MessageAtMeServiceSupport implements MessageAtMeService {

    private CommonDao<MessageAtMe> messageAtMeDao;

    @Autowired
    public void setMessageAtMeDao(CommonDao<MessageAtMe> messageAtMeDao) {
        this.messageAtMeDao = messageAtMeDao;
    }

    @Override
    public PagedResult<MessageAtMe> findAtMe(Integer page, Integer pageSize, String currentUserId) {
        Condition condition = MESSAGE_AT_ME.RECEIVER_ID.eq(currentUserId);
        List<MessageAtMe> list = messageAtMeDao.execute(x -> x.select(
                Fields.start()
                        .add(MESSAGE_AT_ME)
                        .add(MEMBER.HEAD_PORTRAIT_PATH, MEMBER.ID, MEMBER.FULL_NAME)
                        .end())
                .from(MESSAGE_AT_ME)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(MESSAGE_AT_ME.SENDER_ID))
                .where(condition))
                .orderBy(MESSAGE_AT_ME.CREATE_TIME.desc())
                .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                    MessageAtMe messageAtMe = r.into(MessageAtMe.class);
                    messageAtMe.setSenderName(r.getValue(MEMBER.FULL_NAME));
                    messageAtMe.setSenderHeadPortrait(r.getValue(MEMBER.HEAD_PORTRAIT_PATH));
                    return messageAtMe;
                });

        Field<Integer> countId = MESSAGE_AT_ME.ID.count();
        Integer count = messageAtMeDao.execute(x -> x.select(
                Fields.start().add(countId).end())
                .from(MESSAGE_AT_ME).where(condition)).fetchOptional(countId).orElse(0);
        return PagedResult.create(count, list);
    }

    @Override
    public Integer getCount(String currentUserId) {
        return messageAtMeDao.execute(d -> d.select(MESSAGE_AT_ME.ID.count())
                .from(MESSAGE_AT_ME)
                .where(MESSAGE_AT_ME.RECEIVER_ID.eq(currentUserId),
                        MESSAGE_AT_ME.READ_STATUS.eq(MessageAtMe.NOT_READ))
                .fetchOne(MESSAGE_AT_ME.ID.count()));
    }

    @Override
    public String updateReadStatus(String ids) {
        messageAtMeDao.execute(d -> d.update(MESSAGE_AT_ME)
                .set(MESSAGE_AT_ME.READ_STATUS, MessageAtMe.HAS_READ)
                .where(MESSAGE_AT_ME.ID.in(ids.split(",")))
                .execute());
        return ids;
    }

    @Override
    public PagedResult<MessageAtMe> findAtMeSecurity(Integer page, Integer pageSize, String currentUserId) {
        Condition condition = MESSAGE_AT_ME.RECEIVER_ID.eq(currentUserId);
        List<MessageAtMe> list = messageAtMeDao.execute(x -> x.select(
                                Fields.start()
                                        .add(MESSAGE_AT_ME)
                                        .add(MEMBER.HEAD_PORTRAIT_PATH, MEMBER.ID, MEMBER.FULL_NAME)
                                        .end())
                        .from(MESSAGE_AT_ME)
                        .leftJoin(MEMBER).on(MEMBER.ID.eq(MESSAGE_AT_ME.SENDER_ID))
                        .where(condition))
                .orderBy(MESSAGE_AT_ME.CREATE_TIME.desc())
                .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                    MessageAtMe messageAtMe = r.into(MessageAtMe.class);
                    messageAtMe.setSenderName(EncryptUtil.aesEncrypt(r.getValue(MEMBER.FULL_NAME), null));
                    messageAtMe.setSenderHeadPortrait(r.getValue(MEMBER.HEAD_PORTRAIT_PATH));
                    return messageAtMe;
                });

        Field<Integer> countId = MESSAGE_AT_ME.ID.count();
        Integer count = messageAtMeDao.execute(x -> x.select(
                        Fields.start().add(countId).end())
                .from(MESSAGE_AT_ME).where(condition)).fetchOptional(countId).orElse(0);
        return PagedResult.create(count, list);
    }

}
