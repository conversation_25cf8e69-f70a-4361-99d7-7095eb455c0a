package com.zxy.product.system.service.support.premission;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.api.permission.OrganizationService;
import com.zxy.product.system.content.ErrorCode;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.content.SystemConstant;
import com.zxy.product.system.entity.*;
import com.zxy.product.system.util.CodeUtils;
import com.zxy.product.system.util.DateUtil;
import com.zxy.product.system.util.OrganizationUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.system.jooq.Tables.*;
import static java.util.UUID.randomUUID;


/**
 * @user tianjun
 * @date 16/6/15
 */
@Service
public class GrantServiceSupport implements GrantService {
	private static final Logger LOGGER = LoggerFactory.getLogger(GrantServiceSupport.class);
    public static final String CACHE_GRANTED = "cachedGranted#";
	public static final Map<String, String> OPERATOR_TYPE_MAP = ImmutableMap.of("0", "完全控制", "1", "可读", "2", "可编辑", "3", "可删除", "4", "其他");

	private CommonDao<Role> roleDao;
	private CommonDao<Member> memberDao;
	private CommonDao<Menu> menuDao;
    private CommonDao<Grant> grantDao;
    private CommonDao<RoleMenu> roleMenuDao;
    private CommonDao<GrantDetail> grantDetailDao;
	private CommonDao<GrantMember> grantMemberDao;
    private CommonDao<Organization> organizationDao;
    private CommonDao<OrganizationDetail> organizationDetailDao;
    private CommonDao<GrantOrganization> grantOrganizationDao;
	private OrganizationService organizationService;
    private MessageSender sender;
    private Cache cache;

	@Autowired
	public void setOrganizationService(OrganizationService organizationService) {
		this.organizationService = organizationService;
	}

	@Autowired
	public void setMemberDao(CommonDao<Member> memberDao) {
		this.memberDao = memberDao;
	}

	@Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("grant");
    }

    @Autowired
    public void setGrantDao(CommonDao<Grant> grantDao) {
		this.grantDao = grantDao;
	}
    @Autowired
	public void setGrantDetailDao(CommonDao<GrantDetail> grantDetailDao) {
		this.grantDetailDao = grantDetailDao;
	}
    @Autowired
	public void setGrantMemberDao(CommonDao<GrantMember> grantMemberDao) {
		this.grantMemberDao = grantMemberDao;
	}
    @Autowired
	public void setOrganizationDetailDao(CommonDao<OrganizationDetail> organizationDetailDao) {
		this.organizationDetailDao = organizationDetailDao;
	}
    @Autowired
	public void setRoleDao(CommonDao<Role> roleDao) {
		this.roleDao = roleDao;
	}
    @Autowired
	public void setOrganizationDao(CommonDao<Organization> organizationDao) {
		this.organizationDao = organizationDao;
	}
    @Autowired
    public void setRoleMenuDao(CommonDao<RoleMenu> roleMenuDao) {
        this.roleMenuDao = roleMenuDao;
    }
    @Autowired
    public void setMenuDao(CommonDao<Menu> menuDao) {
        this.menuDao = menuDao;
    }
    @Autowired
	public void setSender(MessageSender sender) {
		this.sender = sender;
	}

    @Autowired
	public void setGrantOrganizationDao(CommonDao<GrantOrganization> grantOrganizationDao) {
		this.grantOrganizationDao = grantOrganizationDao;
	}
	@Override
    public  List<Grant> insert(String memberId, String organizationId, String roleIds, String memberIds, String organizationIds, Optional<String> indeterminates, Optional<String>  childFinds , String operatorTypes, Optional<Integer> validType, Optional<Long> validDate, Optional<Integer> notifyFlag, Optional<String> notifyType){
		// 新的授权管理不允许同一个人新增相同角色
		String[] members = memberIds.split(SystemConstant.COMMA);
		String[] roles = roleIds.split(SystemConstant.COMMA);
		List<Grant> grants = grantDao.execute(d -> d.select(GRANT.ID, GRANT.VALID_DATE)
											  .from(GRANT)
											  .innerJoin(GRANT_MEMBER).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
											  .where(GRANT.ROLE_ID.in(roles), GRANT_MEMBER.MEMBER_ID.in(members))
											  .fetchInto(Grant.class));

		List<String> expiredGrantIds = grants.stream().filter(g -> g.getValidDate() != null && g.getValidDate() < System.currentTimeMillis())
									 .map(Grant::getId).collect(Collectors.toList());
		grantDao.delete(expiredGrantIds);
		List<String> validGrantIds = grants.stream().filter(g -> g.getValidDate() == null || g.getValidDate() >= System.currentTimeMillis())
									 .map(Grant::getId).collect(Collectors.toList());
		if (validGrantIds != null && validGrantIds.size() > 0) {
			throw new UnprocessableException(ErrorCode.GrantRoleRepeat);
		}
		// 新的授权管理，一个人一条授权记录，memberIds单个人循环插入
		List<Grant> resultList = new ArrayList<>();
		for (String mId : members) {
			//授权后置业务对接了金库业务与授权记录等业务处理且目前系统目前限制最多10个,改为批处理业务改动点较多.
			for (String roleId : roles) {
				Grant g = doInsertGrant(memberId, organizationId, roleId, mId, organizationIds, indeterminates, childFinds, operatorTypes, validType, validDate, notifyFlag,
						notifyType);
				resultList.add(g);
			}

		}
		return resultList;
	}

	private Grant doInsertGrant(String memberId, String organizationId, String roleId, String memberIds, String organizationIds, Optional<String> indeterminates,
						   Optional<String> childFinds, String operatorTypes, Optional<Integer> validType, Optional<Long> validDate, Optional<Integer> notifyFlag,
						   Optional<String> notifyType) {
		// 添加授权表
		Grant g = new Grant();
		g.forInsert();
		g.setCreateMemberId(memberId);
		g.setOrganizationId(organizationId);
		g.setRoleId(roleId);
		g.setOperatorTypes(operatorTypes);
		g.setValidType(validType.orElse(Grant.VALID_TYPE_FOREVER));
		if (Objects.equals(validType.orElse(null), Grant.VALID_TYPE_FOREVER)) {
			g.setValidDate(null);
		} else {
			validDate.ifPresent(g::setValidDate);
		}
		g.setNotifyFlag(Grant.NOTIFY_YES);
		// 小于等于7天的授权不提醒
		if (!Objects.equals(validType.orElse(null), Grant.VALID_TYPE_THREE_DAY) || Objects.equals(validType.orElse(null), Grant.VALID_TYPE_CUSTOM)) {
			if (validDate.orElse(System.currentTimeMillis()) - System.currentTimeMillis() < 7 * 24 * 60 * 60 * 1000) {
				g.setNotifyFlag(Grant.NOTIFY_NO);
			}
		}
		notifyType.ifPresent(g::setNotifyType);
		g.setRevokeNotifyFlag(Grant.REVOKE_NOTIFY_NO);
		grantDao.insert(g);

		insertGrantMember(g.getId(), memberIds);

		sender.send(MessageTypeContent.SYSTEM_GRANT_INSERT,
				MessageHeaderContent.ID, g.getId(),
				MessageHeaderContent.MEMBER_IDS, memberIds,
				MessageHeaderContent.ORGANIZATION_IDS, organizationIds,
				MessageHeaderContent.INDETERMINATES, indeterminates.orElse(""),
				MessageHeaderContent.CHILD_FINDS, childFinds.orElse(""));

		StringBuilder operatorTypeStr = new StringBuilder();
		if (org.springframework.util.StringUtils.hasText(operatorTypes)) {
			if (operatorTypes.contains("0")) {
				operatorTypeStr.append("完全控制、");
			} else {
				for (String operatorType : operatorTypes.split(SystemConstant.COMMA)) {
					operatorTypeStr.append(OPERATOR_TYPE_MAP.get(operatorType)).append("、");
				}
			}
		}
		sendGrantHistorySaveMsg(memberIds, memberId, roleId, validType, validDate,
			"%s由【%s】授予了【%s】授权，授权类型为【" + operatorTypeStr.substring(0, operatorTypeStr.length() - 1) + "】，授权有效期至【%s】", GrantHistory.OPERATION_TYPE_GRANT);
		return g;
	}

	private static String getDateStr(Optional<Long> time) {
		if (!time.isPresent()) {
			return SystemConstant.EMPTY;
		}
		return DateUtil.dateLongToString(time.get(), DateUtil.YYYY_MM_DD);
	}

	@Override
    public Grant update(String currentUserId, String grantId, String organizationId, String roleIds, String memberIds, String organizationIds, Optional<String> indeterminates,
						Optional<String> childFinds, String operatorTypes, Optional<Integer> validType, Optional<Long> validDate, Optional<Integer> notifyFlag,
						Optional<String> notifyType, String currentMemberName) {
		return doUpdateGrant(currentUserId, grantId, organizationId, roleIds, memberIds, organizationIds, indeterminates, childFinds, operatorTypes, validType, validDate, notifyFlag,
			notifyType, currentMemberName);
	}

	private Grant doUpdateGrant(String currentUserId, String grantId, String organizationId, String roleIds, String memberIds, String organizationIds,
								Optional<String> indeterminates,
								Optional<String> childFinds, String operatorTypes, Optional<Integer> validType, Optional<Long> validDate, Optional<Integer> notifyFlag,
								Optional<String> notifyType, String currentMemberName) {
		Optional<Grant> grantOptional = doGetGrant(grantId, Optional.empty());

		grantOptional.ifPresent(grant -> {
			Integer oldValidType = grant.getValidType();
			Long oldValidDate = grant.getValidDate();
			String oldOperatorTypes = grant.getOperatorTypes();
			List<Organization> oldOrganizations = grant.getOrganizations();
			// 修改主记录
			grant.setOperatorTypes(operatorTypes);
			grant.setRoleId(roleIds);
			grant.setOrganizationId(organizationId);
			validType.ifPresent(grant::setValidType);
			if (Objects.equals(validType.orElse(null), Grant.VALID_TYPE_FOREVER)) {
				grant.setValidDate(null);
			} else {
				validDate.ifPresent(grant::setValidDate);
			}

			notifyFlag.ifPresent(flag -> {
				if (Objects.equals(flag, Grant.NOTIFY_NO)) {
					grant.setNotifyFlag(Grant.NOTIFY_NO);
				} else if (Objects.equals(flag, Grant.NOTIFY_YES)) {
					boolean needsValidation = validType
							.map(type -> Objects.equals(type, Grant.VALID_TYPE_THREE_DAY) || Objects.equals(type, Grant.VALID_TYPE_CUSTOM))
							.orElse(false);

					if (needsValidation) {
						// 校验有效期是否小于等于7天不通知
						if (validDate.isPresent() && (validDate.get() - System.currentTimeMillis()) <= 7L * 24 * 60 * 60 * 1000) {
							grant.setNotifyFlag(Grant.NOTIFY_NO);
						} else {
							grant.setNotifyFlag(Grant.NOTIFY_YES);
						}
					} else {
						// 如果不需要校验，直接设置为通知
						grant.setNotifyFlag(Grant.NOTIFY_YES);
					}
				}
			});

			notifyType.ifPresent(grant::setNotifyType);
			if (!Objects.equals(oldValidDate, grant.getValidDate()) || !Objects.equals(oldValidType, grant.getValidType())) {
				grant.setRevokeNotifyFlag(Grant.REVOKE_NOTIFY_NO);
			}
			grantDao.update(grant);

			List<Grant> grantMembers = this.findGrantMembers(grantId);
			List<String> addedMemberIds = updateGrantMember(grantId, memberIds);

			sender.send(MessageTypeContent.SYSTEM_GRANT_UPDATE,
					MessageHeaderContent.ID, grantId,
					MessageHeaderContent.MEMBER_IDS, memberIds,
					MessageHeaderContent.ADDED_MEMBER_IDS, Joiner.on(",").join(addedMemberIds),
					MessageHeaderContent.ORGANIZATION_IDS, organizationIds,
					MessageHeaderContent.INDETERMINATES, indeterminates.orElse(""),
					MessageHeaderContent.CHILD_FINDS, childFinds.orElse(""));

			if (!org.springframework.util.CollectionUtils.isEmpty(grantMembers)) {
				sendGrantHistoryDeleteMsg(memberIds, grant, grantMembers, currentMemberName);
			}
			if (!Objects.equals(oldValidDate, grant.getValidDate()) || !Objects.equals(oldValidType, grant.getValidType())) {
				sendGrantHistorySaveMsg(memberIds, currentUserId, grant.getRoleId(), validType, validDate, "%s由【%s】续期了【%s】授权，授权有效期至【%s】", GrantHistory.OPERATION_TYPE_RENEW);
			}
			if (!Objects.equals(oldOperatorTypes, grant.getOperatorTypes())) {
				StringBuilder operatorTypeStr = new StringBuilder();
				if (org.springframework.util.StringUtils.hasText(operatorTypes)) {
					if (operatorTypes.contains("0")) {
						operatorTypeStr.append("完全控制、");
					} else {
						for (String operatorType : operatorTypes.split(SystemConstant.COMMA)) {
							operatorTypeStr.append(OPERATOR_TYPE_MAP.get(operatorType)).append("、");
						}
					}
				}
				sendGrantHistorySaveMsg(memberIds, currentUserId, grant.getRoleId(), validType, validDate,
					"%s由【%s】修改了授权类型，修改后的授权类型为【" + operatorTypeStr.substring(0, operatorTypeStr.length() - 1) + "】", GrantHistory.OPERATION_TYPE_UPDATE_OPERATION_TYPE);
			}
			if (!compareOrganizations(oldOrganizations, childFinds, organizationIds).isEmpty()) {
				sendGrantHistorySaveMsg(memberIds, currentUserId, grant.getRoleId(), validType, validDate,
					"%s由【%s】修改了授权节点，修改后的授权节点为【" + handleOrganizationName(childFinds, organizationIds) + "】", GrantHistory.OPERATION_TYPE_UPDATE_GRANT_NODE);
			}
		});

		return grantOptional.orElse(null);
	}

	public static List<Organization> compareOrganizations(List<Organization> oldOrganizations, Optional<String> childFinds, String organizationIds) {
		List<Organization> result = new ArrayList<>();
		List<String> includeList = Lists.newArrayList(childFinds.map(c -> c.split(SystemConstant.COMMA)).orElse(new String[0]));
		List<String> notIncludeList = Lists.newArrayList(organizationIds.split(SystemConstant.COMMA));
		oldOrganizations.forEach(o -> {
			if (Organization.CHILD_FIND_YES.toString().equals(o.getChildFind())) {
				if (!includeList.contains(o.getId())) {
					result.add(o);
				}
			} else {
				if (!notIncludeList.contains(o.getId())) {
					result.add(o);
				}
			}
		});
		return result;
	}

	private String handleOrganizationName(Optional<String> childFinds, String organizationIds) {
		List<String> organizations = Lists.newArrayList(organizationIds.split(SystemConstant.COMMA));
		organizations.addAll(Lists.newArrayList(childFinds.map(c -> c.split(SystemConstant.COMMA)).orElse(new String[0])));
		Map<String, Organization> organizationMap = organizationService.findOrganizationByIds(organizations).stream()
																	   .collect(Collectors.toMap(Organization::getId, e -> e, (o1, o2) -> o1));
		List<String> result = new ArrayList<>();
		for (String organizationId : organizationIds.split(SystemConstant.COMMA)) {
			result.add(organizationMap.getOrDefault(organizationId, new Organization()).getName());
		}
		childFinds.ifPresent(child -> {
			for (String childFind : child.split(SystemConstant.COMMA)) {
				result.add(String.format("%s（%s）", organizationMap.getOrDefault(childFind, new Organization()).getName(), "包含子节点"));
			}
		});
		return String.join(SystemConstant.COMMA, result);
	}

	@Override
	public Grant addMember(String currentUserId, String grantId, String organizationId, String roleIds, String memberIds, String organizationIds, Optional<String> indeterminates,
						   Optional<String> childFinds, String operatorTypes, Optional<Integer> validType, Optional<Long> validDate, Optional<Integer> notifyFlag,
						   Optional<String> notifyType, String currentMemberName) {
		Map<String, Grant> existMap = new HashMap<>();
		String[] members = memberIds.split(SystemConstant.COMMA);
		grantMemberDao.execute(d -> d.select(GRANT.ID, GRANT.ROLE_ID, GRANT_MEMBER.MEMBER_ID)
									 .from(GRANT_MEMBER)
									 .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
									 .where(GRANT.ROLE_ID.eq(roleIds), GRANT_MEMBER.MEMBER_ID.in(members)))
					  .forEach(r -> {
						  Grant g = new Grant();
						  g.setId(r.getValue(GRANT.ID));
						  g.setRoleId(r.getValue(GRANT.ROLE_ID));
						  g.setMemberId(r.getValue(GRANT_MEMBER.MEMBER_ID));
						  existMap.put(r.getValue(GRANT.ROLE_ID) + r.getValue(GRANT_MEMBER.MEMBER_ID), g);
					  });
		for (String mId : members) {
			String key = roleIds + mId;
			if (existMap.containsKey(key)) {
				doUpdateGrant(currentUserId, existMap.get(key).getId(), organizationId, roleIds, mId, organizationIds, indeterminates, childFinds, operatorTypes, validType, validDate,
					notifyFlag, notifyType, currentMemberName);
			} else {
				doInsertGrant(currentUserId, organizationId, roleIds, mId, organizationIds, indeterminates, childFinds, operatorTypes, validType, validDate, notifyFlag,
					notifyType);
			}
		}
		return null;
	}

	private void sendGrantHistoryDeleteMsg(String memberIds, Grant grant, List<Grant> grantMembers, String currentMemberName) {
		HashSet<String> memberSet = Sets.newHashSet(memberIds.split(SystemConstant.COMMA));
		long now = System.currentTimeMillis();
		grantMembers.stream().filter(g -> !memberSet.contains(g.getMemberId())).forEach(gm->{
			String content = String.format("%s由【%s】撤销了【%s】授权", DateUtil.dateLongToString(now, DateUtil.YYYY_MM_DD_HH_MM),
				currentMemberName, gm.getRoleName());
			sender.send(MessageTypeContent.SYSTEM_GRANT_HISTORY_SAVE,
				MessageHeaderContent.CREATE_MEMBER_ID, grant.getCreateMemberId(),
				MessageHeaderContent.MEMBER_ID, gm.getMemberId(),
				MessageHeaderContent.SYSTEM_TIME, String.valueOf(now),
				MessageHeaderContent.OPTER_TYPE, GrantHistory.OPERATION_TYPE_REVOKE.toString(),
				MessageHeaderContent.CONTENT, content);
		});
	}

	private void sendGrantHistorySaveMsg(String memberIds, String currentUserId, String roleId, Optional<Integer> validType, Optional<Long> validDate, String format,
										 Integer operationType) {
		ArrayList<String> memberList = Lists.newArrayList(memberIds.split(SystemConstant.COMMA));
		memberList.add(currentUserId);
		Map<String, Member> memberMap = memberDao.execute(d -> d.select(MEMBER.ID, MEMBER.FULL_NAME)
																.from(MEMBER).where(MEMBER.ID.in(memberList))
																.fetch(r -> {
																	Member member = new Member();
																	member.setId(r.getValue(MEMBER.ID));
																	member.setFullName(r.getValue(MEMBER.FULL_NAME));
																	return member;
																})).stream().collect(Collectors.toMap(Member::getId, e -> e, (o1, o2) -> o1));
		String roleName = roleDao.execute(d -> d.select(ROLE.NAME)
												.from(ROLE).where(ROLE.ID.eq(roleId))
												.fetchOne(ROLE.NAME));
		long now = System.currentTimeMillis();
		for (String mId : memberIds.split(SystemConstant.COMMA)) {
			String validDateStr = validType.isPresent() && Objects.equals(validType.get(), Grant.VALID_TYPE_FOREVER)? "永久": getDateStr(validDate);
			String content = String.format(format, DateUtil.dateLongToString(now, DateUtil.YYYY_MM_DD_HH_MM),
				memberMap.getOrDefault(currentUserId, new Member()).getFullName(), roleName, validDateStr);
			sender.send(MessageTypeContent.SYSTEM_GRANT_HISTORY_SAVE,
				MessageHeaderContent.CREATE_MEMBER_ID, currentUserId,
				MessageHeaderContent.MEMBER_ID, mId,
				MessageHeaderContent.SYSTEM_TIME, String.valueOf(now),
				MessageHeaderContent.OPTER_TYPE, operationType.toString(),
				MessageHeaderContent.VALID_DATE, validDate.map(Object::toString).orElse(SystemConstant.EMPTY),
				MessageHeaderContent.CONTENT, content);
		}
	}

	@Override
    public Integer delete(String grantId, String currentUserId, String currentUserFullName) {
		List<Grant> grantMembers = this.findGrantMembers(grantId);
		int result = grantDao.delete(grantId);
		long now = System.currentTimeMillis();
		Lists.partition(grantMembers, SystemConstant.BATCH_SIZE).forEach(list -> sender.send(MessageTypeContent.SYSTEM_GRANT_DELETE,
			MessageHeaderContent.ID, grantId,
			MessageHeaderContent.SYSTEM_TIME, String.valueOf(now),
			MessageHeaderContent.MEMBER_IDS, Joiner.on(",").join(list.stream().map(Grant::getMemberId).collect(Collectors.toList()))));
		if (org.springframework.util.CollectionUtils.isEmpty(grantMembers)) {
			return result;
		}
		grantMembers.forEach(grant -> {
			String content = String.format("%s由【%s】撤销了【%s】授权", DateUtil.dateLongToString(now, DateUtil.YYYY_MM_DD_HH_MM),
				currentUserFullName, grant.getRoleName());
			sender.send(MessageTypeContent.SYSTEM_GRANT_HISTORY_SAVE,
				MessageHeaderContent.CREATE_MEMBER_ID, currentUserId,
				MessageHeaderContent.MEMBER_ID, grant.getMemberId(),
				MessageHeaderContent.SYSTEM_TIME, String.valueOf(now),
				MessageHeaderContent.OPTER_TYPE, GrantHistory.OPERATION_TYPE_REVOKE.toString(),
				MessageHeaderContent.CONTENT, content);
		});
        return result;
    }

	@Override
	public List<Grant> findGrantMembers(String grantId) {
		return grantDao.execute(d -> d.select(MEMBER.ID, MEMBER.FULL_NAME, ROLE.NAME)
									  .from(GRANT)
									  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
									  .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
									  .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
									  .where(GRANT.ID.eq(grantId))
									  .fetch(r -> {
										  Grant grant = new Grant();
										  grant.setMemberId(r.getValue(MEMBER.ID));
										  grant.setMemberFullName(r.getValue(MEMBER.FULL_NAME));
										  grant.setRoleName(r.getValue(ROLE.NAME));
										  return grant;
									  }));
	}

	@Override
    public Integer deleteGrantMember(String grantMemberId) {
    	return grantMemberDao.getOptional(grantMemberId).map(gm -> {
    		String grantId = gm.getGrantId();
    		String memberId = gm.getMemberId();

    		grantMemberDao.delete(GRANT_MEMBER.GRANT_ID.eq(grantId).and(GRANT_MEMBER.MEMBER_ID.eq(memberId)));
//    		grantDetailDao.delete(GRANT_DETAIL.GRANT_ID.eq(grantId).and(GRANT_DETAIL.MEMBER_ID.eq(memberId)));

    		sender.send(MessageTypeContent.SYSTEM_GRANT_MEMBER_CLEAR, MessageHeaderContent.ID, grantId,
    				MessageHeaderContent.MEMBER_ID, memberId);
    		return 1;
    	}).orElse(0);
    }

    @Override
    public Optional<Grant> getOptional(String grantId) {
		return doGetGrant(grantId, Optional.empty());
	}

	@Override
	public Optional<Grant> getOptional(String grantId, Optional<String> memberId) {
		return doGetGrant(grantId, memberId);
	}

	private Optional<Grant> doGetGrant(String grantId, Optional<String> memberId) {
		List<Condition> conditions = Stream.of(
			Optional.of(GRANT.ID.eq(grantId)),
			Optional.of(ROLE.ID.isNotNull()),
			memberId.map(GRANT_MEMBER.MEMBER_ID::eq)
		).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
		com.zxy.product.system.jooq.tables.Organization GRANT_ORG = ORGANIZATION.as("org");
		com.zxy.product.system.jooq.tables.Organization ROLE_ORG = ORGANIZATION.as("role_org");

		Result<Record20<String, String, String, String, String, String, String, String, String, String, String, String, String, String, Integer, Integer, Long, Integer, String, Long>> details =
				grantDetailDao.execute(ctx -> ctx.select(
						GRANT.ID,
						GRANT.OPERATOR_TYPES,
						ROLE.ID,
						ROLE.NAME,
						ROLE_ORG.ID,
						ROLE_ORG.NAME,
						GRANT_ORG.ID,
						GRANT_ORG.NAME,
						MEMBER.ID,
						MEMBER.NAME,
						MEMBER.FULL_NAME,
						ORGANIZATION.ID,
						ORGANIZATION.NAME,
						ORGANIZATION.PARENT_ID,
						GRANT_ORGANIZATION.CHILD_FIND,
						GRANT.VALID_TYPE,
						GRANT.VALID_DATE,
						GRANT.NOTIFY_FLAG,
						GRANT.NOTIFY_TYPE, GRANT.CREATE_TIME).from(GRANT)
												 .leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
												 .leftJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))

												 .leftJoin(GRANT_ORGANIZATION).on((GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID)))
												 .leftJoin(ORGANIZATION).on(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID))

												 .leftJoin(ROLE).on(GRANT.ROLE_ID.eq(ROLE.ID))
												 .leftJoin(ROLE_ORG).on(ROLE_ORG.ID.eq(ROLE.ORGANIZATION_ID))
												 .leftJoin(GRANT_ORG).on(GRANT.ORGANIZATION_ID.eq(GRANT_ORG.ID))
												 .where(conditions).fetch()
		);

		if(details.isEmpty()) {
			return Optional.empty();
		}

		Grant g = new Grant();
		g.setId(details.getValue(0, GRANT.ID));
		g.setOperatorTypes(details.getValue(0, GRANT.OPERATOR_TYPES));
		g.setOrganizationId(details.getValue(0,GRANT_ORG.ID));
		g.setValidType(details.getValue(0, GRANT.VALID_TYPE));
		g.setValidDate(details.getValue(0, GRANT.VALID_DATE));
		g.setNotifyFlag(details.getValue(0, GRANT.NOTIFY_FLAG));
		g.setNotifyType(details.getValue(0, GRANT.NOTIFY_TYPE));
		g.setCreateTime(details.getValue(0, GRANT.CREATE_TIME));

		Organization org = new Organization();
		org.setId(details.getValue(0,GRANT_ORG.ID));
		org.setName(details.getValue(0, GRANT_ORG.NAME));
		g.setOrganization(org);

		Role role = new Role();
		Organization roleOrg = new Organization();
		roleOrg.setId(details.getValue(0,ROLE_ORG.ID));
		roleOrg.setName(details.getValue(0, ROLE_ORG.NAME));
		role.setOrganization(roleOrg);

		role.setId(Optional.ofNullable(details.getValue(0, ROLE.ID)).orElse(""));
		role.setName(Optional.ofNullable(details.getValue(0, ROLE.NAME)).orElse(""));
		g.setRole(role);

		List<Member> members = details.into(Member.class).stream().filter(m -> m.getId() != null).collect(Collectors.toList());
		g.setMembers(clearRepeatMember(members));

		Map<String, Organization> organizations = details.stream().collect(HashMap::new, (map,r) -> {
			Organization m = new Organization();
			m.setId(r.getValue(ORGANIZATION.ID));
			m.setName(r.getValue(ORGANIZATION.NAME));
			m.setName(r.getValue(ORGANIZATION.NAME));
			m.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
			m.setChildFind(r.getValue(GRANT_ORGANIZATION.CHILD_FIND)+"");
			map.put(m.getId(),m);
		}, Map::putAll);
		List<Organization> orgs = new ArrayList<>(organizations.values());
		orgs.sort((o1, o2) -> {
			if (o1.getOrder() == null && o2.getOrder() == null) {
				return 0;
			} else if(o1.getOrder() == null) {
				return 1;
			} else if (o2.getOrder() == null) {
				return -1;
			} else if (o1.getOrder().intValue() == o2.getOrder().intValue()){
				return 0;
			} else {
				return (o1.getCreateTime() - o2.getCreateTime()) > 0l ? -1 : 1; // long类型不要强转成int
			}
		});
		g.setOrganizations(orgs);
		return Optional.of(g);
	}

	@Override
	public Optional<Grant> getByMemberId(String grantId, String memberId) {
		return doGetGrant(grantId, Optional.of(memberId));
	}

	@Override
	public String findGrantMemberFullNames(String grantId) {
		List<String> list = grantDetailDao.execute(d ->
				d.select(MEMBER.FULL_NAME)
						.from(GRANT)
						.leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
						.leftJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
						.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
						.where(GRANT.ID.eq(grantId).and(MEMBER.ID.isNotNull()).and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())))
						.fetch(MEMBER.FULL_NAME));

		if (CollectionUtils.isNotEmpty(list)) {
			return list.stream().map(String::trim).collect(Collectors.joining(","));
		}
		return "";
	}

    @Override
    public PagedResult<Member> findGrantMembers(String memberId, int page, int pageSize, String grantId) {
        Function<SelectSelectStep<?>, SelectConditionStep<?>> ss = t -> t.from(GRANT)
                .leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                .leftJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                .where(GRANT.ID.eq(grantId).and(MEMBER.ID.isNotNull()).and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())));

        return PagedResult.create(
                grantDetailDao.execute(d -> ss.apply(d.select(MEMBER.ID.countDistinct())).fetchOne(MEMBER.ID.countDistinct())),
                grantDetailDao.execute(d ->
                    ss.apply(d.select(GRANT_MEMBER.ID, MEMBER.ID, MEMBER.NAME, MEMBER.FULL_NAME, MEMBER.COMPANY_ID, MEMBER.ORGANIZATION_ID, ORGANIZATION.NAME))
                    .orderBy(MEMBER.CREATE_TIME.desc())
                    .limit((page - 1) * pageSize, pageSize)
                ).fetch().map(x -> {
                    Member member = new Member();
                    member.setId(x.getValue(MEMBER.ID));
                    member.setName(x.getValue(MEMBER.NAME));
                    member.setFullName(x.getValue(MEMBER.FULL_NAME));
                    member.setCompanyId(x.getValue(MEMBER.COMPANY_ID));
                    member.setHeadPortrait(x.getValue(GRANT_MEMBER.ID)); // 此处借用些字段存下grantMemberId.
                    member.setSelf(memberId.equals(member.getId()));

                    Organization organization = new Organization();
                    organization.setId(x.getValue(MEMBER.ORGANIZATION_ID));
                    organization.setName(x.getValue(ORGANIZATION.NAME));
                    member.setOrganization(organization);
                    return member;
                })
        );
    }
    @Override
    public PagedResult<Grant> find(int page, int pageSize, Optional<String> nodeId, Optional<String> roleName, Optional<Long> start, Optional<Long> end,
								   Optional<String> operatorType, String memberId, Integer contain, Optional<Integer> type, String rootOrganizationId) {

	    List<Condition> conditions = Stream.of(
			roleName.map(ROLE.NAME::contains),
			start.map(GRANT.CREATE_TIME::ge),
			end.map(GRANT.CREATE_TIME::lt),
			type.map(t-> {
				if (Objects.equals(t, Role.TYPE_ELEMENT)) {
					return ROLE.TYPE.eq(t).and(MENU.URI.isNotNull()).and(MENU.URI.ne(""));
				}
				return ROLE.TYPE.eq(t);
			}),
			operatorType.map(GRANT.OPERATOR_TYPES::eq)
			).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

		Map<String, Set<String>> grantOrganizationPathMap = this.findGrantOrganizationByUri(memberId, Grant.URI, rootOrganizationId);

		// 自己的授权
		List<String> grantIds = grantDao.execute(d -> d.selectDistinct(GRANT_MEMBER.GRANT_ID)
													   .from(GRANT_MEMBER)
													   .where(GRANT_MEMBER.MEMBER_ID.eq(memberId)).fetch(GRANT_MEMBER.GRANT_ID));
		List<Condition> grantConditions = new ArrayList<>();
		CodeUtils.generateOrganizationConditions(grantOrganizationPathMap, grantConditions);
		Condition grantCondition = grantConditions.stream().reduce(Condition::and).orElse(DSL.trueCondition()).or(GRANT.ID.in(grantIds)).or(ROLE.CHILD_FLAG.eq(Role.CHILD_FLAG_YES));
		conditions.add(grantCondition);

        Function<SelectSelectStep<?>, SelectConditionStep<?>> ss = t -> t.from(ROLE)
    			.leftJoin(GRANT).on(ROLE.ID.eq(GRANT.ROLE_ID))
			    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ROLE.ORGANIZATION_ID))
			    .leftJoin(MENU).on(MENU.ID.eq(ROLE.MENU_ID))
    			.leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
    			.where(conditions)
				// 历史数据存在role角色没关联菜单的数据，需要过滤掉
				.andExists(DSL.select(ROLE_MENU.ID).from(ROLE_MENU).where(ROLE_MENU.ROLE_ID.eq(ROLE.ID)).limit(1));

		PagedResult<Grant> grantPagedResult = PagedResult.create(
			roleDao.execute(d -> ss.apply(d.select(DSL.countDistinct(ROLE.ID))).fetchOne(DSL.countDistinct(ROLE.ID))),
			roleDao.execute(d ->
				ss.apply(d.selectDistinct(ROLE.ID, ROLE.PARENT_ID, ROLE.DESC, ROLE.TYPE, ROLE.CHILD_FLAG, ROLE.NAME, ROLE.ORDER,
					   MENU.NAME))
				  .orderBy(ROLE.ORDER.asc(), ROLE.CREATE_TIME.desc())
				  .limit((page - 1) * pageSize, pageSize)
			).fetch().map(x -> {
				Role r = new Role();
				r.setId(x.getValue(ROLE.ID));
				r.setName(x.getValue(ROLE.NAME));
				r.setDesc(x.getValue(ROLE.DESC));
				r.setType(x.getValue(ROLE.TYPE));
				r.setParentId(x.getValue(ROLE.PARENT_ID));
				r.setChildFlag(x.getValue(ROLE.CHILD_FLAG));
				r.setOrder(x.getValue(ROLE.ORDER));
				if (Objects.equals(r.getType(), Role.TYPE_ELEMENT)) {
					r.setName(x.getValue(MENU.NAME));
				}

				Grant g = new Grant();
				g.setRole(r);
				return g;
			})
		);
		List<Grant> items = grantPagedResult.getItems();
		if (org.springframework.util.CollectionUtils.isEmpty(items)) {
			return grantPagedResult;
		}
		com.zxy.product.system.jooq.tables.Organization memberOrgTable = ORGANIZATION.as("member_org");
		Map<String, Integer> roleCountMap = new HashMap<>();

		List<String> roleIds = items.stream().map(e -> Optional.ofNullable(e.getRole()).map(Role::getId).orElse(null))
									.filter(Objects::nonNull).collect(Collectors.toList());
		// 查询roleIds里面每个有多少人
		grantMemberDao.execute(d -> d.select(GRANT.ROLE_ID, GRANT_MEMBER.MEMBER_ID.countDistinct())
									 .from(GRANT_MEMBER)
									 .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
									 .where(GRANT.ROLE_ID.in(roleIds))
									 .groupBy(GRANT.ROLE_ID)).fetch(r -> {
			String key = r.getValue(GRANT.ROLE_ID);
			roleCountMap.put(key, r.getValue(GRANT_MEMBER.MEMBER_ID.countDistinct()));
			return key;
		});
		Map<String, String> roleParentNameMap = roleDao.execute(d -> d.select(ROLE.ID, ROLE.NAME)
															.from(ROLE)
															.where(ROLE.ID.in(items.stream().map(e -> Optional.ofNullable(e.getRole()).map(Role::getParentId).orElse(null))
																				   .filter(Objects::nonNull).distinct().collect(Collectors.toList())))
															.fetchMap(ROLE.ID, ROLE.NAME));
		items.forEach(r -> {
			Role role = r.getRole();
			if (role == null) {
				return;
			}
			role.setMemberCount(roleCountMap.getOrDefault(role.getId(), 0));
			role.setParentName(roleParentNameMap.get(role.getParentId()));
		});
		return grantPagedResult;
    }

    @Override
    public Grant find(String grantId) {
        return grantDao.get(grantId);
    }

//    @Override
//    public int findDetailCount(String grantId) {
//    	return grantDetailDao.count(GRANT_DETAIL.GRANT_ID.eq(grantId));
//    }
//
//    @Override
//    public List<GrantDetail> findDetail(String grantId) {
//        return grantDetailDao.fetch(GRANT_DETAIL.GRANT_ID.eq(grantId));
//    }
//
//    @Override
//    public List<GrantDetail> findDetail(String grantId, int page, int pageSize) {
//        return grantDetailDao.fetch(page, pageSize, GRANT_DETAIL.GRANT_ID.eq(grantId)).getItems();
//    }

    @Override
    public List<GrantDetail> findDetail(int page, int pageSize, Condition... conditions) {
        return grantDetailDao.fetch(page, pageSize, conditions).getItems();
    }

    @Override
    public int findDetailCount(Condition... conditions) {
    	return grantDetailDao.count(conditions);
    }

	@Override
	public List<String> findGrantedOrganizationIds(String memberId, String uri, Optional<Integer> level, Optional<String> name, Optional<String> code, Optional<String> excludeId, Optional<String> organizationId, Optional<String> operatorType, Optional<Boolean> allStatus) {

		return organizationDao.execute(d -> {
			List<Condition> conditions = Stream.of(
							Optional.of(memberId).map(GRANT_MEMBER.MEMBER_ID::eq),
							Optional.ofNullable(uri).map(MENU.URI::eq),
							name.map(ORGANIZATION.NAME::contains),
							code.map(ORGANIZATION.CODE::contains),
							operatorType.map(GRANT.OPERATOR_TYPES::contains),
							level.map(ORGANIZATION.LEVEL::le),
							excludeId.map(eId -> ORGANIZATION.PATH.notLike("%" + eId + "%", '!')))
					.filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

			if (allStatus.isPresent()) {
				if (!allStatus.get()) {
					conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
				}
			} else {
				conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
			}

			if (organizationId.isPresent()) {
				Organization organization = organizationDao.get(organizationId.get());
				conditions.add(ORGANIZATION.PATH.like(organization.getPath() + "%"));
			}
			conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));

			return d.selectDistinct(ORGANIZATION.ID)
					.from(GRANT)
					.innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
					.innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
					.leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
					.innerJoin(ORGANIZATION).on(
							DSL.trueCondition().and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
											.and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
									.or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
											.and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
					.where(conditions).orderBy(ORGANIZATION.DEPTH)
					.fetch(r -> r.getValue(ORGANIZATION.ID));

		});
	}


	/**
	 * * 此方法慎用，如需使用先联系技术经理！！！
	 * *（该方法会导致查询超时，dubbo传输超时，可以使用 grantService.findGrantOrganizationByUri 替代）
	 */
    @Override
    public List<Organization> findGrantedOrganization(String memberId, String uri, Optional<Integer> level, Optional<String> name, Optional<String> code, Optional<String> excludeId, Optional<String> organizationId, Optional<String> operatorType, Optional<Boolean> allStatus) {
//    	List<Condition> conditions = Stream.of(
//                Optional.of(memberId).map(GRANT_DETAIL.MEMBER_ID::eq),
//                Optional.of(uri).map(GRANT_DETAIL.URI::eq),
//                name.map(ORGANIZATION.NAME::contains),
//                code.map(ORGANIZATION.CODE::contains),
//                operatorType.map(GRANT_DETAIL.OPERATOR_TYPES::contains),
//                level.map(ORGANIZATION.LEVEL::le),
//                excludeId.map(eId -> ORGANIZATION.PATH.notLike("%" + eId + "%", '!')))
//                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
//        if(organizationId.isPresent()) {
//            Organization organization = organizationDao.get(organizationId.get());
//            conditions.add(ORGANIZATION.PATH.like(organization.getPath() + "%", '!'));
//        }
//        if(allStatus.isPresent()) {
//            if (!allStatus.get()) {
//                conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
//            }
//        } else {
//            conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
//        }
//        return organizationDao.execute(a -> a.selectDistinct(ORGANIZATION.ID)
//            		.from(ORGANIZATION)
//                    .leftJoin(GRANT_DETAIL).on(ORGANIZATION.ID.eq(GRANT_DETAIL.ORGANIZATION_ID))
//            		.where(conditions)
//            		.fetch(r -> {
//                        Organization organization = new Organization();
//                        organization.setId(r.getValue(ORGANIZATION.ID));
//                        return organization;
//                    }));

    	List<Organization> list =  organizationDao.execute(d -> {
	    	List<Condition> conditions = Stream.of(
		                Optional.of(memberId).map(GRANT_MEMBER.MEMBER_ID::eq),
		                Optional.ofNullable(uri).map(MENU.URI::eq),
		                name.map(ORGANIZATION.NAME::contains),
		                code.map(ORGANIZATION.CODE::contains),
		                operatorType.map(GRANT.OPERATOR_TYPES::contains),
		                level.map(ORGANIZATION.LEVEL::le),
		                excludeId.map(eId -> ORGANIZATION.PATH.notLike("%" + eId + "%", '!')))
		            .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

	         if(allStatus.isPresent()) {
	        	 if (!allStatus.get()) {
	        		 conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
	        	 }
	         } else {
	        	 conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
	         }

	         if(organizationId.isPresent() && !"null".equals(organizationId.get())) {
	              Organization organization = organizationDao.get(organizationId.get());
	              conditions.add(ORGANIZATION.PATH.like(organization.getPath() + "%"));
//	              conditions.add(ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT));
	          }
			  conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));

	          List<Organization> orgs = d.selectDistinct(ORGANIZATION.ID)
	                  .from(GRANT)
	                  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
	                  .innerJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
	                  .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
	                  .innerJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
	                  .innerJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
	                  .innerJoin(ORGANIZATION).on(
	                          DSL.trueCondition().and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                                      .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
                              .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
                                      .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
	                  .where(conditions).orderBy(ORGANIZATION.DEPTH)
	                  .fetch(r -> {
	                      Organization organization = new Organization();
	                      organization.setId(r.getValue(ORGANIZATION.ID));
	                      return organization;
	                     });

	          return orgs ;
    	});
    	return list ;
    }


	@Override
	public List<String> getMemberGrantsByOrgIds(String memberId, String uri, List<String> orgIds) {
		if (CollectionUtils.isEmpty(orgIds)) {
			return new ArrayList<>();
		}
		return organizationDao.execute(d -> {
			List<Condition> conditions = Stream.of(
							Optional.of(memberId).map(GRANT_MEMBER.MEMBER_ID::eq),
							Optional.ofNullable(uri).map(MENU.URI::eq))
					.filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
			conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
			conditions.add(ORGANIZATION.ID.in(orgIds));
			conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));
			return d.selectDistinct(ORGANIZATION.ID)
					.from(GRANT)
					.innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
					.innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
					.leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
					.innerJoin(ORGANIZATION).on(
							DSL.trueCondition().and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
											.and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
									.or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
											.and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
					.where(conditions)
					.fetch(ORGANIZATION.ID);
		});
	}

	@Override
	public List<Organization> findGrantedOrganizationAll(String memberId, Optional<String> uri, Optional<Integer> level, Optional<String> name, Optional<String> code, Optional<String> excludeId, Optional<String> organizationId, Optional<String> operatorType, Optional<Boolean> allStatus) {

		List<Organization> list =  organizationDao.execute(d -> {
			List<Condition> conditions = Stream.of(
							Optional.of(memberId).map(GRANT_MEMBER.MEMBER_ID::eq),
							uri.map(MENU.URI::eq),
							name.map(ORGANIZATION.NAME::contains),
							code.map(ORGANIZATION.CODE::contains),
							operatorType.map(GRANT.OPERATOR_TYPES::contains),
							level.map(ORGANIZATION.LEVEL::le),
							excludeId.map(eId -> ORGANIZATION.PATH.notLike("%" + eId + "%", '!')))
					.filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

			if(allStatus.isPresent()) {
				if (!allStatus.get()) {
					conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
				}
			} else {
				conditions.add(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED));
			}

			if(organizationId.isPresent()) {
				Organization organization = organizationDao.get(organizationId.get());
				conditions.add(ORGANIZATION.PATH.like(organization.getPath() + "%"));
//	              conditions.add(ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT));
			}

			conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));
			List<Organization> orgs = d.selectDistinct(ORGANIZATION.ID,ORGANIZATION.NAME,
							ORGANIZATION.PARENT_ID,ORGANIZATION.LEVEL,ORGANIZATION.CODE)
					.from(GRANT)
					.innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
					.innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
					.leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
					.innerJoin(ORGANIZATION).on(
							DSL.trueCondition().and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
											.and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
									.or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
											.and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
					.where(conditions).orderBy(ORGANIZATION.DEPTH)
					.fetch(r -> {
						Organization organization = new Organization();
						organization.setId(r.getValue(ORGANIZATION.ID));
						organization.setName(r.getValue(ORGANIZATION.NAME));
						organization.setCode(r.getValue(ORGANIZATION.CODE));
						organization.setLevel(r.getValue(ORGANIZATION.LEVEL));
						organization.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
						return organization;
					});

			return orgs ;
		});
		return list ;
	}

    @Override
    public List<String> findGrantedTopOrganization(String memberId, String uri, Integer contain){
// 替换  GRANT_DETAIL

   	// List<String> list = grantMemberDao.execute(dao -> {
		// 			    		return dao.selectDistinct(Fields.start().add(GRANT_ORGANIZATION.ORGANIZATION_ID).end())
		// 			    		.from(GRANT_MEMBER)
		// 			    		.leftJoin(GRANT_ORGANIZATION).on(GRANT_MEMBER.GRANT_ID.eq(GRANT_ORGANIZATION.GRANT_ID))
		// 			    		.leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
		// 			    		.where(GRANT_MEMBER.MEMBER_ID.eq(memberId).and(GRANT_DETAIL.URI.eq(uri))).fetch(result -> {
		// 			    			return result.getValue(GRANT_ORGANIZATION.ORGANIZATION_ID);
		// 			    		});
//
		if (Objects.equals(memberId, "1")) {
			return grantDao.execute(d -> d.selectDistinct(Fields.start().add(GRANT_ORGANIZATION.ORGANIZATION_ID).end())
										  .from(GRANT_ORGANIZATION)
										  .fetch(GRANT_ORGANIZATION.ORGANIZATION_ID));
		}
    	List<String> list = grantDao.execute(dao -> {
    		return dao.selectDistinct(Fields.start().add(GRANT_ORGANIZATION.ORGANIZATION_ID).end())
  			  .from(GRANT)
  			  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
  			  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
  			  .innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
  			  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
			  .where(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
  			  .fetch(GRANT_ORGANIZATION.ORGANIZATION_ID);
    	});
    	return list;
    }

    @Override
    public List<String> findHavingGrantedOrganization(String memberId, String uri, String...organizationIds){
// 替换  GRANT_DETAIL
//    	List<String> list = grantMemberDao.execute(dao -> {
//					    		return dao.select(Fields.start().add(ORGANIZATION.ID).end())
//			            		.from(ORGANIZATION)
//			                    .leftJoin(GRANT_DETAIL).on(ORGANIZATION.ID.eq(GRANT_DETAIL.ORGANIZATION_ID))
//					    		.where(
//					    				GRANT_DETAIL.MEMBER_ID.eq(memberId)
//					    				.and(GRANT_DETAIL.URI.eq(uri))
//					    				.and(ORGANIZATION.ID.in(Arrays.asList(organizationIds)))
//					    				.and(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))
//			    				).fetch(result -> {
//					    			return result.getValue(ORGANIZATION.ID);
//					    		});
//					    	});
        List<Condition> conditions = Stream.of(
        		Optional.ofNullable(memberId).map(GRANT_MEMBER.MEMBER_ID :: eq),
        		Optional.ofNullable(uri).map(MENU.URI :: eq)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
		conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));
    	List<String> list = grantDao.execute(dao -> {
    		  return dao.selectDistinct(Fields.start().add(ORGANIZATION.ID).end())
  			  .from(GRANT)
  			  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
  			  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
  			  .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
  			  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
  			  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
  			  .innerJoin(ORGANIZATION).on(
  			          DSL.trueCondition()
  		              .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
  		                      .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
  		              .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
  		                      .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
  			  .where(conditions).and(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED)
  					            .and(ORGANIZATION.ID.in(Arrays.asList(organizationIds))))
  			  .fetch(ORGANIZATION.ID);
    	});
    	return list;
    }

	@Override
	public List<String> findHavingGrantedOrganization(String memberId, String uri, List<String> operatorTypes, String organizationId) {
		List<Condition> conditions = Stream.of(
			Optional.ofNullable(memberId).map(GRANT_MEMBER.MEMBER_ID::eq),
			Optional.ofNullable(uri).map(MENU.URI::eq)
		).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
		Condition condition = DSL.trueCondition();
		for (String operatorType : operatorTypes) {
			condition = condition.or(GRANT.OPERATOR_TYPES.contains(operatorType));
		}
		conditions.add(condition);
		conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));
		return grantDao.execute(dao -> dao.selectDistinct(Fields.start().add(ORGANIZATION.ID).end())
										  .from(GRANT)
										  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
										  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
										  .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
										  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
										  .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
										  .where(conditions).and(ORGANIZATION.ID.eq(organizationId)))
					   .fetch(ORGANIZATION.ID);
	}

	@Override
    public List<Organization> findSimpleGrantedOrganization(String memberId, String uri, Optional<String> name, Optional<String> code, Boolean supportMore) {
	    List<Organization> list =  organizationDao.execute(d -> {
	         Condition listCondition = GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
	                    .or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO).and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID)));

	         List<Condition> conditions = Stream.of(
	                    name.map(ORGANIZATION.NAME::contains),
	                    code.map(ORGANIZATION.CODE::contains),
				 		Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
	            		).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());


	       	 List<Integer> depths = d.selectDistinct(ORGANIZATION.DEPTH)
		       	.from(GRANT_MEMBER)
		       	.leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
		       	.innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
		       	.where(GRANT_MEMBER.MEMBER_ID.eq(memberId))
		       	.orderBy(ORGANIZATION.DEPTH)
		       	.fetch(ORGANIZATION.DEPTH);

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> step = a -> a.from(GRANT)
                    .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                    .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                    .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                    .where(conditions);

	        // 如果是精准查询,则判断查询的数据是否大于1000,并且是查询全部数据;如果是非精准查询,就查4层以上的.
	        if (name.isPresent() || code.isPresent()) {
	        	 int count = step.apply(d.select(Fields.start().add(ORGANIZATION.ID.countDistinct()).end())).and(listCondition).fetchOne().getValue(0, Integer.class);
	        	if (count > 1000) {
	        		throw new UnprocessableException(ErrorCode.QueryHasTooManyRecords);
	        	}
	        } else {
	        	conditions.add(ORGANIZATION.DEPTH.le(depths.get(0) + 3));
	        }
//	        List<Organization> orgs = d.selectDistinct(ORGANIZATION.fields())
//	                .from(GRANT)
//	                .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
//	                .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
//	                .innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
//	                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
//	                .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
//	                .innerJoin(ORGANIZATION).on(
//	                        DSL.trueCondition()
//	                                .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
//	                                        .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
//	                                .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
//	                                        .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
//	                .where(conditions)
//	                .orderBy(ORGANIZATION.ORDER.asc().nullsLast(), ORGANIZATION.CREATE_TIME.desc())
//	                .fetchInto(Organization.class);

            // 查询列表
            List<Organization> orgs = step.apply(d.selectDistinct(ORGANIZATION.fields())).and(listCondition).fetchInto(Organization.class);

	        if (supportMore && !name.isPresent() && !code.isPresent()) {
	        	// 找出所有5层以上(不包含)的已经作为父节点的组织id,用于判断具体的某一个节点是否可以展开
	        	List<String> parentIds = d.selectDistinct(ORGANIZATION.PARENT_ID).from(ORGANIZATION)
	    				.where(ORGANIZATION.PARENT_ID.isNotNull(), ORGANIZATION.DEPTH.le(depths.get(0) + 4)).fetch(ORGANIZATION.PARENT_ID);

	    		// 找出当前用户拥有的,第四层的,包含子节点的组织id,用于判断第四层的节点,是否可以展开(对于第四层的节点,第一要满足是父节点才可以展开,并且第四层的节点,在权限上必须要包含子节点才可以展开)
        		List<String> childFindYesIds = step.apply(d.selectDistinct(Fields.start().add(ORGANIZATION.ID).end()))
                        .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)).fetch(ORGANIZATION.ID);
	            parentIds.retainAll(childFindYesIds);
	            orgs.forEach(o -> o.setIsParent(parentIds.contains(o.getId())));
	        }
//	        List<String> disabledPath = orgs.stream()
//	        		.filter(g -> Organization.STATUS_DISABLED.equals(g.getStatus()))
//	        		.map(g -> g.getPath())
//	        		.collect(Collectors.toList());
//	        for (String p : disabledPath) {
//	        	orgs = orgs.stream().filter(g -> !g.getPath().startsWith(p)).collect(Collectors.toList());
//	        }
	        return orgs;
	    });
	    // 排序
	    list.sort(OrganizationUtil.getOrgComparator());
    	return list;
    }

	/**
	 * 查询字段: orgId,orgPath,orgCode
	 * 查询登录用户的授权组织 高标党建-约课权限配置导入校验使用(目前场景是只查询depth为1和3的组织)
	 * supportMore 默认false
	 * depth 默认 (1,3)
	 *
	 * @param memberId
	 * @param uri
	 * @return
	 */
	@Override
	public List<Organization> findSimpleGrantedOrganizationForGb(String memberId, String uri) {

		List<Organization> list =  organizationDao.execute(d -> {

			Condition listCondition = GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
					.or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO).and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID)));

			List<Condition> conditions = Stream.of(
					Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
			).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

			conditions.add(ORGANIZATION.DEPTH.in(Organization.DEPTH_ONE,Organization.DEPTH_THREE));

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> step = a -> a.from(GRANT)
					.innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
					.innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
					.leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
					.leftJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
					.where(conditions);

			// 查询列表
			List<Organization> orgs = step.apply(d.selectDistinct(Fields.start().add(ORGANIZATION.ID, ORGANIZATION.PATH, ORGANIZATION.CODE).end()))
					.and(listCondition)
					.fetch( r ->{
						Organization org = new Organization();
						org.setId(r.getValue(ORGANIZATION.ID));
						org.setPath(r.getValue(ORGANIZATION.PATH));
						org.setCode(r.getValue(ORGANIZATION.CODE));
						return org;
					} );

			return orgs;
		});

		return list;
	}

	@Override
    public List<Organization> findGrantedOrganizationWithParent(String memberId, String uri){
        return organizationDao.execute(d -> {
// 替换GRANT_DETAIL

//            List<String> grantedOrganizationIds = d.selectDistinct(GRANT_DETAIL.ORGANIZATION_ID)
//                    .from(GRANT_DETAIL)
//                    .where(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                    .and(GRANT_DETAIL.URI.eq(uri))
//                    .fetch(GRANT_DETAIL.ORGANIZATION_ID);
        	List<String> grantedOrganizationIds = getGrantOrganizationIds(memberId, uri, d);

            return d.select(ORGANIZATION.ID, ORGANIZATION.PARENT_ID, ORGANIZATION.LEVEL)
                    .from(ORGANIZATION_DETAIL)
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.ROOT))
                    .where(ORGANIZATION_DETAIL.SUB.in(grantedOrganizationIds))
                    .fetch(r -> {
                        Organization o = new Organization();
                        o.setId(r.getValue(ORGANIZATION.ID));
                        o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
                        o.setLevel(r.getValue(ORGANIZATION.LEVEL));
                        return o;
                    });
        });
    }

    private List<String> getSubIds(String parentId){
        return organizationDetailDao.execute(e->{
            return e.selectDistinct(ORGANIZATION_DETAIL.SUB)
                    .from(ORGANIZATION_DETAIL)
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                    .where(ORGANIZATION_DETAIL.ROOT.eq(parentId), ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT)).fetch(ORGANIZATION_DETAIL.SUB);
        });
    }

    @Override
    public PagedResult<Organization> findGrantedOrganization(int page, int pageSize, String memberId, String uri, Optional<String> id, Optional<String> name, Optional<Integer> level, Optional<Integer> specLevel, Optional<String> code) {
        com.zxy.product.system.jooq.tables.Organization ORG_INFO = ORGANIZATION.as("a");
//        Function<SelectSelectStep<?>, SelectConditionStep<?>> ss = t -> t.from(ORGANIZATION)
//                .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(ORGANIZATION.ID))
//                .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//                .leftJoin(GRANT).on(GRANT.ID.eq(GRANT_DETAIL.GRANT_ID))
//                .leftJoin(ORG_INFO).on(ORG_INFO.ID.eq(ORGANIZATION.PARENT_ID))
//                .where(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                .and(GRANT_DETAIL.URI.eq(uri))
//                .and(Stream.of(
//                    id.map(ORGANIZATION_DETAIL.ROOT::eq).orElse(DSL.trueCondition()),
//                    name.map(ORGANIZATION.NAME::contains).orElse(DSL.trueCondition()),
//                    level.map(ORGANIZATION.LEVEL::le).orElse(DSL.trueCondition()),
//                    specLevel.map(ORGANIZATION.LEVEL::eq).orElse(DSL.trueCondition()),
//                    code.map(ORGANIZATION.CODE::contains).orElse(DSL.trueCondition())
//                ).reduce((a, b) -> a.and(b)).orElse(DSL.trueCondition()));

	    	List<Condition> conditions = Stream.of(
	                Optional.of(memberId).map(GRANT_MEMBER.MEMBER_ID::eq),
	                Optional.of(uri).map(MENU.URI::eq),
	                name.map(ORGANIZATION.NAME::contains),
	                code.map(ORGANIZATION.CODE::contains),
	                level.map(ORGANIZATION.LEVEL::le),
	                specLevel.map(ORGANIZATION.LEVEL::eq))
	            .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

	         if(id.isPresent()) {
	              Organization organization = organizationDao.get(id.get());
	              conditions.add(ORGANIZATION.PATH.like(organization.getPath() + "%"));
	          }
			 conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));
	         Function<SelectSelectStep<?>, SelectConditionStep<?>> ss = t -> t.from(GRANT)
	                  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
	                  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
	                  .innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
	                  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
	                  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
	                  .innerJoin(ORGANIZATION).on(
	                          DSL.trueCondition()
	                                  .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
	                                          .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
	                                  .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
	                                          .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
	                  .leftJoin(ORG_INFO).on(ORG_INFO.ID.eq(ORGANIZATION.PARENT_ID))
	                  .where(conditions);


        return PagedResult.create(
                organizationDao.execute(d -> {
                    return ss.apply(d.select(DSL.countDistinct(ORGANIZATION.ID))).fetchOne(DSL.count(ORGANIZATION.ID));
                }),
                organizationDao.execute(d -> {
                    return ss.apply(d.selectDistinct(Fields.start().add(ORGANIZATION.fields()).add(ORG_INFO.NAME.as("organizationName")).end()))
                            .orderBy(ORGANIZATION.ORDER.asc().nullsLast(), ORGANIZATION.CREATE_TIME.desc())
                            .limit((page - 1) * pageSize, pageSize);
                }).fetch().map(a -> {
                    Organization organization = new Organization();
                    organization.setId(a.getValue(ORGANIZATION.ID));
                    organization.setParentId(a.getValue(ORGANIZATION.PARENT_ID));
                    organization.setName(a.getValue(ORGANIZATION.NAME));
                    organization.setLevel(a.getValue(ORGANIZATION.LEVEL));
                    organization.setCreateTime(a.getValue(ORGANIZATION.CREATE_TIME));
                    organization.setCode(a.getValue(ORGANIZATION.CODE));
                    organization.setStatus(a.getValue(ORGANIZATION.STATUS));
                    organization.setOrder(a.getValue(ORGANIZATION.ORDER));
                    organization.setCompanyId(a.getValue(ORGANIZATION.COMPANY_ID));
                    organization.setOrganizationName(a.getValue("organizationName", String.class));
                    return organization;
                }));
    }

    @Override
    public String findOperatorType(String memberId,String roleId, String[] organizationIds, String[] seconds){
    	Set<String> ids = Sets.newHashSet(organizationIds);
//    	if (seconds.length > 0) {
//    		List<String> childs = grantDao.execute(d -> d.selectDistinct(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL)
//                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
//    				.where(ORGANIZATION_DETAIL.ROOT.in(seconds), ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT)).fetch(ORGANIZATION_DETAIL.SUB));
//    		ids.addAll(childs);
//    	}
//        List<GrantDetail> gds = grantDao.execute(d ->
//                d.selectDistinct(GRANT_DETAIL.URI, GRANT_DETAIL.ORGANIZATION_ID, GRANT_DETAIL.OPERATOR_TYPES).from(GRANT_DETAIL)
//                        .where(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                        .and(GRANT_DETAIL.ORGANIZATION_ID.in(ids))
//                        .and(GRANT_DETAIL.URI.in(d.select(MENU.URI).from(MENU)
//                        		.innerJoin(ROLE_MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
//                        		.where(ROLE_MENU.ROLE_ID.eq(roleId), MENU.URI.isNotNull())))
//                .fetchInto(GrantDetail.class));
//
//        return mergeOperatorTypes(gds);

          List<String> types = grantDao.execute(a -> a.selectDistinct(GRANT.OPERATOR_TYPES)
   		  .from(GRANT)
   		  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
   		  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
   		  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
   		  .innerJoin(ORGANIZATION).on(
   		          DSL.trueCondition()
                     .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                             .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
                     .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
                             .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
   		  .where(ORGANIZATION.ID.in(ids), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
   		  .fetch(GRANT.OPERATOR_TYPES));

           return retainAllOperatorTypes(types);
    }

    /**
     * 角色 组织授权节点 操作类型 交集
     * @param operatorTypes
     * @return
     */
    private String retainAllOperatorTypes(List<String> operatorTypes) {

    	List<Set<String>> orgOpers = Lists.newArrayList();

    	operatorTypes.forEach(g -> {
    		Set<String> s = Sets.newHashSet();
			s.addAll(Arrays.asList(g.split(",")));
			orgOpers.add(s);
		});

		for (int i = 1; i < orgOpers.size(); i++) {
			orgOpers.get(0).retainAll(orgOpers.get(i));
		}

    	return Joiner.on(",").join(orgOpers.get(0));

    }

    private String mergeOperatorTypes(List<GrantDetail> gds) {
    	List<Set<String>> uriOpers = Lists.newArrayList();

    	Map<String, List<GrantDetail>> gdMap = gds.stream().collect(Collectors.groupingBy(GrantDetail::getUri));
    	gdMap.values().forEach(gus -> {

    		List<Set<String>> orgOpers = Lists.newArrayList();
    		gus.stream().collect(Collectors.groupingBy(GrantDetail::getOrganizationId)).values().forEach(gos -> {
    			Set<String> s = Sets.newHashSet();
    			gos.forEach(g -> {
    				s.addAll(Arrays.asList(g.getOperatorTypes().split(",")));
    			});
    			orgOpers.add(s);
    		});
    		for (int i = 1; i < orgOpers.size(); i++) {
    			orgOpers.get(0).retainAll(orgOpers.get(i));
    		}
    		uriOpers.add(orgOpers.get(0));
    	});

		for (int i = 1; i < uriOpers.size(); i++) {
			uriOpers.get(0).retainAll(uriOpers.get(i));
		}

    	return Joiner.on(",").join(uriOpers.get(0));
    }

    @Override
    public List<Organization> findOrganizationByMemberAndRole(String memberId, String roleId) {
//        Role role = roleDao.get(roleId);
//        List<Organization> organizationList = grantDao.execute(t -> t.selectDistinct(ORGANIZATION.fields())
//                .from(ORGANIZATION)
//                .innerJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//                .leftJoin(GRANT).on(GRANT.ID.eq(GRANT_DETAIL.GRANT_ID))
//                .where(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                .and( GRANT.ROLE_ID.eq(roleId).or(GRANT.ROLE_ID.eq(role.getParentId())))
//                .fetchInto(Organization.class));
    	List<Organization> organizationList = organizationDao.execute(a -> a.selectDistinct(ORGANIZATION.fields())
   		  .from(GRANT)
   		  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
   		  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
   		  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
   		  .innerJoin(ORGANIZATION).on(
   		          DSL.trueCondition()
                     .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                             .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
                     .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
                             .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
   		  .where(GRANT.ROLE_ID.eq(roleId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
   		 .fetchInto(Organization.class));

        List<Organization> disable = organizationList.stream().filter(o2 -> Organization.STATUS_DISABLED == o2.getStatus()).collect(Collectors.toList());
        for(Organization o1 : disable) {
            organizationList = organizationList.stream().filter(o2 -> {
                return o2.getStatus() == Organization.STATUS_ENABLED && !o1.getId().equals(o2.getParentId());
            }).collect(Collectors.toList());
        }
        return organizationList;
    }

    @Override
    public Map<String, String> findAll(String memberId) {
//        Map<String, String> map = new HashMap<>();
//        grantDao.execute(a -> {
//            a.select(GRANT_DETAIL.ORGANIZATION_ID, GRANT.OPERATOR_TYPES)
//                    .from(GRANT)
//                    .leftJoin(GRANT_DETAIL).on(GRANT.ID.eq(GRANT_DETAIL.GRANT_ID))
//                    .where(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                    .fetch().stream().forEach(b -> {
//                String organizationId = b.getValue(0, String.class);
//                String operatorTypes = b.getValue(1, String.class);
//                if(map.containsKey(organizationId) && !map.get(organizationId).contains(operatorTypes)) {
//                    map.put(organizationId, map.get(operatorTypes) + "," + operatorTypes);
//                } else {
//                    map.put(organizationId, operatorTypes);
//                }
//            });
//            return null;
//        });
    	return organizationDao.execute(a -> a.selectDistinct(
			 Fields.start().add(ORGANIZATION.ID,GRANT.OPERATOR_TYPES).end())
		  .from(GRANT)
		  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
		  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
		  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
		  .innerJoin(ORGANIZATION).on(
		          DSL.trueCondition()
                  .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                          .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
                  .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
                          .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
			.where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
		  .fetchMap(ORGANIZATION.ID,  GRANT.OPERATOR_TYPES));
    }

    @Override
	public Map<String ,String> findOrganizationOperatorType(String memberId, String meunUrl) {

		Result<Record> result = organizationDao.execute(a -> a.selectDistinct(
						Fields.start().add(ORGANIZATION.ID,GRANT.OPERATOR_TYPES).end())
				.from(GRANT)
				.innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
				.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
				.innerJoin(MENU).on(MENU.URI.eq(meunUrl).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
				.leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
				.leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
				.innerJoin(ORGANIZATION).on(
						DSL.trueCondition()
								.and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
										.and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
								.or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
										.and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
				.where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
				.fetch());
		//  合并所有权限
		Map<String, String> map = new HashMap<>();
		for (Record r : result) {

			String k = r.getValue(ORGANIZATION.ID);
			String v = r.getValue(GRANT.OPERATOR_TYPES);

			if (map.containsKey(k))
				map.put(k, unionOperatorType(map.get(k), v));
			else
				map.put(k, v);

		}
		return map;
	}

    @Override
    public Integer clearMemberGrant(String memberId) {
        int count = grantMemberDao.count(GRANT_MEMBER.MEMBER_ID.eq(memberId));
        if (count > 0) {
            grantMemberDao.delete(GRANT_MEMBER.MEMBER_ID.eq(memberId));

            sender.send(MessageTypeContent.SYSTEM_MEMBER_GRANT_CLEAR, MessageHeaderContent.MEMBER_ID, memberId);
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public Integer countGrantMemberByMemberId(String memberId) {
        return grantMemberDao.count(GRANT_MEMBER.MEMBER_ID.eq(memberId));
    }

	@Override
	public boolean judegOwnedCompanyNode(String memberId, String uri) {
		long currentTime = System.currentTimeMillis();
        return grantDao.execute(a -> a
                .select(ORGANIZATION.ID.countDistinct())
                .from(GRANT)
                .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                .innerJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                .innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                .innerJoin(ORGANIZATION).on(
                        GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                                .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                                .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
                                        .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID)))
                )
                .where(GRANT.VALID_DATE.ge(currentTime).or(GRANT.VALID_DATE.isNull()))
                .fetchOne(ORGANIZATION.ID.count()) > 0
        );
	}

	@Override
	public boolean checkGrants(String memberId, String currentUserId) {
		// 比较菜单
//		List<String> memberUris = grantDetailDao.execute(d -> d.selectDistinct(GRANT_DETAIL.URI).from(GRANT_DETAIL)
//				.where(GRANT_DETAIL.MEMBER_ID.eq(memberId), GRANT_DETAIL.URI.isNotNull()).fetch(GRANT_DETAIL.URI));
//		List<String> currentUris = grantDetailDao.execute(d -> d.selectDistinct(GRANT_DETAIL.URI).from(GRANT_DETAIL)
//				.where(GRANT_DETAIL.MEMBER_ID.eq(currentUserId), GRANT_DETAIL.URI.isNotNull()).fetch(GRANT_DETAIL.URI));
//		if (!currentUris.containsAll(memberUris)) {
//			throw new UnprocessableException(ErrorCode.GrantBeyondOfMe);
//		}
//
//		// 比较节点和操作类型
//		Map<String, List<GrantDetail>> gdMap = grantDetailDao.execute(
//				d -> d.selectDistinct(GRANT_DETAIL.MEMBER_ID, GRANT_DETAIL.URI, GRANT_DETAIL.ORGANIZATION_ID, GRANT_DETAIL.OPERATOR_TYPES)
//						.from(GRANT_DETAIL)
//						.where(GRANT_DETAIL.MEMBER_ID.in(memberId, currentUserId), GRANT_DETAIL.URI.in(memberUris))
//						.fetchInto(GrantDetail.class)).stream().collect(Collectors.groupingBy(GrantDetail::getMemberId));
//		List<GrantDetail> memberGds = mergeGrantDetailsForMember(gdMap.get(memberId));
//		List<GrantDetail> currentGds = mergeGrantDetailsForMember(gdMap.get(currentUserId));
//		compareGrantDetails(memberGds, currentGds);

		List<String> memberUris = menuDao.execute(d -> d.selectDistinct(MENU.URI)
				.from(MENU)
				.leftJoin(ROLE_MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
				.leftJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE_MENU.ROLE_ID))
				.leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
				.where(GRANT_MEMBER.MEMBER_ID.eq(memberId), MENU.URI.isNotNull(), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())).fetch(MENU.URI));

		List<String> currentUris = menuDao.execute(d -> d.selectDistinct(MENU.URI)
				.from(MENU)
				.leftJoin(ROLE_MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
				.leftJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE_MENU.ROLE_ID))
				.leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
				.where(GRANT_MEMBER.MEMBER_ID.eq(currentUserId), MENU.URI.isNotNull(), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())).fetch(MENU.URI));

		if (!currentUris.containsAll(memberUris)) {
			throw new UnprocessableException(ErrorCode.GrantBeyondOfMe);
		}

		// 比较节点和操作类型

		List<String> memberTypes =  grantDao.execute(a -> a.selectDistinct(GRANT.OPERATOR_TYPES)
 		  .from(GRANT_MEMBER).leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
 		  .where(GRANT_MEMBER.ID.eq(memberId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
 		  .fetch(GRANT.OPERATOR_TYPES));

		List<String> currentTypes =  grantDao.execute(a -> a.selectDistinct(GRANT.OPERATOR_TYPES)
 		  .from(GRANT_MEMBER).leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
 		  .where(GRANT_MEMBER.ID.eq(currentUserId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
 		  .fetch(GRANT.OPERATOR_TYPES));

		if (!currentTypes.containsAll(memberTypes)) {
			throw new UnprocessableException(ErrorCode.GrantBeyondOfMe);
		}

		List<String> memberGrantOrgs =  grantDao.execute(a -> a.selectDistinct(ORGANIZATION.PATH)
		 		  .from(GRANT_MEMBER)
				   .join(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
		 		  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
		 		  .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
		 		  .where(GRANT_MEMBER.ID.eq(memberId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
		 		  .fetch(ORGANIZATION.PATH));

		List<String> currentGrantOrgs =  grantDao.execute(a -> a.selectDistinct(ORGANIZATION.PATH)
		 		  .from(GRANT_MEMBER)
			      .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
		 		  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
		 		  .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
		 		  .where(GRANT_MEMBER.ID.eq(currentUserId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
		 		  .fetch(ORGANIZATION.PATH));

		if (!currentGrantOrgs.containsAll(memberGrantOrgs)) {
			throw new UnprocessableException(ErrorCode.GrantBeyondOfMe);
		}

		return true;
	}

    @Override
    public Map<String, Object> fullAdminRoleGrantWhenMenuAdd(String menuIds) {
        final String comma = ",";

        if (!org.springframework.util.StringUtils.hasText(menuIds)) {
            return ImmutableMap.of("status", Boolean.FALSE);
        }

        List<Menu> menus = menuDao.get(Arrays.asList(menuIds.split(comma)));

        // 角色菜单
        roleMenuDao.insert(menus.stream().map(m -> {
            RoleMenu rm = new RoleMenu();
            rm.forInsert();
            rm.setMenuId(m.getId());
            rm.setRoleId("1");
            return rm;
        }).collect(Collectors.toList()));

//        menus.forEach(m -> sender.send(MessageTypeContent.SYSTEM_MENU_INSERT, MessageHeaderContent.ID, m.getId()));

        return ImmutableMap.of("status", Boolean.TRUE);
    }

    /** 通过授权id得到所有授权组织 */
    private List<String> getGrantOrganizationIds(String grantId) {
        return grantDao.execute(d -> d.selectDistinct(ORGANIZATION_DETAIL.SUB)
                .from(GRANT_ORGANIZATION)
                .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.ROOT.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                .where(GRANT_ORGANIZATION.GRANT_ID.eq(grantId), ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT))
                .and(GRANT_ORGANIZATION.CHILD_FIND.ne(Organization.CHILD_FIND_YES).and(ORGANIZATION_DETAIL.ROOT.eq(ORGANIZATION_DETAIL.SUB))
                        .or(GRANT_ORGANIZATION.CHILD_FIND.eq(Organization.CHILD_FIND_YES)))
                .fetch(ORGANIZATION_DETAIL.SUB));
    }

    /** 插入授权人 */
    private void insertGrantMember(String grantId, String memberIds){
		List<String> deleteIds = grantMemberDao.execute(d -> d.select(GRANT_MEMBER.ID)
															.from(GRANT_MEMBER)
															.where(GRANT_MEMBER.GRANT_ID.eq(grantId), GRANT_MEMBER.MEMBER_ID.in(memberIds.split(SystemConstant.COMMA)))
															.fetch(GRANT_MEMBER.ID));
		grantMemberDao.delete(GRANT_MEMBER.ID.in(deleteIds));
		grantMemberDao.insert(Stream.of(memberIds.split(",")).map(x -> {
                    GrantMember grantMember = new GrantMember();
                    grantMember.forInsert();
                    grantMember.setGrantId(grantId);
                    grantMember.setMemberId(x);
                    return grantMember;
                }
        ).collect(Collectors.toList()));
    }

    /** 修改授权人 */
    private List<String> updateGrantMember(String grantId, String memberIds) {
    	List<String> oldMemberIds = grantMemberDao.fetch(GRANT_MEMBER.GRANT_ID.eq(grantId)).stream().map(GrantMember::getMemberId).collect(Collectors.toList());
    	List<String> newMemberIds = new ArrayList<String>(Arrays.asList(memberIds.split(",")));
    	newMemberIds.removeAll(oldMemberIds);

    	grantMemberDao.delete(GRANT_MEMBER.GRANT_ID.eq(grantId));
    	insertGrantMember(grantId, memberIds);

    	// 返回新增的用户id
    	return newMemberIds;
	}

    /** 用户去重 */
    private List<Member> clearRepeatMember(List<Member> members) {
    	Set<String> ids = Sets.newHashSet();
    	List<Member> filtMemberList = Lists.newArrayList();
    	members.forEach(m -> {
    		if (!ids.contains(m.getId())) {
    			ids.add(m.getId());
    			filtMemberList.add(m);
    		}
    	});
//    	for(int i = 0; i< members.size(); i++) {
//    		if (!ids.contains(members.get(i).getId())) {
//    			ids.add(members.get(i).getId());
//    		} else {
//    			members.remove(i);
//    		}
//    	}
    	return filtMemberList;
    }

    /** 比较两用户的权限 */
	private void compareGrantDetails(List<GrantDetail> memberGds, List<GrantDetail> currentGds) {
		Map<String, Set<String>> memberGdMap = Maps.newHashMap();
		Map<String, Set<String>> currentGdMap = Maps.newHashMap();

		final String separator = ",";
		memberGds.forEach(g -> {
			String key = g.getUri() + "_" + g.getOrganizationId();
			Set<String> s = Sets.newHashSet(Arrays.asList(g.getOperatorTypes().split(separator)));
			if (!memberGdMap.containsKey(key)) {
				memberGdMap.put(key, s);
			} else {
				memberGdMap.get(key).addAll(s);
			}
		});
		currentGds.forEach(g -> {
			String key = g.getUri() + "_" + g.getOrganizationId();
			Set<String> s = Sets.newHashSet(Arrays.asList(g.getOperatorTypes().split(separator)));
			if (!currentGdMap.containsKey(key)) {
				currentGdMap.put(key, s);
			} else {
				currentGdMap.get(key).addAll(s);
			}
		});

		memberGdMap.forEach((k, v) -> {
			if (currentGdMap.containsKey(k)) {
				if (!currentGdMap.get(k).containsAll(v)) {
					throw new UnprocessableException(ErrorCode.GrantBeyondOfMe);
				}
			} else {
				throw new UnprocessableException(ErrorCode.GrantBeyondOfMe);
			}
		});

	}
	/** 合并个人的grant-detail,使得每个菜单的每个节点只有一条记录 */
	private List<GrantDetail> mergeGrantDetailsForMember(List<GrantDetail> gds) {
		List<GrantDetail> mergedGds = Lists.newArrayList();

		if (gds == null || gds.isEmpty()) {
			return mergedGds;
		}
    	Map<String, List<GrantDetail>> gdMap = gds.stream().collect(Collectors.groupingBy(GrantDetail::getUri));
    	gdMap.values().forEach(gus -> {
    		gus.stream().collect(Collectors.groupingBy(GrantDetail::getOrganizationId)).values().forEach(gos -> {
    			Set<String> s = Sets.newHashSet();
    			gos.forEach(g -> {
    				s.addAll(Arrays.asList(g.getOperatorTypes().split(",")));
    			});
    			gos.get(0).setOperatorTypes(Joiner.on(",").join(s));
    			mergedGds.add(gos.get(0));
    		});
    	});
    	return mergedGds;
	}
	/** 合并操作类型 */
	private String unionOperatorType(String... str1) {
		final String splitStr = ",";
		Set<String> hs = new HashSet<>();
		for (String str : str1) {
			Collections.addAll(hs, str.split(splitStr));
		}
		return StringUtils.join(hs.toArray(), ",");
	}

    @Override
    public List<Organization> findGrantedOrganizationWithParent(String memberId, String uri, String organizationId) {
        return organizationDao.execute(d -> {
//            List<String> grantedOrganizationIds = d.selectDistinct(GRANT_DETAIL.ORGANIZATION_ID)
//                    .from(GRANT_DETAIL)
//                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(GRANT_DETAIL.ORGANIZATION_ID))
//                    .where(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                    .and(GRANT_DETAIL.URI.eq(uri))
//                    .and(ORGANIZATION_DETAIL.ROOT.eq(organizationId))
//                    .fetch(GRANT_DETAIL.ORGANIZATION_ID);

//            return d.select(ORGANIZATION.ID, ORGANIZATION.PARENT_ID, ORGANIZATION.LEVEL)
//                    .from(ORGANIZATION_DETAIL)
//                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.ROOT))
//                    .where(ORGANIZATION_DETAIL.SUB.in(grantedOrganizationIds))
//                    .fetchInto(ORGANIZATION).into(Organization.class);
//        });

       List<String> grantedOrganizationIds = d.selectDistinct(GRANT_ORGANIZATION.ORGANIZATION_ID)
		  .from(GRANT)
		  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
		  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
		  .innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
		  .innerJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
		   .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
		  .fetch(GRANT_ORGANIZATION.ORGANIZATION_ID);

        return d.select(ORGANIZATION.ID, ORGANIZATION.PARENT_ID, ORGANIZATION.LEVEL)
           .from(ORGANIZATION_DETAIL)
           .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.ROOT))
           .where(ORGANIZATION_DETAIL.SUB.in(grantedOrganizationIds))
           .fetch(r -> {
               Organization o = new Organization();
               o.setId(r.getValue(ORGANIZATION.ID));
               o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
               o.setLevel(r.getValue(ORGANIZATION.LEVEL));
               return o;
           });

//		return d.selectDistinct(ORGANIZATION.ID, ORGANIZATION.PARENT_ID, ORGANIZATION.LEVEL)
//			  .from(GRANT)
//			  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
//			  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
//			  .innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
//			  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
//			  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
//			  .innerJoin(ORGANIZATION).on(
//			          DSL.trueCondition()
//		              .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
//		                      .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
//		              .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
//		                      .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
//			  .fetchInto(ORGANIZATION).into(Organization.class);
      });






    }


	@Override
	public String[] findGrantedMemberByUriAndOrganization(String uri, String organizationId) {
// 培训资源管理员memberId集合
//		String[] grantMemberIds = null;
//		List<GrantDetail> grantDetailList = grantDetailDao.execute(x -> x
//				.select(Fields.start()
//						.add(GRANT_DETAIL.GRANT_ID, GRANT_DETAIL.MEMBER_ID, GRANT_DETAIL.OPERATOR_TYPES,
//								GRANT_DETAIL.MENU_ID)
//						.end())
//				.from(GRANT_DETAIL).where(GRANT_DETAIL.URI.eq(uri).and(GRANT_DETAIL.ORGANIZATION_ID.eq(organizationId)))
//				.fetch(r -> r.into(GrantDetail.class)).stream()
//				.filter((GrantDetail b) -> b.getOperatorTypes().contains("2")).collect(Collectors.toList()));
//		Optional<Menu> menu = menuDao.fetchOne(MENU.URI.eq(uri));
//		String[] grantIds = grantDetailList.stream().map(GrantDetail::getGrantId).toArray(String[]::new);
//		String[] memberIds = grantDetailList.stream().map(GrantDetail::getMemberId).toArray(String[]::new);
//		if(menu.isPresent()){
//			List<GrantMember> grantMembers = grantMemberDao.execute(x -> {
//				return x.selectDistinct(Fields.start().add(GRANT_MEMBER.MEMBER_ID).end())
//				.from(GRANT).leftJoin(ROLE_MENU).on(GRANT.ROLE_ID.eq(ROLE_MENU.ROLE_ID))
//				.leftJoin(GRANT_MEMBER).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
//				.where(GRANT.ID.in(grantIds).and(ROLE_MENU.MENU_ID.eq(menu.get().getId())).and(GRANT_MEMBER.MEMBER_ID.in(memberIds)))
//				.fetch(r -> r.into(GrantMember.class));
//			});
//			grantMemberIds = grantMembers.stream().map(GrantMember::getMemberId).toArray(String[]::new);
//		}

		List<GrantMember> grantMembers = grantMemberDao.execute(x -> {
			return x.selectDistinct(Fields.start().add(GRANT_MEMBER.MEMBER_ID).end())
			.from(GRANT_MEMBER)
			.leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
			.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
			.innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
			.leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
			.leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
			.innerJoin(ORGANIZATION).on(
			          DSL.trueCondition()
		              .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
		                      .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
		              .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
		                      .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))

			.where(ORGANIZATION.ID.in(organizationId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
			.fetch(r -> r.into(GrantMember.class));
		});

		return grantMembers.stream().map(GrantMember::getMemberId).toArray(String[]::new);
	}
	@Override
	public List<String> findGrantedOrganizationAll(String memberId, String uri,Optional<String> organizationId, Optional<String> operatorType) {

		// 获取当前member所拥有grantIds,参数uri,operatorType
		List<String> grantIds = grantDao.execute(x->x.selectDistinct(GRANT.ID).from(GRANT_MEMBER)
				.innerJoin(GRANT).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
				.innerJoin(ROLE_MENU).on(GRANT.ROLE_ID.eq(ROLE_MENU.ROLE_ID))
				.innerJoin(MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
				.where(GRANT_MEMBER.MEMBER_ID.eq(memberId)).and(MENU.URI.eq(uri))
			    .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
				.and(operatorType.map(GRANT.OPERATOR_TYPES::contains).orElse(DSL.trueCondition()))).fetch(GRANT.ID);

		// 根据grantIds获取权限所有子节点,包括5级以下子节点
		return grantDao.execute(d -> d.selectDistinct(ORGANIZATION_DETAIL.SUB)
                .from(GRANT_ORGANIZATION)
                .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.ROOT.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                .where(GRANT_ORGANIZATION.GRANT_ID.in(grantIds))
                .and(GRANT_ORGANIZATION.CHILD_FIND.ne(Organization.CHILD_FIND_YES).and(ORGANIZATION_DETAIL.ROOT.eq(ORGANIZATION_DETAIL.SUB))
                        .or(GRANT_ORGANIZATION.CHILD_FIND.eq(Organization.CHILD_FIND_YES))))
                .fetch(ORGANIZATION_DETAIL.SUB);

	}


    /**
     *
     * 获取4级授权组织替换GRANT_DETAIL
     * @param memberId
     * @param url
     * @param d
     * @return
     */
	private List<String> getGrantOrganizationIds(String memberId, String url, DSLContext d) {
		return d.selectDistinct(Fields.start().add(ORGANIZATION.ID).end())
			  .from(GRANT)
			  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
			  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
			  .innerJoin(MENU).on(MENU.URI.eq(url).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
			  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
			  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
			  .innerJoin(ORGANIZATION).on(
			          DSL.trueCondition()
		              .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
		                      .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
		              .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
		                      .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
			  .where(ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
			  .fetch(ORGANIZATION.ID);
	}
	@Override
	public List<Organization> findGrantedOrganization(String memberId, String uri, Optional<String> organizationId) {
		return organizationDao.execute(d -> {
			 Condition listCondition = GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
	                    .or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO).and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID)));

            List<Condition> conditions = Stream.of(
                    organizationId.map(id -> ORGANIZATION.PATH.contains(id + ",")),
					Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

           List<Organization> orgs = d.selectDistinct(ORGANIZATION.CODE, ORGANIZATION.ID, ORGANIZATION.NAME, ORGANIZATION.LEVEL, ORGANIZATION.PARENT_ID,
        		 ORGANIZATION.STATUS, ORGANIZATION.CREATE_TIME, ORGANIZATION.ORDER, ORGANIZATION.DEPTH)
        		.from(GRANT)
                .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                .where(conditions).and(listCondition).fetch(r -> {
                    Organization o = new Organization();
                    o.setCode(r.getValue(ORGANIZATION.CODE));
                    o.setId(r.getValue(ORGANIZATION.ID));
                    o.setName(r.getValue(ORGANIZATION.NAME));
                    o.setLevel(r.getValue(ORGANIZATION.LEVEL));
                    o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
                    o.setStatus(r.getValue(ORGANIZATION.STATUS));
                    o.setCreateTime(r.getValue(ORGANIZATION.CREATE_TIME));
                    o.setOrder(r.getValue(ORGANIZATION.ORDER));
                    o.setDepth(r.getValue(ORGANIZATION.DEPTH));
                    return o;
                });

             // 去除禁用的组织
             List<String> replaseOrganizationPIds = orgs.stream().filter(or -> or.getStatus() != 1).map(Organization :: getId).collect(Collectors.toList());
             List<String> replaseOrganizationList = d.selectDistinct(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL).where(ORGANIZATION_DETAIL.ROOT.in(replaseOrganizationPIds)).fetchInto(String.class);
             List<Organization> filterOrganizationList = orgs.stream().filter(or -> or.getStatus() == 1).filter(or -> !replaseOrganizationList.contains(or.getId())).collect(Collectors.toList());
             // 排序
             filterOrganizationList.sort(OrganizationUtil.getOrgComparator());
             List<Organization> OrganizationList = OrganizationUtil.treeOrganizations(filterOrganizationList, OrganizationUtil.NAME_COMPOSITE_FALSE).stream().map(m -> {
            	 m.setCreateTime(null);
            	 m.setOrder(null);
//            	 m.setParentId(null);
            	 m.setStatus(null);
            	 return m;
            	 }).collect(Collectors.toList());
             cache.set(CACHE_GRANTED + memberId + "#" + uri, OrganizationList, 60 * 60);
             return OrganizationList;
		});
	}

	@Override
    public void findGrantedOrganizationNoReturn(String memberId, String uri, Optional<String> organizationId) {
		List<Condition> conditions = initPathConditions(organizationId);

        List<Organization> list = organizationDao.execute(d -> {
             Condition listCondition = GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                        .or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO).and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID))
														 .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())));


           List<Organization> orgs = d.selectDistinct(ORGANIZATION.CODE, ORGANIZATION.ID, ORGANIZATION.NAME, ORGANIZATION.LEVEL, ORGANIZATION.PARENT_ID,
                 ORGANIZATION.STATUS, ORGANIZATION.CREATE_TIME, ORGANIZATION.ORDER, ORGANIZATION.DEPTH)
                .from(GRANT)
                .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                .where(conditions).and(listCondition).fetch(r -> {
                    Organization o = new Organization();
                    o.setCode(r.getValue(ORGANIZATION.CODE));
                    o.setId(r.getValue(ORGANIZATION.ID));
                    o.setName(r.getValue(ORGANIZATION.NAME));
                    o.setLevel(r.getValue(ORGANIZATION.LEVEL));
                    o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
                    o.setStatus(r.getValue(ORGANIZATION.STATUS));
                    o.setCreateTime(r.getValue(ORGANIZATION.CREATE_TIME));
                    o.setOrder(r.getValue(ORGANIZATION.ORDER));
                    o.setDepth(r.getValue(ORGANIZATION.DEPTH));
                    return o;
                });

             // 去除禁用的组织
             List<String> replaseOrganizationPIds = orgs.stream().filter(or -> or.getStatus() != 1).map(Organization :: getId).collect(Collectors.toList());
             List<String> replaseOrganizationList = d.selectDistinct(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL).where(ORGANIZATION_DETAIL.ROOT.in(replaseOrganizationPIds)).fetchInto(String.class);
             List<Organization> filterOrganizationList = orgs.stream().filter(or -> or.getStatus() == 1).filter(or -> !replaseOrganizationList.contains(or.getId())).collect(Collectors.toList());
             // 排序
             filterOrganizationList.sort(OrganizationUtil.getOrgComparator());
             List<Organization> OrganizationList = OrganizationUtil.treeOrganizations(filterOrganizationList, OrganizationUtil.NAME_COMPOSITE_FALSE).stream().map(m -> {
                 m.setCreateTime(null);
                 m.setOrder(null);
//               m.setParentId(null);
                 m.setStatus(null);
                 return m;
                 }).collect(Collectors.toList());
             return OrganizationList;
        });
        cache.set(CACHE_GRANTED + memberId + "#" + uri, list, 60 * 60);
    }

	@Override
	public HashSet<String> findGrantedOrganization2(String memberId, String uri, Optional<String> organizationId) {
		List<Condition> conditions = initPathConditions(organizationId);

		Map<Integer, List<String>> result = new HashMap<>();
		organizationDao.execute(d -> {
			Condition listCondition = GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
					.or(GRANT_ORGANIZATION.CHILD_FIND
							.eq(GrantOrganization.CHILD_FIND_NO)
							.and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID))
					).and(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))
				     .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));

			d.selectDistinct(
							ORGANIZATION.DEPTH,
							ORGANIZATION.PATH
					)
					.from(GRANT)
					.innerJoin(GRANT_MEMBER)
					.on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ROLE_MENU)
					.on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
					.innerJoin(MENU)
					.on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
					.leftJoin(GRANT_ORGANIZATION)
					.on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ORGANIZATION.as("grant_org"))
					.on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
					.leftJoin(ORGANIZATION)
					.on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
					.where(conditions).and(listCondition).fetch(r -> {
						result.computeIfAbsent(
										r.getValue(ORGANIZATION.DEPTH),
										k -> new ArrayList<>())
								.add(r.getValue(ORGANIZATION.PATH));
						return null;
					});
			return null;
		});
		Optional<Integer> min = result.keySet().stream().min(Integer::compareTo);
		AtomicReference<HashSet<String>> resultSet = new AtomicReference<>(new HashSet<>());
		min.ifPresent(m -> {
			resultSet.set(new HashSet<>(result.get(m)));
		});
		return resultSet.get();
	}


	@Override
	public List<GrantOrganization> findGrantOrganizationByMemberAndUri(String memberId, String uri) {
		return grantOrganizationDao.execute(d -> {
			return d
					.selectDistinct(GRANT_ORGANIZATION.ORGANIZATION_ID, GRANT_ORGANIZATION.CHILD_FIND).from(GRANT)
					.innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
					.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID)).innerJoin(MENU)
					.on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID))).innerJoin(GRANT_ORGANIZATION)
					.on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
				    .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
					.fetch(r -> {
					    GrantOrganization o = new GrantOrganization();
					    o.setOrganizationId(r.getValue(GRANT_ORGANIZATION.ORGANIZATION_ID));
					    o.setChildFind(r.getValue(GRANT_ORGANIZATION.CHILD_FIND));
					    return o;
	                });

		});
	}


	/**
	 * 权限操作类型， 根据管理员所具有所有角色uri判定，新增的角色权限继承管理员哪一个角色的权限的操作类型
	 *
	 */
	@Override
	public String getOperatorTypes(String memberId,String roleId){

		List<Menu> uriLists = moreRoleAndMenu(memberId);

	     // 以管理员角色分组
	     Map<String, List<Menu>> roleMenuMap = uriLists.stream().collect(Collectors.groupingBy(Menu::getRoleId));

	    if (roleMenuMap.size() == 1 && null != uriLists.get(0)) {
	    	return uriLists.get(0).getOperatorTypes();
		} else { // 多角色多授权

			List<String> roleIdList = roleMenuMap.keySet().stream().collect(Collectors.toList());

			Optional<Role> role = roleDao.getOptional(roleId);

			if (role.isPresent() && roleIdList.contains(roleId)) {
				// 继承原生角色操作类型
				return roleMenuMap.get(roleId).get(0).getOperatorTypes();
			} else if (role.isPresent() && roleIdList.contains(role.get().getParentId())) {
				// 继承原生父角色操作类型
				return roleMenuMap.get(role.get().getParentId()).get(0).getOperatorTypes();
			} else {
				// 多角色菜单 全包含 或者 部分包含
				return otherMoreRoleOperatorType(roleId, roleMenuMap);
			}
		}
	}

	/**
	 * 多角色菜单 全包含 或者 部分包含
	 * @param roleId       角色id
	 * @param roleMenuMap  角色对应菜单
	 * @return
	 */
	private String otherMoreRoleOperatorType(String roleId, Map<String, List<Menu>> roleMenuMap) {

		List<String> childUriList = roleList(roleId);
		List<String> moreRoleIdList = Lists.newArrayList();
		List<String> otherRoleIdList = Lists.newArrayList();
		roleMenuMap.values().forEach(rs -> {
			List<String> roleUriList = rs.stream().map(Menu :: getUri).filter(u -> null != u).collect(Collectors.toList());
			if (roleUriList.containsAll(childUriList) && null != rs.get(0)) {
				moreRoleIdList.add(rs.get(0).getRoleId());
			} else {
				otherRoleIdList.add(rs.get(0).getRoleId());
			}
		});

		if (moreRoleIdList.size() == 0 && otherRoleIdList.size() > 0) {
			// 多角色交叉包含，禁止授权
			return "";
//			return retainAllOperatorTypes(getOperatorTypes(roleMenuMap, otherRoleIdList));
		} else if (moreRoleIdList.size() == 1) {
			// 其他角色的所有菜单被一个原生本角色完全包含
			return roleMenuMap.get(moreRoleIdList.get(0)).get(0).getOperatorTypes();
		} else {
			// 多角色菜单 全包含 交集
			return retainAllOperatorTypes(getOperatorTypes(roleMenuMap, moreRoleIdList));
		}
	}

	private List<String> getOperatorTypes(Map<String, List<Menu>> roleMenuMap, List<String> otherRoleIdList) {
		List<String> opersList = Lists.newArrayList();
		otherRoleIdList.forEach(or -> {
			opersList.add(roleMenuMap.get(or).get(0).getOperatorTypes());
		});
		return opersList;
	}

    /**
     * 角色 组织授权节点 操作类型 合集
     * @param operatorTypes
     * @return
     */
    private String allOperatorTypes(List<String> operatorTypes) {
    	Set<String> orgOpers = Sets.newHashSet();
    	operatorTypes.forEach(g -> {
    		orgOpers.addAll(Arrays.asList(g.split(",")));
		});
    	return Joiner.on(",").join(orgOpers);
    }

	/**
	 * 角色菜单
	 * @param roleId 角色id
	 * @return
	 */
	private List<String> roleList(String roleId) {
		List<String> childList = menuDao.execute(a ->
			a.selectDistinct(MENU.URI)
			.from(MENU)
			.innerJoin(ROLE_MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
			.innerJoin(ROLE).on(ROLE_MENU.ROLE_ID.eq(ROLE.ID))
			.where(ROLE.ID .eq(roleId))
			.fetch(MENU.URI)).stream().filter(r -> null != r).collect(Collectors.toList());
		return childList;
	}

	/**
	 *  管理员菜单
	 * @param memberId  用户id
	 * @return
	 */
	private List<Menu> moreRoleAndMenu(String memberId) {
		// 管理员
		List<Menu> uriLists = grantDao.execute(a ->
         a.selectDistinct(MENU.URI, GRANT.OPERATOR_TYPES, GRANT.ROLE_ID)
   		  .from(GRANT)
   		  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
   		  .innerJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
   		  .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
   		  .where(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
   		  .fetch(r -> {
   			Menu m = r.into(MENU).into(Menu.class);
   			m.setOperatorTypes(r.getValue(GRANT.OPERATOR_TYPES));
   			m.setRoleId(r.getValue(GRANT.ROLE_ID));
   			return m;
   		  }));
		return uriLists;
	}

	/**
	 * 单角色
	 * @param memberId 用户id
	 * @param roleId   所选角色id
	 * @return
	 */
	@Override
	public Map<String, Set<String>> findGrantOrganizationByRoleId(String memberId, String roleId) {

		List<String> roleList = grantDao.execute(a ->
	        a.selectDistinct(GRANT.ROLE_ID)
	  		  .from(GRANT)
	  		  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
	  		  .where(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
	   		  .fetch(GRANT.ROLE_ID));
		if (roleList.size() == 1) {
			// 单角色
			return findGrantOrganizations(memberId, roleList.get(0), Organization.ROOT_ORGANIZATION);
		} else {
			// 多角色
			return moreRoleGrantOrganization(memberId, roleId, roleList);
		}
	}

	/**
	 * 多角色
	 * @param memberId 用户id
	 * @param roleId   所选角色id
	 * @param roleList 用户所有角色id
	 * @return
	 */
	private Map<String, Set<String>> moreRoleGrantOrganization(String memberId, String roleId, List<String> roleList) {
		Optional<Role> role = roleDao.getOptional(roleId);

		if (role.isPresent() && roleList.contains(roleId)) {

			return findGrantOrganizations(memberId, roleId, Organization.ROOT_ORGANIZATION);
		} else if (role.isPresent() && roleList.contains(role.get().getParentId())) {

			return findGrantOrganizations(memberId, role.get().getParentId(), Organization.ROOT_ORGANIZATION);
		} else {

			return otherRoleGrantOrganization(memberId, roleId);
		}
	}

	/**
	 * 其他衍生角色
	 * @param memberId 用户id
	 * @param roleId   所选角色id
	 * @return
	 */
	private Map<String, Set<String>> otherRoleGrantOrganization(String memberId, String roleId) {

		List<String> childList = roleList(roleId);

		List<Menu> menuList = moreRoleAndMenu(memberId);

		Map<String, List<Menu>> roleMenuMap = menuList.stream().collect(Collectors.groupingBy(Menu::getRoleId));

		List<String> roleIdList = Lists.newArrayList();
		List<String> otherroleIdList = Lists.newArrayList();

		roleMenuMap.values().forEach(rs -> {
			List<String> roleUriList = rs.stream().map(Menu :: getUri).filter(u -> null != u).collect(Collectors.toList());
			if (roleUriList.containsAll(childList) && null != rs.get(0)) {
				roleIdList.add(rs.get(0).getRoleId());
			} else {
				otherroleIdList.add(rs.get(0).getRoleId());
			}
		});

		if (roleIdList.size() == 0 && otherroleIdList.size() > 0) {
			// 多角色交叉包含，
//			Map<String, Set<String>> noGrantOrganizationMap = new HashMap<>();
//			noGrantOrganizationMap.put(Organization.INCLUDE_KEY, new HashSet<>());
//			noGrantOrganizationMap.put(Organization.NOT_INCLUDE_KEY, new HashSet<>());
			return null;
//			return otherMoreGrantRoleMap(memberId, otherroleIdList);
		} else if (roleIdList.size() == 1) {
			// 其他角色的所有菜单被一个原生本角色完全包含
			return findGrantOrganizations(memberId, roleIdList.get(0), Organization.ROOT_ORGANIZATION);
		} else {
			// 其他角色的所有菜单被多个原生本角色完全包含， 组织合集
			return otherMoreGrantRoleMap(memberId, roleIdList);
		}
	}

	/**
	 * 其他角色的所有菜单被多个原生本角色完全包含
	 * @param memberId    管理员id
	 * @param roleIdList  角色id
	 * @return
	 */
	private Map<String, Set<String>> otherMoreGrantRoleMap(String memberId, List<String> roleIdList) {
		Map<String, Set<String>> rolesMap  = Maps.newHashMap();
		Set<String> includeSet = Sets.newHashSet();
		Set<String> noIncludeSet = Sets.newHashSet();
		roleIdList.forEach(ri -> {
			Map<String, Set<String>> grantRolesMap = findGrantOrganizations(memberId, ri, Organization.ROOT_ORGANIZATION);
			includeSet.addAll(grantRolesMap.get(Organization.INCLUDE_KEY));
			noIncludeSet.addAll(grantRolesMap.get(Organization.NOT_INCLUDE_KEY));
			rolesMap.put(Organization.INCLUDE_KEY, includeSet);
			rolesMap.put(Organization.NOT_INCLUDE_KEY, noIncludeSet);
		});
		return rolesMap;
	}

	@Override
	public Map<String, Set<String>> findGrantOrganizations(String memberId, String roleId, String rootOrganizationId) {

        List<Condition> collect = Stream.of(
			Optional.of(memberId).map(GRANT_MEMBER.MEMBER_ID :: eq),
			Optional.ofNullable(roleId).map(ROLE.ID :: eq),
			Optional.of(ROLE.ID.isNotNull()),
			Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
         ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

		List<Organization> organizationList = grantDao.execute(e ->
        	e.select(GRANT_ORGANIZATION.ORGANIZATION_ID,
        			ORGANIZATION.PATH, GRANT_ORGANIZATION.CHILD_FIND)
            .from(GRANT)
            .innerJoin(GRANT_ORGANIZATION).on(GRANT.ID.eq(GRANT_ORGANIZATION.GRANT_ID))
	        .innerJoin(GRANT_MEMBER).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
	        .innerJoin(ORGANIZATION).on(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID))
	        .leftJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
            .where(collect)
	        .fetch(r -> {
	            Organization organization = new Organization();
	            organization.setId(r.getValue(GRANT_ORGANIZATION.ORGANIZATION_ID));
	            organization.setPath(r.getValue(ORGANIZATION.PATH));
	            organization.setChildFind(String.valueOf(r.getValue(GRANT_ORGANIZATION.CHILD_FIND)));
	            return organization;
	        }));

        return doGrnatOrganizationPath(rootOrganizationId, organizationList);
    }

	@Override
	public Map<String, Set<String>> findGrantOrganizationByUri(String memberId, String uri, String rootOrganizationId) {
        List<Condition> collect = Stream.of(
    			Optional.of(memberId).map(GRANT_MEMBER.MEMBER_ID :: eq),
				Optional.ofNullable(uri).map(x->MENU.URI.eq(x).or(MENU.ID.eq(x))),
				Optional.of(ROLE.ID.isNotNull()),
				Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
             ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

		List<Organization> organizationList = grantDao.execute(e ->
        	e.select(GRANT_ORGANIZATION.ORGANIZATION_ID,
        			ORGANIZATION.PATH, GRANT_ORGANIZATION.CHILD_FIND)
            .from(GRANT)
            .innerJoin(GRANT_ORGANIZATION).on(GRANT.ID.eq(GRANT_ORGANIZATION.GRANT_ID))
	        .innerJoin(GRANT_MEMBER).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
	        .innerJoin(ORGANIZATION).on(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID))
	        .leftJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
	        .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
	        .leftJoin(MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
            .where(collect)
	        .fetch(r -> {
	            Organization organization = new Organization();
	            organization.setId(r.getValue(GRANT_ORGANIZATION.ORGANIZATION_ID));
	            organization.setPath(r.getValue(ORGANIZATION.PATH));
	            organization.setChildFind(String.valueOf(r.getValue(GRANT_ORGANIZATION.CHILD_FIND)));
	            return organization;
	        }));
		return doGrnatOrganizationPath(rootOrganizationId, organizationList);
	 }

	 /**
	  * 查询组织授权节点
	  * @param rootOrganizationId
	  * @param organizationList
	  * @return
	  */
	 private Map<String, Set<String>> doGrnatOrganizationPath(String rootOrganizationId,
			List<Organization> organizationList) {
		Map<String, Set<String>> grantOrganizationMap = new HashMap<>();
		// 1.过滤组织包含子
		Set<String> includeSet = organizationList.stream().filter(r -> r.getChildFind().equals(String.valueOf(Organization.CHILD_FIND_YES))).map(r -> r.getPath()).collect(Collectors.toSet());
		// 是否是超级管理权限，如果是直接返回
		if (includeSet.contains(rootOrganizationId + ",")) {
			grantOrganizationMap.put(Organization.INCLUDE_KEY, includeSet);
			grantOrganizationMap.put(Organization.NOT_INCLUDE_KEY, new HashSet<>());
			return grantOrganizationMap;
		}
		// 父子路径去除，例如 1,2,; 1,2,3,;1,2,4,; 结果只要父路径1,2,
		Set<String> filterIncludeSet = new HashSet<>();
		includeSet.forEach(item -> {
			if (filterIncludeSet.stream().anyMatch(item :: startsWith)) {
				return;
			}
			filterIncludeSet.removeIf(it -> it.startsWith(item));
			filterIncludeSet.add(item);
		});
		grantOrganizationMap.put(Organization.INCLUDE_KEY, filterIncludeSet);
		// 2.过滤组织不包含子，filterIncludeSet 如果路径中存在 notIncludeSet中值，过滤掉
		Set<String> notIncludeSet = organizationList.stream().filter(r -> r.getChildFind().equals(String.valueOf(Organization.CHILD_FIND_NO))).map(r -> r.getId()).collect(Collectors.toSet());
		// 如果没有，直接返回
		if (notIncludeSet.isEmpty()) {
			grantOrganizationMap.put(Organization.NOT_INCLUDE_KEY, new HashSet<>());
			return grantOrganizationMap;
		}
		Set<String> filterNotIncludeSet = new HashSet<>();
		notIncludeSet.forEach(item -> {
			if (filterIncludeSet.stream().anyMatch(r -> r.endsWith(item + ","))){
				return;
			}
			filterNotIncludeSet.add(item);
		});
		grantOrganizationMap.put(Organization.NOT_INCLUDE_KEY, filterNotIncludeSet);
		return grantOrganizationMap;
	}

	public int isGrantTopic(String uri,String memberId) {
		List<Condition> conditions = Stream.of(
				Optional.of(memberId).map(GRANT_MEMBER.MEMBER_ID :: eq),
				Optional.of(uri).map(MENU.URI :: eq),
				Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
		).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

		SelectOnConditionStep<Record> stepCount = grantMemberDao.execute(e->e.select(Fields.start().add(GRANT_MEMBER.ID.count()).end()).from(GRANT_MEMBER)
					.leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
					.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
					.leftJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
		);
	 	return grantMemberDao.execute(e->e.fetchCount(stepCount.where(conditions)));
	}

	@Override
	public List<String> findMemberByOrganizationAndUri(String orgPath, String uri) {
		if (StringUtils.isEmpty(orgPath)) {
			return null;
		}
		String[] orgPaths = orgPath.split(",");
		for (int i = orgPaths.length - 1; i >= 0; i--) {
			String orgId = orgPaths[i];
			List<String> memberIds = grantMemberDao.execute(x -> x.selectDistinct(Fields.start().add(GRANT_MEMBER.MEMBER_ID).end())
					.from(GRANT_MEMBER)
					.leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
					.leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
					.innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
					.leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
					.where(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(orgId))
                    .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                    .and(GRANT.OPERATOR_TYPES.contains("0"))
					.fetch(GRANT_MEMBER.MEMBER_ID));
			if (memberIds != null && memberIds.size()>0) {
				return memberIds;
			}
		}
		return null;
	}

	@Override
	public String findMemberByOrgPathAndUri(String orgPath, String uri) {

        String[] orgPaths = orgPath.split(",");

        if (orgPaths == null || orgPaths.length == 0) {
			return StringUtils.EMPTY;
		}

        String result = grantMemberDao.execute(x -> x.selectDistinct(Fields.start().add(GRANT_MEMBER.MEMBER_ID).end())
                .from(GRANT_MEMBER)
                .leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                .innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                .where(GRANT_ORGANIZATION.ORGANIZATION_ID.in(orgPaths))
                .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                .and(GRANT.OPERATOR_TYPES.contains("0"))
                .fetch(GRANT_MEMBER.MEMBER_ID)).stream().distinct().collect(Collectors.joining(","));;
		return result;
	}

	@Override
	public List<String> getAllMemberIds() {
		return grantMemberDao.execute(e ->
				e.selectDistinct(GRANT_MEMBER.MEMBER_ID)
						.from(GRANT_MEMBER))
				.fetch(GRANT_MEMBER.MEMBER_ID);
	}

    @Override
    public Map<String, String> findPaasOperatorType(String memberId, String menuId, String rootOrganizationId) {
		return getOperatorTypeData((MENU.URI.eq(menuId).or(MENU.ID.eq(menuId))).and(GRANT_MEMBER.MEMBER_ID.eq(memberId)), rootOrganizationId);
    }

	private Map<String, String> getOperatorTypeData(Condition condition, String rootOrganizationId) {
		Map<String, String> operatorTypeMap = new HashMap<>();
		String allOperatorTypes = "0,1,2,3,4";
		List<Organization> organizationList = grantDao.execute(e -> e.select(GRANT.ORGANIZATION_ID, ORGANIZATION.PATH, GRANT_ORGANIZATION.CHILD_FIND, GRANT.OPERATOR_TYPES)
						.from(GRANT)
						.leftJoin(GRANT_ORGANIZATION).on(GRANT.ID.eq(GRANT_ORGANIZATION.GRANT_ID))
						.leftJoin(GRANT_MEMBER).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
						.innerJoin(ORGANIZATION).on(GRANT.ORGANIZATION_ID.eq(ORGANIZATION.ID))
						.leftJoin(ROLE_MENU).on(GRANT.ROLE_ID.eq(ROLE_MENU.ROLE_ID))
						.leftJoin(MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
						.leftJoin(ROLE).on(ROLE.ID.eq(ROLE_MENU.ROLE_ID))
						.where(condition.and(ROLE.ID.isNotNull())
							.and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))))
				.fetch(r -> {
					Organization organization = new Organization();
					organization.setId(r.getValue(GRANT.ORGANIZATION_ID));
					organization.setPath(r.getValue(ORGANIZATION.PATH));
					organization.setChildFind(String.valueOf(r.getValue(GRANT_ORGANIZATION.CHILD_FIND)));
					organization.setOperatorTypes(r.getValue(GRANT.OPERATOR_TYPES));
					return organization;
				});
		// 过滤出包含子组织
		List<Organization> pathOrganizationList = organizationList.stream().filter(r -> r.getChildFind().equals(Organization.CHILD_FIND_YES_STR)).collect(Collectors.toList());

		// 如果拥有超级管理权限，就直接返回
		Optional<Organization> optionalOrganization = pathOrganizationList.stream().filter(r -> r.getPath().equals(rootOrganizationId + ",") && r.getOperatorTypes().equals(allOperatorTypes)).findFirst();
		if (optionalOrganization.isPresent()) {
			operatorTypeMap.put(rootOrganizationId + ",", allOperatorTypes);
			return operatorTypeMap;
		}
		pathOrganizationList.forEach(r -> {
			if (operatorTypeMap.containsKey(r.getPath())) {
				String operatorTypes = operatorTypeMap.get(r.getPath());
				if (!r.getOperatorTypes().equals(operatorTypes)) {
					operatorTypeMap.put(r.getPath(), operatorTypes + ",".concat(r.getOperatorTypes()));
				}
			} else {
				operatorTypeMap.put(r.getPath(), r.getOperatorTypes());
			}
		});
		// 过滤不包含子组织
		List<Organization> idOrganizationList = organizationList.stream().filter(r -> r.getChildFind().equals(Organization.CHILD_FIND_NO_STR)).collect(Collectors.toList());
		if (ObjectUtils.isEmpty(idOrganizationList) || idOrganizationList.isEmpty()) {
			return operatorTypeMap;
		}
		idOrganizationList.forEach(r -> {
			if(operatorTypeMap.containsKey(r.getId())){
				//操作权限取交集
				String operatorTypesOld = operatorTypeMap.get(r.getId());
				String resultOperatorTypes = String.join(",", Sets.intersection(Sets.newHashSet(r.getOperatorTypes().split(",")), Sets.newHashSet(operatorTypesOld.split(","))));
				operatorTypeMap.put(r.getId(), resultOperatorTypes);
				return;
			}
			operatorTypeMap.put(r.getId(), r.getOperatorTypes());

		});
		return operatorTypeMap;
	}

    private List<Condition> initPathConditions(Optional<String> organizationId) {
		Optional<String> path;
		if (organizationId.isPresent()) {
			String organizationPath = organizationDao.execute(dsl -> dsl
					.select(Fields.start().add(ORGANIZATION.PATH).end())
					.from(ORGANIZATION)
					.where(ORGANIZATION.ID.eq(organizationId.get()))
					.fetchOne(ORGANIZATION.PATH)
			);
			path = Optional.of(organizationPath);
		} else {
			path = Optional.empty();
		}
		return Stream
				.of(path.map(ORGANIZATION.PATH::startsWith))
				.filter(Optional::isPresent)
				.map(Optional::get)
				.collect(Collectors.toList());
	}

	@Override
	public PagedResult<Grant> findMemberPage(int page, int pageSize, Optional<String> nodeId, Optional<String> roleName, Optional<Long> start, Optional<Long> end,
											 Optional<String> operatorType, String memberId, Integer contain, Optional<Integer> type, Optional<String> roleId,
											 Optional<String> memberIdOptional, Optional<String> content, Optional<String> grantId, Optional<String> authorizeOrgId,
											 Integer contains, Optional<String> memberName, Optional<String> fullName) {
		Field<String> oranizationName = ORGANIZATION.NAME.as("oranizationName");
		com.zxy.product.system.jooq.tables.Organization memberOrgTable = ORGANIZATION.as("member_org");


		List<Condition> conditions = Stream.of(
//			Optional.of(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED)),
			roleId.map(ROLE.ID::eq),
			// grantId.map(GRANT.ID::eq),
			roleName.map(ROLE.NAME::contains),
			start.map(GRANT.CREATE_TIME::ge),
			end.map(GRANT.CREATE_TIME::lt),
			type.map(ROLE.TYPE::eq),
			memberIdOptional.map(GRANT_MEMBER.MEMBER_ID::eq),
			operatorType.map(GRANT.OPERATOR_TYPES::eq),
			nodeId.map(orgId-> {
				if (contain == 1) {
					return memberOrgTable.PATH.startsWith(organizationDao.get(orgId).getPath());
				} else {
					return MEMBER.ORGANIZATION_ID.eq(orgId);
				}
			}),
			authorizeOrgId.map(orgId-> {
				if (contains == 1) {
					return ORGANIZATION.PATH.startsWith(organizationDao.get(orgId).getPath());
				} else {
					return GRANT.ORGANIZATION_ID.eq(orgId);
				}
			}),
			memberName.map(MEMBER.NAME::contains),
			fullName.map(MEMBER.FULL_NAME::contains)
		).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
		conditions.add(content.map(c -> MEMBER.FULL_NAME.contains(c).or(MEMBER.NAME.contains(c).or(ORGANIZATION.NAME.contains(c)))).orElse(DSL.trueCondition()));

		Map<String, Set<String>> grantOrganizationPathMap = this.findGrantOrganizationByUri(memberId, Grant.URI, Organization.ROOT_ORGANIZATION);
		CodeUtils.generateOrganizationConditions(grantOrganizationPathMap, conditions);

		// 过滤超级管理员授权
		List<String> grantIds = Arrays.asList("10000");

		Field<String> memberOrgId = memberOrgTable.ID.as("member_org_id");
		Field<String> memberOrgName = memberOrgTable.NAME.as("member_org_name");
		Field<String> memberOrgPath = memberOrgTable.PATH.as("member_org_path");
		Function<SelectSelectStep<?>, SelectConditionStep<?>> ss = t -> t.from(GRANT)
																		 .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
																		 .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
																		 .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
																		 .innerJoin(memberOrgTable).on(memberOrgTable.ID.eq(MEMBER.ORGANIZATION_ID))
																		 .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT.ORGANIZATION_ID))
																		 // 增加查询自己
																		 .where(conditions);

		return PagedResult.create(
			grantDao.execute(d -> ss.apply(d.select(DSL.countDistinct(MEMBER.ID))).fetchOne(DSL.countDistinct(MEMBER.ID))),
			grantDao.execute(d ->
				ss.apply(d.selectDistinct(MEMBER.ID,ROLE.PARENT_ID, ROLE.DESC, ROLE.TYPE, ROLE.CHILD_FLAG, ROLE.NAME,
										GRANT.ID, GRANT.ROLE_ID, GRANT.ORGANIZATION_ID, GRANT.CREATE_TIME, GRANT.OPERATOR_TYPES, GRANT.VALID_DATE, GRANT.VALID_TYPE,
										oranizationName,
					  					MEMBER.FULL_NAME, MEMBER.NAME, memberOrgId, memberOrgName, memberOrgPath))
				  .orderBy(GRANT.CREATE_TIME.desc(), GRANT.ID.desc())
				  .limit((page - 1) * pageSize, pageSize)
			).fetch(x -> {
				Role r = new Role();
				r.setId(x.getValue(GRANT.ROLE_ID));
				r.setName(x.getValue(ROLE.NAME));
				r.setDesc(x.getValue(ROLE.DESC));
				r.setType(x.getValue(ROLE.TYPE));
				r.setParentId(x.getValue(ROLE.PARENT_ID));
				r.setChildFlag(x.getValue(ROLE.CHILD_FLAG));

				Grant g = new Grant();
				g.setId(x.getValue(GRANT.ID));
				g.setRole(r);
				g.setCreateTime(x.getValue(GRANT.CREATE_TIME));
				g.setOperatorTypes(x.getValue(GRANT.OPERATOR_TYPES));
				Integer validType = x.getValue(GRANT.VALID_TYPE);
				g.setValidType(validType);
				if (Objects.equals(validType, Grant.VALID_TYPE_FOREVER)) {
					g.setValidDate(null);
				} else {
					g.setValidDate(x.getValue(GRANT.VALID_DATE));
				}

				Member member = new Member();
				member.setId(x.getValue(MEMBER.ID));
				member.setFullName(x.getValue(MEMBER.FULL_NAME));
				member.setName(x.getValue(MEMBER.NAME));
				Organization memberOrganization = new Organization();
				memberOrganization.setId(x.getValue(memberOrgId));
				memberOrganization.setName(x.getValue(memberOrgName));
				memberOrganization.setPath(x.getValue(memberOrgPath));
				member.setOrganization(memberOrganization);
				g.setMember(member);

				Organization organization = new Organization();
				organization.setId(x.getValue(GRANT.ORGANIZATION_ID));
				organization.setName(x.getValue(oranizationName));
				g.setOrganization(organization);

				g.setOwned(grantIds.contains(g.getId()) || memberId.equals(member.getId()));

				return g;
			})
		);
	}

	@Override
	public Boolean fixGrant() {
		 int page = 1;
		 List<Grant> allGrants = new ArrayList<>();
		 List<Grant> grants;
		 while (true) {
			 int finalPage = page;
			 grants = grantDao.execute(d -> d.select(GRANT.fields())
											 .from(GRANT)
											 .limit((finalPage - 1) * SystemConstant.BATCH_SIZE, SystemConstant.BATCH_SIZE)
											 .fetchInto(Grant.class));
			 if (org.springframework.util.CollectionUtils.isEmpty(grants)) {
				 break;
			 }
			 allGrants.addAll(grants);
			 page++;
		 }
		ArrayList<Grant> insertGrants = new ArrayList<>();
		 HashSet<String> existGrantIds = new HashSet<>();
		ArrayList<GrantMember> updateGrantMembers = new ArrayList<>();
		ArrayList<GrantOrganization> insertGrantOrganizations = new ArrayList<>();
		allGrants.forEach(grant-> addGrantMember(insertGrants, existGrantIds, insertGrantOrganizations, updateGrantMembers, grant));
		grantDao.insert(insertGrants);
		Lists.partition(updateGrantMembers, SystemConstant.BATCH_SIZE)
			 .forEach(list-> grantMemberDao.update(list));
		Lists.partition(insertGrantOrganizations, SystemConstant.BATCH_SIZE)
			 .forEach(list -> grantOrganizationDao.insert(list));
		return true;
	}

	private void addGrantMember(ArrayList<Grant> insertGrants, HashSet<String> existGrantIds,
								ArrayList<GrantOrganization> insertGrantOrganizations,
								ArrayList<GrantMember> updateGrantMembers, Grant grant) {
		List<GrantMember> grantMembers = grantMemberDao.execute(d -> d.select(GRANT_MEMBER.fields())
																 .from(GRANT_MEMBER).where(GRANT_MEMBER.GRANT_ID.eq(grant.getId()))
																 .fetchInto(GrantMember.class));
		for (int i = 0; i < grantMembers.size(); i++) {
			if (existGrantIds.contains(grantMembers.get(i).getGrantId())) {
				GrantMember grantMember = grantMembers.get(i);
				Grant newGrant = new Grant();
				BeanUtils.copyProperties(grant, newGrant);
				newGrant.setId(randomUUID().toString());
				insertGrants.add(newGrant);
				grantMember.setGrantId(newGrant.getId());
				updateGrantMembers.add(grantMember);

				List<GrantOrganization> grantOrganizations = grantOrganizationDao.execute(d -> d.select(GRANT_ORGANIZATION.fields())
																								.from(GRANT_ORGANIZATION).where(GRANT_ORGANIZATION.GRANT_ID.eq(grant.getId()))
																								.fetchInto(GrantOrganization.class));
				for (GrantOrganization grantOrganization : grantOrganizations) {
					GrantOrganization newGrantOrganization = new GrantOrganization();
					BeanUtils.copyProperties(grantOrganization, newGrantOrganization);
					newGrantOrganization.setId(randomUUID().toString());
					newGrantOrganization.setGrantId(newGrant.getId());
					insertGrantOrganizations.add(newGrantOrganization);
				}
			} else {
				existGrantIds.add(grantMembers.get(i).getGrantId());
			}
		}
	}

	@Override
	public Boolean deleteGrantMemberByGrantId(String grantId, String grantMemberId, String currentUserId, String currentUserFullName) {
		Grant deleteGrant = grantDao.execute(d -> d.select(GRANT.ID, MEMBER.ID, MEMBER.FULL_NAME, ROLE.NAME, GRANT_MEMBER.ID)
												   .from(GRANT)
												   .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
												   .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
												   .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
												   .where(GRANT.ID.eq(grantId), MEMBER.ID.eq(grantMemberId))
												   .fetchOne(r -> {
													   Grant grant = new Grant();
													   grant.setId(r.getValue(GRANT.ID));
													   grant.setMemberId(r.getValue(MEMBER.ID));
													   grant.setMemberFullName(r.getValue(MEMBER.FULL_NAME));
													   grant.setRoleName(r.getValue(ROLE.NAME));
													   grant.setGrantMemberId(r.getValue(GRANT_MEMBER.ID));
													   return grant;
												   }));
		sender.send(MessageTypeContent.SYSTEM_GRANT_MEMBER_CLEAR, MessageHeaderContent.ID, grantId,
			MessageHeaderContent.MEMBER_ID, grantMemberId);
		grantMemberDao.delete(deleteGrant.getGrantMemberId());
		long now = System.currentTimeMillis();
		String content = String.format("%s由【%s】撤销了【%s】授权", DateUtil.dateLongToString(now, DateUtil.YYYY_MM_DD_HH_MM),
			currentUserFullName, deleteGrant.getRoleName());
		sender.send(MessageTypeContent.SYSTEM_GRANT_HISTORY_SAVE,
			MessageHeaderContent.CREATE_MEMBER_ID, currentUserId,
			MessageHeaderContent.MEMBER_ID, deleteGrant.getMemberId(),
			MessageHeaderContent.SYSTEM_TIME, String.valueOf(now),
			MessageHeaderContent.OPTER_TYPE, GrantHistory.OPERATION_TYPE_REVOKE.toString(),
			MessageHeaderContent.CONTENT, content);
		return true;
	}

	@Override
	public List<Member> findByMemberNames(List<String> memberNames) {
		return memberDao.execute(dao -> dao
				.select(
						MEMBER.ID,
						MEMBER.NAME,
						MEMBER.FULL_NAME
				)
				.from(MEMBER)
				.where(MEMBER.NAME.in(memberNames))
				.fetch(r -> {
					Member member = new Member();
					member.setId(r.get(MEMBER.ID, String.class));
					member.setName(r.get(MEMBER.NAME, String.class));
					member.setFullName(r.get(MEMBER.FULL_NAME, String.class));
					return member;
				}));
	}
}

