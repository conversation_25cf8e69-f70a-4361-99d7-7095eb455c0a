package com.zxy.product.system.service.support.home;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.zxy.product.system.api.home.HomeSystemService;
import com.zxy.product.system.domain.vo.CustomizeVO;
import com.zxy.product.system.domain.vo.RuleConfigVO;
import com.zxy.product.system.domain.vo.home.Interactive.InteractiveAppVO;
import com.zxy.product.system.domain.vo.home.Interactive.InteractiveParentVO;
import com.zxy.product.system.domain.vo.home.Interactive.InteractivePcVO;
import com.zxy.product.system.domain.vo.home.big.banner.BigBannerAppVO;
import com.zxy.product.system.domain.vo.home.big.banner.BigBannerParentVO;
import com.zxy.product.system.domain.vo.home.big.banner.BigBannerPcVO;
import com.zxy.product.system.domain.vo.home.category.course.CategoryCourseAppVO;
import com.zxy.product.system.domain.vo.home.category.course.CategoryCourseParentVO;
import com.zxy.product.system.domain.vo.home.category.course.CategoryCoursePcVO;
import com.zxy.product.system.domain.vo.home.course.navigation.CourseNavigationAppVO;
import com.zxy.product.system.domain.vo.home.course.navigation.CourseNavigationParentVO;
import com.zxy.product.system.domain.vo.home.course.navigation.CourseNavigationPcVO;
import com.zxy.product.system.domain.vo.home.customize.CustomizeAppVO;
import com.zxy.product.system.domain.vo.home.customize.CustomizeParentVO;
import com.zxy.product.system.domain.vo.home.customize.CustomizePcVO;
import com.zxy.product.system.domain.vo.home.featured.content.FeaturedContentAppVO;
import com.zxy.product.system.domain.vo.home.featured.content.FeaturedContentParentVO;
import com.zxy.product.system.domain.vo.home.featured.content.FeaturedContentPcVO;
import com.zxy.product.system.domain.vo.home.mini.banner.MiniBannerAppVO;
import com.zxy.product.system.domain.vo.home.mini.banner.MiniBannerParentVO;
import com.zxy.product.system.domain.vo.home.mini.banner.MiniBannerPcVO;
import com.zxy.product.system.domain.vo.home.news.NewsAppVO;
import com.zxy.product.system.domain.vo.home.news.NewsParentVO;
import com.zxy.product.system.domain.vo.home.news.NewsPcVO;
import com.zxy.product.system.entity.*;
import org.jooq.*;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Comparator;
import java.util.stream.Collectors;

import static com.zxy.product.system.entity.HomeAdvertisement.CLIENT_TYPE_ALL;
import static com.zxy.product.system.entity.HomeContentTree.DATE_TYPE_COURSE;
import static com.zxy.product.system.entity.HomeContentTree.SHOW_ON;
import static com.zxy.product.system.entity.HomeNews.PROMOTION_ENABLE;
import static com.zxy.product.system.entity.HomeNews.STATE_ENABLE;
import static com.zxy.product.system.jooq.Tables.*;
import static com.zxy.product.system.jooq.tables.HomeContent.HOME_CONTENT;
import static com.zxy.product.system.jooq.tables.HomeModuleConfig.HOME_MODULE_CONFIG;
import static com.zxy.product.system.jooq.tables.HomeNews.HOME_NEWS;
import static com.zxy.product.system.util.DatasetProcessing.*;
import static com.zxy.product.system.util.enums.ClientTypeEnum.*;
import static com.zxy.product.system.util.enums.HomeModelEnum.*;
import static org.jooq.impl.DSL.trueCondition;

/**
 * 十万——重写首页系统Service实现类
 * <AUTHOR>
 * @date 2024年08月02日 16:35
 */
@Service
public class HomeSystemSupport implements HomeSystemService {
    private static  final Logger logger= LoggerFactory.getLogger(HomeSystemSupport.class);
    private static final Integer COURSE=0;
    private static final Integer SUBJECT=1;
    private DSLContext context;

    @Autowired
    public void setContext(DSLContext context){ this.context=context; }

    /**
     * 查询首页配置项信息
     * @param cfgId 首页主配置Id
     * @param clientType 客户端类型
     * @return 首页配置项信息
     */
    @Override
    public List<HomeModuleConfig> homeModuleCfg(String cfgId, Integer clientType) {
        Collection<SelectField<?>> select = doSelect(HOME_MODULE_CONFIG.SORT, HOME_MODULE_CONFIG.APP_SORT,HOME_MODULE_CONFIG.ID,
                HOME_MODULE_CONFIG.REGION_CODE, HOME_MODULE_CONFIG.STYLE, HOME_MODULE_CONFIG.APP_STYLE,
                HOME_MODULE_CONFIG.NAME, HOME_MODULE_CONFIG.HOME_CONFIG_ID, HOME_MODULE_CONFIG.MODULE_CODE,
                HOME_MODULE_CONFIG.LINK_STATE,HOME_MODULE_CONFIG.LINK_ADDRESS);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        return  doSingleRecord(HOME_MODULE_CONFIG, selectStep)
                .where(HOME_MODULE_CONFIG.HOME_CONFIG_ID.eq(cfgId))
                .and(HOME_MODULE_CONFIG.CLIENT_TYPE.in(All.getCode(),clientType))
                .fetch(ew1 -> {
                    HomeModuleConfig moduleCfg = new HomeModuleConfig();
                    moduleCfg.setId(ew1.getValue(HOME_MODULE_CONFIG.ID));
                    moduleCfg.setName(ew1.getValue(HOME_MODULE_CONFIG.NAME));
                    moduleCfg.setStyle(ew1.getValue(HOME_MODULE_CONFIG.STYLE));
                    moduleCfg.setRegionCode(ew1.getValue(HOME_MODULE_CONFIG.REGION_CODE));
                    moduleCfg.setModuleCode(ew1.getValue(HOME_MODULE_CONFIG.MODULE_CODE));
                    moduleCfg.setHomeConfigId(ew1.getValue(HOME_MODULE_CONFIG.HOME_CONFIG_ID));
                    moduleCfg.setSort(ew1.getValue(HOME_MODULE_CONFIG.SORT));
                    moduleCfg.setAppSort(ew1.getValue(HOME_MODULE_CONFIG.APP_SORT));
                    moduleCfg.setAppStyle(ew1.getValue(HOME_MODULE_CONFIG.APP_STYLE));
                    moduleCfg.setLinkState(ew1.getValue(HOME_MODULE_CONFIG.LINK_STATE));
                    moduleCfg.setLinkAddress(ew1.getValue(HOME_MODULE_CONFIG.LINK_ADDRESS));
                    return moduleCfg;
                });
    }

    /**
     * 首页：新闻资讯
     * @param homeCfgId 首页新闻资讯配置Id
     * @param categoryId 分类id
     * @param state 状态 0 未发布 1已发布
     * @param clientType 0全部 1PC 2App
     * @return 首页新闻资讯对象VO
     */
    @Override
    public List<? extends NewsParentVO> newsCfg(String homeCfgId, Optional<String> categoryId, Optional<Integer> state, Integer clientType) {
        Condition condition = Optional.of(state.orElse(0) == 1)
                .map(ew1 -> HOME_NEWS.PROMOTION.eq(PROMOTION_ENABLE).and(HOME_NEWS.STATE.eq(STATE_ENABLE)))
                .orElse(HOME_NEWS.STATE.eq(STATE_ENABLE));
        Collection<SelectField<?>> select = this.doNewsSelectField(clientType);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        return doSingleRecord(HOME_NEWS, selectStep)
                .where(condition)
                .and(HOME_NEWS.MODULE_CONFIG_ID.eq(homeCfgId))
                .and(categoryId.map(HOME_NEWS.CATEGORY_ID::eq).orElse(trueCondition()))
                .fetch(ew0 -> this.doConvertNews(clientType, ew0));
    }

    /**
     * JOOQ根据客户端类型查询首页新闻资讯属性集合
     * @param clientType 客户端类型 1PC  2App 0全部
     * @return JOOQ查询首页新闻资讯属性集合
     */
    private Collection<SelectField<?>> doNewsSelectField(Integer clientType) {
        return Optional.ofNullable(clientType)
                .filter(ew1 -> Objects.equals(PC.getCode(), ew1))
                .map(ew2 -> doSelect(HOME_NEWS.ID, HOME_NEWS.PROMOTION, HOME_NEWS.TITLE))
                .orElseGet(() -> doSelect(
                        HOME_NEWS.TITLE, HOME_NEWS.VISITORS,
                        HOME_NEWS.CREATE_TIME, HOME_NEWS.ID, HOME_NEWS.SUMMARY
                ));
    }

    /**
     * JOOQ根据客户端类型转化新闻资讯的回显VO
     * @param clientType 客户端类型 1PC  2App 0全部
     * @param ew0 JOOQ查询记录流
     * @return 首页新闻资讯回显VO
     */
    private NewsParentVO doConvertNews(Integer clientType, Record ew0) {
        return Optional.ofNullable(clientType)
                .filter(ew1 -> Objects.equals(PC.getCode(), ew1))
                .map(ew2 -> {
                    NewsPcVO newsPcVO = new NewsPcVO();
                    newsPcVO.setId(ew0.getValue(HOME_NEWS.ID));
                    newsPcVO.setTitle(ew0.getValue(HOME_NEWS.TITLE));
                    newsPcVO.setPromotion(ew0.getValue(HOME_NEWS.PROMOTION));
                    return (NewsParentVO) newsPcVO;
                }).orElseGet(() -> {
                    NewsAppVO newsAppVO = new NewsAppVO();
                    newsAppVO.setId(ew0.getValue(HOME_NEWS.ID));
                    newsAppVO.setTitle(ew0.getValue(HOME_NEWS.TITLE));
                    newsAppVO.setSummary(ew0.getValue(HOME_NEWS.SUMMARY));
                    newsAppVO.setVisitors(ew0.getValue(HOME_NEWS.VISITORS));
                    newsAppVO.setCreateTime(ew0.getValue(HOME_NEWS.CREATE_TIME));
                    return newsAppVO;
                });
    }


    /**
     * 首页：课程导航
     * @param cfgId 首页课程导航配置Id
     * @param typeOpt 类型
     * @param clientType 客户端类型 1PC  2App 0全部
     * @return 首页课程导航对象VO
     */
    @Override
    public List<? extends CourseNavigationParentVO> courseNavigationCfg(
            String cfgId, Optional<String> typeOpt, Integer clientType
    ) {
        List<HomeContentTree> contentTreeCollect = this.doBuildContentTree(cfgId, typeOpt);
        return contentTreeCollect.parallelStream()
                .map(ew1->this.doConvertCourseNavigation(ew1,clientType))
                .collect(Collectors.toList());
    }

    /**
     * JOOQ根据客户端类型转化课程导航的回显VO
     * @param contentTree 查询组装的课程导航POJO
     * @param clientType 客户端类型 1PC  2App 0全部
     * @return 首页新闻资讯回显VO
     */
    private CourseNavigationParentVO doConvertCourseNavigation(HomeContentTree contentTree, Integer clientType) {
        return Optional.ofNullable(clientType)
                .filter(ew1 -> Objects.equals(PC.getCode(), ew1))
                .map(ew2 -> {
                    CourseNavigationPcVO courseNavigationPcVO = this.doCourseNavigationPcVO(contentTree);
                    Optional.ofNullable(contentTree.getChildren()).filter(CollectionUtils::isNotEmpty)
                            .ifPresent(ew3-> courseNavigationPcVO.setChildren(ew3.parallelStream().map(this::doCourseNavigationPcVO).collect(Collectors.toList())));
                    return (CourseNavigationParentVO)courseNavigationPcVO;
                }).orElseGet(()->{
                    CourseNavigationAppVO courseNavigationAppVO = this.doCourseNavigationAppVO(contentTree);
                    Optional.ofNullable(contentTree.getChildren()).filter(CollectionUtils::isNotEmpty)
                            .ifPresent(ew4-> courseNavigationAppVO.setChildren(ew4.parallelStream().map(this::doCourseNavigationAppVO).collect(Collectors.toList())));
                    return courseNavigationAppVO;
                });
    }

    /**
     * 执行赋值课程导航PC回显VO
     * @param contentTree 查询组装的课程导航POJO
     * @return 课程导航PC回显VO
     */
    private CourseNavigationPcVO doCourseNavigationPcVO(HomeContentTree contentTree){
        CourseNavigationPcVO courseNavigationPcVO = new CourseNavigationPcVO();
        courseNavigationPcVO.setUrl(contentTree.getUrl());
        courseNavigationPcVO.setDataName(contentTree.getDataName());
        return courseNavigationPcVO;
    }

    /**
     * 执行赋值课程导航App回显VO
     * @param contentTree 查询组装的课程导航POJO
     * @return 课程导航App回显VO
     */
    private CourseNavigationAppVO doCourseNavigationAppVO(HomeContentTree contentTree) {
        CourseNavigationAppVO courseNavigationAppVO=new CourseNavigationAppVO();
        courseNavigationAppVO.setId(contentTree.getId());
        courseNavigationAppVO.setDataName(contentTree.getDataName());
        courseNavigationAppVO.setSort(contentTree.getSort());
        courseNavigationAppVO.setDataId(contentTree.getDataId());
        courseNavigationAppVO.setUrl(contentTree.getUrl());
        courseNavigationAppVO.setDataType(contentTree.getDataType());
        courseNavigationAppVO.setImgPath(contentTree.getImgPath());
        courseNavigationAppVO.setShow(contentTree.getShow());
        courseNavigationAppVO.setType(contentTree.getType());
        courseNavigationAppVO.setParentId(contentTree.getParentId());
        return courseNavigationAppVO;
    }

    /**
     * 组装分类课程|课程导航数据集合
     * @param homeCfgId 首页模块配置Id
     * @param type 类型
     * @return 分类课程|课程导航数据集合
     */
    private List<HomeContentTree> doBuildContentTree(String homeCfgId,  Optional<String> type){
        Collection<SelectField<?>> select = doSelect(HOME_CONTENT_TREE.IMG_PATH, HOME_CONTENT_TREE.DATA_TYPE,HOME_CONTENT_TREE.ID,
                HOME_CONTENT_TREE.DATA_ID, HOME_CONTENT_TREE.DATA_NAME, HOME_CONTENT_TREE.PARENT_ID,
                HOME_CONTENT_TREE.URL, HOME_CONTENT_TREE.TYPE, HOME_CONTENT_TREE.SHOW, HOME_CONTENT_TREE.SORT);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        List<HomeContentTree> contentTreeCollect = doSingleRecord(HOME_CONTENT_TREE, selectStep)
                .where(HOME_CONTENT_TREE.MODULE_CONFIG_ID.eq(homeCfgId))
                .orderBy(HOME_CONTENT_TREE.SORT.asc(), HOME_CONTENT_TREE.CREATE_TIME.desc())
                .fetch(ew2 -> {
                    HomeContentTree contentTree = new HomeContentTree();
                    contentTree.setId(ew2.getValue(HOME_CONTENT_TREE.ID));
                    contentTree.setDataId(ew2.getValue(HOME_CONTENT_TREE.DATA_ID));
                    contentTree.setDataName(ew2.getValue(HOME_CONTENT_TREE.DATA_NAME));
                    contentTree.setUrl(ew2.getValue(HOME_CONTENT_TREE.URL));
                    String parentId = ew2.getValue(HOME_CONTENT_TREE.PARENT_ID);
                    contentTree.setParentId(Strings.isNullOrEmpty(parentId)? StringUtils.EMPTY:parentId);
                    contentTree.setShow(ew2.getValue(HOME_CONTENT_TREE.SHOW));
                    contentTree.setSort(ew2.getValue(HOME_CONTENT_TREE.SORT));
                    contentTree.setType(ew2.getValue(HOME_CONTENT_TREE.TYPE));
                    contentTree.setImgPath(ew2.getValue(HOME_CONTENT_TREE.IMG_PATH));
                    contentTree.setDataType(ew2.getValue(HOME_CONTENT_TREE.DATA_TYPE));
                    return contentTree;
                });
        List<Integer> typeCollect = type.map(ew1 -> Arrays.stream(ew1.split(",")).map(Integer::parseInt).collect(Collectors.toList())).orElse(null);
        contentTreeCollect = contentTreeCollect.parallelStream()
                .filter(ew3 -> (CollectionUtils.isEmpty(typeCollect) || typeCollect.contains(ew3.getType())) && SHOW_ON.equals(ew3.getShow()))
                .collect(Collectors.toList());
        return this.doBuildContentTree(contentTreeCollect);
    }

    /**
     * 构建课程导航模拟树
     * @param contentTreeCollect 首页课程导航数据集合
     * @return 处理后的数据集合
     */
    private List<HomeContentTree> doBuildContentTree(List<HomeContentTree> contentTreeCollect) {
        contentTreeCollect = Optional.of(contentTreeCollect)
                .filter(CollectionUtils::isNotEmpty)
                .map(ew1 -> {
                    // 分组：将节点按照父节点 ID 分组
                    Map<String, List<HomeContentTree>> groupParentCollect = ew1.parallelStream()
                            .collect(Collectors.groupingBy(HomeContentTree::getParentId));
                    // 获取根节点列表并排序
                    List<HomeContentTree> rootCollect = groupParentCollect.getOrDefault("", Collections.emptyList());
                    rootCollect.sort(Comparator.comparingInt(HomeContentTree::getSort));
                    // 递归构建子树
                    rootCollect.forEach(node -> this.doBuildChildren(node, groupParentCollect));
                    return rootCollect;
                }).orElse(Collections.emptyList());
        logger.info("构建课程导航模拟数数据{}", JSON.toJSONString(contentTreeCollect));
        return contentTreeCollect;
    }

    /**
     * 递归查找并设置子节点
     * @param node 首页课程导航|分类课程节点
     * @param groupParentCollect 父级组集合数据
     */
    private void doBuildChildren(HomeContentTree node, Map<String, List<HomeContentTree>> groupParentCollect) {
        List<HomeContentTree> children = groupParentCollect.get(node.getId());
        Optional.ofNullable(children)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(ew1->{
                    ew1.sort(Comparator.comparingInt(HomeContentTree::getSort));
                    node.setChildren(ew1);
                    ew1.forEach(ew2 -> this.doBuildChildren(ew2, groupParentCollect));
                });
    }

    /**
     * 首页：互动学习
     * @param homeCfgId 首页互动学习配置Id
     * @param size 从DB获取的数据长度
     * @param clientType 客户端类型
     * @param dataType 数据类型
     * @return 首页互动学习对象VO
     */
    @Override
    public List<? extends InteractiveParentVO> interactionCfg(
            String homeCfgId, Integer size, Integer clientType, Optional<Integer> dataType
    ) {
        Collection<SelectField<?>> select = this.doInteractionSelect(clientType);
        SelectSelectStep<Record> selectStep = doSelectStep(2, context, select);
        return doSingleRecord(HOME_CONTENT,selectStep)
                .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(homeCfgId))
                .and(dataType.map(HOME_CONTENT.DATA_TYPE::eq).orElse(trueCondition()))
                .orderBy(clientType == App.getCode() ? HOME_CONTENT.APP_SORT.asc() : HOME_CONTENT.SORT.asc(), HOME_CONTENT.CREATE_TIME.desc())
                .limit(0,size)
                .fetch(ew0->this.doConvertInteraction(clientType,ew0));
    }

    /**
     * JOOQ根据客户端类型查询首页互动学习属性集合
     * @param clientType 客户端类型 1PC  2App 0全部
     * @return JOOQ查询首页互动学习属性集合
     */
    private Collection<SelectField<?>> doInteractionSelect(Integer clientType){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->doSelect(HOME_CONTENT.DATA_EXT,HOME_CONTENT.URL,HOME_CONTENT.DATA_NAME))
                .orElseGet(()->doSelect(HOME_CONTENT.DATA_EXT,HOME_CONTENT.URL,HOME_CONTENT.DATA_NAME,HOME_CONTENT.ID));
    }

    /**
     * JOOQ根据客户端类型转化首页互动学习的回显VO
     * @param clientType 客户端类型 1PC  2App 0全部
     * @param ew0 JOOQ查询记录流
     * @return 首页互动学习回显VO
     */
    private InteractiveParentVO doConvertInteraction(Integer clientType, Record ew0){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->{
                    InteractivePcVO interactivePcVO = new InteractivePcVO();
                    interactivePcVO.setUrl(ew0.getValue(HOME_CONTENT.URL));
                    interactivePcVO.setDataExt(ew0.getValue(HOME_CONTENT.DATA_EXT));
                    interactivePcVO.setDataName(ew0.getValue(HOME_CONTENT.DATA_NAME));
                    return (InteractiveParentVO)interactivePcVO;
                }).orElseGet(()->{
                    InteractiveAppVO interactiveAppVO = new InteractiveAppVO();
                    interactiveAppVO.setId(ew0.getValue(HOME_CONTENT.ID));
                    interactiveAppVO.setDataExt(ew0.getValue(HOME_CONTENT.DATA_EXT));
                    interactiveAppVO.setDataName(ew0.getValue(HOME_CONTENT.DATA_NAME));
                    interactiveAppVO.setUrl(ew0.getValue(HOME_CONTENT.URL));
                    return interactiveAppVO;
                });
    }

    /**
     * 首页：BIG_BANNER（新）
     * @param homeCfgId 首页BIG_BANNER配置Id
     * @param clientType 客户端类型
     * @param size 从DB获取的数据长度
     * @return 首页大Banner（新）对象VO
     */
    @Override
    public List<? extends BigBannerParentVO> bigBannerCfg(String homeCfgId, Integer clientType, Optional<Integer> size) {
        Collection<SelectField<?>> select = this.doBigBannerSelect(clientType);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        return doSingleRecord(HOME_ADVERTISEMENT,selectStep)
                .where(HOME_ADVERTISEMENT.MODULE_CONFIG_ID.eq(homeCfgId))
                .and(HOME_ADVERTISEMENT.STATE.eq(STATE_ENABLE))
                .and(HOME_ADVERTISEMENT.CLIENT_TYPE.in(CLIENT_TYPE_ALL, clientType))
                .orderBy(HOME_ADVERTISEMENT.SORT.asc(), HOME_ADVERTISEMENT.CREATE_TIME.desc())
                .limit(0, size.orElse(20))
                .fetch(ew0->this.doConvertBigBanner(clientType,ew0));
    }

    /**
     * JOOQ根据客户端类型查询首页BIG_BANNER属性集合
     * @param clientType 客户端类型 1PC  2App 0全部
     * @return JOOQ查询首页BIG_BANNER属性集合
     */
    private Collection<SelectField<?>> doBigBannerSelect(Integer clientType) {
        return Optional.ofNullable(clientType)
                .filter(ew1 -> Objects.equals(PC.getCode(), ew1))
                .map(ew2 -> doSelect(HOME_ADVERTISEMENT.ID,
                        HOME_ADVERTISEMENT.BUSINESS_ID, HOME_ADVERTISEMENT.BUSINESS_TYPE,
                        HOME_ADVERTISEMENT.LINK_ADDRESS, HOME_ADVERTISEMENT.LINK_TYPE,
                        HOME_ADVERTISEMENT.PC_IMAGE_PATH, HOME_ADVERTISEMENT.TITLE
                )).orElseGet(() -> doSelect(HOME_ADVERTISEMENT.BUSINESS_ID,
                        HOME_ADVERTISEMENT.BUSINESS_TYPE,HOME_ADVERTISEMENT.APP_IMAGE_PATH,
                        HOME_ADVERTISEMENT.ID, HOME_ADVERTISEMENT.TITLE,
                        HOME_ADVERTISEMENT.LINK_ADDRESS, HOME_ADVERTISEMENT.LINK_TYPE
                ));
    }

    /**
     * JOOQ根据客户端类型转化首页BIG_BANNER的回显VO
     * @param clientType 客户端类型 1PC  2App 0全部
     * @param ew0 JOOQ查询记录流
     * @return 首页BIG_BANNER回显VO
     */
    private BigBannerParentVO doConvertBigBanner(Integer clientType, Record ew0){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->{
                    BigBannerPcVO bigBannerPcVO = new BigBannerPcVO();
                    bigBannerPcVO.setId(ew0.getValue(HOME_ADVERTISEMENT.ID));
                    bigBannerPcVO.setTitle(ew0.getValue(HOME_ADVERTISEMENT.TITLE));
                    bigBannerPcVO.setBusinessId(ew0.getValue(HOME_ADVERTISEMENT.BUSINESS_ID));
                    bigBannerPcVO.setBusinessType(ew0.getValue(HOME_ADVERTISEMENT.BUSINESS_TYPE));
                    bigBannerPcVO.setLinkAddress(ew0.getValue(HOME_ADVERTISEMENT.LINK_ADDRESS));
                    bigBannerPcVO.setPcImagePath(ew0.getValue(HOME_ADVERTISEMENT.PC_IMAGE_PATH));
                    bigBannerPcVO.setLinkType(ew0.getValue(HOME_ADVERTISEMENT.LINK_TYPE));
                    return (BigBannerParentVO)bigBannerPcVO;
                }).orElseGet(()->{
                    BigBannerAppVO bigBannerAppVO = new BigBannerAppVO();
                    bigBannerAppVO.setId(ew0.getValue(HOME_ADVERTISEMENT.ID));
                    bigBannerAppVO.setTitle(ew0.getValue(HOME_ADVERTISEMENT.TITLE));
                    bigBannerAppVO.setBusinessId(ew0.getValue(HOME_ADVERTISEMENT.BUSINESS_ID));
                    bigBannerAppVO.setBusinessType(ew0.getValue(HOME_ADVERTISEMENT.BUSINESS_TYPE));
                    bigBannerAppVO.setAppImagePath(ew0.getValue(HOME_ADVERTISEMENT.APP_IMAGE_PATH));
                    bigBannerAppVO.setLinkAddress(ew0.getValue(HOME_ADVERTISEMENT.LINK_ADDRESS));
                    bigBannerAppVO.setLinkType(ew0.getValue(HOME_ADVERTISEMENT.LINK_TYPE));
                    return bigBannerAppVO;
        });
    }

    /**
     * 首页：精选内容
     * @param cfgId 首页精选内容配置Id
     * @param size 从DB获取的数据长度
     * @param clientType 客户端类型 1 PC 2 App 0 全部
     * @return 首页精选内容对象VO
     */
    @Override
    public List<? extends FeaturedContentParentVO> featuredContentCfg(String cfgId, Integer size, Integer clientType) {
        List<FeaturedContentParentVO> contentCollect = Lists.newArrayList();
        Collection<SelectField<?>> select = this.doFeaturedContentSelect(clientType);
        List<FeaturedContentParentVO> courseParentCollect = doContentOrCustomizeStep(clientType, cfgId, size, Optional.of(9), select)
                .fetch(ew0 -> this.doConvertFeaturedContent(clientType, ew0))
                .parallelStream()
                .peek(ew1 -> ew1.setNonPersistentDataType(COURSE))
                .collect(Collectors.toList());
        contentCollect.addAll(courseParentCollect);
        List<FeaturedContentParentVO> subjectParentCollect = doContentOrCustomizeStep(clientType, cfgId, size, Optional.of(10), select)
                .fetch(ew0 -> this.doConvertFeaturedContent(clientType, ew0))
                .parallelStream()
                .peek(ew1 -> ew1.setNonPersistentDataType(SUBJECT))
                .collect(Collectors.toList());
        contentCollect.addAll(subjectParentCollect);
        return contentCollect;
    }

    /**
     * JOOQ查询自定义|精选内容STEP
     * @param clientType 客户端类型 1 PC 2 App 0 全部
     * @param cfgId 自定义|精选内容配置Id
     * @param size 从DB中获取的数据长度
     * @param dataTypeOpt 数据类型
     * @param select JOOQ查询属性集合
     * @return JOOQ查询自定义|精选内容STEP
     */
    private SelectForUpdateStep<Record> doContentOrCustomizeStep(
            Integer clientType,String cfgId,Integer size,Optional<Integer> dataTypeOpt, Collection<SelectField<?>> select
    ){
        return context.select(select)
                .from(HOME_CONTENT)
                .leftJoin(HOME_CONTENT_IMAGE).on(HOME_CONTENT.ID.eq(HOME_CONTENT_IMAGE.CONTENT_ID))
                .and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(clientType))
                .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(cfgId))
                .and(dataTypeOpt.map(HOME_CONTENT.DATA_TYPE::eq).orElse(trueCondition()))
                .orderBy(clientType == App.getCode() ? HOME_CONTENT.APP_SORT.asc() : HOME_CONTENT.SORT.asc(), HOME_CONTENT.CREATE_TIME.desc())
                .limit(0, size);
    }

    /**
     * JOOQ根据客户端类型查询首页精选内容属性集合
     * @param clientType 客户端类型 1PC  2App 0全部
     * @return JOOQ查询首页精选内容属性集合
     */
    private Collection<SelectField<?>> doFeaturedContentSelect(Integer clientType){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->doSelect(
                        HOME_CONTENT.DATA_NAME,HOME_CONTENT.DATA_EXT,
                        HOME_CONTENT.DATA_ID,HOME_CONTENT_IMAGE.IMAGE_PATH))
                .orElseGet(()->doSelect(
                        HOME_CONTENT.ID,HOME_CONTENT.DATA_EXT,HOME_CONTENT.DATA_ID,
                        HOME_CONTENT.DATA_NAME,HOME_CONTENT.DATA_TYPE,HOME_CONTENT.URL,
                        HOME_CONTENT_IMAGE.IMAGE_PATH
                ));
    }

    /**
     * JOOQ根据客户端类型转化首页精选内容的回显VO
     * @param clientType 客户端类型 1PC 2App 0全部
     * @param ew0 JOOQ查询记录流
     * @return 首页精选内容回显VO
     */
    private FeaturedContentParentVO doConvertFeaturedContent(Integer clientType, Record ew0){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->{
                    FeaturedContentPcVO contentPcVO = new FeaturedContentPcVO();
                    contentPcVO.setDataId(ew0.getValue(HOME_CONTENT.DATA_ID));
                    contentPcVO.setDataName(ew0.getValue(HOME_CONTENT.DATA_NAME));
                    contentPcVO.setDataExt(ew0.getValue(HOME_CONTENT.DATA_EXT));
                    contentPcVO.setCoverPath(ew0.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
                    return (FeaturedContentParentVO)contentPcVO;
                }).orElseGet(()->{
                    FeaturedContentAppVO contentAppVO = new FeaturedContentAppVO();
                    contentAppVO.setId(ew0.getValue(HOME_CONTENT.ID));
                    contentAppVO.setUrl(ew0.getValue(HOME_CONTENT.URL));
                    contentAppVO.setDataId(ew0.getValue(HOME_CONTENT.DATA_ID));
                    contentAppVO.setDataType(ew0.getValue(HOME_CONTENT.DATA_TYPE));
                    contentAppVO.setDataExt(ew0.getValue(HOME_CONTENT.DATA_EXT));
                    contentAppVO.setDataName(ew0.getValue(HOME_CONTENT.DATA_NAME));
                    contentAppVO.setImagePath(ew0.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
                    contentAppVO.setCoverPath(ew0.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
                    return contentAppVO;
                });
    }

    /**
     * 首页：分类课程
     * @param cfgId 首页分类课程配置Id
     * @param typeOpt 类型
     * @param clientType 客户端类型 1 PC 2 App 0 全部
     * @return 首页分类课程对象VO
     */
    @Override
    public List<? extends CategoryCourseParentVO> categoryCourseCfg(String cfgId, Optional<String> typeOpt, Integer clientType) {
        List<HomeContentTree> contentTreeCollect = this.doBuildContentTree(cfgId, typeOpt);
        return contentTreeCollect.parallelStream()
                .map(ew1->this.doConvertCategoryCourse(clientType,ew1))
                .collect(Collectors.toList());
    }

    /**
     * JOOQ根据客户端类型转化分类课程的回显VO
     * @param clientType 客户端类型 1PC  2App 0全部
     * @param contentTree 查询组装的分类课程POJO
     * @return 首页分类课程回显VO
     */
    private CategoryCourseParentVO doConvertCategoryCourse(Integer clientType, HomeContentTree contentTree) {
        return Optional.ofNullable(clientType)
                .filter(ew1 -> Objects.equals(PC.getCode(), ew1))
                .map(ew2 -> {
                    CategoryCoursePcVO categoryCoursePcVO = this.doCategoryCoursePcVO(contentTree);
                    Optional.ofNullable(contentTree.getChildren()).filter(CollectionUtils::isNotEmpty)
                            .ifPresent(ew3->categoryCoursePcVO.setChildren(ew3.parallelStream().map(this::doCategoryCoursePcVO).collect(Collectors.toList())));
                    return (CategoryCourseParentVO) categoryCoursePcVO;
                }).orElseGet(() -> {
                    CategoryCourseAppVO categoryCourseAppVO = this.doCategoryCourseAppVO(contentTree);
                    Optional.ofNullable(contentTree.getChildren()).filter(CollectionUtils::isNotEmpty)
                            .ifPresent(ew4->categoryCourseAppVO.setChildren(ew4.parallelStream().map(this::doCategoryCourseAppVO).collect(Collectors.toList())));
                    return categoryCourseAppVO;
                });
    }

    /**
     * 执行赋值分类课程App回显VO
     * @param contentTree 查询组装的分类课程POJO
     * @return 分类课程App回显VO
     */
    public CategoryCourseAppVO doCategoryCourseAppVO(HomeContentTree contentTree){
        CategoryCourseAppVO categoryCourseAppVO=new CategoryCourseAppVO();
        categoryCourseAppVO.setId(contentTree.getId());
        categoryCourseAppVO.setDataId(contentTree.getDataId());
        categoryCourseAppVO.setImgPath(contentTree.getImgPath());
        categoryCourseAppVO.setDataName(contentTree.getDataName());
        categoryCourseAppVO.setUrl(categoryCourseAppVO.getUrl());
        categoryCourseAppVO.setDataType(categoryCourseAppVO.getDataType());
        return categoryCourseAppVO;
    }

    /**
     * 执行赋值分类课程PC回显VO
     * @param contentTree 查询组装的分类课程POJO
     * @return 分类课程PC回显VO
     */
    public CategoryCoursePcVO doCategoryCoursePcVO(HomeContentTree contentTree){
        CategoryCoursePcVO categoryCoursePcVO = new CategoryCoursePcVO();
        categoryCoursePcVO.setId(contentTree.getId());
        categoryCoursePcVO.setImgPath(contentTree.getImgPath());
        categoryCoursePcVO.setDataName(contentTree.getDataName());
        categoryCoursePcVO.setDataId(contentTree.getDataId());
        return categoryCoursePcVO;
    }


    /**
     * 首页：MINI_BANNER（新）
     * @param cfgId 首页MINI_BANNER（新）配置Id
     * @param clientType 客户端类型 1 PC 2 App 0 全部
     * @param sizeOpt 从DB获取的数据长度
     * @return 首页MINI_BANNER（新）对象VO
     */
    @Override
    public List<? extends MiniBannerParentVO> miniBannerCfg(String cfgId, Integer clientType,
                                                                Optional<Integer> sizeOpt) {
        Collection<SelectField<?>> select = this.doMiniBannerSelect(clientType);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        return doSingleRecord(HOME_ADVERTISEMENT,selectStep)
                .where(HOME_ADVERTISEMENT.MODULE_CONFIG_ID.eq(cfgId))
                .and(HOME_ADVERTISEMENT.STATE.eq(STATE_ENABLE))
                .and(HOME_ADVERTISEMENT.CLIENT_TYPE.in(CLIENT_TYPE_ALL, clientType))
                .orderBy(HOME_ADVERTISEMENT.SORT.asc(), HOME_ADVERTISEMENT.CREATE_TIME.desc())
                .limit(0, sizeOpt.orElse(10))
                .fetch(ew0->this.doConvertMiniBanner(clientType,ew0));
    }

    /**
     * JOOQ根据客户端类型查询MINI_BANNER（新）属性集合
     * @param clientType 客户端类型 1PC  2App 0全部
     * @return JOOQ查询首页精选内容属性集合
     */
    private Collection<SelectField<?>> doMiniBannerSelect(Integer clientType){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->doSelect(
                        HOME_ADVERTISEMENT.ID,HOME_ADVERTISEMENT.TITLE,
                        HOME_ADVERTISEMENT.BUSINESS_ID,HOME_ADVERTISEMENT.BUSINESS_TYPE,
                        HOME_ADVERTISEMENT.LINK_TYPE,HOME_ADVERTISEMENT.LINK_ADDRESS,
                        HOME_ADVERTISEMENT.PC_IMAGE_PATH,HOME_ADVERTISEMENT.CONTENT))
                .orElseGet(()->doSelect(
                        HOME_ADVERTISEMENT.APP_IMAGE_PATH,
                        HOME_ADVERTISEMENT.ID,HOME_ADVERTISEMENT.LINK_ADDRESS,
                        HOME_ADVERTISEMENT.LINK_TYPE,HOME_ADVERTISEMENT.TITLE,
                        HOME_ADVERTISEMENT.BUSINESS_ID,HOME_ADVERTISEMENT.BUSINESS_TYPE
                ));
    }

    /**
     * JOOQ根据客户端类型转化MINI_BANNER（新）的回显VO
     * @param clientType 客户端类型 1PC  2App 0全部
     * @param ew0 JOOQ查询数据流
     * @return 首页MINI_BANNER(New)回显VO
     */
    private MiniBannerParentVO doConvertMiniBanner(Integer clientType, Record ew0){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->{
                    MiniBannerPcVO miniBannerPcVO = new MiniBannerPcVO();
                    miniBannerPcVO.setId(ew0.getValue(HOME_ADVERTISEMENT.ID));
                    miniBannerPcVO.setTitle(ew0.getValue(HOME_ADVERTISEMENT.TITLE));
                    miniBannerPcVO.setContent(ew0.getValue(HOME_ADVERTISEMENT.CONTENT));
                    miniBannerPcVO.setBusinessId(ew0.getValue(HOME_ADVERTISEMENT.BUSINESS_ID));
                    miniBannerPcVO.setBusinessType(ew0.getValue(HOME_ADVERTISEMENT.BUSINESS_TYPE));
                    miniBannerPcVO.setLinkAddress(ew0.getValue(HOME_ADVERTISEMENT.LINK_ADDRESS));
                    miniBannerPcVO.setLinkType(ew0.getValue(HOME_ADVERTISEMENT.LINK_TYPE));
                    miniBannerPcVO.setPcImagePath(ew0.getValue(HOME_ADVERTISEMENT.PC_IMAGE_PATH));
                    return (MiniBannerParentVO)miniBannerPcVO;
                }).orElseGet(()->{
                    MiniBannerAppVO miniBannerAppVO = new MiniBannerAppVO();
                    miniBannerAppVO.setTitle(ew0.getValue(HOME_ADVERTISEMENT.TITLE));
                    miniBannerAppVO.setId(ew0.getValue(HOME_ADVERTISEMENT.ID));
                    miniBannerAppVO.setBusinessId(ew0.getValue(HOME_ADVERTISEMENT.BUSINESS_ID));
                    miniBannerAppVO.setBusinessType(ew0.getValue(HOME_ADVERTISEMENT.BUSINESS_TYPE));
                    miniBannerAppVO.setLinkAddress(ew0.getValue(HOME_ADVERTISEMENT.LINK_ADDRESS));
                    miniBannerAppVO.setLinkType(ew0.getValue(HOME_ADVERTISEMENT.LINK_TYPE));
                    miniBannerAppVO.setAppImagePath(ew0.getValue(HOME_ADVERTISEMENT.APP_IMAGE_PATH));
                    return miniBannerAppVO;
                });
    }

    /**
     * 首页：自定义
     * @param cfgId 首页配置Id
     * @param size 从DB获取的数据长度
     * @param clientType 客户端类型
     * @return 首页自定义对象VO
     */
    @Override
    public List<? extends CustomizeParentVO> customizeCfg(String cfgId, Integer size, Integer clientType) {
        Collection<SelectField<?>> select = doCustomizeSelect(clientType);
        return this.doContentOrCustomizeStep(clientType,cfgId,size,Optional.empty(),select).fetch(ew0->this.doConvertCustomize(clientType,ew0));
    }

    /**
     * JOOQ根据客户端类型查询自定义属性集合
     * @param clientType 客户端类型 1PC  2App 0全部
     * @return JOOQ查询首页自定义属性集合
     */
    private Collection<SelectField<?>> doCustomizeSelect(Integer clientType){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->doSelect(
                        HOME_CONTENT.DATA_EXT,HOME_CONTENT.DATA_ID,
                        HOME_CONTENT.DATA_TYPE, HOME_CONTENT.DATA_NAME,HOME_CONTENT.ID,
                        HOME_CONTENT.URL,HOME_CONTENT_IMAGE.IMAGE_PATH))
                .orElseGet(()->doSelect(
                        HOME_CONTENT.DATA_ID,HOME_CONTENT.DATA_TYPE,
                        HOME_CONTENT.DATA_NAME,HOME_CONTENT.DATA_EXT,HOME_CONTENT.URL,
                        HOME_CONTENT.ID,HOME_CONTENT_IMAGE.IMAGE_PATH
                ));
    }

    /**
     * JOOQ根据客户端类型转化自定义（新）的回显VO
     * @param clientType 客户端类型 1PC  2App 0全部
     * @param ew0 JOOQ查询数据流
     * @return 首页自定义回显VO
     */
    private CustomizeParentVO doConvertCustomize(Integer clientType, Record ew0){
        return Optional.ofNullable(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->{
                    CustomizePcVO customizePcVO = new CustomizePcVO();
                    customizePcVO.setId(ew0.getValue(HOME_CONTENT.ID));
                    customizePcVO.setUrl(ew0.getValue(HOME_CONTENT.URL));
                    customizePcVO.setDataId(ew0.getValue(HOME_CONTENT.DATA_ID));
                    customizePcVO.setDataType(ew0.getValue(HOME_CONTENT.DATA_TYPE));
                    customizePcVO.setDataName(ew0.getValue(HOME_CONTENT.DATA_NAME));
                    customizePcVO.setDataExt(ew0.getValue(HOME_CONTENT.DATA_EXT));
                    customizePcVO.setImagePath(ew0.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
                    return (CustomizeParentVO)customizePcVO;
                }).orElseGet(()->{
                    CustomizeAppVO customizeAppVO = new CustomizeAppVO();
                    customizeAppVO.setId(ew0.getValue(HOME_CONTENT.ID));
                    customizeAppVO.setUrl(ew0.getValue(HOME_CONTENT.URL));
                    customizeAppVO.setDataId(ew0.getValue(HOME_CONTENT.DATA_ID));
                    customizeAppVO.setDataExt(ew0.getValue(HOME_CONTENT.DATA_EXT));
                    customizeAppVO.setDataType(ew0.getValue(HOME_CONTENT.DATA_TYPE));
                    customizeAppVO.setDataName(ew0.getValue(HOME_CONTENT.DATA_NAME));
                    customizeAppVO.setCoverPath(ew0.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
                    return customizeAppVO;
        });
    }

    /**
     * 自定义属性查询（资源类型所属课程）
     * @param cfgId 首页配置项主Id
     * @return 查询自定义数据集合 /find-by-ids接口的数据返回值
     */
    @Override
    public Map<String,List<CustomizeVO>> customizeCourseSelect(String cfgId,Integer clientType) {
        Map<String, List<CustomizeVO>> customizeMap = this.doCommonSelect(cfgId, clientType);
        Collection<SelectField<?>> select1 = doSelect(HOME_MODULE_CONFIG.ID);
        Optional<String> pickOpt = context.select(select1)
                .from(HOME_MODULE_CONFIG)
                .where(HOME_MODULE_CONFIG.HOME_CONFIG_ID.eq(cfgId))
                .and(HOME_MODULE_CONFIG.MODULE_CODE.eq(Picks.getCode()))
                .and(HOME_MODULE_CONFIG.CLIENT_TYPE.in(All.getCode(), clientType))
                .fetchOptional(HOME_MODULE_CONFIG.ID);
        pickOpt.ifPresent(ew1->{
            List<CustomizeVO> pickCollect = Lists.newArrayList();
            List<CustomizeVO> courseCustomizeCollect= this.specialSelectCustomize(ew1,clientType,Optional.of(9));
            pickCollect.addAll(courseCustomizeCollect);
            List<CustomizeVO> subjectCustomizeCollect= this.specialSelectCustomize(ew1,clientType,Optional.of(10));
            pickCollect.addAll(subjectCustomizeCollect);
            customizeMap.put(Picks.getCode()+":"+ew1,pickCollect);
        });
        logger.info("当前首页配置查询的数据信息{}",JSON.toJSONString(customizeMap));
        return customizeMap;
    }

    /**
     * 自定义模块公共查询且封装Map，提供RPC私有变量传递
     * @param cfgId 主配置Id
     * @param clientType 客户端类型 1PC 2App
     * @return Map集合数据
     */
    private Map<String,List<CustomizeVO>> doCommonSelect(String cfgId, Integer clientType){
        //Step2 查询自定义模块属性集合数据并设置Map
        Map<String, List<CustomizeVO>> customizeMap=new HashMap<>(8);
        Collection<SelectField<?>> select0 = doSelect(HOME_MODULE_CONFIG.ID);
        List<String> layoutCollect = context.select(select0)
                .from(HOME_MODULE_CONFIG)
                .where(HOME_MODULE_CONFIG.HOME_CONFIG_ID.eq(cfgId))
                .and(HOME_MODULE_CONFIG.MODULE_CODE.eq(Layout.getCode()))
                .and(HOME_MODULE_CONFIG.CLIENT_TYPE.in(All.getCode(),clientType))
                .fetch(HOME_MODULE_CONFIG.ID);
        //Step3 查询精选内容模块属性集合数据并设置Map
        layoutCollect.forEach(ew1-> customizeMap.put(Layout.getCode()+":"+ew1,this.specialSelectCustomize(ew1,clientType,Optional.empty())));
        return customizeMap;
    }

    /**
     * 特殊处理自定义模块中资源的图片地址
     * @param cfgId 首页模块配置Id
     * @param clientType 客户端类型 1PC 2App
     * @param dataTypeOpt 数据类型（非必填）
     * @return 返回的数据信息
     */
    private List<CustomizeVO> specialSelectCustomize(String cfgId, Integer clientType, Optional<Integer> dataTypeOpt){
        Collection<SelectField<?>> select = doSelect(HOME_CONTENT.ID, HOME_CONTENT.DATA_TYPE, HOME_CONTENT.DATA_ID, HOME_CONTENT_IMAGE.IMAGE_PATH);
        return doContentOrCustomizeStep(clientType,cfgId,8,dataTypeOpt,select)
                .fetch(ew1->{
                    CustomizeVO customizeVO = new CustomizeVO();
                    customizeVO.setCfgId(cfgId);
                    customizeVO.setId(ew1.getValue(HOME_CONTENT.ID));
                    customizeVO.setDataId(ew1.getValue(HOME_CONTENT.DATA_ID));
                    customizeVO.setDataType(ew1.getValue(HOME_CONTENT.DATA_TYPE));
                    customizeVO.setUrl(ew1.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
                    return customizeVO;
                });
    }

    @Override
    public List<String> specialClassifiedCoursesSelect(String cfgId){
        return  context.selectDistinct(HOME_CONTENT_TREE.DATA_ID)
                .from(HOME_CONTENT_TREE)
                .where(HOME_CONTENT_TREE.MODULE_CONFIG_ID.eq(cfgId))
                .and(HOME_CONTENT_TREE.DATA_TYPE.eq(DATE_TYPE_COURSE))
                .and(HOME_CONTENT_TREE.SHOW.eq(SHOW_ON))
                .fetch(HOME_CONTENT_TREE.DATA_ID);
    }

    @Override
    public String doClassifiedCourses(String cfgId) {
        return context.select(HOME_MODULE_CONFIG.ID)
                .from(HOME_MODULE_CONFIG)
                .where(HOME_MODULE_CONFIG.HOME_CONFIG_ID.eq(cfgId))
                .and(HOME_MODULE_CONFIG.MODULE_CODE.eq(ClassifiedCourses.getCode()))
                .fetchOne(HOME_MODULE_CONFIG.ID);
    }

    /**
     * 自定义属性查询（图片地址）
     * @param cfgId 首页配置项主Id
     * @param clientType 客户端类型 1PC 2App
     * @return 查询自定义数据集合 /find-by-ids接口的数据返回值
     */
    @Override
    public Map<String,List<CustomizeVO>> customizePathSelect(String cfgId, Integer clientType){ return doCommonSelect(cfgId,clientType); }


    /**
     * 查询系统配置数据集合
     * @return 系统配置数据集合
     */
    @Override
    public List<RuleConfigVO> ruleConfigCollect() {
        Collection<SelectField<?>> select = doSelect(RULE_CONFIG.ORGANIZATION_ID, RULE_CONFIG.STATUS, RULE_CONFIG.TYPE,
                RULE_CONFIG.VALUE, RULE_CONFIG.DESC,RULE_CONFIG.ID, RULE_CONFIG.KEY,RULE_CONFIG.SEQUENCE);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        return doSingleRecord(RULE_CONFIG,selectStep)
                .where(RULE_CONFIG.KEY.ne("MAX_ONLINES"))
                .and(RULE_CONFIG.KEY.ne("EMAIL_MESSAGE_PUSH_CONFIG"))
                .and(RULE_CONFIG.KEY.ne("JG_MESSAGE_PUSH_CONFIG"))
                .and(RULE_CONFIG.KEY.ne("SHORT_MESSAGE_PUSH_CONFIG"))
                .and(RULE_CONFIG.KEY.ne("APP_PUSH_TEMPLATE_CONFIG"))
                .fetch(ew1->{
                    RuleConfigVO ruleConfigVO = new RuleConfigVO();
                    ruleConfigVO.setId(ew1.getValue(RULE_CONFIG.ID));
                    ruleConfigVO.setType(ew1.getValue(RULE_CONFIG.TYPE));
                    ruleConfigVO.setDesc(ew1.getValue(RULE_CONFIG.DESC));
                    ruleConfigVO.setKey(ew1.getValue(RULE_CONFIG.KEY));
                    ruleConfigVO.setValue(ew1.getValue(RULE_CONFIG.VALUE));
                    ruleConfigVO.setStatus(ew1.getValue(RULE_CONFIG.STATUS));
                    ruleConfigVO.setSequence(ew1.getValue(RULE_CONFIG.SEQUENCE));
                    ruleConfigVO.setOrganizationId(ew1.getValue(RULE_CONFIG.ORGANIZATION_ID));
                    return ruleConfigVO;
                });
    }

    /**
     * 根据配置项key实时查询数据库系统配置项
     * @param key 查询系统配置项的KEY
     * @return 出参的RULE_CONFIG对象
     */
    @Override
    public RuleConfig singleRuleConfig(String key) {
        Collection<SelectField<?>> select = doSelect(RULE_CONFIG.ID,RULE_CONFIG.DESC,RULE_CONFIG.ORGANIZATION_ID,
                RULE_CONFIG.KEY, RULE_CONFIG.VALUE, RULE_CONFIG.STATUS, RULE_CONFIG.TYPE);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        List<RuleConfig> ruleConfigCollect = doSingleRecord(RULE_CONFIG, selectStep)
                .where(RULE_CONFIG.KEY.eq(key))
                .fetch(ew1 -> {
                    RuleConfig ruleConfig = new RuleConfig();
                    ruleConfig.setId(ew1.getValue(RULE_CONFIG.ID));
                    ruleConfig.setDesc(ew1.getValue(RULE_CONFIG.DESC));
                    ruleConfig.setOrganizationId(ew1.getValue(RULE_CONFIG.ORGANIZATION_ID));
                    ruleConfig.setKey(ew1.getValue(RULE_CONFIG.KEY));
                    ruleConfig.setValue(ew1.getValue(RULE_CONFIG.VALUE));
                    ruleConfig.setStatus(ew1.getValue(RULE_CONFIG.STATUS));
                    ruleConfig.setType(ew1.getValue(RULE_CONFIG.TYPE));
                    return ruleConfig;
                });
        return buildSingleValue(ruleConfigCollect);
    }
}