package com.zxy.product.system.service.support.premission;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.api.permission.RoleService;
import com.zxy.product.system.content.ErrorCode;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.entity.*;
import com.zxy.product.system.util.CodeUtils;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.system.jooq.Tables.*;

/**
 * <AUTHOR>
 *
 */
@Service
public class RoleServiceSupport implements RoleService{

    private CommonDao<Role> roleDao;
    private CommonDao<Grant> grantDao;
    private CommonDao<RoleMenu> roleMenuDao;
    private CommonDao<Menu> menuDao;
    private CommonDao<Member> memberDao;
    private CommonDao<GrantMember> grantMemberDao;
    private GrantService grantService;
    private CommonDao<Organization> organizationDao;
    private MessageSender messageSender;

    private static final String  ROLE_ID = "b04af591-8ec6-46ae-978b-d08ebc500ccf";
    private static final String  MENU_ID = "001001";

    @Autowired
    public void setGrantDao(CommonDao<Grant> grantDao) {
        this.grantDao = grantDao;
    }

    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
		this.organizationDao = organizationDao;
	}

    @Autowired
    public void setRoleDao(CommonDao<Role> roleDao) {
        this.roleDao = roleDao;
    }

    @Autowired
    public void setRoleMenuDao(CommonDao<RoleMenu> roleMenuDao) {
        this.roleMenuDao = roleMenuDao;
    }

    @Autowired
    public void setMenuDao(CommonDao<Menu> menuDao) {
        this.menuDao = menuDao;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
    public void setGrantMemberDao(CommonDao<GrantMember> grantMemberDao){this.grantMemberDao=grantMemberDao;}

    @Autowired
    public void setGrantService(GrantService grantService) {
        this.grantService = grantService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Override
    public Role insert(String memberId, String name, String organizationId, String parentId, String[] menuIds, Optional<String> desc, Optional<Integer> childFlag) {
        Role role = new Role();
        role.forInsert();
        role.setName(name);
        role.setOrganizationId(organizationId);
        role.setParentId(parentId);
        role.setPath(getPath(role));
        role.setInit(Role.INIT_NO);
        role.setCreateMemberId(memberId);
        role.setType(Role.TYPE_STANDARD);
        role.setOrder(1);
        desc.ifPresent(role::setDesc);
        childFlag.ifPresent(role::setChildFlag);

        setRoleCode(role, menuIds); // 设置角色code,由低中高三个字段组成
        fixRoleOrder(role.getType());
        roleDao.insert(role);
        roleDao.execute(e -> e.update(ROLE)
                              .set(ROLE.ORDER, ROLE.ORDER.add(1))
                              .where(ROLE.ORDER.gt(1)).execute());

        insertRoleMenus(role.getId(), Arrays.asList(menuIds)); // 插入角色菜单

        messageSender.send(MessageTypeContent.SYSTEM_ROLE_INSERT, MessageHeaderContent.ID, role.getId());
        return role;
    }

    private void setRoleCode(Role role, String[] menuIds) {
        List<Menu> menus = menuDao.get(Arrays.asList(menuIds));

        long codeLow = 0l;
        long codeMiddle = 0l;
        long codeHigh = 0l;
        for(Menu m : menus) {
            if (m.getCode() < 64) {
                codeLow |= 1l << m.getCode() - 1;
            } else if (m.getCode() < 127) {
                codeMiddle |= 1l << m.getCode() - 64;
            } else {
                codeHigh |= 1l << m.getCode() - 127;
            }
        }
        role.setCodeLow(codeLow);
        role.setCodeMiddle(codeMiddle);
        role.setCodeHigh(codeHigh);
    }

    private void insertRoleMenus(String roleId, List<String> menuIds) {
        List<RoleMenu> roleMenus  = menuIds.stream().filter(StringUtils::hasText).map(m -> {
            RoleMenu rm = new RoleMenu();
            rm.forInsert();
            rm.setRoleId(roleId);
            rm.setMenuId(m);
            return rm;
        }).collect(Collectors.toList());
        if (!roleMenus.isEmpty()) {
            roleMenuDao.insert(roleMenus);
        }
    }

    @Override
    public int delete(String id) {
        // 判断当前角色是否关联用户
        int count = roleDao.execute(d -> d.select(GRANT.ID.count()).from(GRANT)
                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                          .where(GRANT.ROLE_ID.eq(id), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())).fetchOne(GRANT.ID.count()));
        if (count > 0) {
            throw new UnprocessableException(ErrorCode.RoleReferenced);
        }

        Role role = roleDao.get(id);
        // 判断当前角色和其子角色是否关联用户
        List<String> subIds = roleDao.execute(d -> d.select(ROLE.ID).from(ROLE).where(ROLE.PATH.contains(id)).fetch(ROLE.ID));
        int subRoleCount = roleDao.execute(d -> d.select(GRANT.ID.count()).from(GRANT)
                                                 .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                 .where(GRANT.ROLE_ID.in(subIds), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())).fetchOne(GRANT.ID.count()));
        if (subRoleCount > 0) {
            throw new UnprocessableException(ErrorCode.SubRoleReferenced);
        }

        // 删除角色和其子角色的role-menu记录
        roleMenuDao.delete(ROLE_MENU.ROLE_ID.in(subIds));
        // 删掉过期的授权
        grantDao.delete(GRANT.ROLE_ID.eq(id));

        // 删除角色和其子角色
        int rtnCount = roleDao.execute(d -> d.deleteFrom(ROLE).where(ROLE.ID.in(subIds)).execute());
        updateOrder(role.getOrder(), role.getOrder(), -1, role.getType());

        messageSender.send(MessageTypeContent.SYSTEM_ROLE_DELETE, MessageHeaderContent.ID,id);
        return rtnCount;
    }

    /** 删除角色下指定菜单 */
    private void deleteRoleMenus(String roleId, List<String> menuIds) {
        if (!menuIds.isEmpty()) {
            roleMenuDao.delete(ROLE_MENU.ROLE_ID.eq(roleId), ROLE_MENU.MENU_ID.in(menuIds));
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public Role update(String memberId, String id, Optional<String> name,
                       Optional<String> organizationId, Optional<String> parentId, String[] menuIds, String rootOrganizationId, Optional<Integer> order, Optional<String> desc, Optional<Integer> childFlag) {
        // 旧的角色菜单id
        List<String> oldMenuIds = roleMenuDao.execute(d -> d.select(ROLE_MENU.MENU_ID).from(ROLE_MENU)
                .where(ROLE_MENU.ROLE_ID.eq(id)).fetch(ROLE_MENU.MENU_ID));
        // 新的角色菜单id
        ArrayList<String> newMenuIds = new ArrayList<String>(Arrays.asList(menuIds));
        ArrayList<String> newMenuIdsCopy = (ArrayList<String>) newMenuIds.clone();

        // 更新角色本身
        Role role = roleDao.get(id);
        List<String> havingGrantedOrganization = grantService.findHavingGrantedOrganization(memberId, Role.URI, Lists.newArrayList(Grant.OPERATOR_TYPE_ALL, Grant.OPERATOR_TYPE_EDIT), rootOrganizationId);
        if (Role.CHILD_FLAG_NO.equals(role.getChildFlag()) && CollectionUtils.isEmpty(havingGrantedOrganization)) {
            throw new UnprocessableException(ErrorCode.RoleUpdateUnSupport);
        }
        role.setName(name.orElse(null));
        role.setOrganizationId(organizationId.orElse(null));
        role.setParentId(parentId.orElse(null));
        role.setPath(getPath(role));
        setRoleCode(role, menuIds); // 设置角色code,由低中高三个字段组成
        desc.ifPresent(role::setDesc);
        childFlag.ifPresent(role::setChildFlag);
        if (order.isPresent()) {
            if (role.getOrder() > order.get()) {
                updateOrder(order.get(), role.getOrder() + 1, 1, role.getType());
            } else {
                updateOrder(role.getOrder(), order.get(), -1, role.getType());
            }
            Integer maxOrder = roleDao.execute(d -> d.select(DSL.max(ROLE.ORDER))
                                                     .from(ROLE).where(ROLE.TYPE.eq(role.getType()))
                                                     .fetchOne(DSL.max(ROLE.ORDER)));
            role.setOrder(order.get() > maxOrder ? maxOrder : order.get());
        }
        roleDao.update(role);

        // 算出增量
        Map<String, Object> map = Maps.newHashMap();
        newMenuIdsCopy.retainAll(oldMenuIds); // 找出公有的菜单
        newMenuIds.removeAll(newMenuIdsCopy); // 找出新增的菜单
        oldMenuIds.removeAll(newMenuIdsCopy); // 找出删除的菜单
        map.put("addedMenuIds", newMenuIds);
        map.put("deledMenuIds", oldMenuIds);

        // 增量更新角色菜单
        updateMenus(id, oldMenuIds, newMenuIds);
        messageSender.send(MessageTypeContent.SYSTEM_ROLE_UPDATE, map, MessageHeaderContent.ID, id);

        return role;
    }

    @Override
    public Role updateOrder(String id, Integer order) {
        // 更新角色本身
        Role role = roleDao.get(id);
        if (role.getOrder() > order) {
            updateOrder(order, role.getOrder() + 1, 1, role.getType());
        } else {
            updateOrder(role.getOrder(), order, -1, role.getType());
        }
        Integer maxOrder = roleDao.execute(d -> d.select(DSL.max(ROLE.ORDER))
                                                 .from(ROLE).where(ROLE.TYPE.eq(role.getType()))
                                                 .fetchOne(DSL.max(ROLE.ORDER)));
        role.setOrder(order > maxOrder ? maxOrder : order);
        roleDao.execute(e -> e.update(ROLE)
                              .set(ROLE.ORDER, role.getOrder())
                              .where(ROLE.ID.eq(id)).execute());
        fixRoleOrder(role.getType());
        return role;
    }

    /**
     * 修复角色排序
     */
    private void fixRoleOrder(Integer type) {
        List<Role> roles = roleDao.execute(d -> d.select(ROLE.ID, ROLE.ORDER)
                                                 .from(ROLE)
                                                 .where(ROLE.TYPE.eq(type))
                                                 .orderBy(ROLE.ORDER.asc()))
                                  .fetchInto(Role.class);
        ArrayList<Role> updates = new ArrayList<>();
        for (int i = 0; i < roles.size(); i++) {
            Role role = roles.get(i);
            if (role.getOrder() != i + 1) {
                role.setOrder(i + 1);
                updates.add(role);
            }
        }
        if (!updates.isEmpty()) {
            updates.forEach(role -> roleDao.execute(e -> e.update(ROLE)
                                                      .set(ROLE.ORDER, role.getOrder())
                                                      .where(ROLE.ID.eq(role.getId())).execute()));
        }
    }

    @Override
    public Boolean updateChildFlag(String id, Integer childFlag) {
        roleDao.execute(e -> e.update(ROLE)
                              .set(ROLE.CHILD_FLAG, childFlag)
                              .where(ROLE.ID.eq(id)).execute());
        return true;
    }

    private void updateOrder(int leftOrder, int rightOrder, int addBase, Integer type) {
        if (addBase > 0) {
            if (leftOrder == rightOrder) {
                organizationDao.execute(d -> d.update(ROLE).set(ROLE.ORDER, ROLE.ORDER.add(addBase))
                                              .where(ROLE.ORDER.ge(leftOrder), ROLE.TYPE.eq(type)).execute());
            } else {
                organizationDao.execute(d -> d.update(ROLE).set(ROLE.ORDER, ROLE.ORDER.add(addBase))
                                              .where(ROLE.ORDER.ge(leftOrder), ROLE.ORDER.lt(rightOrder), ROLE.TYPE.eq(type)).execute());
            }
        } else {
            if (leftOrder == rightOrder) {
                organizationDao.execute(d -> d.update(ROLE).set(ROLE.ORDER, ROLE.ORDER.add(addBase))
                                              .where(ROLE.ORDER.le(rightOrder), ROLE.TYPE.eq(type)).execute());
            } else {
                organizationDao.execute(d -> d.update(ROLE).set(ROLE.ORDER, ROLE.ORDER.add(addBase))
                                              .where(ROLE.ORDER.gt(leftOrder), ROLE.ORDER.le(rightOrder), ROLE.TYPE.eq(type)).execute());
            }
        }
    }

    private void updateMenus(String roleId, List<String> deledMenuIds, List<String> addedMenuIds) {
        deleteRoleMenus(roleId, deledMenuIds);
        insertRoleMenus(roleId, addedMenuIds);
    }

    // XXX: 废弃的. 保留原查询逻辑
    public Role get_Outdated(String id, Optional<String> grantId, String currentUserId, String uri, Optional<String> memberId) {
        com.zxy.product.system.jooq.tables.Role roleTableName = ROLE.as("role2");
        Role r = roleDao.execute(e -> e.selectDistinct(ROLE.ID,
                        ROLE.NAME,
                        ROLE.ORGANIZATION_ID,
                        ROLE.PARENT_ID,
                        ROLE.CREATE_TIME,
                        ROLE.DESC,
                        ROLE.TYPE,
                        ROLE.CHILD_FLAG,
                        roleTableName.NAME,
                        ORGANIZATION.NAME)
                .from(ROLE)
                .leftJoin(roleTableName).on(ROLE.PARENT_ID.eq(roleTableName.ID))
                .leftJoin(ORGANIZATION).on(ROLE.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(ROLE.ID.eq(id)).fetchOne(x -> {
                    Role role = new Role();
                    role.setId(x.getValue(ROLE.ID));
                    role.setName(x.getValue(ROLE.NAME));
                    role.setParentId(x.getValue(ROLE.PARENT_ID));
                    role.setParentName(x.getValue(roleTableName.NAME));
                    role.setOrganizationId(x.getValue(ROLE.ORGANIZATION_ID));
                    role.setDesc(x.getValue(ROLE.DESC));
                    role.setType(x.getValue(ROLE.TYPE));
                    role.setChildFlag(x.getValue(ROLE.CHILD_FLAG));
                    Organization organization = new Organization();
                    organization.setId(x.getValue(ROLE.ORGANIZATION_ID));
                    organization.setName(x.getValue(ORGANIZATION.NAME));
                    role.setOrganization(organization);
                    role.setMenus(findMenus(id, role.getType()));
                    role.setCreateTime(x.getValue(ROLE.CREATE_TIME));
                    return role;
                }));
        String grantOrganizationName = roleDao.execute(d -> d.selectDistinct(ORGANIZATION.NAME, GRANT_ORGANIZATION.CHILD_FIND, ORGANIZATION.LEVEL)
                .from(ROLE)
                .leftJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                .where(ROLE.ID.eq(id), memberId.map(GRANT_MEMBER.MEMBER_ID::eq).orElse(DSL.trueCondition()))
                .orderBy(ORGANIZATION.LEVEL.asc())
                .fetch(x -> {
                    Integer childFind = x.getValue(GRANT_ORGANIZATION.CHILD_FIND);
                    String childFindStr = "";
                    if (Objects.equals(childFind, GrantOrganization.CHILD_FIND_YES)) {
                        childFindStr = "（含子部门）";
                    }
                    String orgName = x.getValue(ORGANIZATION.NAME);
                    if (orgName != null) {
                        orgName = orgName + childFindStr;
                    }
                    return orgName;
                })).stream().filter(Objects::nonNull).distinct().reduce((a, b) -> a + "," + b).orElse("");
        r.setGrantOrganizationName(grantOrganizationName);
        Integer memberCount = roleDao.execute(d -> {
            List<String> grantOrgIds = grantService.findGrantedOrganization(
                    currentUserId, Grant.URI, Optional.empty(), Optional.empty(),
                    Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
                    Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());

            com.zxy.product.system.jooq.tables.Organization memberOrgTable = ORGANIZATION.as("member_org");
            List<Condition> conditions = Stream.of(
                    Optional.of(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED)),
                    Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())),
                    Optional.of(GRANT.ROLE_ID.eq(id)),
                    grantId.map(GRANT_MEMBER.GRANT_ID::eq),
                    Optional.of(GRANT.ORGANIZATION_ID.in(grantOrgIds).or(GRANT.ROLE_ID.eq(id).and(GRANT_MEMBER.MEMBER_ID.eq(currentUserId))))
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            return d.select(GRANT_MEMBER.MEMBER_ID.count())
                    .from(GRANT)
                    .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                    .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                    .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                    .innerJoin(memberOrgTable).on(memberOrgTable.ID.eq(MEMBER.ORGANIZATION_ID))
                    .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT.ORGANIZATION_ID))
                    .where(conditions)
                    .groupBy(GRANT.ROLE_ID).fetchOne(GRANT_MEMBER.MEMBER_ID.count());
        });
        r.setMemberCount(memberCount);
        return r;
    }
    // XXX: 废弃的. 保留原查询逻辑
    private List<Menu> findMenus_Outdated(String roleId, Integer type) {
        List<Menu> menus = menuDao.get(roleMenuDao.fetch(ROLE_MENU.ROLE_ID.eq(roleId))
                .stream().map(RoleMenu::getMenuId).collect(Collectors.toList()));
        if (Objects.equals(type, Role.TYPE_STANDARD)) {
            return menus;
        } else {
            HashSet<String> parentIds = new HashSet<>();
            Set<String> exists = menus.stream().map(Menu::getId).collect(Collectors.toSet());
            menus.forEach(m -> {
                Arrays.asList(Optional.ofNullable(m.getPath()).orElse("").split(",")).forEach(a -> {
                    if (!exists.contains(a)) {
                        parentIds.add(a);
                    }
                });
            });
            menus.addAll(menuDao.execute(d -> d.select(MENU.fields())
                    .from(MENU)
                    .where(MENU.ID.in(parentIds))
                    .fetchInto(Menu.class)));
            return menus;
        }
    }

    @Override
    public Role get(String id, Optional<String> grantId, String currentUserId, String uri, Optional<String> memberId) {
        return roleDao.execute(d -> {
            // 第一步: 查询角色信息
            Role r = d.select(ROLE.ID, ROLE.NAME, ROLE.PARENT_ID, ROLE.CREATE_TIME, ROLE.TYPE, ROLE.CHILD_FLAG, ROLE.ORGANIZATION_ID, ROLE.DESC)
                    .from(ROLE)
                    .where(ROLE.ID.eq(id))
                    .fetchOneInto(Role.class);

            if (r == null) {
                return null;
            }

            // 第二步: 查询父角色名称
            String parentName = d.select(ROLE.NAME)
                    .from(ROLE)
                    .where(ROLE.ID.eq(r.getParentId()))
                    .fetchOne(rp -> rp.getValue(0, String.class));
            r.setParentName(parentName);

            // 第三步: 查询角色组织信息
            Organization organization = new Organization();
            String orgName = d.select(ORGANIZATION.NAME)
                    .from(ORGANIZATION)
                    .where(ORGANIZATION.ID.eq(r.getOrganizationId()))
                    .fetchOne(ro -> ro.getValue(0, String.class));
            organization.setId(r.getOrganizationId());
            organization.setName(orgName);
            r.setOrganization(organization);

            // 第四步: 查询grantOrganizationName
            r.setGrantOrganizationName(getGrantOrganizationName(id, d));

            // 第五步: 查询当前角色有几个人
            r.setMemberCount(getMemberCount(id, d));

            // 第六步：查询角色权限内菜单集合
            r.setMenus(findMenus(id, r.getType()));

            return r;
        });
    }

    private List<Menu> findMenus(String roleId, Integer type) {
        List<Menu> menus = menuDao.execute(d -> d.select(MENU.fields())
                .from(MENU)
                .innerJoin(ROLE_MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                .where(ROLE_MENU.ROLE_ID.eq(roleId))
                .fetchInto(Menu.class));

        if (Objects.equals(type, Role.TYPE_STANDARD)) {
            return menus;
        } else {
            Set<String> menuIds = menus.stream().map(Menu::getId).collect(Collectors.toSet());

            List<Condition> pathConditions = menuIds.stream()
                    .map(id -> (Condition) MENU.PATH.like("%," + id + ",%"))
                    .collect(Collectors.toList());

            Condition combinedCondition = pathConditions.stream()
                    .reduce(DSL.falseCondition(), Condition::or);

            List<Menu> parentMenus = menuDao.execute(d -> d.select(MENU.fields())
                    .from(MENU)
                    .where(combinedCondition)
                    .fetchInto(Menu.class));

            menus.addAll(parentMenus);
            return menus;
        }
    }

    @Override
    public PagedResult<Role> findPage(int page, int pageSize, String memberId,
                                      Optional<String> name, Optional<String> parentName, Optional<String> orgainizationId,
                                      Optional<Long> createTimeStart, Optional<Long> createTimeEnd, Integer contain, Optional<Integer> type,
                                      Optional<String> memberIdOptional, Optional<Integer> childFlag, String uri) {

        com.zxy.product.system.jooq.tables.Role parentRole = ROLE.as("parentRole");

        Field<String> organizationName = ORGANIZATION.NAME.as("organization_name");
        Field<String> parentNameField = parentRole.NAME.as("parentName");
        
        return roleDao.execute(d -> {

        	List<Condition> collects = Stream.of(name.map(n -> ROLE.NAME.contains(n).or(ROLE.DESC.contains(n))),
                memberIdOptional.map(GRANT_MEMBER.MEMBER_ID::eq),
                childFlag.map(ROLE.CHILD_FLAG::eq),
                parentName.map(parentRole.NAME::contains),
                 type.map(t-> {
                     if (Objects.equals(t, Role.TYPE_ELEMENT)) {
                         return ROLE.TYPE.eq(t).and(MENU.URI.isNotNull()).and(MENU.URI.ne(""));
                     }
                     return ROLE.TYPE.eq(t);
                 }),
                createTimeStart.map(ROLE.CREATE_TIME::ge),
                createTimeEnd.map(ROLE.CREATE_TIME::lt))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());


            Map<String, Set<String>> map = grantService.findGrantOrganizationByUri(memberId, uri, Organization.ROOT_ORGANIZATION);
            CodeUtils.generateOrganizationConditions(map, collects);

            List<String> grantRoleIds = d.selectDistinct(ROLE.ID)
                    .from(GRANT_MEMBER)
                    .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID), GRANT_MEMBER.MEMBER_ID.eq(memberId))
                    .leftJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                    .where(ROLE.ID.isNotNull()).fetch(ROLE.ID);
           
            collects.add(orgainizationId.map(id -> ORGANIZATION.PATH.startsWith(organizationDao.get(id).getPath())).orElse(DSL.trueCondition()));


            SelectSelectStep<Record> selectFields;
            if (memberIdOptional.isPresent()) {
                selectFields = d.selectDistinct(Fields.start().add(ROLE)
                                                      .add(GRANT.ID)
                                                      .add(parentNameField, organizationName).end());
            } else {
                selectFields = d.selectDistinct(Fields.start().add(ROLE)
                                                      .add(parentNameField, organizationName).end());
            }
            SelectSelectStep<Record> countFields = d.selectDistinct(Fields.start()
                                                                          .add(ROLE.ID).end());
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a.from(ROLE)
                                                                                             .leftJoin(MENU).on(MENU.ID.eq(ROLE.MENU_ID))
                                                                                             .leftJoin(parentRole).on(parentRole.ID.eq(ROLE.PARENT_ID))
                                                                                             .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ROLE.ORGANIZATION_ID))
                                                                                             .leftJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                                                                                             .leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                                                             .where(collects)
                                                                                             // 历史数据存在role角色没关联菜单的数据，需要过滤掉
                                                                                             .andExists(d.select(ROLE_MENU.ID).from(ROLE_MENU).where(ROLE_MENU.ROLE_ID.eq(ROLE.ID)).limit(1));

            int count = d.fetchCount(stepFunc.apply(countFields));
            List<Role> items = stepFunc.apply(selectFields).orderBy(ROLE.ORDER.asc(), ROLE.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize)
                    .fetch(r -> {
                        Role role = new Role();
                        role.setId(r.getValue(ROLE.ID));
                        role.setName(r.getValue(ROLE.NAME));
                        role.setParentName(r.getValue(parentNameField));
                        role.setOrganizationId(r.getValue(ROLE.ORGANIZATION_ID));
                        role.setParentId(r.getValue(ROLE.PARENT_ID));
                        role.setCreateTime(r.getValue(ROLE.CREATE_TIME));
                        role.setInit(r.getValue(ROLE.INIT));
                        role.setDesc(r.getValue(ROLE.DESC));
                        role.setType(r.getValue(ROLE.TYPE));
                        role.setChildFlag(r.getValue(ROLE.CHILD_FLAG));
                        role.setOwned(grantRoleIds.contains(role.getId()));
                        role.setOrder(r.getValue(ROLE.ORDER));
                        role.setGrantId(r.getValue(GRANT.ID));
                        Organization organization = new Organization();
                        organization.setId(r.getValue(ROLE.ORGANIZATION_ID));
                        organization.setName(r.getValue(organizationName));
                        role.setOrganization(organization);
                        return role;
                    });

            List<String> roleIds = items.stream().map(Role::getId).collect(Collectors.toList());

            // 查询roleIds里面每个有多少人
            Map<String, Integer> roleCountMap = d.select(GRANT.ROLE_ID, GRANT_MEMBER.MEMBER_ID.countDistinct())
                    .from(GRANT_MEMBER)
                    .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                    .where(GRANT.ROLE_ID.in(roleIds))
                    .groupBy(GRANT.ROLE_ID).fetchMap(GRANT.ROLE_ID, GRANT_MEMBER.MEMBER_ID.countDistinct());

            items.forEach(r -> r.setMemberCount(roleCountMap.getOrDefault(r.getId(), 0)));
            return PagedResult.create(count, items);
        });
    }

    private String getGrantOrganizationName(String roleId, DSLContext d) {
        return d.select(
                        DSL.groupConcat(DSL.field("name_with_child", String.class)).separator(", ")
                )
                .from(
                        d.selectDistinct(ORGANIZATION.NAME.concat(
                                        DSL.when(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES), DSL.inline("（含子部门）"))
                                                .otherwise(DSL.inline(""))
                                ).as("name_with_child"))
                                .from(GRANT)
                                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                .where(GRANT.ROLE_ID.eq(roleId))
                )
                .fetchOne(r -> r.getValue(0, String.class));
    }

    // 历史数据存在一个角色可以多次给同一个人授予权限，这里查询具体人数时要去重
    private Integer getMemberCount(String roleId, DSLContext d) {
        return  d.select(GRANT_MEMBER.MEMBER_ID.countDistinct())
                .from(GRANT_MEMBER)
                .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                .where(GRANT.ROLE_ID.eq(roleId))
                .fetchOne(GRANT_MEMBER.MEMBER_ID.countDistinct());
    }


    public PagedResult<Role> findPage_Outdated(int page, int pageSize, String memberId,
                                      Optional<String> name, Optional<String> parentName, Optional<String> organizationId,
                                      Optional<Long> createTimeStart, Optional<Long> createTimeEnd, Integer contain, Optional<Integer> type,
                                      Optional<String> memberIdOptional, Optional<Integer> childFlag) {

        return roleDao.execute(d -> {
            List<Condition> conditions = new ArrayList<>();
            name.ifPresent(n -> conditions.add(ROLE.NAME.like("%" + n + "%").or(ROLE.DESC.like("%" + n + "%"))));
            parentName.ifPresent(pn -> conditions.add(ROLE.PARENT_ID.in(
                    d.select(ROLE.ID).from(ROLE).where(ROLE.NAME.like("%" + pn + "%"))
            )));
            organizationId.ifPresent(orgId -> conditions.add(ROLE.ORGANIZATION_ID.eq(orgId)));
            createTimeStart.ifPresent(start -> conditions.add(ROLE.CREATE_TIME.ge(start)));
            createTimeEnd.ifPresent(end -> conditions.add(ROLE.CREATE_TIME.le(end)));
            type.ifPresent(t -> conditions.add(ROLE.TYPE.eq(t)));
            childFlag.ifPresent(cf -> conditions.add(ROLE.CHILD_FLAG.eq(cf)));

            com.zxy.product.system.jooq.tables.Role parentRole = ROLE.as("parentRole");

            // 查询角色总数
            int count = d.fetchCount(d.selectDistinct(ROLE.ID).from(ROLE).where(conditions));

            // 分页查询角色
            List<Role> items = d.select(
                            ROLE.ID,
                            ROLE.NAME,
                            ROLE.PARENT_ID,
                            ROLE.CREATE_TIME,
                            ROLE.TYPE,
                            ROLE.CHILD_FLAG,
                            ROLE.ORGANIZATION_ID,
                            ROLE.DESC,
                            ROLE.ORDER,
                            ROLE.INIT,
                            GRANT.ID.as("grantId"),
                            parentRole.NAME.as("parentName"),
                            ORGANIZATION.NAME.as("organizationName"),
                            getGrantOrganizationNameField().as("grantOrganizationName"),
                            DSL.count(GRANT_MEMBER.MEMBER_ID).as("memberCount") // **修正 `memberCount` 计算方式**
                    )
                    .from(ROLE)
                    .leftJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                    .leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                    .leftJoin(parentRole).on(parentRole.ID.eq(ROLE.PARENT_ID))
                    .where(conditions)
                    .groupBy(ROLE.ID, parentRole.NAME)
                    .orderBy(ROLE.ORDER.asc(), ROLE.CREATE_TIME.desc())
                    .limit((page - 1) * pageSize, pageSize)
                    .fetch(r -> {
                        Role role = new Role();
                        role.setId(r.getValue(ROLE.ID));
                        role.setName(r.getValue(ROLE.NAME));
                        role.setParentId(r.getValue(ROLE.PARENT_ID));
                        role.setParentName(r.getValue("parentName", String.class));
                        role.setOrganizationId(r.getValue(ROLE.ORGANIZATION_ID));
                        role.setDesc(r.getValue(ROLE.DESC));
                        role.setType(r.getValue(ROLE.TYPE));
                        role.setChildFlag(r.getValue(ROLE.CHILD_FLAG));
                        role.setOrder(r.getValue(ROLE.ORDER));
                        role.setInit(r.getValue(ROLE.INIT));
                        role.setGrantId(r.getValue("grantId", String.class));

                        Organization organization = new Organization();
                        organization.setId(role.getOrganizationId());
                        organization.setName(r.getValue("organizationName", String.class));
                        role.setOrganization(organization);

                        role.setGrantOrganizationName(r.getValue("grantOrganizationName", String.class));
                        role.setMemberCount(r.getValue("memberCount", Integer.class));

                        return role;
                    });

            return PagedResult.create(count, items);
        });
    }

    private Field<String> getGrantOrganizationNameField() {
        return DSL.groupConcat(
                        DSL.when(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES),
                                        ORGANIZATION.NAME.concat("（含子部门）"))
                                .otherwise(ORGANIZATION.NAME))
                .separator(", ").as("grantOrganizationName");
    }

    @Override
    public List<Role> findByMemberId(String memberid) {
        return roleDao.execute(x ->
                x.selectDistinct(ROLE.ID, ROLE.NAME, ROLE.ORGANIZATION_ID, ROLE.PARENT_ID, ROLE.CREATE_TIME).from(ROLE)
                        .innerJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                        .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                        .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()), GRANT_MEMBER.MEMBER_ID.eq(memberid)).fetchInto(Role.class));
    }

    /**
     * 查询当前登录用户是否有管理员角色（仅为登录提供）
     *
     * @param memberId 当前登录用户Id
     * @return 查询当前登录用户是否有管理员角色
     */
    @Override
    public Integer whetherAdministrator(String memberId) {
        List<String> relevancyRoleIdCollect = grantMemberDao.execute(ew1 ->
                ew1.select(GRANT_MEMBER.ID)
                        .from(GRANT_MEMBER)
                        .where(GRANT_MEMBER.MEMBER_ID.eq(memberId))
                        .limit(1)
                        .fetch(GRANT_MEMBER.ID)
        );
        return !CollectionUtils.isEmpty(relevancyRoleIdCollect)?relevancyRoleIdCollect.size():0;
    }





    //todo 压测优化点：所属接口/api/v1/system/setting/frontend?_=1731779864509,修改为查询Count()，避免临时表创建
    @Override
    public int getRoleLengthByMemberId(String memberId) {
        List<String> roleIds = roleDao.execute(x ->
                x.selectDistinct(ROLE.ID).from(ROLE)
                        .innerJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                        .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                        .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()), GRANT_MEMBER.MEMBER_ID.eq(memberId)).fetch(ROLE.ID));
        return roleIds.isEmpty() ? 0 : roleIds.size();
    }

    @Override
    public List<Role> findWithGranted(String memberId) {
        return roleDao.execute(d -> {
            return d.selectDistinct(Fields.start().add(ROLE.ID, ROLE.NAME, ROLE.ORGANIZATION_ID).add(GRANT.ID).add(ORGANIZATION.NAME).end())
                    .from(GRANT_MEMBER)
                    .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID), GRANT_MEMBER.MEMBER_ID.eq(memberId))
                    .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ROLE.ORGANIZATION_ID))
                    .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                    .fetch(r -> {
                        Role role = new Role();
                        role.setId(r.getValue(ROLE.ID));
                        role.setName(r.getValue(ROLE.NAME));
                        role.setOrganizationId(r.getValue(ROLE.ORGANIZATION_ID));
                        role.setGrantId(r.getValue(GRANT.ID));
                        Organization organization = new Organization();
                        organization.setId(r.getValue(ROLE.ORGANIZATION_ID));
                        organization.setName(r.getValue(ORGANIZATION.NAME));
                        role.setOrganization(organization);

                        return role;
                    });
        });
    }

    @Override
    public PagedResult<Member> findMemberByRole(Integer page, Integer pageSize, String roleId, String organizationId) {
        return roleDao.execute(d -> {
            com.zxy.product.system.jooq.tables.Organization companyOrg = ORGANIZATION.as("company");
            com.zxy.product.system.jooq.tables.Organization org = ORGANIZATION.as("org");
            Field<String> companyName = companyOrg.NAME.as("companyName");
            Field<Integer> companyLevel = companyOrg.LEVEL.as("companyLevel");
            Field<String> orgName = org.NAME.as("orgName");
            Field<Integer> orgLevel = org.LEVEL.as("orgLevel");
//            SelectConditionStep<Record> scs = d.selectDistinct(Fields.start().add(MEMBER.fields()).add(orgName).add(orgLevel).add(companyName).add(companyLevel).end())
//                    .from(MEMBER)
//                    .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.MEMBER_ID.eq(MEMBER.ID))
//                    .leftJoin(GRANT).on(GRANT.ID.eq(GRANT_DETAIL.GRANT_ID))
//                    .leftJoin(org).on(org.ID.eq(MEMBER.ORGANIZATION_ID))
//                    .leftJoin(companyOrg).on(companyOrg.ID.eq(org.COMPANY_ID))
//                    .where(GRANT.ROLE_ID.eq(roleId))
//                    .and(MEMBER.ORGANIZATION_ID.eq(organizationId));

            SelectConditionStep<Record> scs = d.selectDistinct(Fields.start()
            		.add(MEMBER.fields())
            		.add(orgName)
            		.add(orgLevel)
            		.add(companyName)
            		.add(companyLevel).end())
            .from(GRANT_MEMBER)
            .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
            .leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
            .leftJoin(companyOrg).on(companyOrg.ID.eq(org.COMPANY_ID))
            .where(GRANT.ROLE_ID.eq(roleId))
            .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
            .and(MEMBER.ORGANIZATION_ID.eq(organizationId));
            
            return PagedResult.create(
                    d.fetchCount(scs),
                    scs.limit(((page - 1) * pageSize), pageSize)
                            .fetch(r -> {
                                Member m = r.into(MEMBER).into(Member.class);

                                Organization o = new Organization();
                                o.setName(r.getValue(orgName));
                                o.setLevel(r.getValue(orgLevel));
                                m.setOrganization(o);

                                Organization compOrg = new Organization();
                                compOrg.setName(r.getValue(companyName));
                                compOrg.setLevel(r.getValue(companyLevel));
                                m.setCompanyOrganization(compOrg);
                                return m;
                            }));
        });
    }

    @Override
    public PagedResult<Member> findMemberWithRole(
            Integer page, Integer pageSize, String memberId, String uri, Optional<String> organizationId, Optional<String> fullName,
            Optional<String> name, Optional<String> positionId, Integer contain, Optional<String> content) {

        List<Condition> conditions = new ArrayList<>();
        fullName.ifPresent(value -> conditions.add(MEMBER.FULL_NAME.contains(value)));
        name.ifPresent(value -> conditions.add(MEMBER.NAME.contains(value)));
        positionId.ifPresent(value -> conditions.add(MEMBER.POSITION_ID.eq(value)));
        conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));
        content.ifPresent(value -> conditions.add(
                MEMBER.FULL_NAME.contains(value)
                        .or(MEMBER.NAME.contains(value))
                        .or(ORGANIZATION.NAME.contains(value))
        ));

        // addOrganizationConditions(conditions, memberId, uri, organizationId, contain);

        Map<String, Set<String>> map = grantService.findGrantOrganizationByUri(memberId, uri, Organization.ROOT_ORGANIZATION);
        CodeUtils.generateOrganizationConditions(map, conditions);

        int totalCount = memberDao.execute(d -> d.select(MEMBER.ID.countDistinct())
                .from(GRANT_MEMBER)
                .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                .where(conditions)
                .fetchOne(MEMBER.ID.countDistinct()));

        // 查询分页数据
        List<Member> members = fetchPagedMembers(page, pageSize, conditions, memberId);

        if (members.isEmpty()) {
            return PagedResult.create(totalCount, members);
        }

        Map<String, List<String>> roleNames = fetchMemberRoles(members);

        members.forEach(m -> {
            List<String> roleNameList = roleNames.getOrDefault(m.getId(), Collections.emptyList());
            m.setRoleName(formatRoleNames(roleNameList));
        });

        return PagedResult.create(totalCount, members);
    }

    private void addOrganizationConditions(List<Condition> conditions, String memberId, String uri,
                                           Optional<String> organizationId, Integer contain) {
        if (!organizationId.isPresent() || "null".equals(organizationId.get())) {
            conditions.add(ORGANIZATION.ID.in(grantService.findGrantedTopOrganization(memberId, uri, contain)));
        } else {
            String orgId = organizationId.get();
            if (contain == 0) {
                conditions.add(ORGANIZATION.ID.in(grantService.findHavingGrantedOrganization(memberId, uri, orgId)));
            } else if (contain == 1) {
                String path = organizationDao.execute(d -> d.select(ORGANIZATION.PATH)
                        .from(ORGANIZATION).where(ORGANIZATION.ID.eq(orgId)).fetchOne(ORGANIZATION.PATH));
                conditions.add(ORGANIZATION.PATH.contains(path));
            }
        }
    }

    private List<Member> fetchPagedMembers(Integer page, Integer pageSize, List<Condition> conditions, String memberId) {
        return memberDao.execute(d -> {
            List<Member> members = d.selectDistinct(Fields.start().add(
                                    MEMBER.ID,
                                    MEMBER.FULL_NAME,
                                    MEMBER.NAME,
                                    MEMBER.POSITION_ID,
                                    MEMBER.CREATE_TIME,
                                    MEMBER.ORGANIZATION_ID,
                                    MEMBER.ORGANIZATION_NAME,
                                    MEMBER.COMPANY_ID,
                                    MEMBER.INIT,
                                    ORGANIZATION.ID,
                                    ORGANIZATION.NAME,
                                    ORGANIZATION.LEVEL)
                            .add(DSL.min(GRANT_MEMBER.CREATE_TIME)).end())
                    .from(GRANT_MEMBER)
                    .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                    .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                    .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                    .where(conditions)
                    .groupBy(MEMBER.ID)
                    .orderBy(DSL.min(GRANT_MEMBER.CREATE_TIME).desc())
                    .limit((page - 1) * pageSize, pageSize)
                    .fetch(r -> {
                        Member member = r.into(MEMBER).into(Member.class);
                        member.setOrganization(r.into(ORGANIZATION).into(Organization.class));
                        member.setSelf(memberId.equals(member.getId()));
                        member.setCreateTime(r.getValue(DSL.min(GRANT_MEMBER.CREATE_TIME)));
                        return member;
                    });
            return members;
        });
    }

    private Map<String, List<String>> fetchMemberRoles(List<Member> members) {
        List<String> memberIds = members.stream().map(Member::getId).collect(Collectors.toList());
        return roleDao.execute(d -> d.select(GRANT_MEMBER.MEMBER_ID, ROLE.NAME)
                .from(ROLE)
                .innerJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                .where(GRANT_MEMBER.MEMBER_ID.in(memberIds))
                .fetchGroups(GRANT_MEMBER.MEMBER_ID, ROLE.NAME));
    }

    private String formatRoleNames(List<String> roleNames) {
        if (roleNames.isEmpty()) {
            return null;
        }
        if (roleNames.size() > 3) {
            return roleNames.stream().limit(3).collect(Collectors.joining("，")) + "，...";
        }
        return String.join("，", roleNames);
    }

    // 保留旧逻辑代码
    public PagedResult<Member> findMemberWithRole_obsolete(Integer page, Integer pageSize, String memberId, String uri, Optional<String> organizationId, Optional<String> fullName,
                                                  Optional<String> name, Optional<String> positionId, Integer contain, Optional<String> content) {
        List<Condition> conditions = Stream.of(
                fullName.map(MEMBER.FULL_NAME::contains),
                name.map(MEMBER.NAME::contains),
                positionId.map(MEMBER.POSITION_ID::eq),
                Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        conditions.add(content.map(c -> MEMBER.FULL_NAME.contains(c).or(MEMBER.NAME.contains(c).or(ORGANIZATION.NAME.contains(c)))).orElse(DSL.trueCondition()));

        if (!organizationId.isPresent()) { // 初始化
            conditions.add(ORGANIZATION.ID.in(grantService.findGrantedTopOrganization(memberId, uri, contain)));
        } else if (organizationId.isPresent() && contain == 0) { // 不包含
            conditions.add(ORGANIZATION.ID.in(grantService.findHavingGrantedOrganization(memberId, uri, organizationId.get())));
        } else if (organizationId.isPresent() && contain == 1) { // 包含
            String path = organizationDao.execute(d -> d.select(ORGANIZATION.PATH).from(ORGANIZATION)
                    .where(ORGANIZATION.ID.eq(organizationId.get())).fetchOne(ORGANIZATION.PATH));

            conditions.add(ORGANIZATION.PATH.contains(path));
        }

        List<String> grantOrgIds = grantService.findGrantedOrganization(
                memberId, Grant.URI, Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.empty(), organizationId, Optional.empty(),
                Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());

        conditions.add(ORGANIZATION.ID.in(grantOrgIds));

        PagedResult<Member> adminMembers = PagedResult.create(
                memberDao.execute(d -> d.select(MEMBER.ID.countDistinct())
                        .from(GRANT_MEMBER)
                        .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                        .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                        .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))

                        .where(conditions)
                        .fetchOne(MEMBER.ID.countDistinct())),
                memberDao.execute(d -> {
                    List<Member> members = d.selectDistinct(Fields.start().add(
                                            MEMBER.ID,
                                            MEMBER.FULL_NAME,
                                            MEMBER.NAME,
                                            MEMBER.POSITION_ID,
                                            MEMBER.CREATE_TIME,
                                            MEMBER.ORGANIZATION_ID,
                                            MEMBER.ORGANIZATION_NAME,
                                            MEMBER.COMPANY_ID,
                                            MEMBER.INIT,
                                            ORGANIZATION.ID,
                                            ORGANIZATION.NAME,
                                            ORGANIZATION.LEVEL)
                                    .add(DSL.min(GRANT_MEMBER.CREATE_TIME)).end())
                            .from(GRANT_MEMBER)
                            .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                            .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                            .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                            .where(conditions)
                            .groupBy(MEMBER.ID)
                            .orderBy(DSL.min(GRANT_MEMBER.CREATE_TIME).desc())
                            .limit((page - 1) * pageSize, pageSize)
                            .fetch(r -> {
                                Member member = r.into(MEMBER).into(Member.class);
                                member.setOrganization(r.into(ORGANIZATION).into(Organization.class));
                                member.setSelf(memberId.equals(member.getId()));
                                member.setCreateTime(r.getValue(DSL.min(GRANT_MEMBER.CREATE_TIME)));
                                return member;
                            });
                    return members;
                }));
        if (CollectionUtils.isEmpty(adminMembers.getItems())) {
            return adminMembers;
        }
        Map<String, List<Member>> roleNames = roleDao.execute(d -> d.select(GRANT_MEMBER.MEMBER_ID, ROLE.NAME)
                .from(ROLE)
                .innerJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                .where(
                        GRANT_MEMBER.MEMBER_ID.in(adminMembers.getItems().stream().map(Member::getId).collect(Collectors.toList())))
                .fetch(r -> {
                    Member member = new Member();
                    member.setId(r.getValue(GRANT_MEMBER.MEMBER_ID));
                    member.setRoleName(r.getValue(ROLE.NAME));
                    return member;
                })).stream().collect(Collectors.groupingBy(Member::getId));
        adminMembers.getItems().forEach(m -> {
            List<String> roleNameList = roleNames.getOrDefault(m.getId(), new ArrayList<>()).stream().map(Member::getRoleName).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(roleNameList)) {
                return;
            }
            if (roleNameList.size() > 3) {
                m.setRoleName(roleNameList.stream().limit(3).collect(Collectors.joining("，")) + "，...");
            } else {
                m.setRoleName(String.join("，", roleNameList));
            }
        });
        return adminMembers;
    }

    @Override
    public List<Member> findMemberWithRole(String memberId, String uri, String organizationId) {
        return memberDao.execute(d -> {
//            List<Member> members = d.selectDistinct(Fields.start().add(MEMBER).end())
//                    .from(MEMBER)
//                    .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(MEMBER.ID))
//                    .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(MEMBER.COMPANY_ID))
//                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(MEMBER.COMPANY_ID))
//                    .where(GRANT_DETAIL.MEMBER_ID.eq(memberId), GRANT_DETAIL.URI.eq(uri), ORGANIZATION_DETAIL.ROOT.eq(organizationId))
//                    .fetchInto(Member.class);
        	List<Member> members = d.selectDistinct(Fields.start().add(MEMBER).end())
        			 .from(GRANT_MEMBER)
                     .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                     .leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                     .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                     .leftJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                     .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                     .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(MEMBER.COMPANY_ID))
                     .where(GRANT_MEMBER.MEMBER_ID.eq(memberId), MENU.URI.eq(uri), ORGANIZATION_DETAIL.ROOT.eq(organizationId),
                         GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                     .fetchInto(Member.class);
            return members;
        });
    }

    @Override
    public List<Role> findGrantRoles(String memberId, String uri) {
        return menuDao.execute(d -> {

            Map<String, Set<String>> map = grantService.findGrantOrganizationByUri(memberId, uri, Organization.ROOT_ORGANIZATION);
            List<Condition> conditions = Stream.of(Optional.of(ROLE.TYPE.eq(Role.TYPE_STANDARD))).map(Optional::get).collect(Collectors.toList());
            CodeUtils.generateOrganizationConditions(map, conditions);

            return d.selectDistinct(ROLE.fields())
                    .from(ROLE)
                    .leftJoin(ORGANIZATION).on(ROLE.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .where(conditions)
                    .orderBy(ROLE.CREATE_TIME.desc()).fetchInto(Role.class);
        });
    }

    @Override
    public PagedResult<Role> findGrantRolePage(String memberId, String uri, Optional<String> content, Optional<Integer> type) {
        return menuDao.execute(d -> {

            Map<String, Set<String>> map = grantService.findGrantOrganizationByUri(memberId, uri, Organization.ROOT_ORGANIZATION);
            List<Condition> conditions = Stream.of(
                type.map(ROLE.TYPE::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            CodeUtils.generateOrganizationConditions(map, conditions);

            if (type.isPresent() && Role.TYPE_ELEMENT.equals(type.get())) {
                // conditions.clear();
                conditions.add(MENU.URI.isNotNull());
                conditions.add(MENU.URI.ne(""));
            }
            type.ifPresent(t -> conditions.add(ROLE.TYPE.eq(t)));
            content.ifPresent(c -> conditions.add(ROLE.NAME.contains(c).or(ROLE.DESC.contains(c))));
            // 查询所有标记包含子的角色
            Condition finalConditions = conditions.stream().reduce(Condition::and).map(c -> {
                c = c.or(ROLE.CHILD_FLAG.eq(Role.CHILD_FLAG_YES)
                                        .and(type.map(ROLE.TYPE::eq).orElse(DSL.trueCondition()))
                                        .and(content.map(m -> ROLE.NAME.contains(m).or(ROLE.DESC.contains(m))).orElse(DSL.trueCondition())));
                return c;
            }).orElse(DSL.trueCondition());
            int count = d.select(ROLE.ID.countDistinct()).from(ROLE)
                         .leftJoin(MENU).on(MENU.ID.eq(ROLE.MENU_ID))
                         .leftJoin(ORGANIZATION).on(ROLE.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                         .where(finalConditions)
                         .fetchOne(ROLE.ID.count());

            List<Role> list = d.selectDistinct(ROLE.fields())
                                .from(ROLE)
                                .leftJoin(MENU).on(MENU.ID.eq(ROLE.MENU_ID))
                                .leftJoin(ORGANIZATION).on(ROLE.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                                .where(finalConditions)
                                .orderBy(ROLE.ORDER.asc(), ROLE.CREATE_TIME.desc()).fetchInto(Role.class);
            return PagedResult.create(count, list);
        });
    }

    /** 得到当前角色的所有父id(含自己)组成的path */
    private String getPath(Role role) {
        StringBuilder sb = new StringBuilder();
        return Optional.ofNullable(role.getParentId()).map(t -> {
            return sb.append(roleDao.get(role.getParentId()).getPath()).append(role.getId()).append(",").toString();
        }).orElse(sb.append(role.getId()).append(",").toString());

    }

    @Override
    public List<Member> findAdminMemberForExport(String memberId, String uri, Optional<String> organizationId, Optional<String> fullName,
                                                 Optional<String> name, Optional<String> positionId, Integer contain, String rootOrganizationId, int page, int pageSize) {
        com.zxy.product.system.jooq.tables.Organization ROLE_ORG = ORGANIZATION.as("role_org");
        List<Condition> conditions = Stream.of(
                fullName.map(MEMBER.FULL_NAME::contains),
                name.map(MEMBER.NAME::contains),
                Optional.of(ROLE.ID.isNotNull()),
                Optional.of(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())),
                positionId.map(MEMBER.POSITION_ID::eq)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        Map<String, Set<String>> map = grantService.findGrantOrganizationByUri(memberId, uri, Organization.ROOT_ORGANIZATION);
        CodeUtils.generateOrganizationConditions(map, conditions);

        return menuDao.execute(d ->{
            List<Member> members = d.selectDistinct(Fields.start().add(
            		MEMBER.ID,
            		MEMBER.FULL_NAME,
            		MEMBER.NAME,
            		MEMBER.POSITION_ID,
            		GRANT_MEMBER.CREATE_TIME,
                    ORGANIZATION.ID,
                    ORGANIZATION.NAME,
            		ROLE.ID,
            		ROLE.NAME,
            		ROLE_ORG.ID,
            		ROLE_ORG.NAME,
                    ROLE.ORDER,
                    ROLE.CREATE_TIME,
                    MENU.LEVEL,
                    MENU.NAME
            		 ).end())
        		 .from(GRANT_MEMBER)
                 .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                 .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                 .leftJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                 .innerJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                 .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                 .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                 .leftJoin(ROLE_ORG).on(ROLE_ORG.ID.eq(ROLE.ORGANIZATION_ID))
	             .where(conditions)
                 .limit((page - 1) * pageSize, pageSize)
	             .fetch(r -> {
	                   Member member = r.into(MEMBER).into(Member.class);
	                   member.setCreateTime(r.getValue(GRANT_MEMBER.CREATE_TIME));
                       Organization memberOrganization = new Organization();
                       memberOrganization.setId(r.getValue(ORGANIZATION.ID));
                       memberOrganization.setName(r.getValue(ORGANIZATION.NAME));
                       member.setOrganization(memberOrganization);
	                   member.setRoleId(r.getValue(ROLE.ID));
	                   member.setRoleName(r.getValue(ROLE.NAME));
	                   member.setRoleOrgId(r.getValue(ROLE_ORG.ID));
	                   member.setRoleOrgName(r.getValue(ROLE_ORG.NAME));
                       member.setMenuLevel(r.getValue(MENU.LEVEL));
                       member.setMenuName(r.getValue(MENU.NAME));
                       member.setRoleOrder(r.getValue(ROLE.ORDER));
                       member.setRoleCreateTime(r.getValue(ROLE.CREATE_TIME));
	                   return member;
	              });

            
            // Map<String, List<Member>> memberMap = Maps.newHashMap();
            // members.forEach(m -> {
            //     String k = m.getId() + "&" + m.getRoleId();
            //     if (memberMap.containsKey(k)) {
            //         memberMap.get(k).add(m);
            //     } else {
            //         List<Member> ms = Lists.newArrayList();
            //         ms.add(m);
            //         memberMap.put(k, ms);
            //     }
            // });
            // members.clear();
            // memberMap.values().forEach(ms -> {
            //     if (ms.size() > 1) {
            //         Member m0 = ms.get(0);
            //         Set<String> s = Sets.newHashSet();
            //         for (int i = 0; i < ms.size(); i++) {
            //             Member m = ms.get(i);
            //             if (m0.getCreateTime() - m.getCreateTime() > 0) {
            //                 m0.setCreateTime(m.getCreateTime());
            //             }
            //             if (StringUtils.hasText(m.getGrantOrgName())) {
            //                 s.add(GrantOrganization.CHILD_FIND_YES.equals(m.getGrantOrgChildFind()) ? m.getGrantOrgName() : m.getGrantOrgName() + "(不包含)");
            //             } else {
            //                 s.add(" ");
            //             }
            //         }
            //         m0.setGrantOrgName(Joiner.on(";").join(s));
            //     } else {
            //         Member m = ms.get(0);
            //         if (StringUtils.hasText(m.getGrantOrgName())) {
            //             m.setGrantOrgName(GrantOrganization.CHILD_FIND_YES.equals(m.getGrantOrgChildFind()) ? m.getGrantOrgName() : m.getGrantOrgName() + "(不包含)");
            //         }
            //     }
            //     members.add(ms.get(0));
            // });
            return members;
        });

    }

    @Override
    public List<Member> findAdminMemberIdForExport(String memberId, String uri, Optional<String> organizationId, Optional<String> fullName, Optional<String> name,
                                                   Optional<String> positionId, Integer contain, String rootOrganizationId) {
        List<Condition> conditions = new ArrayList<>();
        fullName.ifPresent(value -> conditions.add(MEMBER.FULL_NAME.contains(value)));
        name.ifPresent(value -> conditions.add(MEMBER.NAME.contains(value)));
        positionId.ifPresent(value -> conditions.add(MEMBER.POSITION_ID.eq(value)));
        conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));

        // addOrganizationConditions(conditions, memberId, uri, organizationId, contain);

        Map<String, Set<String>> map = grantService.findGrantOrganizationByUri(memberId, uri, Organization.ROOT_ORGANIZATION);
        CodeUtils.generateOrganizationConditions(map, conditions);

        return memberDao.execute(d -> d.selectDistinct(Fields.start().add(MEMBER.ID).add(DSL.min(GRANT_MEMBER.CREATE_TIME)).end())
                                   .from(GRANT_MEMBER)
                                   .innerJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
                                   .innerJoin(ROLE).on(ROLE.ID.eq(GRANT.ROLE_ID))
                                   .innerJoin(MEMBER).on(MEMBER.ID.eq(GRANT_MEMBER.MEMBER_ID))
                                   .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                                   .where(conditions)
                                   .groupBy(MEMBER.ID)
                                   .orderBy(DSL.min(GRANT_MEMBER.CREATE_TIME).desc())
                                   .fetch(r -> {
                                    Member member = new Member();
                                    member.setId(r.getValue(MEMBER.ID));
                                    return member;
                                }));
    }

    /**
     * 
     * 获取4级授权组织替换GRANT_DETAIL
     * @param memberId
     * @param url
     * @param d
     * @return
     */
	private List<String> getGrantOrganizationIds(String memberId, String url, DSLContext d) {
		return d.selectDistinct(Fields.start().add(ORGANIZATION.ID).end())
			  .from(GRANT)
			  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
			  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
			  .innerJoin(MENU).on(MENU.URI.eq(url).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
			  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
			  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
			  .innerJoin(ORGANIZATION).on(
			          DSL.trueCondition()
		              .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
		                      .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
		              .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
		                      .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
			  .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
			  .fetch(ORGANIZATION.ID);
	}
	
 
	private List<String> getGrantOrganizationIdsAndChild(String memberId, String uri){
   	
    	List<String> list = roleDao.execute(dao -> {
    		  return dao.selectDistinct(Fields.start().add(ORGANIZATION.ID).end())
  			  .from(GRANT)
  			  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
  			  .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
  			  .innerJoin(MENU).on(MENU.URI.eq(uri).and(MENU.ID.eq(ROLE_MENU.MENU_ID)))
  			  .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
  			  .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
  			  .innerJoin(ORGANIZATION).on(
  			          DSL.trueCondition()
  		              .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
  		                      .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
  		              .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
  		                      .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
  			  .where(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
  			  .fetch(ORGANIZATION.ID);
    	});	  
    	return list;
    }

	@Override
	public Boolean isContainsAdminRole(String memberId, String roleId, String organizationId) {
		return roleDao.execute(x ->
	    	x.select(DSL.count(ROLE.ID))
			.from(ROLE)
	        .innerJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
	        .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
	        .where(GRANT_MEMBER.MEMBER_ID.eq(memberId))
            .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
	        .and(ROLE.ID.eq(roleId)
	        		.and(ROLE.ORGANIZATION_ID.eq(organizationId))))
	        .fetchOne(DSL.count(ROLE.ID)) > 0;
	}

    /**
     * 查询当前角色所有子角色
     *
     * @param roleId
     * @return
     */
    @Override
    public List<Role> listsubRole(String roleId) {
        int number = 0;

        List<Role> list = roleDao.execute(dao -> {
            return dao.selectDistinct(Fields.start().add(ROLE.NAME).end())
                    .from(ROLE)
                    .where(ROLE.PARENT_ID.eq(roleId))
                    .orderBy(ROLE.CREATE_TIME.desc())
                    .fetch(r -> {
                        Role role = new Role();
                        role.setName(r.getValue(ROLE.NAME));
                        return role;
                    });
        });

        //使用id作为排序号赋值
        for (Role item : list) {
            item.setId(Integer.valueOf(++number).toString());
        }

        return list;
    }


    @Override
    public boolean findRoleByMemberId(String currentUserId, Optional<Integer> flag) {
        List<Organization> list = roleDao.execute(r ->
                r.select(ORGANIZATION.PARENT_ID, ORGANIZATION.ID)
                        .from(ROLE)
                        .leftJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                        .leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                        .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                        .where(ROLE.ID.eq(ROLE_ID))
                        .and(GRANT_MEMBER.MEMBER_ID.eq(currentUserId))
                        .fetch(o -> {
                            Organization organization = new Organization();
                            organization.setParentId(o.getValue(ORGANIZATION.PARENT_ID));
                            organization.setId(o.getValue(ORGANIZATION.ID));
                            return organization;
                        }));

        boolean baseCondition = checkListCondition(list);
        if (flag.isPresent()) {
            String strFlag = roleDao.execute(r ->
                    r.select(ROLE.ID)
                            .from(ROLE)
                            .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(ROLE.ID))
                            .leftJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                            .leftJoin(GRANT).on(GRANT.ROLE_ID.eq(ROLE.ID))
                            .leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                            .where(MENU.ID.eq(MENU_ID))
                            .and(GRANT_MEMBER.MEMBER_ID.eq(currentUserId))
                            .fetchAny(ROLE.ID));
            boolean hasStrFlag = !ObjectUtils.isEmpty(strFlag);
            return hasStrFlag && baseCondition;
        }
        return baseCondition;
    }

    private static boolean checkListCondition(List<Organization> list) {
        return !CollectionUtils.isEmpty(list) && list.stream().anyMatch(r ->
                ObjectUtils.isEmpty(r.getParentId())
                        ? r.getId().equals(Organization.ROOT_ORGANIZATION)
                        : Arrays.asList(Organization.ROOT_ORGANIZATION, Organization.INTERNAL_ORGANIZATION)
                        .contains(r.getParentId()));
    }

}

