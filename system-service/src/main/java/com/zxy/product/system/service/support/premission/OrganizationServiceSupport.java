package com.zxy.product.system.service.support.premission;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.system.api.permission.OrganizationDetailService;
import com.zxy.product.system.api.permission.OrganizationService;
import com.zxy.product.system.content.ErrorCode;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.entity.*;
import com.zxy.product.system.util.OrganizationUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.zxy.product.system.jooq.Tables.*;


@Service
public class OrganizationServiceSupport implements OrganizationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrganizationServiceSupport.class);

    private final static String CACHED_COUNT_PARENT = "cachedCountParent#";
    private final static String COMMA = ",";
    private final static String DEFAULT_AREA_CODE = "000";
    private final static int MAX_SEARCH_COUNT = 1000;
    private static final String ROOT_ORGANIZATION_ID = "1";
    private static final String CACHE_ROOT_ORGANIZATION = "root-organization";
    private static final String CACHE_GRANTED_ORGANIZATION = "cachedGrantedOrganization#";
    private static final String CACHE_FAKE_ENABLED_ORGANIZATIONIDS = "cachedFakeEnabledOrganizationIds";

    private CommonDao<Organization> organizationDao;
    private CommonDao<OrganizationDetail> organizationDetailDao;
    private OrganizationDetailService organizationDetailService;
    private MessageSender messageSender;
    private Environment env;
    private Cache cache;
    private CommonDao<DeleteDataSystem> deleteDataSystemCommonDao;
    private CommonDao<OrganizationSupervisor> organizationSupervisorCommonDao;

    @Autowired
    public void setOrganizationSupervisorCommonDao(CommonDao<OrganizationSupervisor> organizationSupervisorCommonDao) {
        this.organizationSupervisorCommonDao = organizationSupervisorCommonDao;
    }

    @Autowired
    public void setEnvironment(CommonDao<DeleteDataSystem> deleteDataSystemCommonDao) {
        this.deleteDataSystemCommonDao = deleteDataSystemCommonDao;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("organization");
    }

    @Autowired
    public void setEnvironment(Environment env) {
        this.env = env;
    }

    @Autowired
    public void setOrganizationDetailService(OrganizationDetailService organizationDetailService) {
        this.organizationDetailService = organizationDetailService;
    }

    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
        this.organizationDao = organizationDao;
    }

    @Autowired
    public void setOrganizationDetailDao(CommonDao<OrganizationDetail> organizationDetailDao) {
        this.organizationDetailDao = organizationDetailDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Override
    public Organization insert(Optional<String> id, String name, Optional<String> shortName, String code, String parentId, Integer cmccLevel, Integer level, Optional<Integer> order, Optional<String> cmccAttribute, Optional<String> cmccCategory, Integer status, Optional<String> extentionParams, Optional<Integer> type, Optional<Long> vaildStartTime, Optional<Long> vaildEndTime,Optional<String> supervisorIds) {
        Organization organization = new Organization();
        organization.forInsert();
        id.ifPresent(organization::setId);
        organization.setName(name);
        shortName.ifPresent(organization::setShortName);
        organization.setCode(code);
        organization.setParentId(parentId);
        organization.setPath(getPath(organization));
        Organization parentOrg = organizationDao.get(parentId);
        organization.setDepth(parentOrg.getDepth() + 1);
        organization.setCmccLevel(cmccLevel);
        organization.setLevel(level);
        organization.setOrder(getOrgOrder(Organization.ADD, order, parentId));
        cmccAttribute.ifPresent(organization::setCmccAttribute);
        cmccCategory.ifPresent(organization::setCmccCategory);
        organization.setStatus(this.checkValidityTime(vaildStartTime, vaildEndTime, status));
        organization.setType(type.orElseGet(parentOrg::getType));
        organization.setCompanyId(level < Organization.LEVEL_DEPARTMENT ? organization.getId() : parentOrg.getCompanyId());
        vaildStartTime.ifPresent(organization::setValidityStartTime);
        vaildEndTime.ifPresent(organization::setValidityEndTime);
        organizationDao.insert(organization);
        updateReleatedOrgOrder(Organization.ADD, organization.getId(), organization.getOrder(), parentId);
        cache.clear(CACHE_FAKE_ENABLED_ORGANIZATIONIDS);
        //设置组织主管数据
        handleSupervisor(supervisorIds,organization.getId());
        messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_INSERT, MessageHeaderContent.ID, organization.getId());
        return organization;
    }

    /**
     * 根据时间获取有效状态
     *
     * @param vaildStartTime 开始时间
     * @param vaildEndTime   结束时间
     * @param status         状态
     * @return java.lang.Integer
     * @throws
     * @method checkValidityTime
     * <AUTHOR> Qian
     * @version 1.0
     * @date 2025/1/7 15:22
     */
    private Integer checkValidityTime(Optional<Long> vaildStartTime, Optional<Long> vaildEndTime, Integer status) {
        long currentTime = System.currentTimeMillis();
        // 如果 startTime 和 endTime 都为 null，表示没有时间限制，默认返回 默认状态
        if (!vaildStartTime.isPresent() && !vaildEndTime.isPresent()) {
            return status;
        }
        status = Organization.STATUS_DISABLED;
        // 如果 startTime 为 null，表示没有时间限制开始，只需判断当前时间是否小于 endTime
        if (!vaildStartTime.isPresent() && currentTime <= vaildEndTime.get()) {
            status = Organization.STATUS_ENABLED;
        }

        // 如果 endTime 为 null，表示没有时间限制结束，只需判断当前时间是否大于 startTime
        if (!vaildEndTime.isPresent() && currentTime >= vaildStartTime.get()) {
            status = Organization.STATUS_ENABLED;
        }

        // 如果 startTime 和 endTime 都有值，判断当前时间是否在时间段内
        if (currentTime >= vaildStartTime.get() && currentTime <= vaildEndTime.get()) {
            status = Organization.STATUS_ENABLED;
        }
        return status;
    }


    @Override
    public Map<String, List<Organization>> batchInsert(List<Organization> organizations, String memberId, Optional<Map<String, Object>> options) {
//        LOGGER.info("用户{}开始批量插入组织", memberId);
        // 过滤1:code=null 2:parentCode=null 3:code=parentCode 4:重复code
        Set<String> codes = new HashSet<>();
        List<Organization> orglist = new ArrayList<>();
        organizations.forEach(o -> {
            if (o.getCode() != null && o.getParentCode() != null && !o.getCode().equals(o.getParentCode()) && !codes.contains(o.getCode())) {
                orglist.add(o);
                codes.add(o.getCode());
            }
        });
        // 过滤数据库中存在的code
        List<String> existCodes = organizationDao.execute(d -> d.select(ORGANIZATION.CODE)
                                                                .from(ORGANIZATION)
                                                                .where(ORGANIZATION.CODE.in(codes))
                                                                .fetch(ORGANIZATION.CODE)
        );
        List<Organization> list = orglist.stream().filter(o -> !existCodes.contains(o.getCode())).collect(Collectors.toList());
        orglist.clear();
        orglist.addAll(list);
        codes.removeAll(existCodes);
        // 需要从数据库中找寻的父结点
        Set<String> parentCode = orglist.stream()
                                        .map(Organization::getParentCode)
                                        .filter(c -> !codes.contains(c))
                                        .collect(Collectors.toSet());
        // 找到父节点,所有的其它节点都是这些父结点之下的
        Map<String, Organization> organizationMap = organizationDao.fetch(ORGANIZATION.CODE.in(parentCode)).stream()
                                                                   .collect(Collectors.toMap(Organization::getCode, o -> o, (o1, o2) -> o1));
        // 将组织树排序,确保父节点比子节点先计算
        List<Organization> sortList = OrganizationUtil.treeOrganizationsByCode(orglist);
        orglist.clear();
        /** 最后组装数据,思路:因为sortList是有序的,所以父结点永远比子节点先算出来,先放进organizationMap中;
         * 如果organizationMap中不存在父节点,说明该父节点没有通过验证或者父节点不存在,那么该父节点下面所有
         * 的子节点都会添加失败,这样就避免了去递归找父节点下面所有的子 */
        for (int i = 0; i < sortList.size(); i++) {
            Organization o = sortList.get(i);
            Organization p = organizationMap.get(o.getParentCode());
            if (p != null && p.getPath() != null) {
                o.forInsert();
                o.setParentId(p.getId());
                o.setPath(p.getPath() + o.getId() + COMMA);
                o.setDepth(p.getDepth() + 1);
                o.setLevel(o.getLevel() == null ? Organization.LEVEL_DEPARTMENT : o.getLevel());
                o.setStatus(o.getStatus() == null ? Organization.STATUS_ENABLED : o.getStatus());
                o.setCompanyId(o.getLevel() < Organization.LEVEL_DEPARTMENT ? o.getId() : p.getCompanyId());
                o.setType(p.getType() == null ? Organization.TYPE_INNER : p.getType());

                int count = organizationDao.execute(d -> d.select(ORGANIZATION.ID.count()).from(ORGANIZATION)
                                                          .where(ORGANIZATION.PARENT_ID.eq(p.getId()))
                                                          .fetchOne(ORGANIZATION.ID.count()));
                o.setOrder(count + 1);
                organizationMap.put(o.getCode(), o);
                orglist.add(o);
            }
        }
        organizationDao.insert(orglist);
        cache.clear(CACHE_FAKE_ENABLED_ORGANIZATIONIDS);
        Map<String, List<Organization>> resultMap = Maps.newHashMap();
        organizations.removeAll(orglist);
        resultMap.put("success", orglist);
        resultMap.put("failed", organizations);
//        LOGGER.info("用户:{}批量插入用户成功,成功数量{},失败数据{}", memberId, orglist.size(), organizations.size());
        if (!orglist.isEmpty()) {
            messageSender.send(
                MessageTypeContent.SYSTEM_ORGANIZATION_BATCH_INSERT,
                MessageHeaderContent.IDS, Joiner.on(",").join(orglist.stream().map(Organization::getId).collect(Collectors.toList())));
        }
        return resultMap;
    }

    @Override
    public Organization update(String id, String name, Optional<String> shortName, String code, String parentId, Integer cmccLevel, Integer level, Optional<Integer> order, Optional<String> cmccAttribute, Optional<String> cmccCategory, Integer status, Optional<String> extentionParams, Optional<Long> vaildStartTime, Optional<Long> vaildEndTime,Optional<String> supervisorIds) {
        Organization organization = organizationDao.get(id);
        String oldParentId = organization.getParentId();
        String oldPath = organization.getPath();
        Integer oldStatus = organization.getStatus();
        Integer oldOrder = organization.getOrder() == null ? 1 : organization.getOrder();
        Integer originalLevel = organization.getLevel();
        organization.setName(name);
        organization.setShortName(shortName.orElse(null));
        organization.setCode(code);
        organization.setCmccLevel(cmccLevel);
        organization.setLevel(level);
        organization.setCmccAttribute(cmccAttribute.orElse(null));
        organization.setCmccCategory(cmccCategory.orElse(null));
        organization.setStatus(status);
        organization.setParentId(parentId);
        organization.setValidityStartTime(vaildStartTime.orElse(null));
        organization.setValidityEndTime(vaildEndTime.orElse(null));
        if (status.equals(Organization.STATUS_DISABLED) && !oldStatus.equals(Organization.STATUS_DISABLED)) {//禁用注意有效期内
            if (vaildEndTime.isPresent() && vaildEndTime.get().compareTo(System.currentTimeMillis()) > 0) {
                organization.setValidityStatus(Organization.STATUS_DISABLE);
            }
        }
        //启用注意时间
        if (status.equals(Organization.STATUS_ENABLED)) {//有效期外不允许启用
            if(vaildEndTime.isPresent()&&vaildEndTime.get().compareTo(System.currentTimeMillis())<=0) {
                throw new UnprocessableException(ErrorCode.OrganizationStausError);
            }else{
                organization.setValidityStatus(Organization.STATUS_ENBELE);
            }
        }
        organization.setModifyDate(null);
        if (parentId != null) {
            Organization parentOrg = organizationDao.get(parentId);
            organization.setPath(parentOrg.getPath() + id + COMMA);
            organization.setOrder(getOrgOrder(Organization.UPDATE, order, parentId));
            organization.setCompanyId(level < Organization.LEVEL_DEPARTMENT ? organization.getId() : parentOrg.getCompanyId());
            if (parentOrg.getDepth() != null && !parentOrg.getDepth().equals(1)) {
                organization.setDepth(parentOrg.getDepth() + 1);
                organization.setType(parentOrg.getType());
            }
            organizationDao.update(organization);
            updateReleatedOrgOrder(Organization.UPDATE, id, oldOrder, oldParentId);
            updateReleatedCompanyId(id, originalLevel, level, parentOrg.getCompanyId());
            // 更新子节点path和depth
            updateReleatedPathAndDepth(id, oldPath, organization.getPath());
        } else {
            organization.setLevel(Organization.LEVEL_HEAD);
            organizationDao.update(organization);
        }
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("before", oldParentId);
        diffMap.put("after", parentId);
        if (oldParentId != null) {
            List<OrganizationDetail> oldParents = organizationDetailService.find(organization.getId());
            List<String> parentIds = new ArrayList<>();
            if (!oldParents.isEmpty()) {
                oldParents.forEach(p -> {
                    parentIds.add(p.getRoot());
                });
            }
            diffMap.put("oldParentIds", parentIds);
        }
        // 清楚缓存数据
        cache.clear(CACHED_COUNT_PARENT + id);
        cache.clear(CACHE_FAKE_ENABLED_ORGANIZATIONIDS);
        // 如果父节点或状态变更，清除所有子节点缓存
        if (!org.apache.commons.lang.StringUtils.equals(oldParentId, organization.getParentId()) || !oldStatus.equals(organization.getStatus())) {
            clearAllSubCache(organization.getParentId());
        }
        handleSupervisor(supervisorIds, id);
        messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_UPDATE, diffMap, MessageHeaderContent.ID, organization.getId());
        return organization;
    }


    private void handleSupervisor(Optional<String> supervisorIds, String id) {
        organizationSupervisorCommonDao.delete(ORGANIZATION_SUPERVISOR.ORGANIZATION_ID.eq(id));
        supervisorIds.ifPresent(s -> {
            List<OrganizationSupervisor> list = new ArrayList<>();
            Arrays.asList(s.split(",")).stream().forEach(a -> {
                OrganizationSupervisor org = new  OrganizationSupervisor();
                org.forInsert();
                org.setMemberId(a);
                org.setOrganizationId(id);
                list.add(org);
            });
            organizationSupervisorCommonDao.insert(list);
        });
    }
    @Override
    public boolean checkGrants(int operator, String memberId, String organizationId) {
        final String opterStr = operator == Organization.UPDATE ? "2" : "3";
        // 先判断当前用户的grant_organization中有没有这个组织,且拥有修改或者删除权限
        int count = organizationDao.execute(d -> d.select(GRANT_ORGANIZATION.ORGANIZATION_ID.count()).from(GRANT)
                                                  .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                  .innerJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                  .where(GRANT.OPERATOR_TYPES.contains(opterStr), GRANT_ORGANIZATION.ORGANIZATION_ID.eq(organizationId),
                                                      GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                  .fetchOne(GRANT_ORGANIZATION.ORGANIZATION_ID.count()));
        // 如果授权组织中有这个组织,就直接返回
        if (count > 0) {
            return true;
        }
        // 再判断当前组织的父组织中,有没有一个在grant_organization中,且拥有修改或者删除权限
        String[] parentIds = organizationDao.getOptional(organizationId)
                                            .orElseThrow(() -> new UnprocessableException(ErrorCode.CanNotFindOrganization)).getPath().split(",");
        count = organizationDao.execute(d -> d.select(GRANT_ORGANIZATION.ORGANIZATION_ID.count()).from(GRANT)
                                              .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                              .innerJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
                                              .where(
                                                  GRANT.OPERATOR_TYPES.contains(opterStr),
                                                  GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES),
                                                  GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()),
                                                  GRANT_ORGANIZATION.ORGANIZATION_ID.in(parentIds))
                                              .fetchOne(GRANT_ORGANIZATION.ORGANIZATION_ID.count()));
        if (count == 0) {
            if (operator == Organization.UPDATE) {
                throw new UnprocessableException(ErrorCode.CanNotUpdateSinceBeyondGrant);
            } else {
                throw new UnprocessableException(ErrorCode.CanNotDeleteSinceBeyondGrant);
            }
        }
        return true;
    }

    @Override
    public int delete(String id) {
        Organization o = organizationDao.getOptional(id).orElseThrow(() -> new UnprocessableException(ErrorCode.CanNotFindOrganization));
        // 根组织不能删除
        if (!StringUtils.hasText(o.getParentId())) {
            throw new UnprocessableException(ErrorCode.CanNotDeleteSinceRootNode);
        }
        // 父组织不能删除
        int childCount = organizationDao.count(ORGANIZATION.PARENT_ID.eq(id));
        if (childCount > 0) {
            throw new UnprocessableException(ErrorCode.CanNotDeleteOrganization);
        }
        // 内外部组织不能删除
        if (o.getPath().split(",").length == 2) {
            throw new UnprocessableException(ErrorCode.InnerOuterOrganizationCantBeDelete);
        }
        // 判断组织关联
        judgeOrgReleated(id);
        // 删除组织
        int rtnCount = organizationDao.delete(id);
        deleteDataSystemCommonDao.insert(DeleteDataSystem
            .getDeleteData(DeleteDataSystem.ORGANIZATION, id, ""));
        updateReleatedOrgOrder(Organization.DELETE, id, o.getOrder(), o.getParentId());
        messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_DELETE, MessageHeaderContent.ID, id);
        return rtnCount;
    }

    @Override
    public Optional<Organization> getById(String id) {
        return organizationDao.getOptional(id).map(org -> {
            List<String> detailIds = organizationDetailDao.execute(dsl ->
                dsl.select(ORGANIZATION_DETAIL.ID)
                   .from(ORGANIZATION_DETAIL)
                   .where(ORGANIZATION_DETAIL.ROOT.eq(org.getId()))
                   // 判断是否父节点：因为其中一个是当前节点，所以查询出来2个
                   .limit(2)
                   .fetch(ORGANIZATION_DETAIL.ID)
            );
            org.setIsParent(!CollectionUtils.isEmpty(detailIds) && detailIds.size() == 2);
            return org;
        });
    }

    /**
     * 当前组织parentId改变时需要修改子组织的Path
     * @param
     */
    @Override
    public Optional<Organization> get(String id) {
        com.zxy.product.system.jooq.tables.Organization PARENT_ORGANIZATION = ORGANIZATION.as("parent_org");
        return organizationDao.execute(o -> o.select(Fields.start().add(ORGANIZATION)
                                                           .add(PARENT_ORGANIZATION.NAME.as("parent_name"))
                                                           .add(PARENT_ORGANIZATION.LEVEL.as("parent_level")).end())
                                             .from(ORGANIZATION)
                                             .leftJoin(PARENT_ORGANIZATION).on(PARENT_ORGANIZATION.ID.eq(ORGANIZATION.PARENT_ID))
                                             .where(ORGANIZATION.ID.eq(id))
                                             .fetchOptional(r -> {
                                                 Organization organization = r.into(ORGANIZATION).into(Organization.class);
                                                 organization.setParentName(r.getValue("parent_name", String.class));
                                                 organization.setParentLevel(r.getValue("parent_level", Integer.class));
                                                 List<OrganizationSupervisor> orgSupList = organizationSupervisorCommonDao.execute(a -> a.select(MEMBER.ID, MEMBER.FULL_NAME)
                                                         .from(ORGANIZATION_SUPERVISOR).innerJoin(MEMBER).on(MEMBER.ID.eq(ORGANIZATION_SUPERVISOR.MEMBER_ID))
                                                         .where(ORGANIZATION_SUPERVISOR.ORGANIZATION_ID.eq(id))).fetch(b -> {
                                                     OrganizationSupervisor orgSup = new OrganizationSupervisor();
                                                     orgSup.setMemberId(b.getValue(MEMBER.ID));
                                                     orgSup.setMemberName(b.getValue(MEMBER.FULL_NAME));
                                                     return orgSup;
                                                 });
                                                 organization.setOrganizationSupervisors(orgSupList);
                                                 return organization;
                                             }));
    }

    @Override
    public List<Organization> findOrgList(String memberId,
                                           String uri,
                                           Optional<Boolean> supportMore,
                                           Optional<Integer> level,
                                           Optional<Boolean> asParent,
                                           Optional<String> name,
                                           Optional<String> code,
                                           Optional<Integer> type,
                                           Optional<String> excludeOrgId,
                                           Optional<String> organizationId,
                                           Optional<String> operatorType,
                                           Optional<Boolean> allStatus) {
        return organizationDao.execute(d -> {
            LOGGER.info("【findOrgList开始】");
            Condition listCondition = GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                    .or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO).and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID)));

            // 构建查询条件
            List<Condition> conditions = new ArrayList<>();
            level.ifPresent(l -> asParent.ifPresent(p ->
                    conditions.add(p ? ORGANIZATION.LEVEL.le(l) : (l.equals(Organization.LEVEL_DEPARTMENT) ? ORGANIZATION.LEVEL.eq(l) : ORGANIZATION.LEVEL.le(l)))
            ));
            name.ifPresent(n -> conditions.add(ORGANIZATION.NAME.contains(n)));
            code.ifPresent(c -> conditions.add(ORGANIZATION.CODE.contains(c)));
            type.ifPresent(t -> conditions.add(t.equals(Organization.TYPE_OUTER) ? ORGANIZATION.TYPE.eq(t) : ORGANIZATION.TYPE.le(t)));
            excludeOrgId.ifPresent(eId -> conditions.add(ORGANIZATION.PATH.notLike("%" + eId + "%")));
            organizationId.ifPresent(id -> conditions.add(ORGANIZATION.PATH.contains(id + ",")));
            operatorType.ifPresent(oType -> conditions.add(GRANT.OPERATOR_TYPES.contains(oType)));
            conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));

            // 如果有 URI，则添加条件
            Optional.ofNullable(uri).ifPresent(ur -> conditions.add(MENU.URI.eq(ur).or(MENU.ID.eq(ur))));

            // 查询授权组织深度
            List<Integer> depths = d.selectDistinct(ORGANIZATION.DEPTH)
                    .from(GRANT_MEMBER)
                    .innerJoin(GRANT).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
                    .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                    .where(GRANT_MEMBER.MEMBER_ID.eq(memberId)
                            .and(ORGANIZATION.DEPTH.isNotNull())
                            .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())))
                    .orderBy(ORGANIZATION.DEPTH)
                    .fetch(ORGANIZATION.DEPTH);

            if (depths.isEmpty()) {
                depths.add(0);
            }
            LOGGER.info("【查询授权组织深度结束】");

            // 构建查询步骤
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> step = a -> a.from(GRANT)
                    .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                    .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                    .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                    .where(conditions);

            // 如果是精准查询,则判断查询的数据是否大于1000,此时没有层级限制;如果是非精准查询,就查5层以上的.
            if (supportMore.isPresent() && supportMore.get() && (name.isPresent() || code.isPresent())) {
                int count = step.apply(d.select(Fields.start().add(ORGANIZATION.ID.countDistinct()).end()))
                        .and(listCondition).fetchOne().getValue(0, Integer.class);
                if (count > MAX_SEARCH_COUNT) {
                    throw new UnprocessableException(ErrorCode.QueryHasTooManyRecords);
                }
            } else { // 非精准查询, 层级限制在5层以内
                conditions.add(ORGANIZATION.DEPTH.le(depths.get(0) + 3));
            }

            // 查询组织数据
            List<Organization> orgs = step.apply(d.selectDistinct(ORGANIZATION.fields()))
                    .and(listCondition).fetchInto(Organization.class);
            LOGGER.info("【查询组织数据结束】");

            // 设置是否可以展开
            if (supportMore.isPresent() && supportMore.get() && !name.isPresent() && !code.isPresent()) {
                // 找出所有5层的已经作为父节点的组织id,用于判断具体的某一个节点是否可以展开
                List<String> parentIds = d.selectDistinct(ORGANIZATION.PARENT_ID)
                        .from(ORGANIZATION)
                        .where(ORGANIZATION.DEPTH.eq(depths.get(0) + 4))
                        .fetch(ORGANIZATION.PARENT_ID);

                // 找出当前用户拥有的第五层的,包含子节点的组织id,用于判断第五层的节点,是否可以展开(对于第五层的节点,第一要满足是父节点才可以展开,并且第五层的节点,在权限上必须要包含子节点才可以展开)
                List<String> childFindYesIds = step.apply(d.selectDistinct(Fields.start().add(ORGANIZATION.ID).end()))
                        .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES))
                        .fetch(ORGANIZATION.ID);
                parentIds.retainAll(childFindYesIds);

                orgs.forEach(o -> o.setIsParent(parentIds.contains(o.getId())));
            }
            LOGGER.info("【处理展开标识结束】");

            // 排序
            orgs.sort(OrganizationUtil.getOrgComparator());
            LOGGER.info("【组织排序结束】");

            // 去除禁用
            List<Organization> filterList = allStatus.map(s -> filterOrg(orgs, s))
                    .orElseGet(() -> filterOrg(orgs, Boolean.FALSE));
            LOGGER.info("【禁用组织过滤结束】");

            return filterList;
        });
    }

    @Override
    public List<Organization> findGrantedOrganization(String memberId, String uri, Optional<String> name, Optional<String> code, Optional<Integer> type, Optional<String> operatorType, Optional<Boolean> supportMore) {
        return findOrgList(memberId,
            uri,
            supportMore,
            Optional.empty(),
            Optional.empty(),
            name,
            code,
            type,
            Optional.empty(),
            Optional.empty(),
            operatorType,
            Optional.of(false));
    }

    @Override
    public List<Organization> findMemberGrantedOrganization(String memberId, String uri, boolean nameComposite, Optional<Integer> depth, Optional<Integer> type) {
        return organizationDao.execute(d -> {
            // 所有授权组织,包括包含子结点和不包含子节点
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                          .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                          .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                          .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            includeOrganizationIds.addAll(excludeOrganizationIds);
            // 将包含子节点的所有子组织查询出来
            //Condition depthCondition = depth.map(dep -> ORGANIZATION.DEPTH.le(dep)).orElse(ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT));
            Condition typeCondition = type.map(t -> t == 1 ? ORGANIZATION.TYPE.le(t) : ORGANIZATION.TYPE.eq(t)).orElse(DSL.trueCondition());
            List<Organization> grantedOrganization = d.selectDistinct(
                                                          ORGANIZATION.ID,
                                                          ORGANIZATION.PARENT_ID,
                                                          ORGANIZATION.LEVEL,
                                                          ORGANIZATION.NAME,
                                                          ORGANIZATION.CODE,
                                                          ORGANIZATION.DEPTH,
                                                          ORGANIZATION.ORDER,
                                                          ORGANIZATION.STATUS,
                                                          ORGANIZATION.COMPANY_ID,
                                                          ORGANIZATION.CREATE_TIME
                                                      ).from(ORGANIZATION_DETAIL)
                                                      .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                                      .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds), typeCondition)
                                                      .fetch(r -> {
                                                          Organization o = new Organization();
                                                          o.setId(r.getValue(ORGANIZATION.ID));
                                                          o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
                                                          o.setLevel(r.getValue(ORGANIZATION.LEVEL));
                                                          o.setName(r.getValue(ORGANIZATION.NAME));
                                                          o.setCode(r.getValue(ORGANIZATION.CODE));
                                                          o.setDepth(r.getValue(ORGANIZATION.DEPTH));
                                                          o.setStatus(r.getValue(ORGANIZATION.STATUS));
                                                          o.setCompanyId(r.getValue(ORGANIZATION.COMPANY_ID));
                                                          o.setOrder(r.getValue(ORGANIZATION.ORDER));
                                                          o.setCreateTime(r.getValue(ORGANIZATION.CREATE_TIME));
                                                          return o;
                                                      });
            // 去除禁用的组织
            List<String> replaseOrganizationPIds = grantedOrganization.stream().filter(or -> or.getStatus() != 1).map(Organization::getId).collect(Collectors.toList());
//            List<Organization> replaseOrganizationList = d.selectDistinct(ORGANIZATION.ID).from(ORGANIZATION).where(ORGANIZATION.PARENT_ID.in(replaseOrganizationPIds)).fetchInto(Organization.class);
            List<String> replaseOrganizationList = d.selectDistinct(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL).where(ORGANIZATION_DETAIL.ROOT.in(replaseOrganizationPIds)).fetchInto(String.class);
            List<Organization> filterOrganizationList = grantedOrganization.stream().filter(or -> or.getStatus() == 1).filter(or -> !replaseOrganizationList.contains(or.getId())).collect(Collectors.toList());
            // 排序
            filterOrganizationList.sort(OrganizationUtil.getOrgComparator());
            List<Organization> orgList = OrganizationUtil.treeOrganizations(filterOrganizationList, nameComposite);
            cache.set(CACHE_GRANTED_ORGANIZATION + memberId + "#" + uri, orgList, 60 * 60);
            // 组装树
            return orgList;
        });
    }

    @Override
    public List<Organization> findMemberGrantedOrganization(String memberId, String uri, boolean nameComposite, Optional<Integer> depth) {
        return findMemberGrantedOrganization(memberId, uri, nameComposite, depth, Optional.empty());
    }

    @Override
    public List<Organization> findMemberGrantedOrganization(String memberId, String uri, boolean nameComposite) {
        return findMemberGrantedOrganization(memberId, uri, nameComposite, Optional.empty(), Optional.empty());
    }

    @Override
    public List<Organization> findMemberGrantedOrganization(String memberId, String uri, Optional<String> orgCode) {
        return organizationDao.execute(d -> {
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                          .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                          .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                          .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            includeOrganizationIds.addAll(excludeOrganizationIds);
            // 将包含子节点的所有子组织查询出来
            Condition codeCondition = orgCode.map(ORGANIZATION.CODE::eq).orElse(DSL.trueCondition());
            List<Organization> grantedOrganization = d.selectDistinct(
                                                          ORGANIZATION.ID,
                                                          ORGANIZATION.NAME,
                                                          ORGANIZATION.CODE
                                                      ).from(ORGANIZATION_DETAIL)
                                                      .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                                      .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds), codeCondition, ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))
                                                      .fetch(r -> {
                                                          Organization o = new Organization();
                                                          o.setId(r.getValue(ORGANIZATION.ID));
                                                          o.setName(r.getValue(ORGANIZATION.NAME));
                                                          o.setCode(r.getValue(ORGANIZATION.CODE));
                                                          return o;
                                                      });
            grantedOrganization.sort(OrganizationUtil.getOrgComparator());
            cache.set(CACHE_GRANTED_ORGANIZATION + memberId + "#" + uri, grantedOrganization, 60 * 60);
            return grantedOrganization;
        });
    }

    @Override
    public List<Organization> findMemberGrantedOrganizationWithCache(String memberId, String uri, Optional<Integer> type) {
        return organizationDao.execute(d -> {
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                          .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                          .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                          .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            includeOrganizationIds.addAll(excludeOrganizationIds);
            // 将包含子节点的所有子组织查询出来
            Condition typeCondition = type.map(ORGANIZATION.TYPE::eq).orElse(DSL.trueCondition());
            List<Organization> grantedOrganization = d.selectDistinct(
                                                          ORGANIZATION.ID,
                                                          ORGANIZATION.NAME,
                                                          ORGANIZATION.CODE
                                                      ).from(ORGANIZATION_DETAIL)
                                                      .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                                      .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds), typeCondition, ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))
                                                      .fetch(r -> {
                                                          Organization o = new Organization();
                                                          o.setId(r.getValue(ORGANIZATION.ID));
                                                          o.setName(r.getValue(ORGANIZATION.NAME));
                                                          o.setCode(r.getValue(ORGANIZATION.CODE));
                                                          return o;
                                                      });
            grantedOrganization.sort(OrganizationUtil.getOrgComparator());
            cache.set(CACHE_GRANTED_ORGANIZATION + memberId + "#" + uri, grantedOrganization, 60 * 60);
            return grantedOrganization;
        });
    }

    @Override
    public void findMemberGrantedOrganizationWithCacheNoReturn(String memberId, String uri, Optional<Integer> type) {
        organizationDao.execute(d -> {
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                          .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                          .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                          .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            includeOrganizationIds.addAll(excludeOrganizationIds);
            // 将包含子节点的所有子组织查询出来
            Condition typeCondition = type.map(ORGANIZATION.TYPE::eq).orElse(DSL.trueCondition());
            List<Organization> grantedOrganization = d.selectDistinct(
                                                          ORGANIZATION.ID,
                                                          ORGANIZATION.NAME,
                                                          ORGANIZATION.CODE
                                                      ).from(ORGANIZATION_DETAIL)
                                                      .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                                      .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds), typeCondition, ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))
                                                      .fetch(r -> {
                                                          Organization o = new Organization();
                                                          o.setId(r.getValue(ORGANIZATION.ID));
                                                          o.setName(r.getValue(ORGANIZATION.NAME));
                                                          o.setCode(r.getValue(ORGANIZATION.CODE));
                                                          return o;
                                                      });
            grantedOrganization.sort(OrganizationUtil.getOrgComparator());
            cache.set(CACHE_GRANTED_ORGANIZATION + memberId + "#" + uri, grantedOrganization, 60 * 60);
            return grantedOrganization;
        });
    }

    @Override
    public List<Organization> findMemberGrantedOrganizationWithCache(String memberId, String uri, String organizationId) {
        return organizationDao.execute(d -> {
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                          .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                          .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            includeOrganizationIds.addAll(excludeOrganizationIds);
            // 将包含子节点的所有子组织查询出来
            Condition typeCondition = ORGANIZATION.PATH.contains(organizationId + ",");
            List<Organization> grantedOrganization = d.selectDistinct(
                                                          ORGANIZATION.ID,
                                                          ORGANIZATION.NAME,
                                                          ORGANIZATION.CODE
                                                      ).from(ORGANIZATION_DETAIL)
                                                      .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                                      .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds), typeCondition, ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))
                                                      .fetch(r -> {
                                                          Organization o = new Organization();
                                                          o.setId(r.getValue(ORGANIZATION.ID));
                                                          o.setName(r.getValue(ORGANIZATION.NAME));
                                                          o.setCode(r.getValue(ORGANIZATION.CODE));
                                                          return o;
                                                      });
            grantedOrganization.sort(OrganizationUtil.getOrgComparator());
            cache.set(CACHE_GRANTED_ORGANIZATION + memberId + "#" + uri + "#" + organizationId, grantedOrganization, 60 * 60);
            return grantedOrganization;
        });
    }

    @Override
    public void findMemberGrantedOrganizationWithCacheNoReturn(String memberId, String uri, String organizationId) {
        organizationDao.execute(d -> {
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                          .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                          .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                          .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            includeOrganizationIds.addAll(excludeOrganizationIds);
            // 将包含子节点的所有子组织查询出来
            Condition typeCondition = ORGANIZATION.PATH.contains(organizationId + ",");
            List<Organization> grantedOrganization = d.selectDistinct(
                                                          ORGANIZATION.ID,
                                                          ORGANIZATION.NAME,
                                                          ORGANIZATION.CODE
                                                      ).from(ORGANIZATION_DETAIL)
                                                      .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                                      .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds), typeCondition, ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))
                                                      .fetch(r -> {
                                                          Organization o = new Organization();
                                                          o.setId(r.getValue(ORGANIZATION.ID));
                                                          o.setName(r.getValue(ORGANIZATION.NAME));
                                                          o.setCode(r.getValue(ORGANIZATION.CODE));
                                                          return o;
                                                      });
            grantedOrganization.sort(OrganizationUtil.getOrgComparator());
            cache.set(CACHE_GRANTED_ORGANIZATION + memberId + "#" + uri + "#" + organizationId, grantedOrganization, 60 * 60);
            return grantedOrganization;
        });
    }

    @Override
    public List<Organization> findOrgsWithNoGrant(String rootId, Optional<Integer> depthLimit, Optional<String> organizationId, Optional<String> name,
                                                  Optional<String> code, Optional<Boolean> isCompany, Optional<Boolean> isAll, Boolean supportMore, Optional<Integer> type) {
        return organizationDao.execute(d -> {
            String orgId = organizationId.orElse(rootId);
//            Integer limit = depthLimit.orElse(3);
            Integer limit = d.select(ORGANIZATION.DEPTH).from(ORGANIZATION)
                             .where(ORGANIZATION.ID.eq(orgId)).fetchOne(ORGANIZATION.DEPTH, Integer.class) + 1;
            // 根据条件找出需要限制的最大层级
            if (name.isPresent() || code.isPresent()) {
                limit = d.select(ORGANIZATION.DEPTH.max()).from(ORGANIZATION_DETAIL)
                         .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                         .where(ORGANIZATION_DETAIL.ROOT.eq(orgId)
                                                        .and(name.map(ORGANIZATION.NAME::contains).orElse(DSL.trueCondition()))
                                                        .and(code.map(ORGANIZATION.CODE::contains).orElse(DSL.trueCondition())))
                         .fetchOne(ORGANIZATION.DEPTH.max());
            }
            // 组装where条件
            List<Condition> conditions = Stream.of(
                                                   Optional.of(ORGANIZATION_DETAIL.ROOT.eq(orgId)),
                                                   name.map(ORGANIZATION.NAME::contains),
                                                   code.map(ORGANIZATION.CODE::contains),
                                                   Optional.of(ORGANIZATION.DEPTH.le(limit)),
                                                   type.map(ORGANIZATION.TYPE::eq),
                                                   isCompany.map(c -> c ? ORGANIZATION.LEVEL.le(Organization.LEVEL_BRANCH) : null))
                                               .filter(Optional::isPresent).map(Optional::get)
                                               .collect(Collectors.toList());
            // 找出父节点下面的所有子节点,如果有层级限制,就找出限制层级以内的组织
            List<Organization> organizations = d.selectDistinct(ORGANIZATION.fields())
                                                .from(ORGANIZATION_DETAIL)
                                                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                                .where(conditions).fetchInto(Organization.class);
            // 找出所有4层以上的已经作为父节点的组织id,用于判断具体的某一个节点是否可以展开
            if (supportMore) {
                Set<String> parentIdSet = d.select(ORGANIZATION.PARENT_ID).from(ORGANIZATION)
                                           .where(ORGANIZATION.PARENT_ID.isNotNull(), ORGANIZATION.DEPTH.le(limit + 1)).fetchSet(ORGANIZATION.PARENT_ID);
                organizations.forEach(o -> o.setIsParent(parentIdSet.contains(o.getId())));
            }
            return filterOrg(organizations, isAll.orElse(false));
        });
    }

    // 嵌套循环,内外层组织都比较多时性能差,复杂度n^2
    // 没有层级限制时,所有组织时最多大概6w*12w=72亿,600s左右
    // 有层级限制时,中国移动3000*1000, 内部组织35406*18543=6.5亿约54s, 广东公司22923*12497=2.9亿约25s
    @Deprecated
    private List<Organization> wrapperOrg(List<Organization> orgs, boolean isAll) {
        // 去除禁用
        if (!isAll) {
            List<String> disabledPath = orgs.stream()
                                            .filter(g -> Organization.STATUS_DISABLED.equals(g.getStatus()))
                                            .map(Organization::getPath)
                                            .collect(Collectors.toList());
            for (String p : disabledPath) {
                orgs = orgs.stream().filter(g -> !g.getPath().startsWith(p)).collect(Collectors.toList());
            }
        }
        return orgs;
    }

    // 筛选出部门和父部门都可用的
    private List<Organization> filterOrg(List<Organization> orgs, boolean isAll) {
        // 去除禁用
        if (!isAll) {
            Set<String> fakeEnabledOrgIds = getFakeEnabledOrgIds();
            return orgs = orgs.stream().filter(o -> Organization.STATUS_ENABLED.equals(o.getStatus()) && !fakeEnabledOrgIds.contains(o.getId()))
                              .collect(Collectors.toList());
        }
        return orgs;
    }

    // 获取部门可用,但父部门禁用的组织ids
    private Set<String> getFakeEnabledOrgIds() {
        return cache.get(CACHE_FAKE_ENABLED_ORGANIZATIONIDS, () -> {
            return organizationDao.execute(e -> e.select(ORGANIZATION.as("subOrg").ID).from(ORGANIZATION)
                                                 .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.ROOT))
                                                 .leftJoin(ORGANIZATION.as("subOrg")).on(ORGANIZATION.as("subOrg").ID.eq(ORGANIZATION_DETAIL.SUB))
                                                 .where(ORGANIZATION.STATUS.eq(Organization.STATUS_DISABLED),
                                                     ORGANIZATION.as("subOrg").STATUS.eq(Organization.STATUS_ENABLED))
                                                 .fetchSet(ORGANIZATION.as("subOrg").ID));
        }, 60 * 60);
    }

    @Override
    public Optional<Organization> getBasic(String id) {
        return organizationDao.getOptional(id);
    }

    @Override
    public Organization getCompanyOrganization(String organizationId, Integer... level) {
        return organizationDao.execute(d -> {
            Map<String, Organization> map = new HashMap<String, Organization>();
            List<Organization> result = d.select(ORGANIZATION.fields())
                                         .from(ORGANIZATION).leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.ROOT))
                                         .where(ORGANIZATION_DETAIL.SUB.eq(organizationId))
                                         .and(Stream.of(level).map(ORGANIZATION.LEVEL::eq).reduce((acc, item) -> acc.or(item)).orElse(DSL.trueCondition()))
                                         .fetchInto(Organization.class);
            result.stream().forEach(r -> map.put(r.getId(), r));
            Set<String> idSet = result.stream().map(Organization::getId).collect(Collectors.toSet());
            Set<String> parentIdSet = result.stream().map(Organization::getParentId).collect(Collectors.toSet());
            idSet.removeAll(parentIdSet);
            return idSet.size() > 0 ? map.get(idSet.toArray(new String[1])[0]) : null;
        });
    }

    @Override
    public int count(String organizationId, Optional<String> name, Optional<String> code, Optional<String> id) {
        return organizationDetailDao.execute(a -> {
            Stream<Condition> condition = Stream.of(name.map(ORGANIZATION.NAME::eq), code.map(ORGANIZATION.CODE::eq)).filter(Optional::isPresent).map(Optional::get);
            return a.selectCount()
                    .from(ORGANIZATION)
                    .leftJoin(ORGANIZATION_DETAIL)
                    .on(ORGANIZATION_DETAIL.SUB.eq(ORGANIZATION.ID))
                    .where(ORGANIZATION_DETAIL.ROOT.eq(organizationId))
                    .and(condition.reduce((acc, item) -> acc.and(item)).orElse(DSL.falseCondition()))
                    .and(id.map(ORGANIZATION.ID::ne).orElse(DSL.trueCondition()))
                    .fetchOne(0, Integer.class);
        });
    }

    @Override
    public Optional<Organization> getByDomain(String domain) {
        return organizationDao.execute(d -> {
            return d.select(Fields.start().add(ORGANIZATION.fields()).end())
                    .from(ORGANIZATION)
                    .leftJoin(SITE).on(SITE.ID.eq(ORGANIZATION.SITE_ID))
                    .where(SITE.DOMAIN.eq(domain))
                    .fetchOptionalInto(Organization.class);
        });
    }

    @Override
    public Optional<Organization> getByCode(String code) {
        return organizationDao.fetchOne(ORGANIZATION.CODE.eq(code));
    }

    @Override
    public Optional<Organization> getByCode(String memberId, String code) {
        return organizationDao.execute(e -> {
//            return e.selectDistinct(ORGANIZATION.fields()).from(ORGANIZATION)
//                    .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//                    .where(GRANT_DETAIL.MEMBER_ID.eq(memberId)).and(ORGANIZATION.CODE.eq(code))
//                    .fetchOptionalInto(Organization.class);
            return e.selectDistinct(ORGANIZATION.fields())
                    .from(GRANT)
                    .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                    .innerJoin(ORGANIZATION).on(
                    DSL.trueCondition()
                       .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                                                         .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
                       .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
                                                        .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
                    .where(ORGANIZATION.CODE.eq(code).and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())))
                    .fetchOptionalInto(Organization.class);
        });
    }

    @Override
    public List<Organization> getByMember(String memberId) {
        return organizationDao.execute(e -> {
//            return e.selectDistinct(ORGANIZATION.fields()).from(ORGANIZATION)
//                    .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//                    .where(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                    .fetch().into(Organization.class);
            return e.selectDistinct(ORGANIZATION.fields())
                    .from(GRANT)
                    .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                    .innerJoin(ORGANIZATION).on(
                    DSL.trueCondition()
                       .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                                                         .and(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH)))
                       .or(GRANT_ORGANIZATION.CHILD_FIND.ne(GrantOrganization.CHILD_FIND_YES)
                                                        .and(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))))
                    .where(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED).and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())))
                    .fetch().into(Organization.class);
        });
    }

    @Override
    public Optional<Integer> getMaxLevelOrganizationById(String id) {
        return organizationDao.execute(d -> {
            return d.select(DSL.min(ORGANIZATION.LEVEL))
                    .from(ORGANIZATION)
                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(ORGANIZATION.ID))
                    .where(ORGANIZATION_DETAIL.ROOT.eq(id))
                    .and(ORGANIZATION_DETAIL.SUB.ne(id))
                    .fetchOptional(DSL.min(ORGANIZATION.LEVEL));
        });
    }

    /**
     * 得到当前节点的所有父id(含自己)组成的path
     */
    private String getPath(Organization org) {
        StringBuilder sb = new StringBuilder();
        return Optional.ofNullable(org.getParentId()).map(t -> {
            return sb.append(organizationDao.get(org.getParentId()).getPath()).append(org.getId()).append(",").toString();
        }).orElse(sb.append(org.getId()).append(",").toString());

    }

    @Override
    public boolean judgeOrgReleated(String id) {
        LOGGER.info("org-delete-tables:{}", env.getProperty("org.releated.tables", ""));
        String[] tables = env.getProperty("org.releated.tables", "").split("\\|");
        for (String table : tables) {
            String[] fields = table.trim().split(",");
            String deleteFlag = Boolean.valueOf(fields[2]) ? "and f_delete_flag<>1" : "";
            organizationDao.execute(e -> {
                int c = e.select(DSL.count()).from(fields[0]).where(fields[1] + "=?" + deleteFlag, id).fetchOne(DSL.count());
                if (c > 0) {
                    throw new UnprocessableException(ErrorCode.getByCode(Integer.valueOf(fields[3])));
                }
                return 0;
            });
        }
        return true;
    }

    @Override
    public List<Organization> findOrgsByMemberIdAndRoleId(String memberId, String roleId) {
        return organizationDao.execute(d -> {
            long currentTime = System.currentTimeMillis();

            List<String> menuList = d.select(MENU.ID).from(MENU)
                                     .leftJoin(ROLE_MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
                                     .where(ROLE_MENU.ROLE_ID.eq(roleId), MENU.URI.isNotNull()).fetch(MENU.ID);
//            List<Organization> orgList = d.select(ORGANIZATION.fields()).from(ROLE_MENU)
//                    .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID), ROLE_MENU.ROLE_ID.eq(roleId), MENU.URI.isNotNull())
//                    .innerJoin(GRANT_DETAIL).on(GRANT_DETAIL.URI.eq(MENU.URI), GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                    .innerJoin(ORGANIZATION).on(GRANT_DETAIL.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//                    .groupBy(ORGANIZATION.ID).having(GRANT_DETAIL.URI.countDistinct().eq(count)).orderBy(ORGANIZATION.ORDER.asc().nullsLast(), ORGANIZATION.CREATE_TIME.desc())
//                    .fetchInto(Organization.class);
            // 查询每个菜单公有的组织
            List<Organization> orgList = d.select(ORGANIZATION.fields())
                                          .from(GRANT)
                                          .leftJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                          .leftJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                                          .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.ROOT.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                          .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                          .where(
                                              GRANT_MEMBER.MEMBER_ID.eq(memberId),
                                              MENU.ID.in(menuList),
                                              GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()),
                                              GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                                                                           .or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO)
                                                                                                            .and(ORGANIZATION_DETAIL.ROOT.eq(ORGANIZATION_DETAIL.SUB)))

                                          )
                                          .groupBy(ORGANIZATION.ID).having(MENU.ID.countDistinct().eq(menuList.size()))
                                          .fetchInto(Organization.class);

            // 去除禁用的组织
            List<String> disableParentIds = orgList.stream()
                                                   .filter(o -> Organization.STATUS_DISABLED.equals(o.getStatus()))
                                                   .map(Organization::getId)
                                                   .collect(Collectors.toList());
            List<String> allDisalbeIds = d.select(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL).where(ORGANIZATION_DETAIL.ROOT.in(disableParentIds)).fetch(ORGANIZATION_DETAIL.SUB);
            orgList = orgList.stream().filter(o -> !allDisalbeIds.contains(o.getId())).collect(Collectors.toList());
            // 计算每个菜单公有的包含子节点的组织
            Map<String, List<String>> menu2GrantIds = Maps.newHashMap();
            Map<String, Set<String>> menu2OrgIds = Maps.newHashMap();
            Map<String, Set<String>> grant2OrgIds = Maps.newHashMap();
            // 查询菜单对应的授权
            com.zxy.product.system.jooq.tables.RoleMenu rm2 = ROLE_MENU.as("rm2");
            List<GrantDetail> gds = d.select(rm2.MENU_ID, GRANT.ID).from(GRANT_MEMBER)
                                     .innerJoin(GRANT).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID), GRANT_MEMBER.MEMBER_ID.eq(memberId))
                                     .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                     .leftJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID), MENU.URI.isNotNull())
                                     .innerJoin(rm2).on(rm2.MENU_ID.eq(ROLE_MENU.MENU_ID), rm2.ROLE_ID.eq(roleId))
                                     .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                     .groupBy(rm2.MENU_ID, GRANT.ID)
                                     .fetch().map(r -> {
                    GrantDetail g = new GrantDetail();
                    g.setMenuId(r.getValue(rm2.MENU_ID));
                    g.setGrantId(r.getValue(GRANT.ID));
                    return g;
                }).stream().collect(Collectors.toList());

            // 菜单授权对应关系
            gds.forEach(g -> {
                String k = g.getMenuId();
                String v = g.getGrantId();
                if (menu2GrantIds.containsKey(k)) {
                    menu2GrantIds.get(k).add(v);
                } else {
                    List<String> s = Lists.newArrayList();
                    s.add(v);
                    menu2GrantIds.put(k, s);
                }
            });

            // 授权和包含子节点组织对应关系
            d.select(GRANT_ORGANIZATION.GRANT_ID, ORGANIZATION_DETAIL.SUB)
             .from(GRANT_ORGANIZATION)
             .innerJoin(ORGANIZATION_DETAIL)
             .on(ORGANIZATION_DETAIL.ROOT.eq(GRANT_ORGANIZATION.ORGANIZATION_ID),
                 GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES))
             .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
             .where(GRANT_ORGANIZATION.GRANT_ID.in(gds.stream().map(GrantDetail::getGrantId).collect(Collectors.toList()))
             )
             .fetch().map(r -> {
                 GrantOrganization go = new GrantOrganization();
                 go.setGrantId(r.getValue(GRANT_ORGANIZATION.GRANT_ID));
                 go.setOrganizationId(r.getValue(ORGANIZATION_DETAIL.SUB));
                 return go;
             }).forEach(g -> {
                 String k = g.getGrantId();
                 String v = g.getOrganizationId();
                 if (grant2OrgIds.containsKey(k)) {
                     grant2OrgIds.get(k).add(v);
                 } else {
                     Set<String> s = Sets.newHashSet();
                     s.add(v);
                     grant2OrgIds.put(k, s);
                 }
             });

            // 菜单和组织对应关系
            menu2GrantIds.forEach((k, v) -> v.forEach(t -> {
                if (grant2OrgIds.get(t) != null && !grant2OrgIds.get(t).isEmpty()) {
                    if (menu2OrgIds.containsKey(k)) {
                        menu2OrgIds.get(k).addAll(grant2OrgIds.get(t));
                    } else {
                        Set<String> ss = Sets.newHashSet();
                        ss.addAll(grant2OrgIds.get(t));
                        menu2OrgIds.put(k, ss);
                    }
                }
            }));

            // 找到公有的包含子节点的组织
            List<Set<String>> menu2OrgList = Lists.newArrayList(menu2OrgIds.values());
            if (menu2OrgList.size() > 1) {
                IntStream.range(1, menu2OrgList.size()).forEach(i -> menu2OrgList.get(0).retainAll(menu2OrgList.get(i)));
            }
            if (!menu2OrgList.isEmpty() && !menu2OrgList.get(0).isEmpty()) {
                Set<String> commonOrgIds = menu2OrgList.get(0);
                for (Organization o : orgList) {
                    if (commonOrgIds.contains(o.getId())) {
                        o.setChildFind(Organization.CHILD_FIND_YES.toString());
                    }
                }
            }
            orgList.sort(OrganizationUtil.getOrgComparator());
            LOGGER.info("findOrgsByMemberIdAndRoleId method runned total times: " + (System.currentTimeMillis() - currentTime));
            return orgList;
        });
    }

    @Override
    public List<Organization> findParent(String id, Integer level) {
        return organizationDao.execute(d -> d.select(ORGANIZATION.fields()).from(ORGANIZATION_DETAIL)
                                             .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.ROOT), ORGANIZATION_DETAIL.SUB.eq(id))
                                             .where(ORGANIZATION.LEVEL.le(level)).fetchInto(Organization.class));
    }

    @Override
    public Organization getCompanyOrganizationWithLevel2ByMemberId(String memberId) {
        return cache.get(CACHE_ROOT_ORGANIZATION, () -> {
            return getBasic(ROOT_ORGANIZATION_ID).get();
        }, -1);
    }

    private String getOrgCompany(String id) {
        return findParent(id, Organization.LEVEL_BRANCH).stream().sorted((o1, o2) -> {
            return o2.getPath().length() - o1.getPath().length();
        }).collect(Collectors.toList()).get(0).getId();
    }

    /**
     * 更新组织的order
     */
    private void updateReleatedOrgOrder(int operType, String orgId, Integer order, String parentId) {
        if (operType == Menu.ADD || operType == Menu.DELETE) {
            // 找到当前组织同级的,顺序比它大的组织
            List<String> orgIds = getOrgIds(parentId, order, Optional.empty());
            orgIds.remove(orgId);
            // 更新被影响的组织顺序
            updateOrgOrder(orgIds, operType);
        } else if (operType == Menu.UPDATE) {
            Organization o = organizationDao.get(orgId);
            int addBase = Integer.compare(order, o.getOrder());
            String oldPid = parentId;
            String newPid = o.getParentId();
            // 当父组织没变化时
            if (ObjectUtils.nullSafeEquals(newPid, oldPid)) {
                if (addBase != 0) {
                    int max = order - o.getOrder() > 0 ? order : o.getOrder();
                    int min = order - o.getOrder() < 0 ? order : o.getOrder();
                    List<String> orgIds = getOrgIds(newPid, min, Optional.of(max));
                    orgIds.remove(orgId);
                    // 更新被影响的组织顺序
                    updateOrgOrder(orgIds, addBase);
                }
                // 当父组织变化时
            } else {
                // 处理旧的父组织那一级的order(比当前组织大的顺序减1)
                List<String> oldOrgIds = getOrgIds(oldPid, order, Optional.empty());
                updateOrgOrder(oldOrgIds, -1);
                // 处理新的父组织那一级的order(比当前组织大的顺序加1)
                List<String> newMenuIds = getOrgIds(newPid, o.getOrder(), Optional.empty());
                newMenuIds.remove(orgId);
                updateOrgOrder(newMenuIds, 1);
            }
        }
    }

    /**
     * 获取同一级的组织id
     */
    private List<String> getOrgIds(String parentId, int order, Optional<Integer> maxOrder) {
        return maxOrder.map(max -> {
            return organizationDao.execute(d -> d.select(ORGANIZATION.ID)
                                                 .from(ORGANIZATION).where(ORGANIZATION.PARENT_ID.eq(parentId).and(ORGANIZATION.ORDER.between(order, max))).fetch(ORGANIZATION.ID));
        }).orElseGet(() -> {
            return organizationDao.execute(d -> d.select(ORGANIZATION.ID).from(ORGANIZATION).where(ORGANIZATION.PARENT_ID.eq(parentId).and(ORGANIZATION.ORDER.ge(order))).fetch(ORGANIZATION.ID));
        });
    }

    /**
     * 更新组织顺序
     */
    private void updateOrgOrder(List<String> orgIds, int addBase) {
        if (!orgIds.isEmpty() && addBase != 0) {
            if (addBase > 0) {
                organizationDao.execute(d -> d.update(ORGANIZATION).set(ORGANIZATION.ORDER, ORGANIZATION.ORDER.add(addBase)).where(ORGANIZATION.ID.in(orgIds)).execute());
            } else {
                organizationDao.execute(d -> d.update(ORGANIZATION).set(ORGANIZATION.ORDER, ORGANIZATION.ORDER.add(addBase)).where(ORGANIZATION.ID.in(orgIds), ORGANIZATION.ORDER.gt(0)).execute());
            }
        }
    }

    /**
     * 获取组织顺序
     */
    private Integer getOrgOrder(int operaType, Optional<Integer> order, String parentId) {
        // 获取最大order
        Integer maxOrder = organizationDao.execute(d -> {
            return d.select(DSL.max(ORGANIZATION.ORDER)).from(ORGANIZATION).where(ORGANIZATION.PARENT_ID.eq(parentId)).fetchOne(DSL.max(ORGANIZATION.ORDER));
        });
        if (maxOrder == null) {
            return 1;
        } else {
            return order.map(o -> {
                if (operaType == Organization.ADD) {
                    return o > maxOrder ? maxOrder + 1 : o;
                } else {
                    return o > maxOrder ? maxOrder : o;
                }
            }).orElseGet(() -> maxOrder + 1);
        }
    }

    @Override
    public Optional<Organization> getByMisCode(String misCode, Integer depth) {
        return organizationDao.fetchOne(ORGANIZATION.MIS_CODE.eq(misCode), ORGANIZATION.DEPTH.eq(depth));
    }

    /**
     * mis 数据同步更新插入数据
     */
    @Override
    public Organization hrSyncInsert(Optional<String> id, String name, Optional<String> shortName, String code,
                                     String parentId, Integer cmccLevel, Integer level, Optional<Integer> order, Optional<String> cmccAttribute,
                                     Optional<String> cmccCategory, Integer status, Optional<String> extentionParams, Optional<String> misCode) {
        Organization organization = new Organization();
        organization.forInsert();
        id.ifPresent(organization::setId);
        organization.setName(name);
        shortName.ifPresent(organization::setShortName);
        organization.setCode(code);
        organization.setParentId(parentId);
        organization.setPath(getPathOrganization(parentId, organization.getId()));

        Organization parentOrg = organizationDao.get(parentId);
        if (parentOrg != null && parentOrg.getDepth() != null && !parentOrg.getDepth().equals(1)) {
            organization.setDepth(parentOrg.getDepth() + 1);
        }
        organization.setCmccLevel(cmccLevel);
        organization.setLevel(level);
        organization.setOrder(getOrgOrder(Organization.ADD, order, parentId));
        cmccAttribute.ifPresent(organization::setCmccAttribute);
        cmccCategory.ifPresent(organization::setCmccCategory);
        organization.setStatus(status);
        organization.setType(Organization.TYPE_INNER);
        misCode.ifPresent(organization::setMisCode);
        String[] organizationPath = organization.getPath().split(",");
        if (Organization.LEVEL_BRANCH.equals(cmccLevel)) {
            organization.setCompanyId(organization.getId());
        } else {
            List<String> parentIds = Stream.of(organizationPath).sorted()
                                           .filter(x -> {
                                               if (get(x).isPresent()) {
                                                   return Organization.LEVEL_BRANCH.equals(get(x).get().getLevel());
                                               }
                                               return false;
                                           }).collect(Collectors.toList());
            if (parentIds.size() > 0) {
                organization.setCompanyId(parentIds.get(0));
            }
        }
        organizationDao.insert(organization);
        updateReleatedOrgOrder(Organization.ADD, organization.getId(), organization.getOrder(), parentId);
        cache.clear(CACHE_FAKE_ENABLED_ORGANIZATIONIDS);
        messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_INSERT, MessageHeaderContent.ID, organization.getId());
        return organization;
    }


    @Override
    public Organization hrSyncUpdate(String id, String name, Optional<String> shortName, String code, String parentId,
                                     Integer cmccLevel, Integer level, Optional<Integer> order, Optional<String> cmccAttribute,
                                     Optional<String> cmccCategory, Integer status, Optional<String> extentionParams, String misCode) {
        Organization organization = organizationDao.get(id);
        String beforeParentId = organization.getParentId();
        String oldParentId = organization.getParentId();
        String oldPath = organization.getPath();
        Integer oldStatus = organization.getStatus();
        Integer oldOrder = organization.getOrder() == null ? 1 : organization.getOrder();
        organization.setName(name);
        organization.setShortName(shortName.orElse(null));
        organization.setCode(code);
        organization.setParentId(parentId);
        organization.setPath(getPathOrganization(parentId, organization.getId()));
        Organization parentOrg = organizationDao.get(parentId);
        if (parentOrg.getDepth() != null) {
            organization.setDepth(parentOrg.getDepth() + 1);
        }
        organization.setCmccLevel(cmccLevel);
        organization.setLevel(level);
        organization.setOrder(getOrgOrder(Organization.UPDATE, order, parentId));
        organization.setCmccAttribute(cmccAttribute.orElse(null));
        organization.setCmccCategory(cmccCategory.orElse(null));
        organization.setStatus(status);
        organization.setMisCode(misCode);
        organization.setModifyDate(null);
        if (Organization.LEVEL_BRANCH.equals(cmccLevel)) {
            organization.setCompanyId(organization.getId());
        } else {
            List<String> parentIds = Stream.of(organization.getPath().split(",")).sorted()
                    .filter(x -> {
                        if (get(x).isPresent()) {
                            return Organization.LEVEL_BRANCH.equals(get(x).get().getLevel());
                        }
                        return false;
                    }).collect(Collectors.toList());
            if (parentIds.size() > 0) {
                organization.setCompanyId(parentIds.get(0));
            }
        }
        organizationDao.update(organization);
        updateReleatedOrgOrder(Organization.UPDATE, id, oldOrder, oldParentId);
        // 更新子节点path和depth
        updateReleatedPathAndDepth(organization.getId(), oldPath, organization.getPath());
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("before", beforeParentId);
        diffMap.put("after", parentId);
        if (beforeParentId != null) {
            List<OrganizationDetail> oldParents = organizationDetailService.find(organization.getId());
            List<String> parentIds = new ArrayList<>();
            if (!oldParents.isEmpty()) {
                oldParents.forEach(p -> {
                    parentIds.add(p.getRoot());
                });
            }
            diffMap.put("oldParentIds", parentIds);
        }
        // 清楚缓存数据
        cache.clear(CACHED_COUNT_PARENT + id);
        cache.clear(CACHE_FAKE_ENABLED_ORGANIZATIONIDS);
        // 如果父节点或状态变更，清除所有子节点缓存
        if (!org.apache.commons.lang.StringUtils.equals(oldParentId, organization.getParentId()) || !oldStatus.equals(organization.getStatus())) {
            clearAllSubCache(organization.getParentId());
        }
        messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_UPDATE, diffMap, MessageHeaderContent.ID, organization.getId());
        return organization;
    }


    @Override
    public List<Organization> findExportOrganization(String memberId, Optional<String> name, Optional<String> code, Optional<Integer> level, Optional<Integer> type) {
        return organizationDao.execute(d -> {
            com.zxy.product.system.jooq.tables.Organization parent = ORGANIZATION.as("parent");
            List<Condition> conditions = Stream.of(
                level.map(l -> l.equals(4) ? ORGANIZATION.LEVEL.eq(l) : ORGANIZATION.LEVEL.le(l)),
                name.map(ORGANIZATION.NAME::contains),
                code.map(ORGANIZATION.CODE::contains),
                type.map(ORGANIZATION.TYPE::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            // 所有授权组织,包括包含子结点和不包含子节点
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                          .innerJoin(MENU).on(MENU.URI.eq(OrganizationService.ORGANIZATION_URI), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                          .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                          .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(GrantOrganization::getOrganizationId)
                                                                   .collect(Collectors.toSet());
            // 将包含子节点的所有子组织查询出来
            Set<String> grantedOrganizationIds = d.select(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL)
                                                  .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds))
                                                  .fetchSet(ORGANIZATION_DETAIL.SUB);
            // 加入不包含的组织,组成完整的组织集合
            grantedOrganizationIds.addAll(excludeOrganizationIds);
            // 分批查询
            List<Organization> result = new ArrayList<>();
            List<String> organizationIds = new ArrayList<>(grantedOrganizationIds);
            List<String> subList;
            int count = organizationIds.size();
            int pageSize = 5000;
            int page = count % pageSize == 0 ? count / pageSize : count / pageSize + 1;
            for (int i = 0; i < page; i++) {
                subList = organizationIds.subList(i * pageSize, (i + 1) * pageSize > count ? count : (i + 1) * pageSize);
                result.addAll(
                    d.select(Fields.start()
                                   .add(
                                       ORGANIZATION.ID,
                                       ORGANIZATION.PARENT_ID,
                                       ORGANIZATION.NAME,
                                       ORGANIZATION.CODE,
                                       ORGANIZATION.LEVEL,
                                       ORGANIZATION.STATUS,
                                       ORGANIZATION.ORDER,
                                       ORGANIZATION.CREATE_TIME,
                                       parent.NAME
                                   )
                                   .end())
                     .from(ORGANIZATION)
                     .leftJoin(parent).on(parent.ID.eq(ORGANIZATION.PARENT_ID))
                     .where(conditions).and(ORGANIZATION.ID.in(subList))
                     .fetch(r -> {
                         Organization o = r.into(ORGANIZATION).into(Organization.class);
                         o.setParentName(r.getValue(parent.NAME));
                         return o;
                     }));
            }
            result.sort(OrganizationUtil.getOrgComparator());
            return result;
        });
    }

    @Override
    public List<Organization> findTemplateOrganization(String memberId) {
        return organizationDao.execute(a -> {
            List<Organization> organizations = a.selectDistinct(Fields.start()
                                                                      .add(ORGANIZATION.ID, ORGANIZATION.NAME, ORGANIZATION.CODE, ORGANIZATION.PARENT_ID, ORGANIZATION.PATH, ORGANIZATION.ORDER, ORGANIZATION.CREATE_TIME).end())
                                                .from(GRANT)
                                                .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId).and(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID)))
                                                .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                .innerJoin(MENU).on(MENU.URI.eq(OrganizationService.ORGANIZATION_URI), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                                .leftJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                                                .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                .orderBy(ORGANIZATION.ORDER.asc(), ORGANIZATION.CREATE_TIME.desc())
                                                .fetch(r -> {
                                                    Organization o = new Organization();
                                                    o.setId(r.getValue(ORGANIZATION.ID));
                                                    o.setName(r.getValue(ORGANIZATION.NAME));
                                                    o.setCode(r.getValue(ORGANIZATION.CODE));
                                                    o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
                                                    o.setPath(r.getValue(ORGANIZATION.PATH));
                                                    o.setOrder(r.getValue(ORGANIZATION.ORDER));
                                                    o.setCreateTime(r.getValue(ORGANIZATION.CREATE_TIME));
                                                    return o;
                                                });
            // 获取额外的,没有查询到的父组织
            Set<String> parentIds = Arrays.stream(organizations.stream().map(Organization::getPath).collect(Collectors.joining())
                                                               .split(",")).collect(Collectors.toSet());
            parentIds.removeAll(organizations.stream().map(Organization::getId).collect(Collectors.toList()));
            List<Organization> parentOrganizations = organizationDao.get(parentIds);
            Map<String, Organization> orgMap = Stream.concat(organizations.stream(), parentOrganizations.stream()).collect(Collectors.toMap(Organization::getId, o -> o));
            organizations.forEach(o -> {
                String path = o.getPath();
                if (StringUtils.hasText(path)) {
                    StringBuffer name = new StringBuffer();
                    Stream.of(path.split(",")).forEach(id -> {
                        Organization org = orgMap.get(id);
                        if (org != null) {
                            name.append(org.getName() + "-->");
                        }
                    });
                    name.delete(name.length() - 3, name.length());
                    o.setName(name.toString());
                }
            });
            return OrganizationUtil.treeOrganizations(organizations, OrganizationUtil.NAME_COMPOSITE_FALSE);
        });
    }

    @Override
    public int countParent(String id, Optional<Integer> level, Optional<Integer> status) {
        int count = cache.get(CACHED_COUNT_PARENT + id, () -> {
            return organizationDao.execute(d ->
                d.select(DSL.count(ORGANIZATION.ID))
                 .from(ORGANIZATION)
                 .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.ROOT.eq(ORGANIZATION.ID))
                 .where(ORGANIZATION_DETAIL.SUB.eq(id))
                 .and(level.map(ORGANIZATION.LEVEL::le).orElse(DSL.trueCondition()))
                 .and(status.map(ORGANIZATION.STATUS::eq).orElse(DSL.trueCondition()))
                 .fetchOne(DSL.count(ORGANIZATION.ID))
            );
        }, -1);
        return count;
    }

    @Override
    public boolean initOrganizationDepth() {
        StringBuilder sb = new StringBuilder();
        List<Organization> orgs = organizationDao.fetch();
        orgs.forEach(o -> {
            if (StringUtils.hasText(o.getPath()) && o.getPath().contains(",")) {
                sb.delete(0, sb.length());
                sb.append(o.getPath());
                int depth = sb.toString().split(",").length;
                o.setDepth(depth);
            }
        });
        int count = orgs.size();
        int pageSize = 2000;
        int page = count % pageSize == 0 ? count / pageSize : count / pageSize + 1;
        for (int i = 0; i < page; i++) {
            int end = (i + 1) * pageSize > count ? count : (i + 1) * pageSize;
            List<Organization> os = orgs.subList(i * pageSize, end);
            organizationDao.update(os);
        }
        return true;
    }

    @Override
    public List<Organization> findChildren(String parentId, Optional<Boolean> onlyCompany) {

        Condition condition = ORGANIZATION.PARENT_ID.eq(parentId);
        if (onlyCompany.isPresent()) {
            if (onlyCompany.get()) {
                condition = condition.and(ORGANIZATION.LEVEL.eq(Organization.LEVEL_BRANCH)); // 添加级别条件
            }
        }

        // 查询子组织数据
        List<Organization> children = organizationDao.fetch(condition);
        List<String> childIds = children.stream()
                .map(Organization::getId)
                .collect(Collectors.toList());

        if (childIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询所有这些子组织是否有父组织
        List<String> canBeParentIds = organizationDao.execute(d -> d.selectDistinct(ORGANIZATION.PARENT_ID)
                .from(ORGANIZATION)
                .where(ORGANIZATION.PARENT_ID.in(childIds))  // 批量查询
                .fetch(ORGANIZATION.PARENT_ID));

        // 转换为Set减少查找父节点的时间复杂度
        Set<String> canBeParentSet = new HashSet<>(canBeParentIds);
        // 设置每个组织是否有父组织
        children.forEach(c -> c.setIsParent(canBeParentSet.contains(c.getId())));

        // 排序
        children.sort(OrganizationUtil.getOrgComparator());

        return children;
    }

    @Override
    public Organization findMaxGrantOriganization(String memberId, String uri) {
//        return organizationDao.execute(dslContext ->dslContext.select(ORGANIZATION.fields()).from(ORGANIZATION)
//            .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//                .where(GRANT_DETAIL.URI.eq(uri)).and(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                .orderBy(ORGANIZATION.DEPTH.asc(),ORGANIZATION.ORDER.asc().nullsLast(), ORGANIZATION.CREATE_TIME.desc())
//                .limit(1).fetchOneInto(Organization.class)
//        );

        return organizationDao.execute(d ->
            d.selectDistinct(ORGANIZATION.fields())
             .from(GRANT_MEMBER)
             .leftJoin(GRANT).on(GRANT.ID.eq(GRANT_MEMBER.GRANT_ID))
             .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
             .leftJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
             .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
             .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
             .where(GRANT_MEMBER.MEMBER_ID.eq(memberId).and(MENU.URI.eq(uri).or(MENU.ID.eq(uri)))
                                          .and(ORGANIZATION.ID.isNotNull())
                                          .and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull())))
             .orderBy(ORGANIZATION.DEPTH.asc(),ORGANIZATION.ORDER.asc().nullsLast(), ORGANIZATION.CREATE_TIME.desc())
             .limit(1).fetchOneInto(Organization.class)
        );
    }


    /**
     * 通过组织编码 和 misCode 获取该省内的组织
     */
    @Override
    public Optional<Organization> getByCodeAndMisCode(String organizationCode, String misCode) {
        return organizationDao.fetchOne(ORGANIZATION.CODE.eq(organizationCode), ORGANIZATION.MIS_CODE.eq(misCode));
    }

    @Override
    public int countByMisCode(String organizationId, Optional<String> name, Optional<String> code,
                              Optional<String> id, Optional<String> misCode) {
        return organizationDetailDao.execute(a -> {
            Stream<Condition> condition = Stream.of(name.map(ORGANIZATION.NAME::eq), code.map(ORGANIZATION.CODE::eq)).filter(Optional::isPresent).map(Optional::get);
            return a.selectCount()
                    .from(ORGANIZATION)
                    .leftJoin(ORGANIZATION_DETAIL)
                    .on(ORGANIZATION_DETAIL.SUB.eq(ORGANIZATION.ID))
                    .where(ORGANIZATION_DETAIL.ROOT.eq(organizationId))
                    .and(condition.reduce((acc, item) -> acc.and(item)).orElse(DSL.falseCondition()))
                    .and(id.map(ORGANIZATION.ID::ne).orElse(DSL.trueCondition()))
                    .and(misCode.map(ORGANIZATION.MIS_CODE::eq).orElse(DSL.trueCondition()))
                    .fetchOne(0, Integer.class);
        });
    }

    @Override
    public List<Organization> getByCodes(Collection<String> codes) {
        return organizationDao.fetch(ORGANIZATION.CODE.in(codes));
    }

    @Override
    public Map<String, String> getAreasByOrgIds(List<String> orgIds) {
        Map<String, String> result = organizationDao.execute(d -> d.selectDistinct(ORGANIZATION_DETAIL.SUB, ORGANIZATION.AREA_CODE).from(ORGANIZATION)
                                                                   .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.ROOT.eq(ORGANIZATION.ID))
                                                                   .where(ORGANIZATION_DETAIL.SUB.in(orgIds), ORGANIZATION.AREA_CODE.isNotNull())
                                                                   .fetchMap(ORGANIZATION_DETAIL.SUB, ORGANIZATION.AREA_CODE)
        );
        orgIds.forEach(id -> {
            if (result.get(id) == null) {
                result.put(id, DEFAULT_AREA_CODE);
            }
        });
        return result;
    }

    @Override
    public Map<String, String> getAreasByOrgIds(String... orgIds) {
        return getAreasByOrgIds(Arrays.asList(orgIds));
    }


    /**
     * 获取组织path
     */
    private String getPathOrganization(String parentId, String id) {
        Organization organization = organizationDao.get(parentId);
        if (organization.getPath().endsWith(COMMA)) {
            return organization.getPath() + id;
        } else {
            return organization.getPath() + "," + id;
        }
    }

    @Override
    public void syncBatchInsert(List<Organization> organizations, String version) {
        Map<String, List<Organization>> batchInsert = batchInsert(organizations, "mis_admin", Optional.empty());
        List<Organization> failedList = batchInsert.get("failed");
        if (failedList.size() == 0) {  // 成功
            messageSender.send(MessageTypeContent.HR_REPORT_TO_SYSTEM_ORGANIZATION, MessageHeaderContent.SYNC_FAILE_OR_SUCCESS, "S",
                MessageHeaderContent.VERSION, version);
        } else { // 失败
            List<String> ids = failedList.stream().map(Organization::getSyncItemOrganizationId).collect(Collectors.toList());
            messageSender.send(MessageTypeContent.HR_REPORT_TO_SYSTEM_ORGANIZATION, MessageHeaderContent.SYNC_FAILE_OR_SUCCESS, "BF",
                MessageHeaderContent.VERSION, version, MessageHeaderContent.IDS, Joiner.on(",").join(ids));
        }
    }

    @Override
    public void syncBatchUpdate(List<Organization> list, String version) {
        List<String> errorIds = new ArrayList<>();
        list.forEach(o -> {
            try {
                misUpdate(o.getId(), o.getName(), Optional.ofNullable(o.getShortName()), o.getCode(),
                    o.getParentId(), o.getStatus());
            } catch (Exception e) {
                //			messageSender.send(MessageTypeContent.HR_REPORT_TO_SYSTEM_ORGANIZATION ,  MessageHeaderContent.SYNC_FAILE_OR_SUCCESS, "OF",
                //					 MessageHeaderContent.VERSION, version, MessageHeaderContent.ID, o.getId());
                errorIds.add(o.getSyncItemOrganizationId());
                LOGGER.error("syncBatchUpdate org id:" + o.getSyncItemOrganizationId());
                LOGGER.error(e.toString());
            }
        });
        if (errorIds.stream().filter(Objects::nonNull).collect(Collectors.toList()).size() > 0) {
            messageSender.send(MessageTypeContent.HR_REPORT_TO_SYSTEM_ORGANIZATION, MessageHeaderContent.SYNC_FAILE_OR_SUCCESS, "BF",
                MessageHeaderContent.VERSION, version, MessageHeaderContent.IDS, Joiner.on(",").join(errorIds));
        } else {
            messageSender.send(MessageTypeContent.HR_REPORT_TO_SYSTEM_ORGANIZATION, MessageHeaderContent.SYNC_FAILE_OR_SUCCESS, "S",
                MessageHeaderContent.VERSION, version);
        }
    }

    @Override
    public Organization misUpdate(String id, String name, Optional<String> shortName, String code, String parentId, Integer status) {
        Organization organization = organizationDao.get(id);
        String oldParentId = organization.getParentId();
        String oldPath = organization.getPath();
        Integer oldStatus = organization.getStatus();
        Integer oldOrder = organization.getOrder() == null ? 1 : organization.getOrder();
        organization.setName(name);
        organization.setShortName(shortName.orElse(null));
        organization.setCode(code);
//	        organization.setCmccLevel(cmccLevel);
//	        organization.setLevel(level);
//	        organization.setCmccAttribute(cmccAttribute.orElse(null));
//	        organization.setCmccCategory(cmccCategory.orElse(null));
        organization.setStatus(status);
        organization.setParentId(parentId);
        if (parentId != null) {
            Organization parentOrg = organizationDao.get(parentId);
            organization.setPath(parentOrg.getPath() + id + COMMA);
//	            organization.setCompanyId(level.intValue() < Organization.LEVEL_DEPARTMENT ? organization.getId() : parentOrg.getCompanyId());
            if (parentOrg.getDepth() != null && !parentOrg.getDepth().equals(1)) {
                organization.setDepth(parentOrg.getDepth() + 1);
                organization.setType(parentOrg.getType());
            }
            organizationDao.update(organization);
            updateReleatedOrgOrder(Organization.UPDATE, id, oldOrder, oldParentId);
            // 更新子节点path和depth
            updateReleatedPathAndDepth(organization.getId(), oldPath, organization.getPath());
        } else {
            organization.setLevel(Organization.LEVEL_HEAD);
            organizationDao.update(organization);
        }
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("before", oldParentId);
        diffMap.put("after", parentId);
        if (oldParentId != null) {
            List<OrganizationDetail> oldParents = organizationDetailService.find(organization.getId());
            List<String> parentIds = new ArrayList<>();
            if (!oldParents.isEmpty()) {
                oldParents.forEach(p -> {
                    parentIds.add(p.getRoot());
                });
            }
            diffMap.put("oldParentIds", parentIds);
        }
        // 清楚缓存数据
        cache.clear(CACHED_COUNT_PARENT + id);
        cache.clear(CACHE_FAKE_ENABLED_ORGANIZATIONIDS);
        // 如果父节点或状态变更，清除所有子节点缓存
        if (!org.apache.commons.lang.StringUtils.equals(oldParentId, parentId) || !oldStatus.equals(status)) {
            clearAllSubCache(id);
        }
        messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_UPDATE, diffMap, MessageHeaderContent.ID, organization.getId());
        return organization;
    }

    // 清除所有子节点缓存
    public void clearAllSubCache(String id) {
        List<String> subIds = organizationDetailDao.fetch(ORGANIZATION_DETAIL.ROOT.eq(id)).stream().map(OrganizationDetail::getSub)
                                                   .collect(Collectors.toList());
        subIds.remove(id);
        subIds.forEach(x -> cache.clear(CACHED_COUNT_PARENT + x));
    }

    @Override
    public String getOrganizationIdWithDepth5(String orgId) {
        return organizationDao.execute(d -> d.select(ORGANIZATION_DETAIL.ROOT).from(ORGANIZATION_DETAIL)
                                             .leftJoin(ORGANIZATION).on(ORGANIZATION_DETAIL.ROOT.eq(ORGANIZATION.ID))
                                             .where(ORGANIZATION_DETAIL.SUB.eq(orgId), ORGANIZATION.DEPTH.eq(4)).fetchOne(ORGANIZATION_DETAIL.ROOT));
    }

    @Override
    public List<Organization> findAndExportOrganization(String memberId, Optional<String> name, Optional<String> code,
                                                        Optional<Integer> level, Optional<Integer> type){
        return organizationDao.execute(d -> {
            com.zxy.product.system.jooq.tables.Organization parent = ORGANIZATION.as("parent");
            List<Condition> conditions = Stream.of(
                level.map(l -> l.equals(4) ? ORGANIZATION.LEVEL.eq(l) : ORGANIZATION.LEVEL.le(l)),
                name.map(ORGANIZATION.NAME::contains),
                code.map(ORGANIZATION.CODE::contains),
                type.map(ORGANIZATION.TYPE::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            // 所有授权组织,包括包含子结点和不包含子节点
            List<Organization> grantOrganizations = d.selectDistinct(
                                                         Fields.start().add(
                                                             ORGANIZATION.ID,
                                                             ORGANIZATION.PATH,
                                                             ORGANIZATION.DEPTH,
                                                             GRANT_ORGANIZATION.CHILD_FIND
                                                         ).end())
                                                     .from(GRANT)
                                                     .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                     .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                     .innerJoin(MENU).on(MENU.URI.eq(OrganizationService.ORGANIZATION_URI), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                     .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                     .join(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                                     .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                     .orderBy(ORGANIZATION.DEPTH.desc())
                                                     .fetch(r -> {
                                                         Organization o = new Organization();
                                                         o.setId(r.getValue(ORGANIZATION.ID));
                                                         o.setPath(r.getValue(ORGANIZATION.PATH));
                                                         o.setDepth(r.getValue(ORGANIZATION.DEPTH));
                                                         o.setChildFind(String.valueOf(r.getValue(GRANT_ORGANIZATION.CHILD_FIND)));
                                                         return o;
                                                     });
            OrganizationUtil.getOrgPath(grantOrganizations);
            List<Organization> collect = grantOrganizations.stream().sorted((o1, o2) -> { return o1.getDepth() - o2.getDepth();}).collect(Collectors.toList());
            List<Organization> result = new ArrayList<>();
            collect.forEach(go -> {
                if (!GrantOrganization.CHILD_FIND_ORG.equals(go.getChildFind())) { // 获取不包含子节点的组织
                    result.addAll(
                        d.select(Fields.start().add(
                             ORGANIZATION.ID,
                             ORGANIZATION.PARENT_ID,
                             ORGANIZATION.NAME,
                             ORGANIZATION.CODE,
                             ORGANIZATION.LEVEL,
                             ORGANIZATION.STATUS,
                             ORGANIZATION.ORDER,
                             ORGANIZATION.CREATE_TIME,
                             parent.NAME).end())
                         .from(ORGANIZATION)
                         .leftJoin(parent).on(parent.ID.eq(ORGANIZATION.PARENT_ID))
                         .where(conditions).and(ORGANIZATION.ID.eq(go.getId()))
                         .orderBy(ORGANIZATION.PATH)
                         .fetch(r -> {
                             Organization o = r.into(ORGANIZATION).into(Organization.class);
                             o.setParentName(r.getValue(parent.NAME));
                             return o;
                         })
                    );
                } else { // 获取包含子节点的组织
                    result.addAll(
                        d.select(Fields.start().add(
                             ORGANIZATION.ID,
                             ORGANIZATION.PARENT_ID,
                             ORGANIZATION.NAME,
                             ORGANIZATION.CODE,
                             ORGANIZATION.LEVEL,
                             ORGANIZATION.STATUS,
                             ORGANIZATION.ORDER,
                             ORGANIZATION.CREATE_TIME,
                             parent.NAME).end())
                         .from(ORGANIZATION)
                         .leftJoin(parent).on(parent.ID.eq(ORGANIZATION.PARENT_ID))
                         .where(conditions).and(ORGANIZATION.PATH.like(go.getPath() + "%"))
                         .orderBy(ORGANIZATION.PATH)
                         .fetch(r -> {
                             Organization o = r.into(ORGANIZATION).into(Organization.class);
                             o.setParentName(r.getValue(parent.NAME));
                             return o;
                         })
                    );
                }
            });
            // 去除禁用的组织
            List<String> replaseOrganizationPIds = result.stream().filter(or -> or.getStatus() != 1).map(Organization :: getId).collect(Collectors.toList());
            List<String> replaseOrganizationList = d.selectDistinct(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL).where(ORGANIZATION_DETAIL.ROOT.in(replaseOrganizationPIds)).fetchInto(String.class);
            List<Organization> filterOrganizationList = result.stream().filter(or -> or.getStatus() == 1).filter(or -> !replaseOrganizationList.contains(or.getId())).collect(Collectors.toList());
            filterOrganizationList.sort(OrganizationUtil.getOrgComparator());
            List<Organization> treeOrganizationSord = OrganizationUtil.treeOrganizations(filterOrganizationList, OrganizationUtil.NAME_COMPOSITE_FALSE);
            List<Organization> treeOrganizationFilter = treeOrganizationSord.stream().map(o -> {
                o.setCreateTime(null);
                o.setId(null);
                o.setParentId(null);
                return o;
            } ).collect(Collectors.toList());

            return treeOrganizationFilter;
        });
    }

    @Override
    public List<Organization> findAndExportOrganizationInExam(String memberId, Optional<String> name, Optional<String> code,
                                                              Optional<Integer> level, Optional<Integer> type){
        String EXAM_AGENT = "exam/agent";
        return organizationDao.execute(d -> {
            com.zxy.product.system.jooq.tables.Organization parent = ORGANIZATION.as("parent");
            List<Condition> conditions = Stream.of(
                level.map(l -> l.equals(4) ? ORGANIZATION.LEVEL.eq(l) : ORGANIZATION.LEVEL.le(l)),
                name.map(ORGANIZATION.NAME::contains),
                code.map(ORGANIZATION.CODE::contains),
                type.map(ORGANIZATION.TYPE::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            // 所有授权组织,包括包含子结点和不包含子节点
            List<Organization> grantOrganizations = d.selectDistinct(
                                                         Fields.start().add(
                                                             ORGANIZATION.ID,
                                                             ORGANIZATION.PATH,
                                                             ORGANIZATION.DEPTH,
                                                             GRANT_ORGANIZATION.CHILD_FIND
                                                         ).end())
                                                     .from(GRANT)
                                                     .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                     .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                     .innerJoin(MENU).on(MENU.URI.eq(EXAM_AGENT), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                     .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                     .join(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                                     .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                     .orderBy(ORGANIZATION.DEPTH.desc())
                                                     .fetch(r -> {
                                                         Organization o = new Organization();
                                                         o.setId(r.getValue(ORGANIZATION.ID));
                                                         o.setPath(r.getValue(ORGANIZATION.PATH));
                                                         o.setDepth(r.getValue(ORGANIZATION.DEPTH));
                                                         o.setChildFind(String.valueOf(r.getValue(GRANT_ORGANIZATION.CHILD_FIND)));
                                                         return o;
                                                     });
            OrganizationUtil.getOrgPath(grantOrganizations);
            List<Organization> collect = grantOrganizations.stream().sorted((o1, o2) -> { return o1.getDepth() - o2.getDepth();}).collect(Collectors.toList());
            List<Organization> result = new ArrayList<>();
            collect.forEach(go -> {
                if (!GrantOrganization.CHILD_FIND_ORG.equals(go.getChildFind())) { // 获取不包含子节点的组织
                    result.addAll(
                        d.select(Fields.start().add(
                             ORGANIZATION.ID,
                             ORGANIZATION.PARENT_ID,
                             ORGANIZATION.NAME,
                             ORGANIZATION.CODE,
                             ORGANIZATION.LEVEL,
                             ORGANIZATION.STATUS,
                             ORGANIZATION.ORDER,
                             ORGANIZATION.CREATE_TIME,
                             parent.NAME).end())
                         .from(ORGANIZATION)
                         .leftJoin(parent).on(parent.ID.eq(ORGANIZATION.PARENT_ID))
                         .where(conditions).and(ORGANIZATION.ID.eq(go.getId()))
                         .orderBy(ORGANIZATION.PATH)
                         .fetch(r -> {
                             Organization o = r.into(ORGANIZATION).into(Organization.class);
                             o.setParentName(r.getValue(parent.NAME));
                             return o;
                         })
                    );
                } else { // 获取包含子节点的组织
                    result.addAll(
                        d.select(Fields.start().add(
                             ORGANIZATION.ID,
                             ORGANIZATION.PARENT_ID,
                             ORGANIZATION.NAME,
                             ORGANIZATION.CODE,
                             ORGANIZATION.LEVEL,
                             ORGANIZATION.STATUS,
                             ORGANIZATION.ORDER,
                             ORGANIZATION.CREATE_TIME,
                             parent.NAME).end())
                         .from(ORGANIZATION)
                         .leftJoin(parent).on(parent.ID.eq(ORGANIZATION.PARENT_ID))
                         .where(conditions).and(ORGANIZATION.PATH.like(go.getPath() + "%"))
                         .orderBy(ORGANIZATION.PATH)
                         .fetch(r -> {
                             Organization o = r.into(ORGANIZATION).into(Organization.class);
                             o.setParentName(r.getValue(parent.NAME));
                             return o;
                         })
                    );
                }
            });
            result.sort(OrganizationUtil.getOrgComparator());
            List<Organization> treeOrganizationSord = OrganizationUtil.treeOrganizations(result, OrganizationUtil.NAME_COMPOSITE_FALSE);
            List<Organization> treeOrganizationFilter = treeOrganizationSord.stream().map(o -> {
                o.setCreateTime(null);
                o.setId(null);
                o.setParentId(null);
                return o;
            } ).collect(Collectors.toList());
            return treeOrganizationFilter;
        });
    }

    private void updateReleatedCompanyId(String id, Integer originalLevel, Integer level, String parentCompanyId) {
        List<String> subIds = organizationDetailDao.fetch(ORGANIZATION_DETAIL.ROOT.eq(id)).stream().map(OrganizationDetail::getSub)
                                                   .collect(Collectors.toList());
        if (originalLevel > level) {//部门改为分公司
            organizationDao.execute(d -> d.update(ORGANIZATION).set(ORGANIZATION.COMPANY_ID, id).where(ORGANIZATION.ID.in(subIds)).execute());
        } else if (originalLevel < level) {//分公司改为部门
            organizationDao.execute(d -> d.update(ORGANIZATION).set(ORGANIZATION.COMPANY_ID, parentCompanyId).where(ORGANIZATION.ID.in(subIds)).execute());
        }
    }

    private void updateReleatedPathAndDepth(String id, String oldPath, String newPath) {
        if (!org.apache.commons.lang.StringUtils.equals(oldPath, newPath)) {
            List<String> subIds = organizationDetailDao.fetch(ORGANIZATION_DETAIL.ROOT.eq(id)).stream().map(OrganizationDetail::getSub)
                                                       .collect(Collectors.toList());
            subIds.remove(id);
            int depthGap = newPath.split(COMMA).length - oldPath.split(COMMA).length;
            organizationDao.execute(e -> e.update(ORGANIZATION)
                                          .set(ORGANIZATION.PATH, DSL.replace(ORGANIZATION.PATH, oldPath, newPath))
                                          .set(ORGANIZATION.DEPTH, ORGANIZATION.DEPTH.add(depthGap))
                                          .where(ORGANIZATION.ID.in(subIds))).execute();
        }
    }

    @Override
    public List<Organization> findOrganizationByIds(List<String> oIds) {
        return organizationDao.execute(d -> {
            return d.selectDistinct(ORGANIZATION.CODE, ORGANIZATION.ID, ORGANIZATION.NAME, ORGANIZATION.PARENT_ID, ORGANIZATION.LEVEL)
                    .from(ORGANIZATION)
                    .where(ORGANIZATION.ID.in(oIds))
                    .orderBy(ORGANIZATION.ORDER.asc(), ORGANIZATION.CREATE_TIME.desc())
                    .fetch(r -> {
                        Organization o = new Organization();
                        o.setCode(r.getValue(ORGANIZATION.CODE));
                        o.setId(r.getValue(ORGANIZATION.ID));
                        o.setName(r.getValue(ORGANIZATION.NAME));
                        o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
                        o.setLevel(r.getValue(ORGANIZATION.LEVEL));
                        return o;
                    });
        });
    }

    @Override
    public List<Organization> findGrantOrgsByMemberIdAndUri(String memberId, String uri) {
        return organizationDao.execute(d -> {
            List<Integer> depths = getDepths(memberId, d);
            List<Condition> conditions = Stream.of(GRANT_MEMBER.MEMBER_ID.eq(memberId).and(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                                         .and(ORGANIZATION.DEPTH.le(depths.get(0) + 3))).collect(Collectors.toList());
            conditions.add(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                                                        .or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO)
                                                                                         .and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID))));
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> step = a -> a.from(GRANT)
                                                                                         .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                                                         .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                                                         .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                                                         .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                                                         .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                                                                         .innerJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                                                                                         .where(conditions);
            return getResult(d, depths, step);
        });
    }

    @Override
    public List<Organization> findGrantOrgsByMemberIdAndroleId(String memberId, String roleId) {
        return organizationDao.execute(d -> {
            List<String> menuList = d.select(MENU.ID).from(MENU)
                                     .leftJoin(ROLE_MENU).on(ROLE_MENU.MENU_ID.eq(MENU.ID))
                                     .where(ROLE_MENU.ROLE_ID.eq(roleId), MENU.URI.isNotNull()).fetch(MENU.ID);
            List<Integer> depths = getDepths(memberId, d);
            List<Condition> conditions = Stream.of(GRANT_MEMBER.MEMBER_ID.eq(memberId)
                                                                         .and(ORGANIZATION.DEPTH.le(depths.get(0) + 3))).collect(Collectors.toList());
            conditions.add(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                                                        .or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO)
                                                                                         .and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID))));
            conditions.add(MENU.ID.in(menuList));
            conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> step = a ->
                a.from(GRANT)
                 .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                 .innerJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                 .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                 .innerJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                 .innerJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                 .innerJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                 .where(conditions);
            return getResult(d, depths, step);
        });
    }

    private List<Organization> getResult(DSLContext d, List<Integer> depths,
                                         Function<SelectSelectStep<Record>, SelectConditionStep<Record>> step) {
        // 查询列表 id, parentId, name, level, createTime, childFind
        List<Organization> orgs = step.apply(d.selectDistinct(Fields.start().add(ORGANIZATION.ID, ORGANIZATION.NAME, ORGANIZATION.PARENT_ID, ORGANIZATION.COMPANY_ID, ORGANIZATION.LEVEL, ORGANIZATION.CREATE_TIME, ORGANIZATION.ORDER).end()))
                                      .fetch(r -> {
                                          Organization o = new Organization();
                                          o.setId(r.getValue(ORGANIZATION.ID));
                                          o.setName(r.getValue(ORGANIZATION.NAME));
                                          o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
                                          o.setCompanyId(r.getValue(ORGANIZATION.COMPANY_ID));
                                          o.setLevel(r.getValue(ORGANIZATION.LEVEL));
                                          o.setCreateTime(r.getValue(ORGANIZATION.CREATE_TIME));
                                          o.setOrder(r.getValue(ORGANIZATION.ORDER));
                                          return o;
                                      });
        // 找出所有5层以上(不包含)的已经作为父节点的组织id,用于判断具体的某一个节点是否可以展开
        List<String> parentIds = d.selectDistinct(ORGANIZATION.PARENT_ID).from(ORGANIZATION)
                                  .where(ORGANIZATION.PARENT_ID.isNotNull(), ORGANIZATION.DEPTH.le(depths.get(0) + 4)).fetch(ORGANIZATION.PARENT_ID);
        // 找出当前用户拥有的,第四层的,包含子节点的组织id,用于判断第四层的节点,是否可以展开(对于第四层的节点,第一要满足是父节点才可以展开,并且第四层的节点,在权限上必须要包含子节点才可以展开)
        List<String> childFindYesIds = step.apply(d.selectDistinct(Fields.start().add(ORGANIZATION.ID).end()))
                                           .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)).fetch(ORGANIZATION.ID);
        parentIds.retainAll(childFindYesIds);
        orgs.forEach(o -> {
            if (parentIds.contains(o.getId())) {
                o.setChildFind(Organization.CHILD_FIND_YES.toString());
                o.setIsParent(true);
            }
        });
        // 排序
        orgs.sort(OrganizationUtil.getOrgComparator());
        return orgs;
    }


    private List<Integer> getDepths(String memberId, DSLContext d) {
        List<Integer> depths = d.selectDistinct(ORGANIZATION.DEPTH)
                                .from(GRANT_MEMBER)
                                .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
                                .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                .where(GRANT_MEMBER.MEMBER_ID.eq(memberId))
                                .orderBy(ORGANIZATION.DEPTH)
                                .fetch(ORGANIZATION.DEPTH);
        return depths;
    }

    @Override
    public List<Organization> findAllOrgsByMemberIdAndUri(String memberId, String uri) {
        return organizationDao.execute(d -> {
//			 com.zxy.product.system.jooq.tables.Organization parent = ORGANIZATION.as("parent");
            Integer fetch = d.select(GRANT.ID.countDistinct()).from(GRANT)
                             .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                             .innerJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                             .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                             .where(GRANT_MEMBER.MEMBER_ID.eq(memberId),  MENU.URI.eq(uri), GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                             .fetchOne(DSL.count(GRANT.ID));
            if (fetch > 0) {
                List<Organization> result = d.select(Fields.start().add(
                                                 ORGANIZATION.ID,
                                                 ORGANIZATION.PARENT_ID,
                                                 ORGANIZATION.NAME,
                                                 ORGANIZATION.CODE,
                                                 ORGANIZATION.LEVEL,
                                                 ORGANIZATION.STATUS,
                                                 ORGANIZATION.ORDER,
                                                 ORGANIZATION.CREATE_TIME
//                          parent.NAME
                                             ).end())
                                             .from(ORGANIZATION)
//                  .leftJoin(parent).on(parent.ID.eq(ORGANIZATION.PARENT_ID))
                                             .where(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))
                                             .fetch(r -> {
                                                 Organization o = r.into(ORGANIZATION).into(Organization.class);
//                      o.setParentName(r.getValue(parent.NAME));
                                                 return o;
                                             });
                result.sort(OrganizationUtil.getOrgComparator());
                List<Organization> treeOrganizationSord = OrganizationUtil.treeOrganizations(result, OrganizationUtil.NAME_COMPOSITE_FALSE);
                List<Organization> treeOrganizationFilter = treeOrganizationSord.stream().map(o -> {
                    o.setCreateTime(null); // 除去排序后的值
                    o.setId(null);
                    o.setLevel(null);
                    o.setStatus(null);
                    o.setOrder(null);
                    o.setParentId(null);
                    return o;
                }).collect(Collectors.toList());
                return treeOrganizationFilter;
            }
            return null;
        });
    }

    @Override
    public List<Organization> findOrganizationByRoleId(String memberId, Optional<String> roleId,
                                                       Optional<Boolean> supportMore, Map<String, Set<String>> grantOrganizationMap,
                                                       Optional<String> name, Optional<String> code) {
        return organizationDao.execute(d -> {
            // step1 拼接查询字段和条件
            LOGGER.info("【findOrganizationByRoleId开始】");
            Collection<Field<?>> selectFields = Fields.start().add(
                ORGANIZATION.ID,
                ORGANIZATION.NAME,
                ORGANIZATION.PARENT_ID,
                ORGANIZATION.STATUS,
                ORGANIZATION.DEPTH,
                ORGANIZATION.LEVEL,
                ORGANIZATION.CODE,
                ORGANIZATION.PATH,
                ORGANIZATION.ORDER,
                ORGANIZATION.CREATE_TIME,
                ORGANIZATION.COMPANY_ID).end();
            List<Condition> conditions = Stream.of(
                name.map(ORGANIZATION.NAME::contains),
                code.map(ORGANIZATION.CODE::contains)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            // 包含子组织条件，用于组织包含子节点是判断是否张开的条件
            Condition childCondition = grantOrganizationMap.get(Organization.INCLUDE_KEY)
                                                           .stream().map(ORGANIZATION.PATH::startsWith).reduce(DSL::or).orElse(DSL.falseCondition());
            Condition grantCondition = findGrantCondition(grantOrganizationMap);
            conditions.add(grantCondition);
            LOGGER.info("【拼接查询字段结束】");

            // step2 查询当前用户最大权限节点深度,拼接条件
            List<Integer> depths = d.selectDistinct(ORGANIZATION.DEPTH)
                                    .from(GRANT_MEMBER)
                                    .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT_MEMBER.GRANT_ID))
                                    .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                    .where(GRANT_MEMBER.MEMBER_ID.eq(memberId).and(ORGANIZATION.DEPTH.isNotNull()))
                                    .orderBy(ORGANIZATION.DEPTH)
                                    .fetch(ORGANIZATION.DEPTH);
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> step = a -> a.from(ORGANIZATION).where(conditions);
            // 如果是精准查询,则判断查询的数据是否大于1000,此时没有层级限制;如果是非精准查询,就查5层以上的.
            if (supportMore.isPresent() && supportMore.get() && (name.isPresent() || code.isPresent())) {
                int count = step.apply(d.select(Fields.start().add(ORGANIZATION.ID.countDistinct()).end())).fetchOne(ORGANIZATION.ID.countDistinct());
                if (count > MAX_SEARCH_COUNT) {
                    throw new UnprocessableException(ErrorCode.QueryHasTooManyRecords);
                }
            } else { // 非精准查询,层级限制在5层以内
                conditions.add(ORGANIZATION.DEPTH.le(depths.get(0) + 3));
            }
            LOGGER.info("【查询授权组织深度结束】");

            // step3 获取组织数据,fetchInto性能查,改为fetch
            List<Organization> orgs = step.apply(d.select(selectFields)).fetch(r -> {
                Organization o = new Organization();
                o.setId(r.get(ORGANIZATION.ID));
                o.setName(r.get(ORGANIZATION.NAME));
                o.setParentId(r.get(ORGANIZATION.PARENT_ID));
                o.setStatus(r.get(ORGANIZATION.STATUS));
                o.setDepth(r.get(ORGANIZATION.DEPTH));
                o.setLevel(r.get(ORGANIZATION.LEVEL));
                o.setCode(r.get(ORGANIZATION.CODE));
                o.setPath(r.get(ORGANIZATION.PATH));
                o.setOrder(r.get(ORGANIZATION.ORDER));
                o.setCreateTime(r.get(ORGANIZATION.CREATE_TIME));
                o.setCompanyId(r.get(ORGANIZATION.COMPANY_ID));
                return o;
            });
            LOGGER.info("【获取组织数据结束】");

            // step4 过滤组织是否展开查询
            if (supportMore.isPresent() && supportMore.get() && !name.isPresent() && !code.isPresent()) {
                List<String> parentIds = d.selectDistinct(ORGANIZATION.PARENT_ID).from(ORGANIZATION)
                                          .where(ORGANIZATION.DEPTH.le(depths.get(0) + 4)).and(childCondition)
                                          .fetch(ORGANIZATION.PARENT_ID);
                orgs.forEach(o -> {
                    if (parentIds.contains(o.getId())) {
                        o.setChildFind(Organization.CHILD_FIND_YES.toString());
                        o.setIsParent(true);
                    }
                });
            }
            LOGGER.info("【处理展开标识结束】");

            // step5 排序
            orgs.sort(OrganizationUtil.getOrgComparator());
            LOGGER.info("【组织排序结束】");

            // step6 去除禁用
            List<Organization> organizations = filterOrg(orgs, Boolean.FALSE);
            LOGGER.info("【禁用组织过滤结束】");
            return organizations;
        });
    }

    private Condition findGrantCondition(Map<String, Set<String>> grantOrganizationMap) {
        Set<String> organizationIdSet = grantOrganizationMap.get(Organization.NOT_INCLUDE_KEY);
        Set<String> pathSet = grantOrganizationMap.get(Organization.INCLUDE_KEY);
        Condition condition;
        if (pathSet.isEmpty()) {
            condition = Optional.of(organizationIdSet).map(r -> r.isEmpty() ? DSL.trueCondition() : ORGANIZATION.ID.in(r)).orElse(DSL.trueCondition());
        } else {
            condition = pathSet.stream().map(ORGANIZATION.PATH::startsWith).reduce(DSL::or).orElse(DSL.trueCondition());
            if (!organizationIdSet.isEmpty()) {
                condition = condition.or(ORGANIZATION.ID.in(organizationIdSet));
            }
        }
        return condition;
    }

    @Override
    public void exportOrganizationByGrantPath(String cacheKey, String memberId, Optional<String> name, Optional<String> code,
                                              Optional<Integer> level, Optional<Integer> type, Map<String, Set<String>> grantOrganizationMap) {
        LOGGER.info("【组织查询开始】：{}", System.currentTimeMillis());
        List<Organization> exportOrganization = organizationDao.execute(d -> {
            // 拼接查询条件
            List<Condition> conditions = Stream.of(
                level.map(l -> l.equals(4) ? ORGANIZATION.LEVEL.eq(l) : ORGANIZATION.LEVEL.le(l)),
                name.map(ORGANIZATION.NAME::contains),
                code.map(ORGANIZATION.CODE::contains),
                type.map(ORGANIZATION.TYPE::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            Condition grantCondition = findGrantCondition(grantOrganizationMap);
            conditions.add(grantCondition);

            // 查询组织数据
            List<Organization> result = d.select(Fields.start().add(
                                             ORGANIZATION.ID,
                                             ORGANIZATION.PARENT_ID,
                                             ORGANIZATION.NAME,
                                             ORGANIZATION.CODE,
                                             ORGANIZATION.LEVEL,
                                             ORGANIZATION.STATUS,
                                             ORGANIZATION.ORDER,
                                             ORGANIZATION.PATH,
                                             ORGANIZATION.CREATE_TIME
                                         ).end())
                                         .from(ORGANIZATION)
                                         .where(conditions)
                                         .fetch(r -> {
                                             Organization o = new Organization();
                                             o.setId(r.get(ORGANIZATION.ID));
                                             o.setParentId(r.get(ORGANIZATION.PARENT_ID));
                                             o.setName(r.get(ORGANIZATION.NAME));
                                             o.setCode(r.get(ORGANIZATION.CODE));
                                             o.setLevel(r.get(ORGANIZATION.LEVEL));
                                             o.setStatus(r.get(ORGANIZATION.STATUS));
                                             o.setOrder(r.get(ORGANIZATION.ORDER));
                                             o.setPath(r.get(ORGANIZATION.PATH));
                                             o.setCreateTime(r.get(ORGANIZATION.CREATE_TIME));
                                             return o;
                                         });
            LOGGER.info("【组织查询结束】：{}", System.currentTimeMillis());

            // 查询父组织数据并拼接
            com.zxy.product.system.jooq.tables.Organization parent = ORGANIZATION.as("parent");
            Map<String, String> parentNameMap = d.selectDistinct(Fields.start().add(
                                                     ORGANIZATION.PARENT_ID,
                                                     parent.NAME
                                                 ).end())
                                                 .from(ORGANIZATION)
                                                 .innerJoin(parent).on(parent.ID.eq(ORGANIZATION.PARENT_ID))
                                                 .where(conditions)
                                                 .fetchMap(ORGANIZATION.PARENT_ID, parent.NAME);
            LOGGER.info("【父组织查询结束】：{}", System.currentTimeMillis());
            result.forEach(o -> {
                if (!StringUtils.isEmpty(o.getParentId())) {
                    o.setParentName(parentNameMap.get(o.getParentId()));
                }
            });
            LOGGER.info("【父组织拼接结束】：{}", System.currentTimeMillis());

            // 按排序字段和创建时间排序
            result.sort(OrganizationUtil.getOrgComparator());
            LOGGER.info("【组织排序结束】：{}", System.currentTimeMillis());

            // 构建组织树顺序
            List<Organization> organizationSord = OrganizationUtil.treeOrganizations(result, OrganizationUtil.NAME_COMPOSITE_FALSE);
            LOGGER.info("【构建组织树结束】：{}", System.currentTimeMillis());

            // 父节点组织为禁用的,将子节点也显示为禁用,只导出可用节点
            // 优化2020/07/07 原嵌套循环12w*6w判断执行10分钟,改为业务查询,查出父节点禁用、子节点可用的id后筛选
            Set<String> fakeEnabledOrgIds = getFakeEnabledOrgIds();
            LOGGER.info("【子节点可用父节点禁用数量】：{}", fakeEnabledOrgIds.size());
            return organizationSord.stream()
                                   .filter(or -> {
                                       // 筛选出部门和父部门都可用的
                                       return Organization.STATUS_ENABLED.equals(or.getStatus()) && !fakeEnabledOrgIds.contains(or.getId());
                                   }).map(o -> {
                    o.setCreateTime(null);
                    o.setId(null);
                    o.setPath(null);
                    return o;
                }).collect(Collectors.toList());
        });
        LOGGER.info("【组织过滤结束】：{}", System.currentTimeMillis());
        cache.set(EXPORT_ORGANIZATIONS_CACHE_KEY + cacheKey, exportOrganization, 2 * 60);
    }

    @Override
    public Map<String, String> findParentOrganizationName(String memberId, Optional<String> name, Optional<String> code,
                                                          Optional<Integer> level, Optional<Integer> type, Map<String, Set<String>> grantOrganizationMap) {
        return organizationDao.execute(d -> {
            com.zxy.product.system.jooq.tables.Organization parent = ORGANIZATION.as("parent");
            List<Condition> conditions = Stream.of(
                level.map(l -> l.equals(4) ? ORGANIZATION.LEVEL.eq(l) : ORGANIZATION.LEVEL.le(l)),
                name.map(ORGANIZATION.NAME::contains),
                code.map(ORGANIZATION.CODE::contains),
                type.map(ORGANIZATION.TYPE::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            Condition grantCondition = findGrantCondition(grantOrganizationMap);
            conditions.add(grantCondition);
            return d.selectDistinct(Fields.start().add(
                        ORGANIZATION.PARENT_ID,
                        parent.NAME
                    ).end())
                    .from(ORGANIZATION)
                    .innerJoin(parent).on(parent.ID.eq(ORGANIZATION.PARENT_ID))
                    .where(conditions)
                    .fetchMap(ORGANIZATION.PARENT_ID, parent.NAME);
        });
    }

    @Override
    public List<Organization> findOrganizationByGrantPath(Map<String, Set<String>> grantOrganizationMap) {
        return organizationDao.execute(d -> {
            Condition grantCondition = findGrantCondition(grantOrganizationMap);
            List<Organization> result = d.select(Fields.start().add(
                                             ORGANIZATION.ID,
                                             ORGANIZATION.PARENT_ID,
                                             ORGANIZATION.NAME,
                                             ORGANIZATION.CODE,
                                             ORGANIZATION.LEVEL,
                                             ORGANIZATION.STATUS,
                                             ORGANIZATION.ORDER,
                                             ORGANIZATION.CREATE_TIME
                                         ).end())
                                         .from(ORGANIZATION)
                                         .where(grantCondition)
                                         .fetch(r -> {
                                             Organization o = r.into(ORGANIZATION).into(Organization.class);
                                             return o;
                                         });
            result.sort(OrganizationUtil.getOrgComparator());
            List<Organization> treeOrganizationSord = OrganizationUtil.treeOrganizations(result, OrganizationUtil.NAME_COMPOSITE_FALSE);
            List<Organization> treeOrganizationFilter = treeOrganizationSord.stream().map(o -> {
                o.setCreateTime(null); // 除去排序后的值
                o.setStatus(null);
                o.setOrder(null);
                o.setParentId(null);
                return o;
            }).collect(Collectors.toList());
            return treeOrganizationFilter;
        });
    }

    /*********************************************** ihr同步start *****************************************************/
    @Override
    public void ihrSyncBatchInsert(List<Organization> organizations, String fileName) {
        LOGGER.info("ihrSyncBatchInsert 开始... organizations size: {}", organizations.size());
        // step1 过滤异常数据1:ihrCode=null 2:parentCode=null 3:ihrCode=parentCode 4:ihrCode已存在
        Set<String> ihrCodes = new HashSet<>();
        List<Organization> orgList = new ArrayList<>();
        organizations.forEach(o -> {
            if (o.getIhrCode() != null && o.getParentCode() != null && !o.getIhrCode().equals(o.getParentCode()) && !ihrCodes.contains(o.getIhrCode())) {
                orgList.add(o);
                ihrCodes.add(o.getIhrCode());
            }
        });
        LOGGER.info("ihrSyncBatchInsert 过滤异常数据 organizations size: {}", orgList.size());

        // step2 过滤数据库中存在的ihrCode
        List<String> existIhrCodes = organizationDao.execute(d -> d.select(ORGANIZATION.IHR_CODE)
                                                                   .from(ORGANIZATION)
                                                                   .where(ORGANIZATION.IHR_CODE.in(ihrCodes))
                                                                   .fetch(ORGANIZATION.IHR_CODE)
        );
        List<Organization> list = orgList.stream().filter(o -> !existIhrCodes.contains(o.getIhrCode())).collect(Collectors.toList());
        orgList.clear();
        ihrCodes.removeAll(existIhrCodes);
        LOGGER.info("ihrSyncBatchInsert 过滤数据库中存在的ihrCode organizations size: {}", list.size());

        // step3 查找父节点
        Set<String> parentIhrCode = list.stream().map(Organization::getParentCode)
                                        .filter(c -> !ihrCodes.contains(c)).collect(Collectors.toSet());
        LOGGER.info("ihrSyncBatchInsert 需要从数据库中找寻的父节点 parentIhrCode size: {}", parentIhrCode.size());
        Map<String, Organization> parentMap = organizationDao.fetch(ORGANIZATION.IHR_CODE.in(parentIhrCode)).stream()
                                                             .collect(Collectors.toMap(Organization::getIhrCode, o -> o, (o1, o2) -> o1));
        LOGGER.info("ihrSyncBatchInsert 找到的父节点 organizationMap size: {}", parentMap.size());

        // step4 将组织树排序,确保父节点比子节点先计算(虽然同一批次ihr树深度相同,但ihr数据不完全可信,所以仍做排序)
        List<Organization> sortList = OrganizationUtil.treeOrganizationsByIhrCode(list);
        list.clear();

        /* step5 组装数据,思路:因为sortList是有序的,所以父结点永远比子节点先算出来,先放进organizationMap中;
         * 如果organizationMap中不存在父节点,说明该父节点没有通过验证或者父节点不存在,那么该父节点下面所有
         * 的子节点都会添加失败,这样就避免了去递归找父节点下面所有的子节点 */
        Map<String, Integer> orderMap = new HashMap<>();
        for (Organization o : sortList) {
            Organization p = parentMap.get(o.getParentCode());
            if (p != null && p.getPath() != null) {
                o.forInsert();
                // ihr新增节点的code与ihrCode相同
                o.setCode(o.getIhrCode());
                o.setParentId(p.getId());
                o.setPath(p.getPath() + o.getId() + COMMA);
                o.setDepth(p.getDepth() + 1);
                o.setCmccLevel(o.getLevel() == null ? Organization.LEVEL_DEPARTMENT : o.getCmccLevel());
                o.setLevel(o.getLevel() == null ? Organization.LEVEL_DEPARTMENT : o.getLevel());
                o.setStatus(o.getStatus() == null ? Organization.STATUS_ENABLED : o.getStatus());
                o.setCompanyId(o.getLevel() < Organization.LEVEL_DEPARTMENT ? o.getId() : p.getCompanyId());
                o.setType(p.getType() == null ? Organization.TYPE_INNER : p.getType());
                o.setMisCode("ihr");

                // 获取排序
                Integer order = orderMap.get(p.getId()) != null ? orderMap.get(p.getId()) :
                    organizationDao.execute(d -> d.select(ORGANIZATION.ID.count()).from(ORGANIZATION)
                                                  .where(ORGANIZATION.PARENT_ID.eq(p.getId())).fetchOne(ORGANIZATION.ID.count()));
                o.setOrder(order + 1);
                orderMap.put(p.getId(), order + 1);

                parentMap.put(o.getIhrCode(), o);
                orgList.add(o);
            }
        }
        LOGGER.info("ihrSyncBatchInsert 要新增的组织 orgList size: {}", orgList.size());
        organizationDao.insert(orgList);

        cache.clear(CACHE_FAKE_ENABLED_ORGANIZATIONIDS);

        // 返回成功失败记录
        List<String> successIds = orgList.stream().map(Organization::getIhrItemId).collect(Collectors.toList());
        List<String> failedIds = organizations.stream().map(Organization::getIhrItemId).collect(Collectors.toList());
        failedIds.removeAll(successIds);
        LOGGER.info("ihrBatchInsert 批量插入组织成功,成功数量{},失败数据{}", successIds.size(), failedIds.size());
        if (!orgList.isEmpty()) {
            messageSender.send(
                MessageTypeContent.SYSTEM_ORGANIZATION_BATCH_INSERT,
                MessageHeaderContent.IDS, Joiner.on(",").join(orgList.stream().map(Organization::getId).collect(Collectors.toList())));
        }

        // ihr批同步组织完成,发送回调通知
        messageSender.send(MessageTypeContent.IHR_SYNC_ORGANIZATION_OVER_NOTICE,
            MessageHeaderContent.IHR_SYNC_STATUS, "1",
            MessageHeaderContent.IHR_FILE_NAME, fileName,
            MessageHeaderContent.IHR_SYNC_ITEM_SUCCESS_IDS, Joiner.on(",").join(successIds),
            MessageHeaderContent.IHR_SYNC_ITEM_FAILURE_IDS, Joiner.on(",").join(failedIds)
        );
    }

    @Override
    public void ihrSyncBatchUpdate(List<Organization> list, String fileName, Integer updateType) {
        List<String> failedIds = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        list.forEach(o -> {
            boolean status;
            try {
                status = ihrUpdate(o, updateType);
            } catch (Exception e) {
                status = false;
                LOGGER.error("ihrSyncBatchUpdate org id:" + o.getIhrItemId());
                LOGGER.error(e.toString());
            }
            if (status) {
                successIds.add(o.getIhrItemId());
            } else {
                failedIds.add(o.getIhrItemId());
            }
        });
        LOGGER.info("ihr批量修改组织完成,成功数量{},失败数据{}", successIds.size(), failedIds.size());

        // ihr批同步组织完成,发送回调通知
        messageSender.send(MessageTypeContent.IHR_SYNC_ORGANIZATION_OVER_NOTICE,
            MessageHeaderContent.IHR_SYNC_STATUS, "1",
            MessageHeaderContent.IHR_FILE_NAME, fileName,
            MessageHeaderContent.IHR_SYNC_ITEM_SUCCESS_IDS, Joiner.on(",").join(successIds),
            MessageHeaderContent.IHR_SYNC_ITEM_FAILURE_IDS, Joiner.on(",").join(failedIds)
        );
    }

    @Override
    public void ihrSyncBatchDisable(List<Organization> list, String fileName, Integer updateType) {
        List<String> failedIds = new ArrayList<>();
        List<String> successIds = new ArrayList<>();
        List<String> asyncIds = null;
        int count = 0;
        // 4.按oldCode禁用;5.按ihrCode禁用
        if (updateType == 4) {
            Set<String> codes = list.stream().filter(x -> x.getCode() != null).map(Organization::getCode).collect(Collectors.toSet());
            count = organizationDao.execute(x -> x.update(ORGANIZATION).set(ORGANIZATION.STATUS, Organization.STATUS_DISABLED)
                                                  .where(ORGANIZATION.CODE.in(codes))).execute();

            asyncIds = organizationDao.execute(dslContext -> {
                return dslContext.select(Fields.start().add(ORGANIZATION.ID).end())
                                 .from(ORGANIZATION).where(ORGANIZATION.CODE.in(codes)).fetch(ORGANIZATION.ID);
            });
        } else if (updateType == 5) {
            Set<String> ihrCodes = list.stream().filter(x -> x.getIhrCode() != null).map(Organization::getIhrCode).collect(Collectors.toSet());
            count = organizationDao.execute(x -> x.update(ORGANIZATION).set(ORGANIZATION.STATUS, Organization.STATUS_DISABLED)
                                                  .where(ORGANIZATION.CODE.in(ihrCodes))).execute();

            asyncIds = organizationDao.execute(dslContext -> {
                return dslContext.select(Fields.start().add(ORGANIZATION.ID).end())
                                 .from(ORGANIZATION).where(ORGANIZATION.CODE.in(ihrCodes)).fetch(ORGANIZATION.ID);
            });
        }
        Set<String> ihrItems = list.stream().map(Organization::getIhrItemId).collect(Collectors.toSet());
        if (count == 0) {
            failedIds.addAll(ihrItems);
        } else {
            successIds.addAll(ihrItems);
        }

        // ihr批同步组织完成,发送回调通知
        messageSender.send(MessageTypeContent.IHR_SYNC_ORGANIZATION_OVER_NOTICE,
            MessageHeaderContent.IHR_SYNC_STATUS, "1",
            MessageHeaderContent.IHR_FILE_NAME, fileName,
            MessageHeaderContent.IHR_SYNC_ITEM_SUCCESS_IDS, Joiner.on(",").join(successIds),
            MessageHeaderContent.IHR_SYNC_ITEM_FAILURE_IDS, Joiner.on(",").join(failedIds),
            MessageHeaderContent.IHR_SYNC_SUCCESS_IDS, Joiner.on(",").join(asyncIds)

        );
    }

    @Override
    public List<String> getAllOrgIds(int start, int limit) {
        return organizationDao.execute(dslContext -> {
            return dslContext.select(ORGANIZATION.ID).from(ORGANIZATION).limit(start, limit).fetch(ORGANIZATION.ID);
        });
    }

    @Override
    public void findMemberGrantedOrganizationNo(String memberId, String uri, boolean nameComposite) {
        organizationDao.execute(d -> {
            // 所有授权组织,包括包含子结点和不包含子节点
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                                                          .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                          .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                          .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                          .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                          .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                                                          .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(o -> o.getOrganizationId())
                                                                   .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                                                                   .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                                                                   .map(o -> o.getOrganizationId())
                                                                   .collect(Collectors.toSet());
            includeOrganizationIds.addAll(excludeOrganizationIds);
            // 将包含子节点的所有子组织查询出来
            //Condition depthCondition = depth.map(dep -> ORGANIZATION.DEPTH.le(dep)).orElse(ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT));
            List<Organization> grantedOrganization = d.selectDistinct(
                                                          ORGANIZATION.ID,
                                                          ORGANIZATION.PARENT_ID,
                                                          ORGANIZATION.LEVEL,
                                                          ORGANIZATION.NAME,
                                                          ORGANIZATION.CODE,
                                                          ORGANIZATION.DEPTH,
                                                          ORGANIZATION.ORDER,
                                                          ORGANIZATION.STATUS,
                                                          ORGANIZATION.CREATE_TIME
                                                      ).from(ORGANIZATION_DETAIL)
                                                      .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                                                      .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds))
                                                      .fetch(r -> {
                                                          Organization o = new Organization();
                                                          o.setId(r.getValue(ORGANIZATION.ID));
                                                          o.setParentId(r.getValue(ORGANIZATION.PARENT_ID));
                                                          o.setLevel(r.getValue(ORGANIZATION.LEVEL));
                                                          o.setName(r.getValue(ORGANIZATION.NAME));
                                                          o.setCode(r.getValue(ORGANIZATION.CODE));
                                                          o.setDepth(r.getValue(ORGANIZATION.DEPTH));
                                                          o.setStatus(r.getValue(ORGANIZATION.STATUS));
                                                          o.setOrder(r.getValue(ORGANIZATION.ORDER));
                                                          o.setCreateTime(r.getValue(ORGANIZATION.CREATE_TIME));
                                                          return o;
                                                      });
            // 去除禁用的组织
            List<String> replaseOrganizationPIds = grantedOrganization.stream().filter(or -> or.getStatus() != 1).map(Organization::getId).collect(Collectors.toList());
//            List<Organization> replaseOrganizationList = d.selectDistinct(ORGANIZATION.ID).from(ORGANIZATION).where(ORGANIZATION.PARENT_ID.in(replaseOrganizationPIds)).fetchInto(Organization.class);
            List<String> replaseOrganizationList = d.selectDistinct(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL).where(ORGANIZATION_DETAIL.ROOT.in(replaseOrganizationPIds)).fetch(ORGANIZATION_DETAIL.SUB);
            List<Organization> filterOrganizationList = grantedOrganization.stream().filter(or -> or.getStatus() == 1).filter(or -> !replaseOrganizationList.contains(or.getId())).collect(Collectors.toList());
            // 排序
            filterOrganizationList.sort(OrganizationUtil.getOrgComparator());
            List<Organization> orgList = OrganizationUtil.treeOrganizations(filterOrganizationList, nameComposite);
            LOGGER.error("无返回值获取当前用户权限组织：org数量={}", orgList == null ? 0 : orgList.size());
            cache.set(CACHE_GRANTED_ORGANIZATION + memberId + "#" + uri, orgList, 60 * 60);
            return orgList;
        });
    }

    @Override
    public List<Organization> getOrganizationByIds(List<String> ids) {
        return organizationDao.execute(dslContext -> {
            return dslContext.select(
                                 Fields.start()
                                       .add(ORGANIZATION.ID)
                                       .add(ORGANIZATION.NAME)
                                       .add(ORGANIZATION.SHORT_NAME)
                                       .add(ORGANIZATION.PARENT_ID)
                                       .add(ORGANIZATION.STATUS).end())
                             .from(ORGANIZATION)
                             .where(ORGANIZATION.ID.in(ids).and(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))).fetch(record -> {
                    Organization organization = new Organization();
                    organization.setId(record.get(ORGANIZATION.ID));
                    organization.setName(record.get(ORGANIZATION.NAME));
                    organization.setShortName(record.get(ORGANIZATION.SHORT_NAME));
                    organization.setParentId(record.get(ORGANIZATION.PARENT_ID));
                    organization.setStatus(record.get(ORGANIZATION.STATUS));
                    return organization;
                });
        });
    }

    private boolean ihrUpdate(Organization o, Integer updateType) {
        LOGGER.info("ihrUpdate 开始, ihrCode: {}, updateType: {}", o.getIhrCode(), updateType);

        // step1 1.通过旧编码获取组织;2.通过新编码获取组织;3.特殊组织只修改ihrCode
        Optional<Organization> optionalOrganization = Optional.empty();
        if (updateType == 1) {
            optionalOrganization = organizationDao.fetchOne(ORGANIZATION.CODE.eq(o.getCode()));
        } else if (updateType == 2) {
            optionalOrganization = organizationDao.fetchOne(ORGANIZATION.IHR_CODE.eq(o.getIhrCode()));
        } else if (updateType == 3) {
            int record = organizationDao.execute(x -> x.update(ORGANIZATION).set(ORGANIZATION.IHR_CODE, o.getIhrCode())
                                                       .where(ORGANIZATION.CODE.eq(o.getCode()))).execute();
            return record > 0;
        }
        if (!optionalOrganization.isPresent()) {
            return false;
        }
        Organization organization = optionalOrganization.get();
        LOGGER.info("ihrUpdate step1 获取组织, orgId: {}", organization.getId());

        // step2 通过新编码获取父组织
        Optional<Organization> optionalParent = organizationDao.fetchOne(ORGANIZATION.IHR_CODE.eq(o.getParentCode()));
        if (!optionalParent.isPresent()) {
            return false;
        }
        Organization parent = optionalParent.get();
        LOGGER.info("ihrUpdate step2 获取父组织, newParentId: {}", parent.getId());

        // step3 更新组织
        String oldParentId = organization.getParentId();
        String oldPath = organization.getPath();
        Integer oldStatus = organization.getStatus();
        Integer oldOrder = organization.getOrder() == null ? 1 : organization.getOrder();
        // 省公司不修改公司名称
        if (!"10000001".equals(parent.getId())) {
            organization.setName(o.getName());
            organization.setShortName(o.getShortName());
        }
        organization.setIhrCode(o.getIhrCode());
        organization.setCmccAttribute(o.getCmccAttribute());
        organization.setCmccCategory(o.getCmccCategory());
        organization.setCmccLevel(o.getCmccLevel());
        organization.setLevel(o.getLevel());
        organization.setStatus(o.getStatus());
        organization.setParentId(parent.getId());
        organization.setModifyDate(null);
        organization.setPath(parent.getPath() + organization.getId() + COMMA);
        if (parent.getDepth() != null && !parent.getDepth().equals(1)) {
            organization.setDepth(parent.getDepth() + 1);
            organization.setType(parent.getType());
        }
        organizationDao.update(organization);
        LOGGER.info("ihrUpdate step3 更新成功, oldParentId: {}", oldParentId);
        updateReleatedOrgOrder(Organization.UPDATE, organization.getId(), oldOrder, oldParentId);
        // 更新子节点path和depth
        updateReleatedPathAndDepth(organization.getId(), oldPath, organization.getPath());

        // step4 处理组织详情,清除缓存数据
        Map<String, Object> diffMap = new HashMap<>();
        diffMap.put("before", oldParentId);
        diffMap.put("after", parent.getId());
        if (oldParentId != null) {
            List<OrganizationDetail> oldParents = organizationDetailService.find(organization.getId());
            List<String> parentIds = new ArrayList<>();
            if (!oldParents.isEmpty()) {
                oldParents.forEach(p -> parentIds.add(p.getRoot()));
            }
            diffMap.put("oldParentIds", parentIds);
        }
        cache.clear(CACHED_COUNT_PARENT + organization.getId());
        cache.clear(CACHE_FAKE_ENABLED_ORGANIZATIONIDS);
        // 如果父节点或状态变更，清除所有子节点缓存
        if (!org.apache.commons.lang.StringUtils.equals(oldParentId, organization.getParentId()) || !oldStatus.equals(organization.getStatus())) {
            clearAllSubCache(organization.getParentId());
        }
        messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_UPDATE, diffMap, MessageHeaderContent.ID, organization.getId());
        LOGGER.info("ihrUpdate step4 清除缓存数据,处理组织详情消息发送完成");
        return true;
    }

    /*********************************************** ihr同步end *****************************************************/
    @Override
    public String getRootOrganizationId(String id) {
        Organization organization = organizationDao.getOptional(id).orElse(null);
        if (organization == null) {
            return "";
        }
        String[] strArray = organization.getPath().split(",");
        return strArray[0];
    }

    @Override
    public List<Organization> findOrgListVirtualSpace(String currentUserId,
                                                      String uri,
                                                      Optional<Boolean> supportMore,
                                                      Optional<Integer> level,
                                                      Optional<Boolean> asParent,
                                                      Optional<String> name,
                                                      Optional<String> code,
                                                      Optional<Integer> type,
                                                      Optional<String> excludeId,
                                                      Optional<Boolean> allStatus) {
        return organizationDao.execute(d -> {
            LOGGER.info("【findOrgList开始】");
            Condition listCondition = GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)
                                                                   .or(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_NO).and(GRANT_ORGANIZATION.ORGANIZATION_ID.eq(ORGANIZATION.ID)));
            List<Condition> conditions = Stream.of(
                level.map(ORGANIZATION.LEVEL::le),
                name.map(ORGANIZATION.NAME::contains),
                code.map(ORGANIZATION.CODE::contains),
                type.map(t -> t.equals(Organization.TYPE_OUTER) ? ORGANIZATION.TYPE.eq(t) : ORGANIZATION.TYPE.le(t))
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            Optional.ofNullable(uri).ifPresent(ur-> conditions.add(MENU.URI.eq(ur).or(MENU.ID.eq(ur))));
            conditions.add(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()));
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> step = a -> a.from(GRANT)
                                                                                         .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(currentUserId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                                                                                         .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                                                                                         .innerJoin(MENU).on(MENU.ID.eq(ROLE_MENU.MENU_ID))
                                                                                         .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                                                                                         .leftJoin(ORGANIZATION.as("grant_org")).on(ORGANIZATION.as("grant_org").ID.eq(GRANT_ORGANIZATION.ORGANIZATION_ID))
                                                                                         .leftJoin(ORGANIZATION).on(ORGANIZATION.PATH.startsWith(ORGANIZATION.as("grant_org").PATH))
                                                                                         .where(conditions);
            conditions.add(ORGANIZATION.DEPTH.le(level.orElse(Organization.LEVEL_DEPARTMENT)));
            // 查询列表
            List<Organization> orgs = step.apply(d.selectDistinct(ORGANIZATION.fields())).and(listCondition).fetchInto(Organization.class);
            LOGGER.info("【查询组织数据结束】");

            // 设置是否可以展开
            if (supportMore.isPresent() && supportMore.get() && !name.isPresent() && !code.isPresent()) {
                // 找出所有5层的已经作为父节点的组织id,用于判断具体的某一个节点是否可以展开
                List<String> parentIds = d.selectDistinct(ORGANIZATION.PARENT_ID).from(ORGANIZATION)
                                          .where(ORGANIZATION.DEPTH.eq(Organization.LEVEL_DEPARTMENT)).fetch(ORGANIZATION.PARENT_ID);
                // 找出当前用户拥有的第五层的,包含子节点的组织id,用于判断第五层的节点,是否可以展开(对于第五层的节点,第一要满足是父节点才可以展开,并且第五层的节点,在权限上必须要包含子节点才可以展开)
                List<String> childFindYesIds = step.apply(d.selectDistinct(Fields.start().add(ORGANIZATION.ID).end()))
                                                   .and(GRANT_ORGANIZATION.CHILD_FIND.eq(GrantOrganization.CHILD_FIND_YES)).fetch(ORGANIZATION.ID);
                parentIds.retainAll(childFindYesIds);
                orgs.forEach(o -> o.setIsParent(parentIds.contains(o.getId())));
            }
            LOGGER.info("【处理展开标识结束】");

            // 排序
            orgs.sort(OrganizationUtil.getOrgComparator());
            LOGGER.info("【组织排序结束】");

            // 去除禁用
            List<Organization> filterList = allStatus.map(s -> filterOrg(orgs, s)).orElseGet(() -> filterOrg(orgs, Boolean.FALSE));
            LOGGER.info("【禁用组织过滤结束】");
            return filterList;
        });
    }

    @Override
    public void handleOrganizationVaildDate() {
        //大于截止日期禁用
        List<String> list = organizationDao.execute(r -> r.select(ORGANIZATION.ID).from(ORGANIZATION).
                where(ORGANIZATION.VALIDITY_END_TIME.le(System.currentTimeMillis()).
                                and(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED)))).fetch(ORGANIZATION.ID);

        //小于开始时间禁用
        List<String> idList = organizationDao.execute(r ->
                r.select(ORGANIZATION.ID).from(ORGANIZATION)
                        .where(ORGANIZATION.VALIDITY_START_TIME.ge(System.currentTimeMillis())
                                .and(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED))))
                .fetch(ORGANIZATION.ID);

        list.addAll(idList);

        if (!CollectionUtils.isEmpty(list)) {
            organizationDao.execute(r -> r.update(ORGANIZATION).set(ORGANIZATION.STATUS, Organization.STATUS_DISABLED).where(ORGANIZATION.ID.in(list))).execute();
            for (String id : list) {
                messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_UPDATE, MessageHeaderContent.ID, id);
            }
        }

        //启用 被手工禁用过不允许启用
        List<String> listStatusEnable = organizationDao.execute(r -> r.select(ORGANIZATION.ID).from(ORGANIZATION).
                where(ORGANIZATION.VALIDITY_END_TIME.ge(System.currentTimeMillis())
                        .and(ORGANIZATION.VALIDITY_START_TIME.le(System.currentTimeMillis())
                                .and(ORGANIZATION.STATUS.eq(Organization.STATUS_DISABLED))
                                .and(ORGANIZATION.VALIDITY_STATUS.ne(Organization.STATUS_DISABLE)))).fetch(ORGANIZATION.ID));
        if (!CollectionUtils.isEmpty(listStatusEnable)) {
            organizationDao.execute(r -> r.update(ORGANIZATION).set(ORGANIZATION.STATUS, Organization.STATUS_ENABLED).where(ORGANIZATION.ID.in(listStatusEnable))).execute();
            for (String id : listStatusEnable) {
                messageSender.send(MessageTypeContent.SYSTEM_ORGANIZATION_UPDATE, MessageHeaderContent.ID, id);
            }
        }

    }


    /**
     * 获取指定
     *
     * @param parentId
     * @return java.util.List<com.zxy.product.system.entity.Organization>
     * @throws
     * @method getOrganizationByParentId
     * <AUTHOR> Qian
     * @version 1.0
     * @date 2024/12/10 18:14
     */
    @Override
    public List<Organization> getOrganizationByParentId(String parentId, Integer depth) {
        Condition condition = depth == 4 ? ORGANIZATION.DEPTH.eq(depth) : ORGANIZATION.DEPTH.in(depth, depth + 1);
        return organizationDao.execute(d -> d.select(Fields.start()
                        .add(ORGANIZATION).end())
                .from(ORGANIZATION)
                .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                .where(ORGANIZATION_DETAIL.ROOT.eq(parentId).and(condition).and(ORGANIZATION.STATUS.eq(Organization.STATUS_ENABLED)))
                .orderBy(ORGANIZATION.CREATE_TIME.desc()).fetchInto(Organization.class));

    }


    /**
     * 根据id获取组织信息
     *
     * @param id id
     * @return com.zxy.product.system.entity.Organization
     * @throws
     * @method get
     * <AUTHOR> Qian
     * @version 1.0
     * @date 2024/12/12 14:19
     */
    @Override
    public Organization getByPriId(String id){
        return organizationDao.get(id);
    }

    @Override
    public List<Organization> getByMemberOrganization(String memberId, String uri, boolean nameComposite, Optional<Integer> depth, Optional<Integer> type) {
        return organizationDao.execute(d -> {
            // 所有授权组织,包括包含子结点和不包含子节点
            List<GrantOrganization> grantOrganizations = d.select(GRANT_ORGANIZATION.fields()).from(GRANT)
                    .innerJoin(GRANT_MEMBER).on(GRANT_MEMBER.MEMBER_ID.eq(memberId), GRANT_MEMBER.GRANT_ID.eq(GRANT.ID))
                    .leftJoin(ROLE_MENU).on(ROLE_MENU.ROLE_ID.eq(GRANT.ROLE_ID))
                    .innerJoin(MENU).on(MENU.URI.eq(uri), MENU.ID.eq(ROLE_MENU.MENU_ID))
                    .leftJoin(GRANT_ORGANIZATION).on(GRANT_ORGANIZATION.GRANT_ID.eq(GRANT.ID))
                    .where(GRANT.VALID_DATE.ge(System.currentTimeMillis()).or(GRANT.VALID_DATE.isNull()))
                    .fetchInto(GrantOrganization.class);
            // 获取不包含子节点的组织
            Set<String> excludeOrganizationIds = grantOrganizations.stream()
                    .filter(o -> !GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                    .map(GrantOrganization::getOrganizationId)
                    .collect(Collectors.toSet());
            // 获取包含子节点的组织
            Set<String> includeOrganizationIds = grantOrganizations.stream()
                    .filter(o -> GrantOrganization.CHILD_FIND_YES.equals(o.getChildFind()))
                    .map(GrantOrganization::getOrganizationId)
                    .collect(Collectors.toSet());
            includeOrganizationIds.addAll(excludeOrganizationIds);
            // 将包含子节点的所有子组织查询出来
            //Condition depthCondition = depth.map(dep -> ORGANIZATION.DEPTH.le(dep)).orElse(ORGANIZATION.DEPTH.le(OrganizationUtil.ORG_DEPTH_LIMIT));
            Condition typeCondition = type.map(t -> t == 1 ? ORGANIZATION.TYPE.le(t) : ORGANIZATION.TYPE.eq(t)).orElse(DSL.trueCondition());
            List<Organization> grantedOrganization = d.selectDistinct(
                            ORGANIZATION.ID,
                            ORGANIZATION.NAME,
                            ORGANIZATION.CODE
                    ).from(ORGANIZATION_DETAIL)
                    .innerJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                    .where(ORGANIZATION_DETAIL.ROOT.in(includeOrganizationIds), typeCondition)
                    .fetch(r -> {
                        Organization o = new Organization();
                        o.setId(r.getValue(ORGANIZATION.ID));
                        o.setName(r.getValue(ORGANIZATION.NAME));
                        o.setCode(r.getValue(ORGANIZATION.CODE));
                        return o;
                    });
            // 去除禁用的组织
            List<String> replaseOrganizationPIds = grantedOrganization.stream().filter(or -> or.getStatus() != 1).map(Organization::getId).collect(Collectors.toList());
//            List<Organization> replaseOrganizationList = d.selectDistinct(ORGANIZATION.ID).from(ORGANIZATION).where(ORGANIZATION.PARENT_ID.in(replaseOrganizationPIds)).fetchInto(Organization.class);
            List<String> replaseOrganizationList = d.selectDistinct(ORGANIZATION_DETAIL.SUB).from(ORGANIZATION_DETAIL).where(ORGANIZATION_DETAIL.ROOT.in(replaseOrganizationPIds)).fetchInto(String.class);
            List<Organization> filterOrganizationList = grantedOrganization.stream().filter(or -> or.getStatus() == 1).filter(or -> !replaseOrganizationList.contains(or.getId())).collect(Collectors.toList());
            // 排序
            filterOrganizationList.sort(OrganizationUtil.getOrgComparator());
            List<Organization> orgList = OrganizationUtil.treeOrganizations(filterOrganizationList, nameComposite);
//            cache.set(CACHE_GRANTED_ORGANIZATION + memberId + "#" + uri, orgList, 60 * 60);
            // 组装树
            return orgList;
        });
    }
}
