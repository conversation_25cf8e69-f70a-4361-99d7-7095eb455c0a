package com.zxy.product.system.service.support.homeconfig;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.homeconfig.HomeContentService;
import com.zxy.product.system.entity.HomeCfgCacheItem;
import com.zxy.product.system.entity.HomeContent;
import com.zxy.product.system.entity.HomeContentImage;
import com.zxy.product.system.entity.HomeModuleConfig;
import com.zxy.product.system.jooq.tables.pojos.HomeContentEntity;
import com.zxy.product.system.util.HomeCfgUtil;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.impl.DSL;
import org.nlpcn.commons.lang.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;

import static com.zxy.product.system.jooq.Tables.HOME_CONTENT;
import static com.zxy.product.system.jooq.Tables.HOME_CONTENT_IMAGE;

/**
 * Created by chengzhi on 17/3/13.
 */
@Service
public class HomeContentServiceSupport implements HomeContentService {

    private CommonDao<HomeContent> homeContentDao;

    private CommonDao<HomeContentImage> homeContentImageDao;

    private MongoTemplate mongoTemplate;

    @Autowired
	public void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Autowired
    public void setHomeContentDao(CommonDao<HomeContent> homeContentDao) {
        this.homeContentDao = homeContentDao;
    }

    @Autowired
    public void setHomeContentImageDao(CommonDao<HomeContentImage> homeContentImageDao) {
        this.homeContentImageDao = homeContentImageDao;
    }

    @Override
    public PagedResult<HomeContent> findPages(int page, int pageSize, String moduleConfigId , Integer clientType) {

        Field<String> image = HOME_CONTENT_IMAGE.IMAGE.as("image");
        Field<String> imagePath = HOME_CONTENT_IMAGE.IMAGE_PATH.as("imagePath");

        return homeContentDao.fetchPage(page, pageSize,
                e -> e.selectDistinct(Fields.start().add(HOME_CONTENT.fields()).add(image).add(imagePath).end()).from(HOME_CONTENT)
                        .leftJoin(HOME_CONTENT_IMAGE).on(HOME_CONTENT_IMAGE.CONTENT_ID.eq(HOME_CONTENT.ID)
                                .and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(clientType == 2 ? HomeContentImage.IMAGE_CLIENT_TYPE_APP :HomeContentImage.IMAGE_CLIENT_TYPE_PC)))
                        .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId))
                        .orderBy(clientType == 2 ? HOME_CONTENT.APP_SORT.asc() :HOME_CONTENT.SORT.asc(), HOME_CONTENT.CREATE_TIME.desc()),
                record -> {
                    HomeContent homeContent = record.into(HomeContent.class);
                    homeContent.setImage(record.getValue(image));
                    homeContent.setImagePath(record.getValue(imagePath));
                    return homeContent;
                });
    }

    @Override
    public List<HomeContent> insertBatch(String moduleConfigId, List<HomeContent> list) {
        int sort = getMaxSort(moduleConfigId,1);
//        int appSort =getMaxSort(moduleConfigId,2);
        for (HomeContent r : list) {
            if (findOne(moduleConfigId, r.getDataId()).isPresent()) {
                continue;
            }
            sort++;
//            appSort++;
            r.setModuleConfigId(moduleConfigId);
            r.setSort(sort);
            r.setAppSort(sort);
            r.forInsert();
            homeContentDao.insert(r);
        }
        return list;
    }

    @Override
    public String delete(String id) {
        HomeContent homeContent = homeContentDao.get(id);
//        this.deleteContentImage(homeContent.getModuleConfigId(),Optional.empty());
        homeContentDao.delete(id);
        return id;
    }

    @Override
    public List<HomeContent> findList(String moduleConfigId, int size, Integer clientType, Optional<Integer> dataType) {
        Field<String> image = HOME_CONTENT_IMAGE.IMAGE.as("image");
        Field<String> imagePath = HOME_CONTENT_IMAGE.IMAGE_PATH.as("imagePath");
        return homeContentDao.execute(e -> {
            Condition whereCondition = HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId);
            if (dataType.isPresent()) {
                whereCondition = whereCondition.and(HOME_CONTENT.DATA_TYPE.eq(dataType.get()));
            }
            return e.selectDistinct(Fields.start().add(HOME_CONTENT.fields()).add(image).add(imagePath).end())
                .from(HOME_CONTENT).leftJoin(HOME_CONTENT_IMAGE).on(HOME_CONTENT_IMAGE.CONTENT_ID.eq(HOME_CONTENT.ID)
                        .and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(clientType == 2 ? HomeContentImage.IMAGE_CLIENT_TYPE_APP :HomeContentImage.IMAGE_CLIENT_TYPE_PC)))
                .where(whereCondition)
                .orderBy(clientType == 2 ? HOME_CONTENT.APP_SORT.asc() :HOME_CONTENT.SORT.asc(), HOME_CONTENT.CREATE_TIME.desc()).limit(0, size).fetch(record -> {
                    HomeContent homeContent = record.into(HomeContent.class);
                    homeContent.setImage(record.getValue(image));
                    homeContent.setImagePath(record.getValue(imagePath));
                    return homeContent;
                });});
    }

    public void deleteContentImage(String moduleConfigId, Optional<Integer> clientType){
        Field<String> contentId = HOME_CONTENT.ID.as("id");
        List<String> ids = homeContentDao.execute(e -> e.select(Fields.start().add(contentId).end())
                .from(HOME_CONTENT)
                .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId)).fetch(contentId));
        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.in(ids)
                .and(clientType.map(HOME_CONTENT_IMAGE.CLIENT_TYPE::eq).orElse(DSL.trueCondition())));
    }

    @Override
    public int getMaxSort(String moduleConfigId , Integer clientType) {
        Field<Integer> maxSort = clientType == 2 ? HOME_CONTENT.APP_SORT.max() :HOME_CONTENT.SORT.max();
        return homeContentDao.execute(e -> e.select(Fields.start().add(maxSort).end()).from(HOME_CONTENT)
                .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId))).fetchOptional(maxSort).orElse(0);
    }

    @Override
    public String updateSortById(String id, int sort , Integer clientType) {
        HomeContent homeContent = homeContentDao.get(id);
        Field<Integer> sortField = clientType == 2 ? HOME_CONTENT.APP_SORT :HOME_CONTENT.SORT;
        Integer currentSort = clientType == 2 ? homeContent.getAppSort() : homeContent.getSort();
        int maxSort = getMaxSort(homeContent.getModuleConfigId() , clientType);
        if (sort >= currentSort) {
            homeContentDao.execute(e -> e.update(HOME_CONTENT)
                    .set(sortField,
                            DSL.when(sortField.isNull(), 1).otherwise(sortField).minus(1))
                    .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(homeContent.getModuleConfigId()).and(HOME_CONTENT.ID.ne(id))
                            .and(sortField.gt(currentSort)).and(sortField.le(sort)))
                    .execute());
            if(clientType == 2 ){
                homeContent.setAppSort(sort > maxSort ? maxSort : sort);
            } else {
                homeContent.setSort(sort > maxSort ? maxSort : sort);
            }
        } else {
            homeContentDao.execute(e -> e.update(HOME_CONTENT)
                    .set(sortField, DSL.when(sortField.isNull(), 1).otherwise(sortField).add(1))
                    .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(homeContent.getModuleConfigId()).and(HOME_CONTENT.ID.ne(id))
                            .and(sortField.ge(sort)).and(sortField.lt(currentSort)))
                    .execute());
            if(clientType == 2 ){
                homeContent.setAppSort(sort);
            } else {
                homeContent.setSort(sort);
            }
        }
        homeContentDao.update(homeContent);

//        this.deleteContentImage(homeContent.getModuleConfigId(),Optional.of(clientType));
        return id;
    }

    @Override
    public Optional<HomeContent> findOne(String moduleConfigId, String dataId) {
        return homeContentDao.execute(e -> e.select(Fields.start().add(HOME_CONTENT).end()).from(HOME_CONTENT)
                .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId).and(HOME_CONTENT.DATA_ID.eq(dataId)))
                .fetchOptional(record -> {
                    HomeContent hc = record.into(HomeContent.class);
                    return hc;
                }));
    }

    @Override
    public String updateImage(String id, String imageId,String imageIdPath, Integer clientType) {
        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(id).and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(clientType)));
        HomeContentImage homeContentImage = new HomeContentImage();
        homeContentImage.forInsert();
        homeContentImage.setContentId(id);
        homeContentImage.setClientType(clientType);
        homeContentImage.setImage(imageId);
        homeContentImage.setImagePath(imageIdPath);
        homeContentImageDao.insert(homeContentImage);
        return id;
    }

	@Override
	public List<HomeContent> findMoreList(String moduleConfigId, Integer clientType) {
	    Field<String> image = HOME_CONTENT_IMAGE.IMAGE.as("image");
	    Field<String> imagePath = HOME_CONTENT_IMAGE.IMAGE_PATH.as("imagePath");
        return homeContentDao.execute(e -> e.selectDistinct(Fields.start().add(HOME_CONTENT.fields()).add(image).add(imagePath).end())
                .from(HOME_CONTENT).leftJoin(HOME_CONTENT_IMAGE).on(HOME_CONTENT_IMAGE.CONTENT_ID.eq(HOME_CONTENT.ID)
                        .and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(clientType == 2 ? HomeContentImage.IMAGE_CLIENT_TYPE_APP :HomeContentImage.IMAGE_CLIENT_TYPE_PC)))
                .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId))
                .orderBy(clientType == 2 ? HOME_CONTENT.APP_SORT.asc() :HOME_CONTENT.SORT.asc(), HOME_CONTENT.CREATE_TIME.desc()).fetch(record -> {
                    HomeContent homeContent = record.into(HomeContent.class);
                    homeContent.setImage(record.getValue(image));
                    homeContent.setImagePath(record.getValue(imagePath));
                    return homeContent;
                }));
	}

	@Override
    public PagedResult<HomeContent> findPagesFromCache(String userId, String userToken, int page, int pageSize
    		, String moduleConfigId , Integer clientType) {
    	HomeModuleConfig homeModuleCfg = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_MODULE, null, null
    			, moduleConfigId, mongoTemplate);
    	Query queryParas = HomeCfgUtil.getQuery(userId, userToken, HomeCfgCacheItem.M_CONTENT
    			, homeModuleCfg.getHomeConfigId(), moduleConfigId, true);
    	if (clientType == HomeContentImage.IMAGE_CLIENT_TYPE_APP) {
        	queryParas.with(new Sort(new Order("data.appSort"), new Order(Direction.DESC, "data.createTime"), new Order(Direction.DESC, "data._createTime")));
    	}else {
        	queryParas.with(new Sort(new Order("data.sort"), new Order(Direction.DESC, "data.createTime"), new Order(Direction.DESC, "data._createTime")));
    	}

    	PagedResult<HomeContent> pagedResult = HomeCfgUtil.findAndLoadPageFromCache(userId, userToken, page, pageSize, HomeCfgCacheItem.M_CONTENT
    			, homeModuleCfg.getHomeConfigId(), moduleConfigId, mongoTemplate, p -> {
    		com.zxy.product.system.jooq.tables.HomeContentImage appCntImg = HOME_CONTENT_IMAGE.as("appCntImg");
            Field<String> appImage = appCntImg.IMAGE.as("appImage");
            Field<String> appImagePath = appCntImg.IMAGE_PATH.as("appImagePath");

            return homeContentDao.fetchPage(p, 20,
                    e -> e.selectDistinct(Fields.start().add(HOME_CONTENT.fields()).add(HOME_CONTENT_IMAGE.IMAGE)
                    		.add(HOME_CONTENT_IMAGE.IMAGE_PATH).add(appImage).add(appImagePath).end()).from(HOME_CONTENT)
                            .leftJoin(HOME_CONTENT_IMAGE).on(HOME_CONTENT_IMAGE.CONTENT_ID.eq(HOME_CONTENT.ID)
                                    .and(HOME_CONTENT_IMAGE.CLIENT_TYPE.ne(HomeContentImage.IMAGE_CLIENT_TYPE_APP)))
                            .leftJoin(appCntImg).on(appCntImg.CONTENT_ID.eq(HOME_CONTENT.ID)
                            	.and(appCntImg.CLIENT_TYPE.eq(HomeContentImage.IMAGE_CLIENT_TYPE_APP)))
                            .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId)),
                    record -> {
                        HomeContent homeContent = record.into(HomeContent.class);
                        homeContent.setImage(record.getValue(HOME_CONTENT_IMAGE.IMAGE));
                        homeContent.setImagePath(record.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
                        homeContent.setAppImage(record.getValue(appImage));
                        homeContent.setAppImagePath(record.getValue(appImagePath));
                        return homeContent;
                    });
    	}, queryParas, (HomeContent c) -> {return c.getId();});

    	if (pagedResult != null && pagedResult.getItems() != null) {
    		for (HomeContent cnt : pagedResult.getItems()) {
                if (clientType == HomeContentImage.IMAGE_CLIENT_TYPE_APP) {
                    cnt.setImage(cnt.getAppImage());
                    cnt.setImagePath(cnt.getAppImagePath());
                }
    		}
    	}
    	return pagedResult;
    }

	@Override
	public void saveAsFinal(String userId, String userToken, String homeConfigId) {
		HomeCfgUtil.saveListAsFinal(userId, userToken, HomeCfgCacheItem.M_CONTENT
				, homeConfigId, null, mongoTemplate
				, (HomeCfgCacheItem<HomeContent> item) -> {
					delete(item.getData().getId());
		            HomeContentImage image = item.getData().getContentImage();
		            if (image != null) {
				        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(item.getData().getId())
				        		.and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(image.getClientType())));
		            }
		            HomeContentImage appImage = item.getData().getAppContentImage();
		            if (appImage != null) {
				        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(item.getData().getId())
				        		.and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(appImage.getClientType())));
		            }
				}
				, (HomeCfgCacheItem<HomeContent> item) -> {
                    HomeContent homeContent = item.getData();
                    homeContentDao.insert(homeContent);
		            HomeContentImage image = homeContent.getContentImage();
		            if (image != null) {
				        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(homeContent.getId())
				        		.and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(image.getClientType())));
				        homeContentImageDao.insert(image);
		            } else {
                        if (!ObjectUtils.isEmpty(homeContent.getImagePath())) {
                            image = new HomeContentImage();
                            image.forInsert();
                            image.setContentId(homeContent.getId());
                            image.setClientType(HomeModuleConfig.CLIENT_TYPE_PC);
                            image.setImage(homeContent.getImage());
                            image.setImagePath(homeContent.getImagePath());
                            homeContentImageDao.insert(image);
                        }
                    }
		            HomeContentImage appImage = homeContent.getAppContentImage();
		            if (appImage != null) {
				        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(homeContent.getId())
				        		.and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(appImage.getClientType())));
				        homeContentImageDao.insert(appImage);
		            } else {
                        if (!ObjectUtils.isEmpty(homeContent.getAppImagePath())) {
                            image = new HomeContentImage();
                            image.forInsert();
                            image.setContentId(homeContent.getId());
                            image.setClientType(HomeModuleConfig.CLIENT_TYPE_APP);
                            image.setImage(homeContent.getAppImage());
                            image.setImagePath(homeContent.getAppImagePath());
                            homeContentImageDao.insert(image);
                        }
                    }

		            /*
					Map<String, Object> mapExt = item.getExtData();
					if (item.getData() != null && mapExt != null) {
						HomeContentImage homeContentImage = (HomeContentImage) mapExt.get("img");
						if (homeContentImage != null) {
					        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(item.getData().getId())
					        		.and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(homeContentImage.getClientType())));
					        homeContentImage.forInsert();
					        homeContentImageDao.insert(homeContentImage);
						}
					}
					*/
				}
				, (HomeCfgCacheItem<HomeContent> item) -> {
		            HomeContentImage image = item.getData().getContentImage();
		            if (image != null) {
				        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(item.getData().getId())
				        		.and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(image.getClientType())));
				        homeContentImageDao.insert(image);
		            }
		            HomeContentImage appImage = item.getData().getAppContentImage();
		            if (appImage != null) {
				        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(item.getData().getId())
				        		.and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(appImage.getClientType())));
				        homeContentImageDao.insert(appImage);
		            }

					/*
					Map<String, Object> mapExt = item.getExtData();
					if (item.getData() != null && mapExt != null) {
						HomeContentImage homeContentImage = (HomeContentImage) mapExt.get("img");
						if (homeContentImage != null) {
					        homeContentImageDao.delete(HOME_CONTENT_IMAGE.CONTENT_ID.eq(item.getData().getId())
					        		.and(HOME_CONTENT_IMAGE.CLIENT_TYPE.eq(homeContentImage.getClientType())));
					        homeContentImage.forInsert();
					        homeContentImageDao.insert(homeContentImage);
						}
					}
					*/
				}
				, (HomeCfgCacheItem<HomeContent> item) -> {
			        homeContentDao.update(item.getData());
				});
	}


    @Override
    public List<HomeContent> insertBatchToCache(String userId, String userToken
    		, String moduleConfigId, List<HomeContent> list){
    	HomeModuleConfig homeModuleCfg = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_MODULE, null, null
    			, moduleConfigId, mongoTemplate);
    	int sort = HomeCfgUtil.getMaxSortInCache(userId, userToken
    			, HomeCfgCacheItem.M_CONTENT, null, moduleConfigId, mongoTemplate, (HomeContent content) -> {
    				return content.getSort();
    			});
        for (HomeContent r : list) {
            if(r.getId()!=null){
                HomeCfgUtil.updateItemDataExtAndUrlAndDataNameAndSort(userId,userToken,HomeCfgCacheItem.M_CONTENT,homeModuleCfg.getHomeConfigId()
                ,moduleConfigId,r.getId(),r.getDataExt(),r.getDataName(),r.getUrl(),r.getSort(),r.getAppSort(),mongoTemplate);
            }else {
                // 图片类型，互动学习和好书推荐类型无dataId,赋随机UUID
                if (HomeContent.DATA_TYPE_IMAGE.equals(r.getDataType()) || HomeContent.DATA_TYPE_INTERACTIVE_LEARNING.equals(r.getDataType())
                        || HomeContent.DATA_TYPE_BOOK_RECOMMENDATION.equals(r.getDataType())) {
                    r.setDataId(java.util.UUID.randomUUID().toString());
                }
                if (HomeCfgUtil.existsByFieldID(userId, userToken, HomeCfgCacheItem.M_CONTENT, null, moduleConfigId
                        , "dataId", r.getDataId(), mongoTemplate)) {
                    continue;
                }
                sort++;
                r.forInsert();
                r.setModuleConfigId(moduleConfigId);
                r.setSort(sort);
                r.setAppSort(sort);
                HomeCfgUtil.addItem(userId, userToken, HomeCfgCacheItem.M_CONTENT, homeModuleCfg.getHomeConfigId()
                        , moduleConfigId, r, null, mongoTemplate);
            }
        }
        return list;
    }

    @Override
    public String deleteFromCache(String userId, String userToken, String id) {
    	return HomeCfgUtil.deleteItemWithFlag(userId, userToken, HomeCfgCacheItem.M_CONTENT, null, null, id, mongoTemplate);
    }


    @Override
    public List<HomeContent> findListFromCache(String userId, String userToken
    		, String moduleConfigId, int size, Integer clientType,Optional<Integer> dataType) {
		HomeModuleConfig cfg = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_MODULE
				, null, null, moduleConfigId, mongoTemplate);

    	Query queryParas = HomeCfgUtil.getQuery(userId, userToken, HomeCfgCacheItem.M_CONTENT
    			, cfg.getHomeConfigId(), moduleConfigId, true);
    	if(dataType.isPresent()){
            queryParas.addCriteria(Criteria.where("data.dataType").is(dataType.get()));
        }
    	if (clientType == HomeContentImage.IMAGE_CLIENT_TYPE_APP) {
        	queryParas.with(new Sort(new Order("data.appSort"), new Order(Direction.DESC, "data.createTime"), new Order(Direction.DESC, "data._createTime")));
    	}else {
        	queryParas.with(new Sort(new Order("data.sort"), new Order(Direction.DESC, "data.createTime"), new Order(Direction.DESC, "data._createTime")));
    	}
    	queryParas.limit(size);

    	List<HomeContent> list = HomeCfgUtil.findAndLoadListFromCache(userId, userToken, cfg.getHomeConfigId(), HomeCfgCacheItem.M_CONTENT
    			, moduleConfigId, mongoTemplate, () -> {
    	    		com.zxy.product.system.jooq.tables.HomeContentImage appCntImg = HOME_CONTENT_IMAGE.as("appCntImg");
    	            Field<String> appImage = appCntImg.IMAGE.as("appImage");
    	            Field<String> appImagePath = appCntImg.IMAGE_PATH.as("appImagePath");

    	            return homeContentDao.execute(
    	                    e -> e.selectDistinct(Fields.start().add(HOME_CONTENT.fields()).add(HOME_CONTENT_IMAGE.IMAGE)
    	                    		.add(HOME_CONTENT_IMAGE.IMAGE_PATH).add(appImage).add(appImagePath).end()).from(HOME_CONTENT)
    	                            .leftJoin(HOME_CONTENT_IMAGE).on(HOME_CONTENT_IMAGE.CONTENT_ID.eq(HOME_CONTENT.ID)
    	                                    .and(HOME_CONTENT_IMAGE.CLIENT_TYPE.ne(HomeContentImage.IMAGE_CLIENT_TYPE_APP)))
    	                            .leftJoin(appCntImg).on(appCntImg.CONTENT_ID.eq(HOME_CONTENT.ID)
    	                            	.and(appCntImg.CLIENT_TYPE.eq(HomeContentImage.IMAGE_CLIENT_TYPE_APP)))
                                    .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId))
    	                            .fetch(
    	                    record -> {
    	                        HomeContent homeContent = record.into(HomeContent.class);
    	                        homeContent.setImage(record.getValue(HOME_CONTENT_IMAGE.IMAGE));
    	                        homeContent.setImagePath(record.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
    	                        homeContent.setAppImage(record.getValue(appImage));
    	                        homeContent.setAppImagePath(record.getValue(appImagePath));
    	                        return homeContent;
    	                    }));

    			}, queryParas, (HomeContent a) -> {return a.getId();});
    	if (list != null) {
    		for (HomeContent cnt : list) {
                if (clientType == HomeContentImage.IMAGE_CLIENT_TYPE_APP) {
                    cnt.setImage(cnt.getAppImage());
                    cnt.setImagePath(cnt.getAppImagePath());
                }
    		}
    	}
    	return list;

    }

    @Override
    public String saveCheck(String userId, String userToken, String moduleConfigId, Integer clientType) {
        HomeModuleConfig cfg = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_MODULE
                , null, null, moduleConfigId, mongoTemplate);

        Query queryParas = HomeCfgUtil.getQuery(userId, userToken, HomeCfgCacheItem.M_CONTENT
                , cfg.getHomeConfigId(), moduleConfigId, true);
        if (clientType == HomeContentImage.IMAGE_CLIENT_TYPE_APP) {
            queryParas.with(new Sort(new Order("data.appSort"), new Order(Direction.DESC, "data.createTime"), new Order(Direction.DESC, "data._createTime")));
        }else {
            queryParas.with(new Sort(new Order("data.sort"), new Order(Direction.DESC, "data.createTime"), new Order(Direction.DESC, "data._createTime")));
        }

        List<HomeContent> list = HomeCfgUtil.findAndLoadListFromCache(userId, userToken, cfg.getHomeConfigId(), HomeCfgCacheItem.M_CONTENT
                , moduleConfigId, mongoTemplate, () -> {
                    com.zxy.product.system.jooq.tables.HomeContentImage appCntImg = HOME_CONTENT_IMAGE.as("appCntImg");
                    Field<String> appImage = appCntImg.IMAGE.as("appImage");
                    Field<String> appImagePath = appCntImg.IMAGE_PATH.as("appImagePath");

                    return homeContentDao.execute(
                            e -> e.selectDistinct(Fields.start().add(HOME_CONTENT.fields()).add(HOME_CONTENT_IMAGE.IMAGE)
                                    .add(HOME_CONTENT_IMAGE.IMAGE_PATH).add(appImage).add(appImagePath).end()).from(HOME_CONTENT)
                                    .leftJoin(HOME_CONTENT_IMAGE).on(HOME_CONTENT_IMAGE.CONTENT_ID.eq(HOME_CONTENT.ID)
                                            .and(HOME_CONTENT_IMAGE.CLIENT_TYPE.ne(HomeContentImage.IMAGE_CLIENT_TYPE_APP)))
                                    .leftJoin(appCntImg).on(appCntImg.CONTENT_ID.eq(HOME_CONTENT.ID)
                                            .and(appCntImg.CLIENT_TYPE.eq(HomeContentImage.IMAGE_CLIENT_TYPE_APP)))
                                    .where(HOME_CONTENT.MODULE_CONFIG_ID.eq(moduleConfigId)).fetch(
                                            record -> {
                                                HomeContent homeContent = record.into(HomeContent.class);
                                                homeContent.setImage(record.getValue(HOME_CONTENT_IMAGE.IMAGE));
                                                homeContent.setImagePath(record.getValue(HOME_CONTENT_IMAGE.IMAGE_PATH));
                                                homeContent.setAppImage(record.getValue(appImage));
                                                homeContent.setAppImagePath(record.getValue(appImagePath));
                                                return homeContent;
                                            }));

                }, queryParas, HomeContentEntity::getId);

        // 返回图片类型但未配置图片的
        for (HomeContent homeContent : list) {
            if (HomeContent.DATA_TYPE_IMAGE.equals(homeContent.getDataType())) {
                if (HomeModuleConfig.CLIENT_TYPE_PC == clientType && StringUtil.isBlank(homeContent.getImage())) {
                    return homeContent.getDataName();
                }
                if (HomeModuleConfig.CLIENT_TYPE_APP == clientType && StringUtil.isBlank(homeContent.getAppImage())) {
                    return homeContent.getDataName();
                }
            }
        }
        return "";
    }

    @Override
    public String updateSortByIdToCache(String userId, String userToken, String contentId, int sort, Integer clientType,String moduleHomeConfigId) {
    	HomeContent content = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_CONTENT, null, moduleHomeConfigId, contentId, mongoTemplate);
    	if (clientType == 2) {
        	HomeCfgUtil.sort(userId, userToken, HomeCfgCacheItem.M_CONTENT, null, moduleHomeConfigId, contentId, "appSort", content.getAppSort(), sort, mongoTemplate);
    	}else {
        	HomeCfgUtil.sort(userId, userToken, HomeCfgCacheItem.M_CONTENT, null, moduleHomeConfigId, contentId, "sort", content.getSort(), sort, mongoTemplate);
    	}
    	return contentId;

    }

    @Override
    public String updateBasicInfoToCache(String userId, String userToken, String contentId, Optional<String> dataExt, Optional<String> dataName, Optional<String> url) {
        HomeCfgUtil.updateItemDataExtAndUrlAndDataNameAndSort(userId, userToken, HomeCfgCacheItem.M_CONTENT, null, null, contentId,
            dataExt.orElse(null), dataName.orElse(null), url.orElse(null), null, null, mongoTemplate);
        return contentId;
    }

    @Override
    public String updateImageToCache(String userId, String userToken
    		, String contentId, String imageId,String imageIdPath, Integer clientType) {
        HomeContentImage homeContentImage = new HomeContentImage();
        homeContentImage.forInsert();
        homeContentImage.setContentId(contentId);
        homeContentImage.setClientType(clientType);
        homeContentImage.setImage(imageId);
        homeContentImage.setImagePath(imageIdPath);

    	HomeContent content = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_CONTENT, null, null, contentId, mongoTemplate);
    	if (clientType == HomeContentImage.IMAGE_CLIENT_TYPE_APP) {
        	content.setAppImage(imageId);
        	content.setAppImagePath(imageIdPath);
        	content.setAppContentImage(homeContentImage);
    	}else {
        	content.setImage(imageId);
        	content.setImagePath(imageIdPath);
        	content.setContentImage(homeContentImage);
    	}
    	HomeCfgUtil.updateItem(userId, userToken, HomeCfgCacheItem.M_CONTENT, null, null, contentId, content, null, mongoTemplate);
    	return contentId;
    }

}
