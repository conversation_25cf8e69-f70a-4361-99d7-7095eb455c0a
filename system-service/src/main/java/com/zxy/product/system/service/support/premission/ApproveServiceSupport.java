package com.zxy.product.system.service.support.premission;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.permission.ApproveService;
import com.zxy.product.system.content.SystemConstant;
import com.zxy.product.system.entity.OrganizationSupervisor;
import com.zxy.product.system.entity.RegisterTodo;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.zxy.product.system.jooq.Tables.ORGANIZATION_SUPERVISOR;
import static com.zxy.product.system.jooq.Tables.REGISTER_TODO;

@Service
public class ApproveServiceSupport implements ApproveService {

    private CommonDao<RegisterTodo> registerTodoCommonDao;

    private CommonDao<OrganizationSupervisor> organizationSupervisorCommonDao;

    @Autowired
    public void setRegisterTodoCommonDao(CommonDao<RegisterTodo> registerTodoCommonDao) {
        this.registerTodoCommonDao = registerTodoCommonDao;
    }
    @Autowired
    public void setOrganizationSupervisorCommonDao(CommonDao<OrganizationSupervisor> organizationSupervisorCommonDao) {
        this.organizationSupervisorCommonDao = organizationSupervisorCommonDao;
    }


    @Override
    public RegisterTodo insert(String registerId, Integer registerType, String registerOrganizationId, String registerMemberName, Integer todoStatus, List<String> approveMemberId, String reasonId) {
        List<RegisterTodo> list  = new ArrayList<>();
        approveMemberId.stream().forEach( m -> {
            RegisterTodo registerTodo =new RegisterTodo();
            registerTodo.forInsert();
            registerTodo.setRegisterId(registerId);
            registerTodo.setRegisterType(registerType);
            registerTodo.setRegisterOrganizationId(registerOrganizationId);
            registerTodo.setRegisterMemberName(registerMemberName);
            registerTodo.setTodoStatus(todoStatus);
            registerTodo.setReasonId(reasonId);
            registerTodo.setApproveMemberId(m);
            list.add(registerTodo);
        });
        registerTodoCommonDao.insert(list);
        return new RegisterTodo();
    }

    @Override
    public int delete(String id) {
        return registerTodoCommonDao.delete(id);
    }

    @Override
    public void update(String registerId, Integer todoStatus) {
        registerTodoCommonDao.execute(cb -> cb.update(REGISTER_TODO)
                .set(REGISTER_TODO.TODO_STATUS, todoStatus))
                .where(REGISTER_TODO.REGISTER_ID.eq(registerId)).execute();
    }

    @Override
    public PagedResult<RegisterTodo> findPage(int page, int pageSize, String memberId, Integer status, Optional<Integer> registerType, Optional<String> reasonId) {
        Integer count = registerTodoCommonDao.execute(e -> e.select(REGISTER_TODO.ID.count()))
                .from(REGISTER_TODO)
                .where(REGISTER_TODO.APPROVE_MEMBER_ID.eq(memberId)
                        , REGISTER_TODO.TODO_STATUS.eq(status))
                .and(registerType.isPresent() ? REGISTER_TODO.REGISTER_TYPE.eq(registerType.get()) : DSL.trueCondition())
                .and(reasonId.isPresent() ? REGISTER_TODO.REASON_ID.eq(reasonId.get()) : DSL.trueCondition())
                .fetchOne(REGISTER_TODO.ID.count());
        if (count <= SystemConstant.ZERO) {
            return PagedResult.create(count, new ArrayList<>());
        }
        List<RegisterTodo> list = registerTodoCommonDao.execute(e -> e.select(REGISTER_TODO.fields()))
                .from(REGISTER_TODO)
                .where(REGISTER_TODO.APPROVE_MEMBER_ID.eq(memberId)
                        , REGISTER_TODO.TODO_STATUS.eq(status))
                .and(registerType.isPresent() ? REGISTER_TODO.REGISTER_TYPE.eq(registerType.get()) : DSL.trueCondition())
                .and(reasonId.isPresent() ? REGISTER_TODO.REASON_ID.eq(reasonId.get()) : DSL.trueCondition())
                .orderBy(REGISTER_TODO.CREATE_TIME.desc())
                .limit((page - 1) * pageSize, pageSize)
                .fetch(r -> {
                    RegisterTodo registerTodo = r.into(REGISTER_TODO).into(RegisterTodo.class);
                    return registerTodo;
                });
        return PagedResult.create(count, list);
    }

    @Override
    public List<String> findOrgSupervisor(String orgId) {
        return organizationSupervisorCommonDao.execute(cb -> cb.selectDistinct(ORGANIZATION_SUPERVISOR.MEMBER_ID)
                .from(ORGANIZATION_SUPERVISOR)
                .where(ORGANIZATION_SUPERVISOR.ORGANIZATION_ID.eq(orgId))
                .fetch(ORGANIZATION_SUPERVISOR.MEMBER_ID));
    }
}
