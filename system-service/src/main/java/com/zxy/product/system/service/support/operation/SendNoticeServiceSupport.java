package com.zxy.product.system.service.support.operation;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.operation.SendNoticeService;
import com.zxy.product.system.entity.MessageNoticeRecord;

/**
 * Created by TJ on 2017/5/24.
 */
@Service
public class SendNoticeServiceSupport implements SendNoticeService {

    private CommonDao<MessageNoticeRecord> noticeRecordCommonDao;

    @Autowired
    public void setNoticeRecordCommonDao(CommonDao<MessageNoticeRecord> noticeRecordCommonDao) {
        this.noticeRecordCommonDao = noticeRecordCommonDao;
    }


    @Override
    public void sendMessageToMember(String senderId, String[] receiverId, String subject, String content, String textContent, Integer businessType,
                                    Optional<String> businessId, String organizationId, String link) {
        int count = receiverId.length;
        int groupCount = count % SEND_USER_MAX == 0 ? count / SEND_USER_MAX : count / SEND_USER_MAX + 1;
        for (int i = 1; i <= groupCount; i++) {
            int skipNum = (i - 1) * SEND_USER_MAX;
            List<MessageNoticeRecord> recordList = Arrays.stream(receiverId).skip(skipNum).limit(SEND_USER_MAX)
                    .map(id -> {
                        MessageNoticeRecord record = new MessageNoticeRecord();
                        record.forInsert();
                        record.setSubject(subject);
                        record.setContent(content);
                        record.setTextContent(textContent);
                        record.setSenderId(senderId);
                        record.setReceiverId(id);
                        record.setReadStatus(READ_STATUS_NO);
                        record.setLink(link);
                        record.setBusinessType(businessType);
                        businessId.ifPresent(record::setBusinessId);
                        record.setOrganizationId(organizationId);
                        return record;
                    }).collect(Collectors.toList());
            noticeRecordCommonDao.insert(recordList);
        }
    }
}
