package com.zxy.product.system.service.support.operation;


import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.system.api.operation.PointGradeService;
import com.zxy.product.system.content.ErrorCode;
import com.zxy.product.system.entity.DeleteDataSystem;
import com.zxy.product.system.entity.PointGrade;
import com.zxy.product.system.entity.PointResultByMemberIdVo;
import com.zxy.product.system.jooq.tables.pojos.PointGradeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static com.zxy.product.system.jooq.Tables.POINT_GRADE;


/**
 * <AUTHOR>
 * @create 2023/12/13 11:09
 */
@Service
public class PointGradeServiceSupport implements PointGradeService {

    private CommonDao<PointGrade> PointGradeDao;
    private CommonDao<DeleteDataSystem> deleteDataSystemDao;

    @Autowired
    public void setPointResult(CommonDao<PointGrade> PointGradeDao) {
        this.PointGradeDao = PointGradeDao;
    }

    @Autowired
    public void setDeleteDataSystemDao(CommonDao<DeleteDataSystem> deleteDataSystemDao) {
        this.deleteDataSystemDao = deleteDataSystemDao;
    }

    /**
     * 积分等级添加
     *
     * @param gradeName
     * @param cover
     * @param pointGrade
     * @return
     */
    @Override
    public PointGrade add(String gradeName, Optional<String> cover, Integer pointGrade) {

        //1.分数查询
        Optional<PointGrade> pointGradeOptional = PointGradeDao.fetchOne(POINT_GRADE.POINT_GRADE_.eq(pointGrade));

        //2.判断是否有重复的分
        if (pointGradeOptional.isPresent()) {

            //重复分数禁止添加!
            throw new UnprocessableException(ErrorCode.POINT_GREADE_NOT_REPEAT);
        }

        //3.存储
        PointGrade pointGradeEntity = new PointGrade();
        pointGradeEntity.forInsert();
        pointGradeEntity.setGradeName(gradeName);
        pointGradeEntity.setCover(cover.orElse(null));
        pointGradeEntity.setPointGrade(pointGrade);
        pointGradeEntity.setCreateTime(System.currentTimeMillis());
        return PointGradeDao.insert(pointGradeEntity);
    }

    /**
     * 根据id修改积分等级列表
     *
     * @param id
     * @param gradeName
     * @param cover
     * @param pointGrade
     * @return
     */
    @Override
    public PointGrade updateById(String id, String gradeName, Optional<String> cover, Integer pointGrade) {

        //1.对象查询
        Optional<PointGrade> pointGradeOptional = PointGradeDao.fetchOne(POINT_GRADE.ID.eq(id));

        if (!pointGradeOptional.isPresent()){
            throw new UnprocessableException(ErrorCode.POINT_GREADE_NOT_EXISTS);
        }

        //2.分数重复查询并校验
        Optional<PointGrade> pointGradeOptionalByPoint = PointGradeDao.fetchOne(POINT_GRADE.POINT_GRADE_.eq(pointGrade));

        //3.判断是否有重复的分或者对象是否存在
        if (pointGradeOptionalByPoint.isPresent() && !pointGradeOptionalByPoint.get().getId().equals(id)) {
            //重复分数禁止添加!
            throw new UnprocessableException(ErrorCode.POINT_GREADE_NOT_REPEAT);
        }

        //4.修改
        PointGrade pointGradeEntity = pointGradeOptional.get();
        pointGradeEntity.setGradeName(gradeName);
        pointGradeEntity.setCover(cover.orElse(null));
        pointGradeEntity.setPointGrade(pointGrade);
        return PointGradeDao.update(pointGradeEntity);
    }

    /**
     * 根据id删除积分等级列表
     *
     * @param id
     * @return
     */
    @Override
    public Boolean deleteById(String id) {

        deleteDataSystemDao.insert(DeleteDataSystem.getDeleteData(DeleteDataSystem.POINT_GRADE, id, null));

        return PointGradeDao.delete(id) > 0 ? true : false;
    }

    /**
     * 获取积分等级列表
     */
    @Override
    public List<PointGrade> list() {

        List<PointGrade> resultList = PointGradeDao.execute(dao -> dao.select(POINT_GRADE.fields()))
                .from(POINT_GRADE)
                .orderBy(POINT_GRADE.POINT_GRADE_)
                .fetchInto(PointGrade.class);

        return resultList;
    }

    /**
     * 根据历史积分获取积分等级,封面及下一级所需要的积分并赋值
     *
     * @param resultEntity
     */
    @Override
    public PointResultByMemberIdVo setGradeByPointHistory(PointResultByMemberIdVo resultEntity) {

        Integer pointHistory = resultEntity.getPointHistory();

        // 合并查询
        List<PointGrade> pointGrades = PointGradeDao.execute(dao ->
                dao.select(POINT_GRADE.fields())
                        .from(POINT_GRADE)
                        .where(POINT_GRADE.POINT_GRADE_.le(pointHistory).or(POINT_GRADE.POINT_GRADE_.gt(pointHistory)))
                        .orderBy(POINT_GRADE.POINT_GRADE_.desc())
                        .fetchInto(PointGrade.class)
        );

        if (!pointGrades.isEmpty()) {
            // 获取当前等级
            PointGrade currentGrade = pointGrades.stream()
                    .filter(grade -> grade.getPointGrade() <= pointHistory)
                    .findFirst()
                    .orElse(null);

            if (currentGrade != null) {
                resultEntity.setGradeName(currentGrade.getGradeName());
                resultEntity.setCover(currentGrade.getCover());
            }

            // 获取下一等级
            PointGrade nextGrade = pointGrades.stream()
                    .filter(grade -> grade.getPointGrade() > pointHistory)
                    .min(Comparator.comparingInt(PointGradeEntity::getPointGrade))
                    .orElse(null);

            if (nextGrade != null) {
                resultEntity.setNextGradePoint(nextGrade.getPointGrade() - pointHistory);
                resultEntity.setNextGradeName(nextGrade.getGradeName());
            } else {
                // 满级时, 下一等级返回0, 名称不用赋值
                resultEntity.setNextGradePoint(0);
            }
        }

        // 查询是否为第一级
        Integer firstGradeCount = PointGradeDao.execute(dao ->
                dao.select(POINT_GRADE.ID.count())
                        .from(POINT_GRADE)
                        .where(POINT_GRADE.POINT_GRADE_.lt(pointHistory))
                        .fetchOne().value1()
        );
        resultEntity.setFirstGrade(firstGradeCount <= 0);
        return resultEntity;
    }
}
