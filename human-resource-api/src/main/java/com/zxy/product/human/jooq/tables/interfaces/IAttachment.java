/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.human.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 附件表
 */
@Generated(
        value = {
                "http://www.jooq.org",
                "jOOQ version:3.9.6"
        },
        comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAttachment extends Serializable {

    /**
     * Setter for <code>human-resource.t_attachment.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>human-resource.t_attachment.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>human-resource.t_attachment.f_filename</code>.
     */
    public void setFilename(String value);

    /**
     * Getter for <code>human-resource.t_attachment.f_filename</code>.
     */
    public String getFilename();

    /**
     * Setter for <code>human-resource.t_attachment.f_content_type</code>.
     */
    public void setContentType(String value);

    /**
     * Getter for <code>human-resource.t_attachment.f_content_type</code>.
     */
    public String getContentType();

    /**
     * Setter for <code>human-resource.t_attachment.f_extention</code>.
     */
    public void setExtention(String value);

    /**
     * Getter for <code>human-resource.t_attachment.f_extention</code>.
     */
    public String getExtention();

    /**
     * Setter for <code>human-resource.t_attachment.f_path</code>.
     */
    public void setPath(String value);

    /**
     * Getter for <code>human-resource.t_attachment.f_path</code>.
     */
    public String getPath();

    /**
     * Setter for <code>human-resource.t_attachment.f_cover</code>. 封面
     */
    public void setCover(String value);

    /**
     * Getter for <code>human-resource.t_attachment.f_cover</code>. 封面
     */
    public String getCover();

    /**
     * Setter for <code>human-resource.t_attachment.f_size</code>.
     */
    public void setSize(Long value);

    /**
     * Getter for <code>human-resource.t_attachment.f_size</code>.
     */
    public Long getSize();

    /**
     * Setter for <code>human-resource.t_attachment.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>human-resource.t_attachment.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>human-resource.t_attachment.f_translate_id</code>.
     */
    public void setTranslateId(String value);

    /**
     * Getter for <code>human-resource.t_attachment.f_translate_id</code>.
     */
    public String getTranslateId();

    /**
     * Setter for <code>human-resource.t_attachment.translate_flag</code>. 0 转换中 1 完成 2 失败
     */
    public void setTranslateFlag(Integer value);

    /**
     * Getter for <code>human-resource.t_attachment.translate_flag</code>. 0 转换中 1 完成 2 失败
     */
    public Integer getTranslateFlag();

    /**
     * Setter for <code>human-resource.t_attachment.f_translate_level</code>. 视频转换等级，1，480p, 2，720p 3，1080p,0/null 没有清晰度,4 : 关联的mp3
     */
    public void setTranslateLevel(Integer value);

    /**
     * Getter for <code>human-resource.t_attachment.f_translate_level</code>. 视频转换等级，1，480p, 2，720p 3，1080p,0/null 没有清晰度,4 : 关联的mp3
     */
    public Integer getTranslateLevel();

    /**
     * Setter for <code>human-resource.t_attachment.f_duration</code>. 资源长度 media毫秒
     */
    public void setDuration(Integer value);

    /**
     * Getter for <code>human-resource.t_attachment.f_duration</code>. 资源长度 media毫秒
     */
    public Integer getDuration();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAttachment
     */
    public void from(IAttachment from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAttachment
     */
    public <E extends IAttachment> E into(E into);
}
