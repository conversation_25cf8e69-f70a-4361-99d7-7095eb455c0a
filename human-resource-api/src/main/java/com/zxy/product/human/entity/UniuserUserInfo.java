package com.zxy.product.human.entity;

import com.zxy.product.human.jooq.tables.pojos.UniuserUserInfoEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/4
 */
public class UniuserUserInfo extends UniuserUserInfoEntity {
    private static final long serialVersionUID = -2258377345148771454L;


    public static final String ACCOUNT_STATUS_ENABLE = "1";

    public static final String GENDER_MAN = "M";
    public static final String GENDER_WOMEN = "F";


    public static final Integer OPERATION_INSERT = 1;
    public static final Integer OPERATION_UPDATE = 2;

    public static final Integer CHECK_STATUS_SUCCESS = 1;
    public static final Integer CHECK_STATUS_FAILURE = 2;

    public static final Integer SYNC_STATUS_SUCCESS = 1;
    public static final Integer SYNC_STATUS_FAILURE = 2;

    private List<UniuserDeputyAccountInfo> deputyAccountInfos;

    private String companyId;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public List<UniuserDeputyAccountInfo> getDeputyAccountInfos() {
        return deputyAccountInfos;
    }

    public void setDeputyAccountInfos(List<UniuserDeputyAccountInfo> deputyAccountInfos) {
        this.deputyAccountInfos = deputyAccountInfos;
    }

}
