/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables;


import com.zxy.product.human.jooq.HumanResource;
import com.zxy.product.human.jooq.Keys;
import com.zxy.product.human.jooq.tables.records.MemberEmailValidateRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberEmailValidate extends TableImpl<MemberEmailValidateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>human-resource.t_member_email_validate</code>
     */
    public static final MemberEmailValidate MEMBER_EMAIL_VALIDATE = new MemberEmailValidate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MemberEmailValidateRecord> getRecordType() {
        return MemberEmailValidateRecord.class;
    }

    /**
     * The column <code>human-resource.t_member_email_validate.f_id</code>.
     */
    public final TableField<MemberEmailValidateRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>human-resource.t_member_email_validate.f_create_time</code>.
     */
    public final TableField<MemberEmailValidateRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "");

    /**
     * The column <code>human-resource.t_member_email_validate.f_member_id</code>. 用户id
     */
    public final TableField<MemberEmailValidateRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户id");

    /**
     * The column <code>human-resource.t_member_email_validate.f_email</code>. 邮箱
     */
    public final TableField<MemberEmailValidateRecord, String> EMAIL = createField("f_email", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "邮箱");

    /**
     * Create a <code>human-resource.t_member_email_validate</code> table reference
     */
    public MemberEmailValidate() {
        this("t_member_email_validate", null);
    }

    /**
     * Create an aliased <code>human-resource.t_member_email_validate</code> table reference
     */
    public MemberEmailValidate(String alias) {
        this(alias, MEMBER_EMAIL_VALIDATE);
    }

    private MemberEmailValidate(String alias, Table<MemberEmailValidateRecord> aliased) {
        this(alias, aliased, null);
    }

    private MemberEmailValidate(String alias, Table<MemberEmailValidateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return HumanResource.HUMAN_RESOURCE_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MemberEmailValidateRecord> getPrimaryKey() {
        return Keys.KEY_T_MEMBER_EMAIL_VALIDATE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MemberEmailValidateRecord>> getKeys() {
        return Arrays.<UniqueKey<MemberEmailValidateRecord>>asList(Keys.KEY_T_MEMBER_EMAIL_VALIDATE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberEmailValidate as(String alias) {
        return new MemberEmailValidate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MemberEmailValidate rename(String name) {
        return new MemberEmailValidate(name, null);
    }
}
