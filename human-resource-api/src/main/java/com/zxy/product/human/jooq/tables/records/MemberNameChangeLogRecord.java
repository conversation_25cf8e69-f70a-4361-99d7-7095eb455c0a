/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.records;


import com.zxy.product.human.jooq.tables.MemberNameChangeLog;
import com.zxy.product.human.jooq.tables.interfaces.IMemberNameChangeLog;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberNameChangeLogRecord extends UpdatableRecordImpl<MemberNameChangeLogRecord> implements Record4<String, String, String, Long>, IMemberNameChangeLog {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>human-resource.t_member_name_change_log.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>human-resource.t_member_name_change_log.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>human-resource.t_member_name_change_log.f_before_name</code>. 变动前员工编号
     */
    @Override
    public void setBeforeName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>human-resource.t_member_name_change_log.f_before_name</code>. 变动前员工编号
     */
    @Override
    public String getBeforeName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>human-resource.t_member_name_change_log.f_after_name</code>. 变动后员工编号
     */
    @Override
    public void setAfterName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>human-resource.t_member_name_change_log.f_after_name</code>. 变动后员工编号
     */
    @Override
    public String getAfterName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>human-resource.t_member_name_change_log.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>human-resource.t_member_name_change_log.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, String, String, Long> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, String, String, Long> valuesRow() {
        return (Row4) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return MemberNameChangeLog.MEMBER_NAME_CHANGE_LOG.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return MemberNameChangeLog.MEMBER_NAME_CHANGE_LOG.BEFORE_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return MemberNameChangeLog.MEMBER_NAME_CHANGE_LOG.AFTER_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return MemberNameChangeLog.MEMBER_NAME_CHANGE_LOG.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getBeforeName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getAfterName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberNameChangeLogRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberNameChangeLogRecord value2(String value) {
        setBeforeName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberNameChangeLogRecord value3(String value) {
        setAfterName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberNameChangeLogRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberNameChangeLogRecord values(String value1, String value2, String value3, Long value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMemberNameChangeLog from) {
        setId(from.getId());
        setBeforeName(from.getBeforeName());
        setAfterName(from.getAfterName());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMemberNameChangeLog> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MemberNameChangeLogRecord
     */
    public MemberNameChangeLogRecord() {
        super(MemberNameChangeLog.MEMBER_NAME_CHANGE_LOG);
    }

    /**
     * Create a detached, initialised MemberNameChangeLogRecord
     */
    public MemberNameChangeLogRecord(String id, String beforeName, String afterName, Long createTime) {
        super(MemberNameChangeLog.MEMBER_NAME_CHANGE_LOG);

        set(0, id);
        set(1, beforeName);
        set(2, afterName);
        set(3, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.human.jooq.tables.pojos.MemberNameChangeLogEntity)) {
            return false;
        }
        com.zxy.product.human.jooq.tables.pojos.MemberNameChangeLogEntity pojo = (com.zxy.product.human.jooq.tables.pojos.MemberNameChangeLogEntity)source;
        pojo.into(this);
        return true;
    }
}
