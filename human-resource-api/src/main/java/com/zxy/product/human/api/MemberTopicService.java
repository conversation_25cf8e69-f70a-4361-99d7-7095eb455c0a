package com.zxy.product.human.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.human.entity.MemberTopic;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/4/22.
 */
@RemoteService
public interface MemberTopicService {

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<MemberTopic> findByMemberId(String memberId);

    @Transactional
    String[] insert(String memberId, String[] topicIds, Boolean isDelete);

    /***
     * 关注标签
     * @param memberId
     * @param topicId
     * @return
     */
    @Transactional
    String concernTopic(String memberId, String topicId);

    /**8
     * 取消关注标签
     * @param memberId
     * @param topicId
     * @return
     */
    @Transactional
    String cancelConcernTopic(String memberId, String topicId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<MemberTopic> getOptional(String id);

    /**
     * 根据话题id算出当月的关注数
     * @param id 话题id
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<MemberTopic> getMonthWatchSumByTopicId(String id);

    /**
     * 根据话题id算出总的关注数
     * @param id 话题id
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<MemberTopic> getWatchSumByTopicId(String id);

    /**
     * 根据ids 查询对应的标签id
     * @param memberIds
     * @return
     */
    List<MemberTopic> findByMemberIds(List<String> memberIds);

}
