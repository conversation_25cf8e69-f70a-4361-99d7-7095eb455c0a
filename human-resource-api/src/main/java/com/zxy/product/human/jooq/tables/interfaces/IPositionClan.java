/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.human.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 职位族表（主族和子族）
 */
@Generated(
        value = {
                "http://www.jooq.org",
                "jOOQ version:3.9.6"
        },
        comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IPositionClan extends Serializable {

    /**
     * Setter for <code>human-resource.t_position_clan.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>human-resource.t_position_clan.f_code</code>. 族编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_code</code>. 族编码
     */
    public String getCode();

    /**
     * Setter for <code>human-resource.t_position_clan.f_name</code>. 族名称
     */
    public void setName(String value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_name</code>. 族名称
     */
    public String getName();

    /**
     * Setter for <code>human-resource.t_position_clan.f_type</code>. 族类型 0标准职位族 1网大职位族
     */
    public void setType(Integer value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_type</code>. 族类型 0标准职位族 1网大职位族
     */
    public Integer getType();

    /**
     * Setter for <code>human-resource.t_position_clan.f_parent_id</code>. 父节点
     */
    public void setParentId(String value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_parent_id</code>. 父节点
     */
    public String getParentId();

    /**
     * Setter for <code>human-resource.t_position_clan.f_status</code>. 状态 0:禁用 1可用
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_status</code>. 状态 0:禁用 1可用
     */
    public Integer getStatus();

    /**
     * Setter for <code>human-resource.t_position_clan.f_delete_flag</code>. 删除标识  0:未删除 1:已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_delete_flag</code>. 删除标识  0:未删除 1:已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>human-resource.t_position_clan.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>human-resource.t_position_clan.f_parent_name</code>. 父节点名称
     */
    public void setParentName(String value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_parent_name</code>. 父节点名称
     */
    public String getParentName();

    /**
     * Setter for <code>human-resource.t_position_clan.f_atlas_status</code>. 职位族是否有图谱
     */
    public void setAtlasStatus(Integer value);

    /**
     * Getter for <code>human-resource.t_position_clan.f_atlas_status</code>. 职位族是否有图谱
     */
    public Integer getAtlasStatus();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IPositionClan
     */
    public void from(IPositionClan from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IPositionClan
     */
    public <E extends IPositionClan> E into(E into);
}
