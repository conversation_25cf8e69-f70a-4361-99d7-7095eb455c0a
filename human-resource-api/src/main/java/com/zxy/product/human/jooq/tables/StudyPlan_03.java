/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables;


import com.zxy.product.human.jooq.HumanResource;
import com.zxy.product.human.jooq.Keys;
import com.zxy.product.human.jooq.tables.records.StudyPlan_03Record;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习计划表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyPlan_03 extends TableImpl<StudyPlan_03Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>human-resource.t_study_plan_03</code>
     */
    public static final StudyPlan_03 STUDY_PLAN_03 = new StudyPlan_03();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyPlan_03Record> getRecordType() {
        return StudyPlan_03Record.class;
    }

    /**
     * The column <code>human-resource.t_study_plan_03.f_id</code>. 主键
     */
    public final TableField<StudyPlan_03Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>human-resource.t_study_plan_03.f_member_id</code>. 人员id
     */
    public final TableField<StudyPlan_03Record, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "人员id");

    /**
     * The column <code>human-resource.t_study_plan_03.f_business_id</code>. 关联业务id
     */
    public final TableField<StudyPlan_03Record, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "关联业务id");

    /**
     * The column <code>human-resource.t_study_plan_03.f_business_type</code>. 业务类型： 1:课程 2:专题 3:考试 4:调研 5:培训班
     */
    public final TableField<StudyPlan_03Record, Integer> BUSINESS_TYPE = createField("f_business_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "业务类型： 1:课程 2:专题 3:考试 4:调研 5:培训班");

    /**
     * The column <code>human-resource.t_study_plan_03.f_business_name</code>. 业务名称
     */
    public final TableField<StudyPlan_03Record, String> BUSINESS_NAME = createField("f_business_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "业务名称");

    /**
     * The column <code>human-resource.t_study_plan_03.f_required_complete_rate</code>. 必须课程/章节 完成率
     */
    public final TableField<StudyPlan_03Record, Integer> REQUIRED_COMPLETE_RATE = createField("f_required_complete_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "必须课程/章节 完成率");

    /**
     * The column <code>human-resource.t_study_plan_03.f_finish_status</code>. 完成状态 0:未完成 1:已完成
     */
    public final TableField<StudyPlan_03Record, Integer> FINISH_STATUS = createField("f_finish_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "完成状态 0:未完成 1:已完成");

    /**
     * The column <code>human-resource.t_study_plan_03.f_source_type</code>. 数据来源 1:自主添加 2:组织推送
     */
    public final TableField<StudyPlan_03Record, Integer> SOURCE_TYPE = createField("f_source_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").nullable(false), this, "数据来源 1:自主添加 2:组织推送");

    /**
     * The column <code>human-resource.t_study_plan_03.f_applicant_start_time</code>. 报名开始时间
     */
    public final TableField<StudyPlan_03Record, Long> APPLICANT_START_TIME = createField("f_applicant_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "报名开始时间");

    /**
     * The column <code>human-resource.t_study_plan_03.f_applicant_end_time</code>. 报名截止时间
     */
    public final TableField<StudyPlan_03Record, Long> APPLICANT_END_TIME = createField("f_applicant_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "报名截止时间");

    /**
     * The column <code>human-resource.t_study_plan_03.f_start_time</code>. 开始时间
     */
    public final TableField<StudyPlan_03Record, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "开始时间");

    /**
     * The column <code>human-resource.t_study_plan_03.f_dead_line</code>. 截止时间
     */
    public final TableField<StudyPlan_03Record, Long> DEAD_LINE = createField("f_dead_line", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "截止时间");

    /**
     * The column <code>human-resource.t_study_plan_03.f_complete_time</code>. 完成时间
     */
    public final TableField<StudyPlan_03Record, Long> COMPLETE_TIME = createField("f_complete_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "完成时间");

    /**
     * The column <code>human-resource.t_study_plan_03.f_remind_time</code>. 提醒时间
     */
    public final TableField<StudyPlan_03Record, Long> REMIND_TIME = createField("f_remind_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "提醒时间");

    /**
     * The column <code>human-resource.t_study_plan_03.f_remind_type</code>. 提醒类型  1:站内信  2:邮件 3:APP  4:短信  5:OA  以，隔开
     */
    public final TableField<StudyPlan_03Record, String> REMIND_TYPE = createField("f_remind_type", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "提醒类型  1:站内信  2:邮件 3:APP  4:短信  5:OA  以，隔开");

    /**
     * The column <code>human-resource.t_study_plan_03.f_remind_status</code>. 提醒状态 0:未设置提醒 1:未提醒 2:已提醒
     */
    public final TableField<StudyPlan_03Record, Integer> REMIND_STATUS = createField("f_remind_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "提醒状态 0:未设置提醒 1:未提醒 2:已提醒");

    /**
     * The column <code>human-resource.t_study_plan_03.f_create_time</code>. 创建时间
     */
    public final TableField<StudyPlan_03Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>human-resource.t_study_plan_03.f_modify_date</code>. 修改时间
     */
    public final TableField<StudyPlan_03Record, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>human-resource.t_study_plan_03</code> table reference
     */
    public StudyPlan_03() {
        this("t_study_plan_03", null);
    }

    /**
     * Create an aliased <code>human-resource.t_study_plan_03</code> table reference
     */
    public StudyPlan_03(String alias) {
        this(alias, STUDY_PLAN_03);
    }

    private StudyPlan_03(String alias, Table<StudyPlan_03Record> aliased) {
        this(alias, aliased, null);
    }

    private StudyPlan_03(String alias, Table<StudyPlan_03Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习计划表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return HumanResource.HUMAN_RESOURCE_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyPlan_03Record> getPrimaryKey() {
        return Keys.KEY_T_STUDY_PLAN_03_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyPlan_03Record>> getKeys() {
        return Arrays.<UniqueKey<StudyPlan_03Record>>asList(Keys.KEY_T_STUDY_PLAN_03_PRIMARY, Keys.KEY_T_STUDY_PLAN_03_IDX_BUSINESS_ID_MEMBER_ID);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyPlan_03 as(String alias) {
        return new StudyPlan_03(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyPlan_03 rename(String name) {
        return new StudyPlan_03(name, null);
    }
}
