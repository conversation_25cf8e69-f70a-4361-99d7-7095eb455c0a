/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.human.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 学习计划表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyPlan_04 extends Serializable {

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_business_id</code>. 关联业务id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_business_id</code>. 关联业务id
     */
    public String getBusinessId();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_business_type</code>. 业务类型： 1:课程 2:专题 3:考试 4:调研 5:培训班
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_business_type</code>. 业务类型： 1:课程 2:专题 3:考试 4:调研 5:培训班
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_business_name</code>. 业务名称
     */
    public void setBusinessName(String value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_business_name</code>. 业务名称
     */
    public String getBusinessName();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_required_complete_rate</code>. 必须课程/章节 完成率
     */
    public void setRequiredCompleteRate(Integer value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_required_complete_rate</code>. 必须课程/章节 完成率
     */
    public Integer getRequiredCompleteRate();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_finish_status</code>. 完成状态 0:未完成 1:已完成
     */
    public void setFinishStatus(Integer value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_finish_status</code>. 完成状态 0:未完成 1:已完成
     */
    public Integer getFinishStatus();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_source_type</code>. 数据来源 1:自主添加 2:组织推送
     */
    public void setSourceType(Integer value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_source_type</code>. 数据来源 1:自主添加 2:组织推送
     */
    public Integer getSourceType();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_applicant_start_time</code>. 报名开始时间
     */
    public void setApplicantStartTime(Long value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_applicant_start_time</code>. 报名开始时间
     */
    public Long getApplicantStartTime();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_applicant_end_time</code>. 报名截止时间
     */
    public void setApplicantEndTime(Long value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_applicant_end_time</code>. 报名截止时间
     */
    public Long getApplicantEndTime();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_start_time</code>. 开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_start_time</code>. 开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_dead_line</code>. 截止时间
     */
    public void setDeadLine(Long value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_dead_line</code>. 截止时间
     */
    public Long getDeadLine();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_complete_time</code>. 完成时间
     */
    public void setCompleteTime(Long value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_complete_time</code>. 完成时间
     */
    public Long getCompleteTime();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_remind_time</code>. 提醒时间
     */
    public void setRemindTime(Long value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_remind_time</code>. 提醒时间
     */
    public Long getRemindTime();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_remind_type</code>. 提醒类型  1:站内信  2:邮件 3:APP  4:短信  5:OA  以，隔开
     */
    public void setRemindType(String value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_remind_type</code>. 提醒类型  1:站内信  2:邮件 3:APP  4:短信  5:OA  以，隔开
     */
    public String getRemindType();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_remind_status</code>. 提醒状态 0:未设置提醒 1:未提醒 2:已提醒
     */
    public void setRemindStatus(Integer value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_remind_status</code>. 提醒状态 0:未设置提醒 1:未提醒 2:已提醒
     */
    public Integer getRemindStatus();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>human-resource.t_study_plan_04.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>human-resource.t_study_plan_04.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyPlan_04
     */
    public void from(IStudyPlan_04 from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyPlan_04
     */
    public <E extends IStudyPlan_04> E into(E into);
}
