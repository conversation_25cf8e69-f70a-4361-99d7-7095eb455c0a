package com.zxy.product.exam.service.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.Arrays;
import java.util.List;

@Configuration
public class RedissonAutoConfiguration implements EnvironmentAware {

    private String redisCluster;
    private String password;

    @Override
    public void setEnvironment(Environment environment) {
        this.redisCluster = environment.getProperty("spring.redis.cluster.nodes");
        this.password = environment.getProperty("spring.redis.password");
    }

    @Bean
    public RedissonClient redisson() {
        List<String> nodeAddresses = Arrays.asList(redisCluster.split(","));
        Config config = new Config();
        if (nodeAddresses.size() < 3) {
            config.useSingleServer()
                    .setAddress("redis://" + nodeAddresses.get(0))
                    .setPassword(password)
                    .setRetryInterval(5000)
                    .setTimeout(10000)
                    .setConnectTimeout(10000);
        } else {
            String[] ips = new String[nodeAddresses.size()];
            for (int i = 0; i < nodeAddresses.size(); i++) {
                ips[i] = "redis://" + nodeAddresses.get(i);
            }
            config.useClusterServers()
                    .addNodeAddress(ips)
                    .setPassword(password)
                    .setRetryInterval(5000)
                    .setTimeout(10000)
                    .setConnectTimeout(10000);
        }
        return Redisson.create(config);
    }


}
