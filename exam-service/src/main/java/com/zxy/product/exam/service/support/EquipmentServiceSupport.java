package com.zxy.product.exam.service.support;

import static com.zxy.product.exam.jooq.Tables.EQUIPMENT_TYPE;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.api.EquipmentService;
import com.zxy.product.exam.entity.EquipmentType;

@Service
public class EquipmentServiceSupport implements EquipmentService{

    private CommonDao<EquipmentType> equipmentTypeCommonDao;
    /**
     * 查询全部设备方法
     * @return
     */
    @Override
    public List<EquipmentType> getEquipment(boolean delete) {

        // 后台列表查询需查全部数据，前台列表只展示未删除数据
        List<EquipmentType> list=new ArrayList<EquipmentType>();
        if (delete)
            list= this.equipmentTypeCommonDao.fetch(EQUIPMENT_TYPE.DELETE.ne(EquipmentType.DELETE_YES));
        else
            list= this.equipmentTypeCommonDao.fetch();
    	List<EquipmentType> tempList=new ArrayList<EquipmentType>();
    	EquipmentType temp=new EquipmentType();
    	for(int i=0;i<list.size();i++){
    		if(!list.get(i).getName().equals("无"))
    			tempList.add(list.get(i));
    		else if(list.get(i).getName().equals("无"))
    			temp=list.get(i);
    	}
    	if(temp.getName()!=null)
    	tempList.add(temp);
    	return tempList;
    }

    @Autowired
    public void setEquipmentTypeCommonDao(CommonDao<EquipmentType> equipmentTypeCommonDao) {
        this.equipmentTypeCommonDao = equipmentTypeCommonDao;
    }
}
