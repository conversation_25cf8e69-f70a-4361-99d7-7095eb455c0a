package com.zxy.product.report.async.csv;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springside.modules.utils.collection.ListUtil;
import org.springside.modules.utils.collection.MapUtil;

import com.zxy.product.exam.api.CertificateRecordService;
import com.zxy.product.exam.entity.CertificateRecord;
import com.zxy.product.report.entity.CsvTime;

@Service
public class ExamCertificateRecord {
	private static final Logger LOGGER = LoggerFactory.getLogger(ExamCertificateRecord.class);
	private Integer pageSize = 5000;
	private CertificateRecordService certificateRecordService;
	private CsvUpload  csvUpload;

	@Autowired
	public void setCsvUpload(CsvUpload csvUpload) {
		this.csvUpload = csvUpload;
	}

	@Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }

    public Map<String, List<String>> getExamCertificateRecord() {
		Map<String, List<String>> batchMap = MapUtil.newHashMap();
	    Integer tatol = certificateRecordService.findExamCertificateRecordCount();
	    LOGGER.info("ExamCertificateRecord do getExamCertificateRecord get tatol : " + tatol);
	    Integer pages = (tatol + pageSize -1) / pageSize;
	    LOGGER.info("ExamCertificateRecord do getExamCertificateRecord get pages : " + pages);
		for (int i = 1; i < pages + 1; i++) {
			List<CertificateRecord> examCertificateRecordList = new ArrayList<CertificateRecord>();
			examCertificateRecordList = certificateRecordService.findExamCertificateRecord(i, pageSize)
			        .stream()
                    .filter(c -> null != c.getMember().getName())
                    .filter(c -> ((null != c.getProfession().getName())
                                    || (null != c.getSubProfession().getName())
                                    || (null != c.getEquipmentType().getName())
                                    || (null != c.getProfessionLevel().getLevelName())))
                    .collect(Collectors.toList());
		    LOGGER.info("ExamCertificateRecord do getExamCertificateRecord get examCertificateRecordList() batch count  : " + examCertificateRecordList.size());
			List<String> list = ListUtil.newArrayList();
			examCertificateRecordList.forEach(c -> {
			    String certificateRecordId = c.getId();
			    Integer certificateRecordScoreLevel = c.getScoreLevel();
			    Long certificateRecordIssueTime = c.getIssueTime();
			    Long certificateRecordValidDate = c.getValidDate();
			    String memberName = csvUpload.filte(c.getMember().getName());
			    String memberFullName = csvUpload.filte(c.getMember().getFullName());
			    String professionName = csvUpload.filte(c.getProfession().getName());
			    String subProfessionName = csvUpload.filte(c.getSubProfession().getName());
			    String equipmentTypename = csvUpload.filte(c.getEquipmentType().getName());
			    String levelName = csvUpload.filte(c.getProfessionLevel().getLevelName());

				String info = certificateRecordId + CsvTime.CSV_COMMA +
				              memberName + CsvTime.CSV_COMMA +
						      memberFullName + CsvTime.CSV_COMMA +
						      professionName + CsvTime.CSV_COMMA +
						      subProfessionName + CsvTime.CSV_COMMA +
						      equipmentTypename + CsvTime.CSV_COMMA +
						      levelName + CsvTime.CSV_COMMA +
						      certificateRecordScoreLevel + CsvTime.CSV_COMMA +
						      certificateRecordIssueTime + CsvTime.CSV_COMMA +
						      certificateRecordValidDate;
				list.add(info);
			});
			batchMap.put(i + "p", list);
		}
		return batchMap;
	}

}
