
package com.zxy.product.report.async.intelligence.jiutian.producer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingQueue;

import static com.zxy.product.report.async.intelligence.jiutian.JiuTianConstant.PAGE_SIZE;

/**
 * @ProjectName: zxy-mobile-report
 * @Package: com.zxy.product.report.async.intelligence.jiutian.consumer
 * @ClassName: AbstractSyncProducer
 * @Author: futzh
 * @Description: 九天生产者线程抽象类
 * @Date: 2021/8/3 14:03
 * @Version: 1.0
 */
public abstract class AbstractSyncProducer<T> implements Runnable {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractSyncProducer.class);

    private final T poison;
    private final LinkedBlockingQueue<T> queue;
    private final Optional<Long> startTime;
    private final Optional<Long> endTime;

    public AbstractSyncProducer(T poison, LinkedBlockingQueue<T> queue, Optional<Long> startTime, Optional<Long> endTime) {
        this.poison = poison;
        this.queue = queue;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    @Override
    public void run() {

        LOGGER.info("生产者线程已启动：{}", Thread.currentThread());
        int page = 1;
        while (true) {
            int offset = (page - 1) * PAGE_SIZE;
            List<T> list = new ArrayList<>();
            try {
                list = this.findPage(offset, startTime, endTime);
            } catch (Exception e) {
                e.printStackTrace();
            }
            LOGGER.info("分页查询数量第{}页:{}", page, list.size());
            page++;

            LOGGER.info("队列数量：{}", queue.size());
            if (list.size() > 0) {
                list.forEach(e -> {
                    try {
                        queue.put(e);
                    } catch (InterruptedException interruptedException) {
                        interruptedException.printStackTrace();
                    }
                });
            }
            // 最后一次不满pageSize或为0时，添加毒丸对象，跳出循环
            if (list.size() < PAGE_SIZE) {
                try {
                    queue.put(poison);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                break;
            }
        }
        LOGGER.info("生产者线程已结束：{}", Thread.currentThread());
    }

    protected abstract List<T> findPage(int offset, Optional<Long> startTime, Optional<Long> endTime);

}