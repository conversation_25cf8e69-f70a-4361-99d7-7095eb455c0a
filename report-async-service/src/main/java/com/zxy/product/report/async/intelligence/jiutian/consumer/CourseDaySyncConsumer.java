package com.zxy.product.report.async.intelligence.jiutian.consumer;

import com.zxy.product.course.entity.CourseSectionStudyLogAhDay;
import com.zxy.product.report.async.intelligence.jiutian.JiuTianConstant;
import com.zxy.product.report.async.intelligence.jiutian.JiuTianService;
import com.zxy.product.report.async.intelligence.jiutian.util.CourseUpload;
import com.zxy.product.report.async.intelligence.jiutian.util.DateUtil;
import com.zxy.product.system.api.operation.MessageSendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;

import static com.zxy.product.report.async.intelligence.jiutian.JiuTianConstant.DELIMITER;


public class CourseDaySyncConsumer extends AbstractSyncConsumer<CourseSectionStudyLogAhDay> {

    private final Logger LOGGER = LoggerFactory.getLogger(CourseDaySyncConsumer.class);

    public CourseDaySyncConsumer(LinkedBlockingQueue<CourseSectionStudyLogAhDay> queue, CourseSectionStudyLogAhDay poison, int producerCount, MessageSendService messageSendService) {
        super(queue, poison, producerCount, messageSendService);
    }

    @Override
    protected void writeAndUpload(CourseUpload uploader, List<CourseSectionStudyLogAhDay> list, int seq, boolean finishFlag) {
        StringBuilder sb = new StringBuilder();
        String businessTypeCourse = "1";
        list.forEach(d -> sb
                .append(d.getId()).append(DELIMITER)
                .append(d.getMemberId()).append(DELIMITER)
                .append(d.getCourseId()).append(DELIMITER)
                .append(JiuTianService.replace(d.getCourseName())).append(DELIMITER)
                .append(businessTypeCourse).append(DELIMITER)
                .append(d.getStudyTime()).append(DELIMITER)
                .append(d.getAppStudyTime()).append(DELIMITER)
                .append(d.getPcStudyTime()).append(DELIMITER)
                .append(d.getCourseStatus()).append(DELIMITER)
                .append(DateUtil.format(new Date(Objects.nonNull(d.getCreateTime()) ? d.getCreateTime() : System.currentTimeMillis()), DateUtil.DATE_TIME_SEPARATOR_PATTERN))
                .append("\r\n"));
        LOGGER.info("学习的长度为 {}", list.size());
        String data="";
        if (sb.length() > 2) {
            data = sb.delete(sb.length() - 2, sb.length()).toString().replaceAll("null", "");
        }
        uploader.upload(data, JiuTianConstant.SYNC_TYPE_USER_STUDY, seq, list.size(), finishFlag);
    }

    @Override
    protected String getSyncType() {
        return JiuTianConstant.SYNC_TYPE_USER_STUDY;
    }
}
