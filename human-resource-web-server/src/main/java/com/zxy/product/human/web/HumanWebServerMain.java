package com.zxy.product.human.web;


import com.zxy.common.restful.websocket.WebSocketConfig;
import com.zxy.product.human.web.config.*;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration;
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration;
import org.springframework.boot.autoconfigure.web.*;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@Configuration
//@ComponentScan
@EnableAspectJAutoProxy
@Import({
        RestfulConfig.class,
        RPCClientConfig.class,
        WebConfig.class,
        MessageConfig.class,
        CacheConfig.class,
        WebSocketConfig.class,
        FastDFSConfig.class,
        RabbitAutoConfiguration.class,
        EmbeddedServletContainerAutoConfiguration.class,
        DispatcherServletAutoConfiguration.class,
        HttpEncodingAutoConfiguration.class,
        HttpMessageConvertersAutoConfiguration.class,
        JacksonAutoConfiguration.class,
        MultipartAutoConfiguration.class,
        ServerPropertiesAutoConfiguration.class,
        PropertyPlaceholderAutoConfiguration.class,
        WebMvcAutoConfiguration.class,
        ErrorMvcAutoConfiguration.class
})
public class HumanWebServerMain {

    public static void main(String[] args) {
        SpringApplication.run(HumanWebServerMain.class, args);
    }

}
