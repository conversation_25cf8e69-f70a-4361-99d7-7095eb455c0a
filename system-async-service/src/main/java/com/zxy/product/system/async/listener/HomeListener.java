package com.zxy.product.system.async.listener;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.restful.multipart.Attachment;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.multipart.support.FastDFSAttachmentResolver;
import com.zxy.common.restful.util.Encrypt;
import com.zxy.product.course.api.CourseChapterInfoService;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.jooq.tables.pojos.CourseChapterSectionEntity;
import com.zxy.product.exam.api.ExamService;
import com.zxy.product.exam.api.ResearchRecordService;
import com.zxy.product.exam.entity.ResearchQuestionary;
import com.zxy.product.report.api.home.HomeReportService;
import com.zxy.product.report.domain.ShortVideoCfgVO;
import com.zxy.product.system.api.home.HomeSystemService;
import com.zxy.product.system.api.home.SystemPolymerService;
import com.zxy.product.system.api.homeconfig.HomeCertifyService;
import com.zxy.product.system.api.homeconfig.HomeConfigService;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.domain.vo.*;
import com.zxy.product.system.domain.vo.home.certify.CertifyCDN;
import com.zxy.product.system.domain.vo.home.customize.CustomizeParentVO;
import com.zxy.product.system.domain.vo.home.featured.content.ContentRegionVO;
import com.zxy.product.system.domain.vo.home.featured.content.FeaturedContentParentVO;
import com.zxy.product.system.domain.vo.home.video.ShortVideoAppVO;
import com.zxy.product.system.domain.vo.home.video.ShortVideoParentVO;
import com.zxy.product.system.entity.*;
import com.zxy.product.train.api.home.HomeTrainService;
import com.zxy.product.train.vo.HomeLecturerVO;
import org.jooq.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.Comparator;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.zxy.product.system.async.util.FileUtil.transferTo;
import static com.zxy.product.system.content.CacheKeyConstant.CERTIFY_OUTSIDE_CERTIFICATION_COURSE_COUNT_REDIS_KEY;
import static com.zxy.product.system.content.CacheKeyConstant.CERTIFY_OUTSIDE_CERTIFICATION_REDIS_KEY;
import static com.zxy.product.system.content.ErrorCode.HomeFileUploadFailedAndWillBeRetriedSoon;
import static com.zxy.product.system.jooq.Tables.*;
import static com.zxy.product.system.util.DatasetProcessing.*;
import static com.zxy.product.system.util.enums.ClientTypeEnum.App;
import static com.zxy.product.system.util.enums.ClientTypeEnum.PC;
import static com.zxy.product.system.util.enums.HomeModelEnum.*;

/**
 * 十万——首页CDN文件地址监听类
 * <AUTHOR>
 * @date 2024年08月08日 17:12
 */
@Component
public class HomeListener extends AbstractMessageListener {
    private static final Logger logger= LoggerFactory.getLogger(HomeListener.class);
    private DSLContext context;
    private HomeSystemService systemService;
    private SystemPolymerService systemPolymerService;
    private AttachmentResolver attachmentResolver;
    private HomeTrainService trainService;
    private HomeReportService homeReportService;
    private HomeCertifyService homeCertifyService;
    private CourseChapterInfoService courseChapterInfoService;

    @Resource
    private ExamService examService;
    @Resource
    private ResearchRecordService researchRecordService;
    @Resource
    private HomeConfigService homeConfigService;
    @Resource
    private CommonDao<HomeCdn> commonDao;
    @Resource
    private Redis redis;
    @Autowired
    public void setContext(DSLContext context){this.context=context; }

    @Autowired
    public void setHomeSystemService(HomeSystemService systemService){ this.systemService=systemService; }

    @Autowired
    public void setSystemPolymerService(SystemPolymerService systemPolymerService){
        this.systemPolymerService=systemPolymerService;
    }

    @Autowired
    public void setAttachmentResolver(FastDFSAttachmentResolver  attachmentResolver){
        this.attachmentResolver=attachmentResolver;
    }

    @Autowired
    public void setTrainService(HomeTrainService trainService){ this.trainService=trainService; }

    @Autowired
    public void setHomeReportService(HomeReportService homeReportService){ this.homeReportService=homeReportService; }

    @Autowired
    public void setHomeCertifyService(HomeCertifyService homeCertifyService) {
        this.homeCertifyService = homeCertifyService;
    }

    @Autowired
    public void setCourseChapterInfoService(CourseChapterInfoService courseChapterInfoService) {
        this.courseChapterInfoService = courseChapterInfoService;
    }

    @Override
    protected void onMessage(Message message) {
        logger.info("消息接收成功{}", JSON.toJSONString(message));
        Map<Integer,Runnable> runnableMap=new HashMap<>(5);
        runnableMap.put(MessageTypeContent.HOME_CONFIG, this::overrideSystemCfg);
        runnableMap.put(MessageTypeContent.HOME_SYSTEM,
                () -> {
                    String cfgId = message.getHeader(MessageHeaderContent.HOME_CFG_ID);
                    this.overrideHomeCfg(cfgId, PC.getCode());
                    this.overrideHomeCfg(cfgId, App.getCode());
                });
        runnableMap.put(MessageTypeContent.HOME_CERTIFY_CONFIG, this::doCertifyCfgFile);
        runnableMap.get(message.getType()).run();
    }

    /**
     * 获取首页模块配置Id
     * @param modelCode 首页模块编码
     * @param modelMap 以模块编码分组的首页模块Map
     * @return 获取首页模块配置Id
     */
    private HomeModuleConfig doHomeModel(String modelCode,
                                         Map<String, List<HomeModuleConfig>> modelMap){
        List<HomeModuleConfig> moduleCfgCollect = modelMap.get(modelCode);
        return Optional.ofNullable(moduleCfgCollect)
                .filter(CollectionUtils::isNotEmpty)
                .filter(ew1->!Objects.equals(Layout.getCode(),modelCode))
                .map(ew2->ew2.get(0))
                .orElse(null);
    }

    /**
     * 执行填充|构建数据集合
     * @param homeCfg 首页配置块POJO
     * @param cfgCollect 回显文件的JSON文件集合数据
     * @param supplier 首页配置块方法函数
     */
    private void doBuildCfgCollect(HomeModuleConfig homeCfg, List<HomeCfgVO> cfgCollect,
                                   Supplier<List<? extends HomeParentVO>> supplier, Integer clientType) {
        Optional.ofNullable(homeCfg)
                .ifPresent(ew1 -> {
                    HomeCfgVO cfgVO = this.doBuildCommonCfgVO(clientType,ew1);
                    HomeCfgVO.HomeCfgModelVO modelVO = cfgVO.getValue();
                    modelVO.setList(supplier.get());
                    cfgVO.setValue(modelVO);
                    cfgCollect.add(cfgVO);
                });
    }

    /**
     * 执行构建公共配置VO
     * @param clientType 客户端类型 1PC 2App 0全部
     * @param moduleCfg 首页配置POJO对象
     * @return 首页CDN文件中单一配置VO
     */
    private HomeCfgVO doBuildCommonCfgVO(Integer clientType,HomeModuleConfig moduleCfg){
        HomeCfgVO cfgVO=new HomeCfgVO();
        cfgVO.setName(moduleCfg.getModuleCode());
        Optional.of(clientType).filter(ew2->Objects.equals(PC.getCode(),ew2)).ifPresent(ew3->cfgVO.setSort(moduleCfg.getSort()));
        Optional.of(clientType).filter(ew2->Objects.equals(App.getCode(),ew2)).ifPresent(ew3->cfgVO.setSort(moduleCfg.getAppSort()));
        HomeCfgVO.HomeCfgModelVO cfgModelVO = new HomeCfgVO.HomeCfgModelVO();
        cfgModelVO.setConfigId(moduleCfg.getId());
        cfgModelVO.setName(moduleCfg.getName());
        cfgModelVO.setConfigCode(moduleCfg.getModuleCode());
        cfgModelVO.setLinkState(moduleCfg.getLinkState());
        cfgModelVO.setLinkAddress(moduleCfg.getLinkAddress());
        Optional.of(clientType).filter(ew2-> Objects.equals(PC.getCode(),ew2)).ifPresent(ew3->cfgModelVO.setStyle(moduleCfg.getStyle()));
        Optional.of(clientType).filter(ew2->Objects.equals(App.getCode(),ew2)).ifPresent(ew3->cfgModelVO.setStyle(moduleCfg.getAppStyle()));
        cfgVO.setValue(cfgModelVO);
        return cfgVO;
    }

    /**
     * 初始化首页配置数据填充映射
     * @param clientType 客户端类型 1PC 2App
     * @param modelMap 首页配置块映射Map
     * @return 首页模块填充数据实现类映射Map集合
     */
    private  List<HomeCfgVO> innitHomeCfg(Integer clientType, Map<String, List<HomeModuleConfig>> modelMap){
        List<HomeCfgVO> homeCfgCollect = Lists.newArrayList();
        HomeModuleConfig studyCard = this.doHomeModel(StudyCard.getCode(), modelMap);
        this.doBuildCfgCollect(studyCard,homeCfgCollect, Lists::newArrayList,clientType);
        HomeModuleConfig picksHot = this.doHomeModel(PicksHot.getCode(), modelMap);
        this.doBuildCfgCollect(picksHot,homeCfgCollect,Lists::newArrayList,clientType);
        HomeModuleConfig recommendNew = this.doHomeModel(RecommendNew.getCode(), modelMap);
        this.doBuildCfgCollect(recommendNew,homeCfgCollect,Lists::newArrayList,clientType);
        HomeModuleConfig expert = this.doHomeModel(Expert.getCode(), modelMap);
        this.doBuildCfgCollect(expert,homeCfgCollect,Lists::newArrayList,clientType);
        HomeModuleConfig openClass = this.doHomeModel(OpenClass.getCode(), modelMap);
        this.doBuildCfgCollect(openClass,homeCfgCollect, Lists::newArrayList,clientType);
        HomeModuleConfig hotApp = this.doHomeModel(HotApp.getCode(), modelMap);
        this.doBuildCfgCollect(hotApp,homeCfgCollect, Lists::newArrayList,clientType);
        this.paddingFeaturedContentCfg(clientType,modelMap,homeCfgCollect);
        this.paddingLecturerCfg(clientType,modelMap,homeCfgCollect);
        this.paddingShortVideoCfg(clientType,modelMap,homeCfgCollect);
        HomeModuleConfig  courseDirectory= this.doHomeModel(CourseDirectory.getCode(), modelMap);
        Optional<String> typeOpt=PC.getCode()==clientType?Optional.of("0,1"):Optional.of("0,2");
        this.doBuildCfgCollect(courseDirectory,homeCfgCollect,()-> systemService.courseNavigationCfg(courseDirectory.getId(), typeOpt,clientType),clientType);
        HomeModuleConfig bigBanner = this.doHomeModel(BigBannerNew.getCode(), modelMap);
        this.doBuildCfgCollect(bigBanner,homeCfgCollect,()->systemService.bigBannerCfg(bigBanner.getId(),clientType,Optional.empty()),clientType);
        HomeModuleConfig smallBanner = this.doHomeModel(SmallBannerNew.getCode(), modelMap);
        this.doBuildCfgCollect(smallBanner,homeCfgCollect,()->systemService.miniBannerCfg(smallBanner.getId(),clientType,Optional.empty()),clientType);
        HomeModuleConfig homeNews = this.doHomeModel(News.getCode(), modelMap);
        this.doBuildCfgCollect(homeNews,homeCfgCollect, ()->systemService.newsCfg(homeNews.getId(), Optional.empty(),Optional.empty(),clientType),clientType);
        HomeModuleConfig classifiedCourses = this.doHomeModel(ClassifiedCourses.getCode(), modelMap);
        this.doBuildCfgCollect(classifiedCourses,homeCfgCollect,()->systemService.categoryCourseCfg(classifiedCourses.getId(),typeOpt,clientType),clientType);
        HomeModuleConfig interactiveLearning = this.doHomeModel(InteractiveLearning.getCode(), modelMap);
        this.doBuildCfgCollect(interactiveLearning,homeCfgCollect,()->systemService.interactionCfg(interactiveLearning.getId(),3,clientType,Optional.empty()),clientType);
        if (PC.getCode() == clientType) this.paddingCertifyCfg(clientType,modelMap,homeCfgCollect);
        return homeCfgCollect;
    }

    /**
     * 特殊处理：填充首页短视频（目前短视频只有在App存在）
     * @param clientType 客户端类型 1PC 2App
     * @param modelMap 映射Map
     * @param cfgCollect 回显前端的首页配置集合
     */
    private void paddingShortVideoCfg(Integer clientType,
                                      Map<String, List<HomeModuleConfig>> modelMap,
                                      List<HomeCfgVO> cfgCollect){
        HomeModuleConfig shortVideoCfg = this.doHomeModel(ShortVideo.getCode(), modelMap);
        Optional.ofNullable(shortVideoCfg).ifPresent(ew1->{
            List<ShortVideoCfgVO> shortVideoCfgCollect = homeReportService.shortVideoCfg(ew1.getId(), 10);
            List<ShortVideoParentVO> convertShortVideoCollect = shortVideoCfgCollect.parallelStream()
                    .map(ew2 -> {
                        ShortVideoAppVO shortVideoAppVO=new ShortVideoAppVO();
                        shortVideoAppVO.setId(ew2.getId());
                        shortVideoAppVO.setCoverPath(ew2.getCoverPath());
                        shortVideoAppVO.setShortVideoId(ew2.getShortVideoId());
                        shortVideoAppVO.setShortVideoTitle(ew2.getShortVideoTitle());
                        shortVideoAppVO.setCoverOriginalPath(ew2.getCoverOriginalPath());
                        return (ShortVideoParentVO)shortVideoAppVO;
                    }).collect(Collectors.toList());
            HomeCfgVO cfgVO = this.doBuildCommonCfgVO(clientType,ew1);
            HomeCfgVO.HomeCfgModelVO modelVO = cfgVO.getValue();
            modelVO.setList(convertShortVideoCollect);
            cfgVO.setValue(modelVO);
            cfgCollect.add(cfgVO);
        });
    }

    /**
     * 特殊处理：填充首页精选内容
     * @param clientType 客户端类型 1PC 2App
     * @param modelMap 映射Map
     * @param cfgCollect 回显前端的首页配置集合
     */
    private void paddingFeaturedContentCfg(Integer clientType,
                                           Map<String, List<HomeModuleConfig>> modelMap,
                                           List<HomeCfgVO> cfgCollect){
        HomeModuleConfig picksContent = this.doHomeModel(Picks.getCode(), modelMap);
        Optional.ofNullable(picksContent).ifPresent(ew1->{
            List<? extends FeaturedContentParentVO> contentCollect = systemService.featuredContentCfg(ew1.getId(), 6, clientType);
            Map<String,Object> contentMap=new HashMap<>(4);
            List<? extends FeaturedContentParentVO> courseCollect = contentCollect.parallelStream().filter(ew2 -> Objects.equals(0, ew2.getNonPersistentDataType())).collect(Collectors.toList());
            contentMap.put("course",courseCollect);
            List<? extends FeaturedContentParentVO> subjectCollect = contentCollect.parallelStream().filter(ew3 -> Objects.equals(1, ew3.getNonPersistentDataType())).collect(Collectors.toList());
            contentMap.put("subject",subjectCollect);
            HomeCfgVO cfgVO = this.doBuildCommonCfgVO(clientType,ew1);
            cfgVO.setName(PC.getCode()==clientType?PicksContent.getCode():Picks.getCode());
            HomeCfgVO.HomeCfgModelVO modelVO = cfgVO.getValue();
            List<ContentRegionVO> contentRegionCollect = JSON.parseArray(picksContent.getRegionCode(), ContentRegionVO.class);
            Optional<ContentRegionVO> contentOpt = contentRegionCollect.parallelStream().filter(ew2 -> Objects.equals(cfgVO.getName(), ew2.getModuleCode())).findFirst();
            modelVO.setName(contentOpt.map(ContentRegionVO::getName).orElse("精选内容"));
            modelVO.setData(contentMap);
            modelVO.setConfigCode(PC.getCode()==clientType?PicksContent.getCode():Picks.getCode());
            cfgVO.setValue(modelVO);
            cfgCollect.add(cfgVO);
            Optional.of(clientType).filter(ew3->Objects.equals(PC.getCode(),ew3)).ifPresent(ew4->this.paddingHotContentCfg(ew4,picksContent,cfgCollect));
        });
    }

    /**
     * 特殊处理：填充首页热门内容（因为精选内容于热门内容均包含在Pick模块中，前端分开解析，固特殊处理）
     * @param clientType 客户端类型 1PC 2App
     * @param picksContent 热门内容POJO
     * @param cfgCollect 回显前端的首页配置集合
     */
    private void paddingHotContentCfg(Integer clientType,
                                   HomeModuleConfig picksContent, List<HomeCfgVO> cfgCollect){
        HomeCfgVO cfgVO = this.doBuildCommonCfgVO(clientType, picksContent);
        cfgVO.setName(PicksHot.getCode());
        HomeCfgVO.HomeCfgModelVO modelVO = cfgVO.getValue();
        List<ContentRegionVO> contentRegionCollect = JSON.parseArray(picksContent.getRegionCode(), ContentRegionVO.class);
        Optional<ContentRegionVO> hotContentOpt = contentRegionCollect.parallelStream().filter(ew1 -> Objects.equals(cfgVO.getName(), ew1.getModuleCode())).findFirst();
        modelVO.setName(hotContentOpt.map(ContentRegionVO::getName).orElse("热门内容"));
        modelVO.setConfigCode(PicksHot.getCode());
        modelVO.setList(Lists.newArrayList());
        cfgVO.setValue(modelVO);
        cfgCollect.add(cfgVO);
    }

    /**
     * 特殊处理：填充首页讲师榜
     * @param clientType 客户端类型 1PC 2App
     * @param modelMap 映射Map
     * @param cfgCollect 回显前端的首页配置集合
     */
    private void paddingLecturerCfg(Integer clientType,
                                    Map<String, List<HomeModuleConfig>> modelMap,
                                    List<HomeCfgVO> cfgCollect){
        HomeModuleConfig lectureCfg = this.doHomeModel(Lecturer.getCode(), modelMap);
        Optional.ofNullable(lectureCfg)
                .ifPresent(ew0->{
                    List<HomeLecturerVO> homeLecturerCollect = trainService.lecturerCollect(ew0.getId(), 10);
                    List<ConvertLectureVO> convertLectureCollect = homeLecturerCollect.parallelStream()
                            .map(ew1 -> {
                                ConvertLectureVO convertLectureVO = new ConvertLectureVO();
                                convertLectureVO.setLecturerId(ew1.getLecturerId());
                                ConvertLectureVO.LecturerVO lecturerVO = new ConvertLectureVO.LecturerVO();
                                lecturerVO.setAttributeId(ew1.getLecturer().getAttributeId());
                                lecturerVO.setCoverPath(ew1.getLecturer().getCoverPath());
                                lecturerVO.setInstitutions(ew1.getLecturer().getInstitutions());
                                lecturerVO.setJobName(ew1.getLecturer().getJobName());
                                lecturerVO.setLevelName(ew1.getLecturer().getLevelName());
                                lecturerVO.setName(ew1.getLecturer().getName());
                                lecturerVO.setType(ew1.getLecturer().getType());
                                lecturerVO.setUnit(ew1.getLecturer().getUnit());
                                convertLectureVO.setLecturer(lecturerVO);
                                return convertLectureVO;
                            }).collect(Collectors.toList());
                    HomeCfgVO cfgVO = this.doBuildCommonCfgVO(clientType, ew0);
                    HomeCfgVO.HomeCfgModelVO modelVO = cfgVO.getValue();
                    modelVO.setList(convertLectureCollect);
                    cfgVO.setValue(modelVO);
                    cfgCollect.add(cfgVO);
                });
    }
    private void paddingCertifyCfg(Integer clientType,
                                      Map<String, List<HomeModuleConfig>> modelMap,
                                      List<HomeCfgVO> cfgCollect){
        HomeModuleConfig certifyCfg = this.doHomeModel(Certify.getCode(), modelMap);

        Optional.ofNullable(certifyCfg).ifPresent(modelconfig->{
            Map<String, List<CertifyCDN>> cdnFileCfgs = homeCertifyService.findCDNFileCfgs(modelconfig.getHomeConfigId());
            // 提前将外部认证的课程数据加载到配置中使用
            List<CertifyCDN> outsideCertify = cdnFileCfgs.get(HomeCertify.DATA_TYPE_OUTSIDE.toString());
            if (Objects.nonNull(outsideCertify)) {
                Map<String, List<CourseChapterSection>> sectionMap = paddingData(outsideCertify);

            outsideCertify.forEach(certify -> {
                String dataId = certify.getDataId();

                List<CertifyCDN.ResourcesCDN> collect = Optional.ofNullable(sectionMap.get(dataId)).orElse(new ArrayList<>()).stream().map(
                        sectionInfo -> new CertifyCDN.ResourcesCDN()
                                .setResourceId(sectionInfo.getResourceId())
                                .setResourceType(sectionInfo.getSectionType())
                                .setTimeMinute(sectionInfo.getTimeMinute())
                                .setTimeSecond(sectionInfo.getTimeSecond())
                                .setUrl(sectionInfo.getUrl())
                                .setCoverPath(sectionInfo.getCoverPath())
                                .setName(sectionInfo.getName())
                ).collect(Collectors.toList());

                    certify.setResources(collect);
                });
                cdnFileCfgs.put(HomeCertify.DATA_TYPE_OUTSIDE.toString(),outsideCertify);
            }

            HomeCfgVO cfgVO = this.doBuildCommonCfgVO(clientType,modelconfig);
            HomeCfgVO.HomeCfgModelVO modelVO = cfgVO.getValue();
            modelVO.setData(new HashMap<>(Optional.of(cdnFileCfgs).orElse(new HashMap<>())));
            cfgVO.setValue(modelVO);

            cfgCollect.add(cfgVO);
        });
    }

    private Map<String, List<CourseChapterSection>> paddingData(List<CertifyCDN> outsideCertify) {
        // region 获取外部认证业务数据
        List<String> subjectIds = outsideCertify.stream().map(CertifyCDN::getDataId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, List<CourseChapterSection>> sectionMap = courseChapterInfoService.findBySubjectId(subjectIds);
        Collection<List<CourseChapterSection>> values = sectionMap.values();
        Map<Integer, List<CourseChapterSection>> sectionTypeMap = values.stream()
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(CourseChapterSection::getSectionType));
        // 填充课程数量
        Optional.ofNullable(redis.process(redis -> redis.get(CERTIFY_OUTSIDE_CERTIFICATION_COURSE_COUNT_REDIS_KEY))).ifPresent(
                json -> {
                    Map map = JSON.parseObject(json, Map.class);
                    outsideCertify.forEach(certify -> {
                        String dataId = certify.getDataId();
                        certify.setCourseCount(Optional.ofNullable(map.get(dataId)).map(Integer.class::cast).orElse(0));
                    });
                }
        );
        // 考试
        List<String> examInfoIds = sectionTypeMap.getOrDefault(CourseChapterSection.SECTION_TYPE_EXAM, Collections.emptyList()).stream().map(CourseChapterSectionEntity::getResourceId).collect(Collectors.toList());
        Map<String, String> knowledgeMap = examService.findCoverByIds(examInfoIds);
        // 调研
        List<String> researchInfoIds = sectionTypeMap.getOrDefault(CourseChapterSection.SECTION_TYPE_RESEARCH, Collections.emptyList()).stream().map(CourseChapterSectionEntity::getResourceId).collect(Collectors.toList());
        Map<String, String> courseInfoMap = researchRecordService.getMapCoverPaths(researchInfoIds, ResearchQuestionary.TYPE_RESEARCH_QUESTIONARY);
        // 评估
        List<String> evaluationInfoIds = sectionTypeMap.getOrDefault(CourseChapterSection.SECTION_TYPE_EVALUATION, Collections.emptyList()).stream().map(CourseChapterSectionEntity::getResourceId).collect(Collectors.toList());
        Map<String, String> genseeInfoMap = researchRecordService.getMapCoverPaths(evaluationInfoIds,ResearchQuestionary.TYPE_EVALUATE_QUESTIONARY);
        // 填充数据
        values.forEach(v -> v.forEach(v1 -> {
            if (Objects.isNull(v1.getCoverPath()) || v1.getCoverPath().isEmpty()){
                v1.setCoverPath(genseeInfoMap.getOrDefault(v1.getResourceId(), courseInfoMap.getOrDefault(v1.getResourceId(), knowledgeMap.getOrDefault(v1.getResourceId(), ""))));
            }
        }));
        // 初始化外部资源中的子资源缓存
        List<String> ids = sectionTypeMap.values().stream().flatMap(Collection::stream).map(CourseChapterSectionEntity::getResourceId).collect(Collectors.toList());
        ids.addAll(subjectIds);
        initCertifyCache(ids);
        // endregion
        return sectionMap;
    }


    /**
     * 特殊处理：填充自定义文件且复写首页配置项CDN文件
     * @param homeCfgId 首页主配置Id
     * @param clientType 客户端类型
     */
    private void overrideHomeCfg(String homeCfgId, Integer clientType) {
        List<HomeModuleConfig> configCollect = systemService.homeModuleCfg(homeCfgId, clientType);
        Map<String, List<HomeModuleConfig>> modelMap = configCollect.parallelStream()
                .collect(Collectors.groupingBy(HomeModuleConfig::getModuleCode));
        List<HomeCfgVO> cfgCollect = this.innitHomeCfg(clientType, modelMap);
        List<HomeModuleConfig> customCfgCollect = modelMap.get(Layout.getCode());
        Optional.ofNullable(customCfgCollect)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(ew0->{
                    List<HomeCfgVO> customizeCollect = ew0.stream().map(ew1 -> {
                        List<? extends CustomizeParentVO> contentChildren = systemService.customizeCfg(ew1.getId(), 20, clientType);
                        HomeCfgVO cfgVO = this.doBuildCommonCfgVO(clientType, ew1);
                        HomeCfgVO.HomeCfgModelVO modelVO = cfgVO.getValue();
                        modelVO.setList(contentChildren);
                        cfgVO.setValue(modelVO);
                        return cfgVO;
                    }).collect(Collectors.toList());
                    cfgCollect.addAll(customizeCollect);
                });
        cfgCollect.sort(Comparator.comparingInt(HomeCfgVO::getSort));
        this.overrideFile(cfgCollect,homeCfgId,clientType);
    }

    /**
     * 添加页眉页脚并覆写首页配置项文件JSON
     * @param paddingCollect 首页配置项数据集合
     * @param cfgId 首页主配置Id
     * @param clientType 客户端类型 1PC 2App
     */
    private void overrideFile(List<HomeCfgVO> paddingCollect,String cfgId,Integer clientType) {
        HomeParentCfgVO parentCfgVO = new HomeParentCfgVO();
        parentCfgVO.setCfgCollect(paddingCollect);
        List<HomeNav> navCollect = systemPolymerService.navCollect(cfgId, clientType);
        parentCfgVO.setNavCollect(navCollect);
        logger.info("生成的配置文件信息{}",JSON.toJSONString(parentCfgVO));
        String path = this.doOverrideFile(JSON.toJSONString(parentCfgVO), cfgId + clientType);
        this.editHomeAddress(cfgId,path, clientType);
    }

    /**
     * 修正首页文件地址
     * @param cfgId 主配置Id
     * @param aesPath 加密路径
     * @param clientType 客户端类型
     */
    private void editHomeAddress(String cfgId, String aesPath, Integer clientType){
        Collection<SelectField<?>> select = doSelect(HOME_CDN.APP_PATH,HOME_CDN.PC_PATH,HOME_CDN.CONFIG_ID,
                HOME_CDN.ORG_ID, HOME_CDN.CREATE_TIME,HOME_CDN.ID);
        Optional<HomeCdn> cdnOpt = context.select(select)
                .from(HOME_CDN)
                .leftJoin(HOME_CONFIG).on(HOME_CONFIG.ID.eq(HOME_CDN.CONFIG_ID))
                .where(HOME_CDN.CONFIG_ID.eq(cfgId))
                .fetchOptional(ew1 -> {
                    HomeCdn cdn = new HomeCdn();
                    cdn.setOrgId(ew1.getValue(HOME_CDN.ORG_ID));
                    cdn.setId(ew1.getValue(HOME_CDN.ID));
                    cdn.setAppPath(ew1.getValue(HOME_CDN.APP_PATH));
                    cdn.setPcPath(ew1.getValue(HOME_CDN.PC_PATH));
                    cdn.setConfigId(ew1.getValue(HOME_CDN.CONFIG_ID));
                    cdn.setCreateTime(ew1.getValue(HOME_CDN.CREATE_TIME));
                    return cdn;
                });
        Map<Integer,Runnable> runnableMap=new HashMap<>(3);
        runnableMap.put(PC.getCode(),()->this.orderingPcAddress(cfgId,aesPath,cdnOpt));
        runnableMap.put(App.getCode(),()->this.orderingAppAddress(cfgId,aesPath,cdnOpt));
        runnableMap.get(clientType).run();
    }

    /**
     * 首页PC客户端CDN文件落盘
     * @param cfgId 首页著配置Id
     * @param aesPath CDN加密文件地址
     * @param homeCdnOpt 查询的首页文件对象（赋值|校验）
     */
    private void orderingPcAddress(String cfgId, String aesPath, Optional<HomeCdn> homeCdnOpt){
        homeCdnOpt.map(ew1 -> context.update(HOME_CDN)
                .set(HOME_CDN.PC_PATH, aesPath)
                .where(HOME_CDN.ID.eq(ew1.getId()))
                .execute()
        ).orElseGet(() -> context.insertInto(HOME_CDN)
                .set(HOME_CDN.ID, String.valueOf(UUID.randomUUID()))
                .set(HOME_CDN.CONFIG_ID, cfgId)
                .set(HOME_CDN.PC_PATH, aesPath)
                .set(HOME_CDN.CREATE_TIME, System.currentTimeMillis())
                .execute());
    }

    /**
     * 首页App客户端CDN文件落盘
     * @param cfgId 首页著配置Id
     * @param aesPath CDN加密文件地址
     * @param homeCdnOpt 查询的首页文件对象（赋值|校验）
     */
    private void orderingAppAddress(String cfgId, String aesPath, Optional<HomeCdn> homeCdnOpt){
        homeCdnOpt.map(ew1 -> context.update(HOME_CDN)
                .set(HOME_CDN.APP_PATH, aesPath)
                .where(HOME_CDN.ID.eq(ew1.getId()))
                .execute()
        ).orElseGet(() -> context.insertInto(HOME_CDN)
                .set(HOME_CDN.ID, String.valueOf(UUID.randomUUID()))
                .set(HOME_CDN.CONFIG_ID, cfgId)
                .set(HOME_CDN.APP_PATH, aesPath)
                .set(HOME_CDN.CREATE_TIME, System.currentTimeMillis())
                .execute());
    }

    /**复写|落盘系统配置数据*/
    public void overrideSystemCfg(){
        List<RuleConfigVO> ruleConfigCollect = systemService.ruleConfigCollect();
        logger.info("规则数据集合{}",JSON.toJSONString(ruleConfigCollect));
        String path = doOverrideFile(JSON.toJSONString(ruleConfigCollect), "system-config");
        Collection<SelectField<?>> select = doSelect(CONFIG_CDN.ID, CONFIG_CDN.PATH, CONFIG_CDN.CREATE_TIME);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        Optional<ConfigCdn> configCdnOpt = doSingleRecord(CONFIG_CDN, selectStep)
                .fetchOptional(ew1 -> {
                    ConfigCdn configCdn = new ConfigCdn();
                    configCdn.setId(ew1.getValue(CONFIG_CDN.ID));
                    configCdn.setPath(ew1.getValue(CONFIG_CDN.PATH));
                    configCdn.setCreateTime(ew1.getValue(CONFIG_CDN.CREATE_TIME));
                    return configCdn;
                });
        configCdnOpt.map(ew1 -> context.update(CONFIG_CDN)
                .set(CONFIG_CDN.PATH, path)
                .where(CONFIG_CDN.ID.eq(ew1.getId()))
                .execute()
        ).orElseGet(() -> context.insertInto(CONFIG_CDN)
                .set(CONFIG_CDN.ID, String.valueOf(UUID.randomUUID()))
                .set(CONFIG_CDN.PATH, path)
                .set(CONFIG_CDN.CREATE_TIME, System.currentTimeMillis())
                .execute());
    }

    /**
     * 执行复写|上传文件
     * @param targetJson 写入文件的目标JSON
     * @param fileName 文件名称
     * @return 文件路径
     */
    private String doOverrideFile(String targetJson, String fileName) {
        try{
            byte[] bytes = new byte[]{(byte) 0xef, (byte) 0xbb, (byte) 0xbf};
            String aesJson = Encrypt.aesEncrypt(targetJson, "d8cg8gVakEq9Agup");
            aesJson=aesJson.replaceAll("[\\r]", "");
            byte[] contentBytes = aesJson.getBytes(StandardCharsets.UTF_8);
            byte[] bytesWithBom = new byte[contentBytes.length + 3];
            System.arraycopy(bytes, 0, bytesWithBom, 0, 3);
            System.arraycopy(contentBytes, 0, bytesWithBom, 3, contentBytes.length);
            InputStream inputStream = new ByteArrayInputStream(bytesWithBom);
            MultipartFile multipartFile = transferTo(inputStream, "application/json", fileName+System.currentTimeMillis()+".json", inputStream.available());
            logger.info("当前文件长度{}",JSON.toJSONString(multipartFile.getSize()));
            Attachment attachment = attachmentResolver.store(null, multipartFile, Optional.empty());
            String path = Encrypt.aesEncrypt(attachment.getPath(), "d8cg8gVakEq9Agup");
            path=path.replaceAll("[\\r]", "");
            return path;
        }catch (Exception e){
            logger.error("执行复写|上传文件出现异常，文件名{}，异常信息", JSON.toJSONString(fileName), e);
            throw new UnprocessableException(HomeFileUploadFailedAndWillBeRetriedSoon);
        }
    }
    public String doCertifyCfgFile() {
        String rootOrgId = "1";
        try{
            Optional<HomeConfig> enableHomeConfig = homeConfigService.getEnableHomeConfig(rootOrgId, HomeConfig.TYPE_INSTITUTIONS);
            String homeCfgId = enableHomeConfig.get().getId();
            String path = getHomeCDNPCAddress(homeCfgId);

            path = Encrypt.Decrypt(path, "d8cg8gVakEq9Agup");
            Attachment attachment = new Attachment();
            attachment.setPath(path);
            InputStream inputStream = attachmentResolver.resolveToRead(attachment);
            String jsonString = convertInputStreamToString(inputStream);
            jsonString = Encrypt.Decrypt(jsonString, "d8cg8gVakEq9Agup");

            HomeParentCfgVO homeParentCfgVO = JSON.parseObject(jsonString, HomeParentCfgVO.class);

            List<HomeCfgVO> collect = homeParentCfgVO.getCfgCollect().stream().filter(x -> !x.getName().equals(Certify.getCode())).collect(Collectors.toList());

            List<HomeModuleConfig> configCollect = systemService.homeModuleCfg(homeCfgId, PC.getCode());
            Map<String, List<HomeModuleConfig>> modelMap = configCollect.parallelStream().collect(Collectors.groupingBy(HomeModuleConfig::getModuleCode));
            paddingCertifyCfg(PC.getCode(),modelMap,collect);
            collect.sort(Comparator.comparingInt(HomeCfgVO::getSort));

            this.overrideFile(collect,homeCfgId,PC.getCode());
            return path;
        }catch (Exception e){
            logger.error("执行复写|上传文件出现异常，异常信息", e);
            throw new UnprocessableException(HomeFileUploadFailedAndWillBeRetriedSoon);
        }
    }

    private String  getHomeCDNPCAddress(String cfgId){
        return  commonDao.execute(dslContext -> dslContext.select(HOME_CDN.PC_PATH).from(HOME_CDN).where(HOME_CDN.CONFIG_ID.eq(cfgId))).limit(1).fetchOne(Record1::value1);
    }
    public String convertInputStreamToString(InputStream inputStream) throws Exception {
        inputStream = removeBOM(inputStream);
        StringBuilder stringBuilder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
        }
        return stringBuilder.toString();
    }
    private InputStream removeBOM(InputStream inputStream) throws IOException {
        PushbackInputStream pushbackInputStream = new PushbackInputStream(inputStream, 3);
        byte[] bom = new byte[3];
        int bytesRead = pushbackInputStream.read(bom);

        if (bytesRead != 3) {
            // 如果读取的字节数少于3个，则直接返回原始流
            pushbackInputStream.unread(bom, 0, bytesRead);
            return pushbackInputStream;
        }

        if (!(bom[0] == (byte) 0xef && bom[1] == (byte) 0xbb && bom[2] == (byte) 0xbf)) {
            // 如果不是BOM，则将读取的字节放回流中
            pushbackInputStream.unread(bom, 0, 3);
        }

        return pushbackInputStream;
    }
    private void initCertifyCache(List<String> ids){
        String value = "1";
        redis.process(r -> r.del(CERTIFY_OUTSIDE_CERTIFICATION_REDIS_KEY));
        Map<String, String> map = ids.stream().collect(Collectors.toMap(String::toString, v -> value, (v1, v2) -> v1));
        redis.process(r -> r.hmset(CERTIFY_OUTSIDE_CERTIFICATION_REDIS_KEY, map));
    }

/*    public static void main(String[] args) {
        String inputCsv = "C:/Users/<USER>/Desktop/首页模拟压测数据用户Id.csv";
        String outputCsv = "C:/Users/<USER>/Desktop/首页模拟压测数据用户Id-输出.csv.csv";
        try (BufferedReader reader = new BufferedReader(new FileReader(inputCsv));
             BufferedWriter writer = new BufferedWriter(new FileWriter(outputCsv))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String encryptedLine = Encrypt.aesEncrypt(line,"d8cg8gVakEq9Agup"); // 加密每一行
                writer.write(encryptedLine); // 写入加密后的行
                writer.newLine(); // 换行
            }
            System.out.println("文件加密并已写入到 " + outputCsv);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

    @Override
    public int[] getTypes() {
        return new int[]{MessageTypeContent.HOME_SYSTEM, MessageTypeContent.HOME_CONFIG,MessageTypeContent.HOME_CERTIFY_CONFIG};
    }
}
