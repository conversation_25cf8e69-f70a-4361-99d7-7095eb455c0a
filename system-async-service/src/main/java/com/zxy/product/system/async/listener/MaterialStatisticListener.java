package com.zxy.product.system.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.system.api.smartimg.PictureCategoryService;
import com.zxy.product.system.api.smartimg.StyleService;
import com.zxy.product.system.content.MaterialStatisticTypeEnum;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import com.zxy.product.system.entity.Material;
import org.jooq.Condition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zxy.product.system.content.DeleteFlagEnum.DISABLE;
import static com.zxy.product.system.content.DeleteFlagEnum.ENABLE;
import static com.zxy.product.system.jooq.Tables.MATERIAL;
import static java.util.Objects.isNull;

/**
 * @Description: 图片分类、样式下的 素材统计更新
 * @Author: liuc
 * @CreateDate: 2021/7/20 10:43
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Component
public class MaterialStatisticListener extends AbstractMessageListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialStatisticListener.class);

    @Autowired
    private CommonDao<Material> materialDao;

    @Autowired
    private PictureCategoryService categoryService;

    @Autowired
    private StyleService styleService;

    @Override
    protected void onMessage(Message message) {
        LOGGER.debug("knowledge-operation/MaterialStatisticListener listener: {}", message);
        String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
        String businessType = message.getHeader(MessageHeaderContent.BUSINESS_TYPE);
        int type = message.getType();
        switch (type) {
            case MessageTypeContent.SMART_IMG_MATERIAL_STATISTIC:
                deleteMaterialByBusinessType(businessId, businessType);
                break;
            default:
                break;
        }
    }

    private void deleteMaterialByBusinessType(String businessId, String businessType) {
        Condition condition = getQueryConditionByBusinessType(businessId, businessType);
        if (isNull(condition)) {
            return;
        }
        // step1 先查询出要更新统计计数的 styleId 或者 categoryId
        List<Material> lstMaterials = materialDao.execute(dsl -> dsl
                .select(Fields.start().add(MATERIAL.ID, MATERIAL.STYLE_ID, MATERIAL.CATEGORY_ID).end())
                .from(MATERIAL)
                .where(condition)
                .fetchInto(Material.class));
        //step2 软删除对应素材
        materialDao.execute(dsl -> dsl.update(MATERIAL).set(MATERIAL.DELETE_FLAG,
                DISABLE.getType()).where(condition).execute());
        //step3 更新目录/样式下的统计数
        if (MaterialStatisticTypeEnum.STYLE.getValue().equals(businessType)) {
            Set<String> setCategory = lstMaterials.stream().map(Material::getCategoryId).collect(Collectors.toSet());
            setCategory.forEach(cateId -> {
                statisticsAfresh(cateId, null);
            });
        } else if (MaterialStatisticTypeEnum.PICTURE_CATEGORY.getValue().equals(businessType)) {
            Set<String> setStyle = lstMaterials.stream().map(Material::getStyleId).collect(Collectors.toSet());
            setStyle.forEach(styleId -> {
                statisticsAfresh(null, styleId);
            });
        }
    }

    private void statisticsAfresh(String categoryId, String styleId) {
        Optional.ofNullable(categoryId).ifPresent(cateId -> {
            int countCategory = materialDao.count(MATERIAL.CATEGORY_ID.eq(categoryId),
                    MATERIAL.DELETE_FLAG.eq(ENABLE.getType()));
            categoryService.statisticsMaterials(categoryId, countCategory);
        });
        Optional.ofNullable(styleId).ifPresent(styId -> {
            int countStyle = materialDao.count(MATERIAL.STYLE_ID.eq(styleId),
                    MATERIAL.DELETE_FLAG.eq(ENABLE.getType()));
            styleService.statisticsMaterials(styleId, countStyle);
        });


    }

    /**
     * 根据businessType的值获取条件
     *
     * @param businessId
     * @param businessType
     * @return
     */
    private Condition getQueryConditionByBusinessType(String businessId, String businessType) {
        if (MaterialStatisticTypeEnum.STYLE.getValue().equals(businessType)) {
            return MATERIAL.STYLE_ID.eq(businessId);
        } else if (MaterialStatisticTypeEnum.PICTURE_CATEGORY.getValue().equals(businessType)) {
            return MATERIAL.CATEGORY_ID.eq(businessId);
        } else {
            return null;
        }
    }


    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.SMART_IMG_MATERIAL_STATISTIC
        };
    }
}
