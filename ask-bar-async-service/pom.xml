<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zxy.product</groupId>
		<artifactId>ask-bar</artifactId>
		<version>cmu-9.6.0</version>
	</parent>
	<artifactId>ask-bar-async-service</artifactId>
	<dependencies>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>async-service-parent</artifactId>
			<type>pom</type>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>ask-bar-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>system-api</artifactId>
            <version>${version}</version>
        </dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>human-resource-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>course-study-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.3</version>
		</dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<dependencies>
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>springloaded</artifactId>
						<version>1.2.3.RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>
