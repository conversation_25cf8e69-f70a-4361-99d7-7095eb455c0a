/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.ResourceVisitRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 资源浏览统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResourceVisit extends TableImpl<ResourceVisitRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_resource_visit</code>
     */
    public static final ResourceVisit RESOURCE_VISIT = new ResourceVisit();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResourceVisitRecord> getRecordType() {
        return ResourceVisitRecord.class;
    }

    /**
     * The column <code>zxy-log.t_resource_visit.f_id</code>. ID
     */
    public final TableField<ResourceVisitRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>zxy-log.t_resource_visit.f_content_id</code>. 内容id
     */
    public final TableField<ResourceVisitRecord, String> CONTENT_ID = createField("f_content_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "内容id");

    /**
     * The column <code>zxy-log.t_resource_visit.f_content_name</code>. 内容名称
     */
    public final TableField<ResourceVisitRecord, String> CONTENT_NAME = createField("f_content_name", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "内容名称");

    /**
     * The column <code>zxy-log.t_resource_visit.f_content_type</code>. 内容类型：1-专题，2-课程
     */
    public final TableField<ResourceVisitRecord, Integer> CONTENT_TYPE = createField("f_content_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "内容类型：1-专题，2-课程");

    /**
     * The column <code>zxy-log.t_resource_visit.f_visit</code>. 每日浏览量
     */
    public final TableField<ResourceVisitRecord, Integer> VISIT = createField("f_visit", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "每日浏览量");

    /**
     * The column <code>zxy-log.t_resource_visit.f_day</code>. 日期YYYYMMDD
     */
    public final TableField<ResourceVisitRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "日期YYYYMMDD");

    /**
     * The column <code>zxy-log.t_resource_visit.f_create_time</code>. 创建时间
     */
    public final TableField<ResourceVisitRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>zxy-log.t_resource_visit</code> table reference
     */
    public ResourceVisit() {
        this("t_resource_visit", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_resource_visit</code> table reference
     */
    public ResourceVisit(String alias) {
        this(alias, RESOURCE_VISIT);
    }

    private ResourceVisit(String alias, Table<ResourceVisitRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResourceVisit(String alias, Table<ResourceVisitRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "资源浏览统计表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResourceVisitRecord> getPrimaryKey() {
        return Keys.KEY_T_RESOURCE_VISIT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResourceVisitRecord>> getKeys() {
        return Arrays.<UniqueKey<ResourceVisitRecord>>asList(Keys.KEY_T_RESOURCE_VISIT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisit as(String alias) {
        return new ResourceVisit(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResourceVisit rename(String name) {
        return new ResourceVisit(name, null);
    }
}
