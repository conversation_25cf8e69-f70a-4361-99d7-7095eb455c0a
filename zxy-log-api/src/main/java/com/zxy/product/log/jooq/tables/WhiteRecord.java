/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.WhiteRecordRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 白名单登陆异常记录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class WhiteRecord extends TableImpl<WhiteRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_white_record</code>
     */
    public static final WhiteRecord WHITE_RECORD = new WhiteRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<WhiteRecordRecord> getRecordType() {
        return WhiteRecordRecord.class;
    }

    /**
     * The column <code>zxy-log.t_white_record.f_id</code>. ID
     */
    public final TableField<WhiteRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>zxy-log.t_white_record.f_create_time</code>. 创建时间
     */
    public final TableField<WhiteRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>zxy-log.t_white_record.f_member_id</code>. 用户id
     */
    public final TableField<WhiteRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户id");

    /**
     * The column <code>zxy-log.t_white_record.f_login_name</code>. 登陆账号
     */
    public final TableField<WhiteRecordRecord, String> LOGIN_NAME = createField("f_login_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "登陆账号");

    /**
     * The column <code>zxy-log.t_white_record.f_type</code>. 登陆终端：1pc 2app
     */
    public final TableField<WhiteRecordRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "登陆终端：1pc 2app");

    /**
     * The column <code>zxy-log.t_white_record.f_ip</code>. 登陆ip地址
     */
    public final TableField<WhiteRecordRecord, String> IP = createField("f_ip", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "登陆ip地址");

    /**
     * The column <code>zxy-log.t_white_record.f_model</code>. 手机型号
     */
    public final TableField<WhiteRecordRecord, String> MODEL = createField("f_model", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "手机型号");

    /**
     * The column <code>zxy-log.t_white_record.f_error</code>. 错误原因
     */
    public final TableField<WhiteRecordRecord, String> ERROR = createField("f_error", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "错误原因");

    /**
     * The column <code>zxy-log.t_white_record.f_organization_id</code>. 组织id
     */
    public final TableField<WhiteRecordRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织id");

    /**
     * The column <code>zxy-log.t_white_record.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    public final TableField<WhiteRecordRecord, Integer> PASSWORD_TYPE = createField("f_password_type", org.jooq.impl.SQLDataType.INTEGER, this, "登录密码类型，0静态密码 1动态密码");

    /**
     * The column <code>zxy-log.t_white_record.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    public final TableField<WhiteRecordRecord, Integer> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.INTEGER, this, "登录来源，0网大 1 OA单点 2党建单点");

    /**
     * Create a <code>zxy-log.t_white_record</code> table reference
     */
    public WhiteRecord() {
        this("t_white_record", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_white_record</code> table reference
     */
    public WhiteRecord(String alias) {
        this(alias, WHITE_RECORD);
    }

    private WhiteRecord(String alias, Table<WhiteRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private WhiteRecord(String alias, Table<WhiteRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "白名单登陆异常记录");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<WhiteRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_WHITE_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<WhiteRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<WhiteRecordRecord>>asList(Keys.KEY_T_WHITE_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecord as(String alias) {
        return new WhiteRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public WhiteRecord rename(String name) {
        return new WhiteRecord(name, null);
    }
}
