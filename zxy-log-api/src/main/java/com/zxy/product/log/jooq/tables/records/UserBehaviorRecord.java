/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.UserBehavior;
import com.zxy.product.log.jooq.tables.interfaces.IUserBehavior;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UserBehaviorRecord extends UpdatableRecordImpl<UserBehaviorRecord> implements Record12<String, String, String, String, String, String, String, String, String, String, Long, Long>, IUserBehavior {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_id</code>. id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_id</code>. id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_user_id</code>. 用户id
     */
    @Override
    public void setUserId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_user_id</code>. 用户id
     */
    @Override
    public String getUserId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_content_id</code>. 内容id
     */
    @Override
    public void setContentId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_content_id</code>. 内容id
     */
    @Override
    public String getContentId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_content_type</code>. 内容类型
     */
    @Override
    public void setContentType(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_content_type</code>. 内容类型
     */
    @Override
    public String getContentType() {
        return (String) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_content_name</code>. 内容名称
     */
    @Override
    public void setContentName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_content_name</code>. 内容名称
     */
    @Override
    public String getContentName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.client_type</code>. 客户端类型
     */
    @Override
    public void setClientType(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.client_type</code>. 客户端类型
     */
    @Override
    public String getClientType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_type</code>. 1:收藏；2：评论；3：评分；4：不喜欢/踩（暂时没有数据）; 5：赞；6：下载；7：分享
     */
    @Override
    public void setType(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_type</code>. 1:收藏；2：评论；3：评分；4：不喜欢/踩（暂时没有数据）; 5：赞；6：下载；7：分享
     */
    @Override
    public String getType() {
        return (String) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_value</code>. 具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值
     */
    @Override
    public void setValue(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_value</code>. 具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值
     */
    @Override
    public String getValue() {
        return (String) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_page_source</code>. 页面来源
     */
    @Override
    public void setPageSource(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_page_source</code>. 页面来源
     */
    @Override
    public String getPageSource() {
        return (String) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_status</code>. 状态
     */
    @Override
    public void setStatus(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_status</code>. 状态
     */
    @Override
    public String getStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_modifi_time</code>. 修改时间
     */
    @Override
    public void setModifiTime(Long value) {
        set(11, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_modifi_time</code>. 修改时间
     */
    @Override
    public Long getModifiTime() {
        return (Long) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, String, String, String, String, String, String, String, String, String, Long, Long> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, String, String, String, String, String, String, String, String, String, Long, Long> valuesRow() {
        return (Row12) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return UserBehavior.USER_BEHAVIOR.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return UserBehavior.USER_BEHAVIOR.USER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return UserBehavior.USER_BEHAVIOR.CONTENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return UserBehavior.USER_BEHAVIOR.CONTENT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return UserBehavior.USER_BEHAVIOR.CONTENT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return UserBehavior.USER_BEHAVIOR.CLIENT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return UserBehavior.USER_BEHAVIOR.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return UserBehavior.USER_BEHAVIOR.VALUE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return UserBehavior.USER_BEHAVIOR.PAGE_SOURCE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return UserBehavior.USER_BEHAVIOR.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field11() {
        return UserBehavior.USER_BEHAVIOR.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field12() {
        return UserBehavior.USER_BEHAVIOR.MODIFI_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getUserId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getContentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getContentType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getContentName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getClientType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getValue();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getPageSource();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value11() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value12() {
        return getModifiTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value2(String value) {
        setUserId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value3(String value) {
        setContentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value4(String value) {
        setContentType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value5(String value) {
        setContentName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value6(String value) {
        setClientType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value7(String value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value8(String value) {
        setValue(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value9(String value) {
        setPageSource(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value10(String value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value11(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord value12(Long value) {
        setModifiTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehaviorRecord values(String value1, String value2, String value3, String value4, String value5, String value6, String value7, String value8, String value9, String value10, Long value11, Long value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IUserBehavior from) {
        setId(from.getId());
        setUserId(from.getUserId());
        setContentId(from.getContentId());
        setContentType(from.getContentType());
        setContentName(from.getContentName());
        setClientType(from.getClientType());
        setType(from.getType());
        setValue(from.getValue());
        setPageSource(from.getPageSource());
        setStatus(from.getStatus());
        setCreateTime(from.getCreateTime());
        setModifiTime(from.getModifiTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IUserBehavior> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserBehaviorRecord
     */
    public UserBehaviorRecord() {
        super(UserBehavior.USER_BEHAVIOR);
    }

    /**
     * Create a detached, initialised UserBehaviorRecord
     */
    public UserBehaviorRecord(String id, String userId, String contentId, String contentType, String contentName, String clientType, String type, String value, String pageSource, String status, Long createTime, Long modifiTime) {
        super(UserBehavior.USER_BEHAVIOR);

        set(0, id);
        set(1, userId);
        set(2, contentId);
        set(3, contentType);
        set(4, contentName);
        set(5, clientType);
        set(6, type);
        set(7, value);
        set(8, pageSource);
        set(9, status);
        set(10, createTime);
        set(11, modifiTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.UserBehaviorEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.UserBehaviorEntity pojo = (com.zxy.product.log.jooq.tables.pojos.UserBehaviorEntity)source;
        pojo.into(this);
        return true;
    }
}
