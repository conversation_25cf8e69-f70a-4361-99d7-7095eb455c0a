/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.OrganizationDetail;
import com.zxy.product.log.jooq.tables.interfaces.IOrganizationDetail;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationDetailRecord extends UpdatableRecordImpl<OrganizationDetailRecord> implements Record4<String, String, String, Long>, IOrganizationDetail {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_organization_detail.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_detail.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_organization_detail.f_root</code>.  父节点
     */
    @Override
    public void setRoot(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_detail.f_root</code>.  父节点
     */
    @Override
    public String getRoot() {
        return (String) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_organization_detail.f_sub</code>. 子节点
     */
    @Override
    public void setSub(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_detail.f_sub</code>. 子节点
     */
    @Override
    public String getSub() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_organization_detail.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_detail.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, String, String, Long> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, String, String, Long> valuesRow() {
        return (Row4) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return OrganizationDetail.ORGANIZATION_DETAIL.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return OrganizationDetail.ORGANIZATION_DETAIL.ROOT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return OrganizationDetail.ORGANIZATION_DETAIL.SUB;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return OrganizationDetail.ORGANIZATION_DETAIL.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getRoot();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getSub();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationDetailRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationDetailRecord value2(String value) {
        setRoot(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationDetailRecord value3(String value) {
        setSub(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationDetailRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationDetailRecord values(String value1, String value2, String value3, Long value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IOrganizationDetail from) {
        setId(from.getId());
        setRoot(from.getRoot());
        setSub(from.getSub());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IOrganizationDetail> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OrganizationDetailRecord
     */
    public OrganizationDetailRecord() {
        super(OrganizationDetail.ORGANIZATION_DETAIL);
    }

    /**
     * Create a detached, initialised OrganizationDetailRecord
     */
    public OrganizationDetailRecord(String id, String root, String sub, Long createTime) {
        super(OrganizationDetail.ORGANIZATION_DETAIL);

        set(0, id);
        set(1, root);
        set(2, sub);
        set(3, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.OrganizationDetailEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.OrganizationDetailEntity pojo = (com.zxy.product.log.jooq.tables.pojos.OrganizationDetailEntity)source;
        pojo.into(this);
        return true;
    }
}
