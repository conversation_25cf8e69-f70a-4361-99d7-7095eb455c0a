/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.MemberDetail;
import com.zxy.product.log.jooq.tables.interfaces.IMemberDetail;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record21;
import org.jooq.Row21;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberDetailRecord extends UpdatableRecordImpl<MemberDetailRecord> implements Record21<String, Long, String, String, Long, String, String, String, Long, String, String, String, String, String, String, Integer, Long, Long, String, Long, String>, IMemberDetail {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_member_detail.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_member_id</code>. 人员ID
     */
    @Override
    public void setMemberId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_member_id</code>. 人员ID
     */
    @Override
    public String getMemberId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_incumbency_status</code>. 在职状态
     */
    @Override
    public void setIncumbencyStatus(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_incumbency_status</code>. 在职状态
     */
    @Override
    public String getIncumbencyStatus() {
        return (String) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_entry_date</code>. 入职时间
     */
    @Override
    public void setEntryDate(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_entry_date</code>. 入职时间
     */
    @Override
    public Long getEntryDate() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_head_portrait</code>. 头像图片ID
     */
    @Override
    public void setHeadPortrait(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_head_portrait</code>. 头像图片ID
     */
    @Override
    public String getHeadPortrait() {
        return (String) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_head_portrait_path</code>. 头像图片路径
     */
    @Override
    public void setHeadPortraitPath(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_head_portrait_path</code>. 头像图片路径
     */
    @Override
    public String getHeadPortraitPath() {
        return (String) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_customer_type</code>. 客户类型
     */
    @Override
    public void setCustomerType(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_customer_type</code>. 客户类型
     */
    @Override
    public String getCustomerType() {
        return (String) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_expiry_date</code>. 有效期
     */
    @Override
    public void setExpiryDate(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_expiry_date</code>. 有效期
     */
    @Override
    public Long getExpiryDate() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_nationality_id</code>. 国籍id
     */
    @Override
    public void setNationalityId(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_nationality_id</code>. 国籍id
     */
    @Override
    public String getNationalityId() {
        return (String) get(9);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_ethnicity_id</code>. 民族id
     */
    @Override
    public void setEthnicityId(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_ethnicity_id</code>. 民族id
     */
    @Override
    public String getEthnicityId() {
        return (String) get(10);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_politicalization_id</code>. 政治面貌id
     */
    @Override
    public void setPoliticalizationId(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_politicalization_id</code>. 政治面貌id
     */
    @Override
    public String getPoliticalizationId() {
        return (String) get(11);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_education_id</code>. 学历id
     */
    @Override
    public void setEducationId(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_education_id</code>. 学历id
     */
    @Override
    public String getEducationId() {
        return (String) get(12);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_credential_type</code>. 证件类型id
     */
    @Override
    public void setCredentialType(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_credential_type</code>. 证件类型id
     */
    @Override
    public String getCredentialType() {
        return (String) get(13);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_credential_value</code>. 证件值
     */
    @Override
    public void setCredentialValue(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_credential_value</code>. 证件值
     */
    @Override
    public String getCredentialValue() {
        return (String) get(14);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_is_leader</code>. 是否领导班子成员(1=是;0=否)
     */
    @Override
    public void setIsLeader(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_is_leader</code>. 是否领导班子成员(1=是;0=否)
     */
    @Override
    public Integer getIsLeader() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_join_date</code>. 参加本单位时间
     */
    @Override
    public void setJoinDate(Long value) {
        set(16, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_join_date</code>. 参加本单位时间
     */
    @Override
    public Long getJoinDate() {
        return (Long) get(16);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_born_date</code>. 出生日期
     */
    @Override
    public void setBornDate(Long value) {
        set(17, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_born_date</code>. 出生日期
     */
    @Override
    public Long getBornDate() {
        return (Long) get(17);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_school</code>. 学院
     */
    @Override
    public void setSchool(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_school</code>. 学院
     */
    @Override
    public String getSchool() {
        return (String) get(18);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_graduate_date</code>. 毕业时间
     */
    @Override
    public void setGraduateDate(Long value) {
        set(19, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_graduate_date</code>. 毕业时间
     */
    @Override
    public Long getGraduateDate() {
        return (Long) get(19);
    }

    /**
     * Setter for <code>zxy-log.t_member_detail.f_summary</code>. 个人简介
     */
    @Override
    public void setSummary(String value) {
        set(20, value);
    }

    /**
     * Getter for <code>zxy-log.t_member_detail.f_summary</code>. 个人简介
     */
    @Override
    public String getSummary() {
        return (String) get(20);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record21 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row21<String, Long, String, String, Long, String, String, String, Long, String, String, String, String, String, String, Integer, Long, Long, String, Long, String> fieldsRow() {
        return (Row21) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row21<String, Long, String, String, Long, String, String, String, Long, String, String, String, String, String, String, Integer, Long, Long, String, Long, String> valuesRow() {
        return (Row21) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return MemberDetail.MEMBER_DETAIL.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return MemberDetail.MEMBER_DETAIL.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return MemberDetail.MEMBER_DETAIL.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return MemberDetail.MEMBER_DETAIL.INCUMBENCY_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return MemberDetail.MEMBER_DETAIL.ENTRY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return MemberDetail.MEMBER_DETAIL.HEAD_PORTRAIT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return MemberDetail.MEMBER_DETAIL.HEAD_PORTRAIT_PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return MemberDetail.MEMBER_DETAIL.CUSTOMER_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return MemberDetail.MEMBER_DETAIL.EXPIRY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return MemberDetail.MEMBER_DETAIL.NATIONALITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return MemberDetail.MEMBER_DETAIL.ETHNICITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return MemberDetail.MEMBER_DETAIL.POLITICALIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field13() {
        return MemberDetail.MEMBER_DETAIL.EDUCATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field14() {
        return MemberDetail.MEMBER_DETAIL.CREDENTIAL_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field15() {
        return MemberDetail.MEMBER_DETAIL.CREDENTIAL_VALUE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field16() {
        return MemberDetail.MEMBER_DETAIL.IS_LEADER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field17() {
        return MemberDetail.MEMBER_DETAIL.JOIN_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field18() {
        return MemberDetail.MEMBER_DETAIL.BORN_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field19() {
        return MemberDetail.MEMBER_DETAIL.SCHOOL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field20() {
        return MemberDetail.MEMBER_DETAIL.GRADUATE_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field21() {
        return MemberDetail.MEMBER_DETAIL.SUMMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getIncumbencyStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getEntryDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getHeadPortrait();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getHeadPortraitPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getCustomerType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getExpiryDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getNationalityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getEthnicityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getPoliticalizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value13() {
        return getEducationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value14() {
        return getCredentialType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value15() {
        return getCredentialValue();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value16() {
        return getIsLeader();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value17() {
        return getJoinDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value18() {
        return getBornDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value19() {
        return getSchool();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value20() {
        return getGraduateDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value21() {
        return getSummary();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value3(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value4(String value) {
        setIncumbencyStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value5(Long value) {
        setEntryDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value6(String value) {
        setHeadPortrait(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value7(String value) {
        setHeadPortraitPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value8(String value) {
        setCustomerType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value9(Long value) {
        setExpiryDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value10(String value) {
        setNationalityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value11(String value) {
        setEthnicityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value12(String value) {
        setPoliticalizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value13(String value) {
        setEducationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value14(String value) {
        setCredentialType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value15(String value) {
        setCredentialValue(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value16(Integer value) {
        setIsLeader(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value17(Long value) {
        setJoinDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value18(Long value) {
        setBornDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value19(String value) {
        setSchool(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value20(Long value) {
        setGraduateDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord value21(String value) {
        setSummary(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetailRecord values(String value1, Long value2, String value3, String value4, Long value5, String value6, String value7, String value8, Long value9, String value10, String value11, String value12, String value13, String value14, String value15, Integer value16, Long value17, Long value18, String value19, Long value20, String value21) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        value21(value21);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMemberDetail from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setMemberId(from.getMemberId());
        setIncumbencyStatus(from.getIncumbencyStatus());
        setEntryDate(from.getEntryDate());
        setHeadPortrait(from.getHeadPortrait());
        setHeadPortraitPath(from.getHeadPortraitPath());
        setCustomerType(from.getCustomerType());
        setExpiryDate(from.getExpiryDate());
        setNationalityId(from.getNationalityId());
        setEthnicityId(from.getEthnicityId());
        setPoliticalizationId(from.getPoliticalizationId());
        setEducationId(from.getEducationId());
        setCredentialType(from.getCredentialType());
        setCredentialValue(from.getCredentialValue());
        setIsLeader(from.getIsLeader());
        setJoinDate(from.getJoinDate());
        setBornDate(from.getBornDate());
        setSchool(from.getSchool());
        setGraduateDate(from.getGraduateDate());
        setSummary(from.getSummary());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMemberDetail> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MemberDetailRecord
     */
    public MemberDetailRecord() {
        super(MemberDetail.MEMBER_DETAIL);
    }

    /**
     * Create a detached, initialised MemberDetailRecord
     */
    public MemberDetailRecord(String id, Long createTime, String memberId, String incumbencyStatus, Long entryDate, String headPortrait, String headPortraitPath, String customerType, Long expiryDate, String nationalityId, String ethnicityId, String politicalizationId, String educationId, String credentialType, String credentialValue, Integer isLeader, Long joinDate, Long bornDate, String school, Long graduateDate, String summary) {
        super(MemberDetail.MEMBER_DETAIL);

        set(0, id);
        set(1, createTime);
        set(2, memberId);
        set(3, incumbencyStatus);
        set(4, entryDate);
        set(5, headPortrait);
        set(6, headPortraitPath);
        set(7, customerType);
        set(8, expiryDate);
        set(9, nationalityId);
        set(10, ethnicityId);
        set(11, politicalizationId);
        set(12, educationId);
        set(13, credentialType);
        set(14, credentialValue);
        set(15, isLeader);
        set(16, joinDate);
        set(17, bornDate);
        set(18, school);
        set(19, graduateDate);
        set(20, summary);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.MemberDetailEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.MemberDetailEntity pojo = (com.zxy.product.log.jooq.tables.pojos.MemberDetailEntity)source;
        pojo.into(this);
        return true;
    }
}
