/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.OrganizationLoginCountDay;
import com.zxy.product.log.jooq.tables.interfaces.IOrganizationLoginCountDay;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 各组织日登陆统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationLoginCountDayRecord extends UpdatableRecordImpl<OrganizationLoginCountDayRecord> implements Record10<String, Inte<PERSON>, Inte<PERSON>, <PERSON>te<PERSON>, Inte<PERSON>, Inte<PERSON>, Inte<PERSON>, <PERSON>, Long, Timestamp>, IOrganizationLoginCountDay {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_day</code>. 日
     */
    @Override
    public void setDay(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_day</code>. 日
     */
    @Override
    public Integer getDay() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_month</code>. 月
     */
    @Override
    public void setMonth(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_month</code>. 月
     */
    @Override
    public Integer getMonth() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_year</code>. 年
     */
    @Override
    public void setYear(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_year</code>. 年
     */
    @Override
    public Integer getYear() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_member_num</code>. 登录人数
     */
    @Override
    public void setMemberNum(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_member_num</code>. 登录人数
     */
    @Override
    public Integer getMemberNum() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_member_time</code>. 登录人次
     */
    @Override
    public void setMemberTime(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_member_time</code>. 登录人次
     */
    @Override
    public Integer getMemberTime() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_organization_num</code>. 各组织可用总人数
     */
    @Override
    public void setOrganizationNum(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_organization_num</code>. 各组织可用总人数
     */
    @Override
    public Integer getOrganizationNum() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_organization_id</code>. 组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_organization_id</code>. 组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, Integer, Integer, Integer, Integer, Integer, Integer, String, Long, Timestamp> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, Integer, Integer, Integer, Integer, Integer, Integer, String, Long, Timestamp> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field2() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.DAY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MONTH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.YEAR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field10() {
        return OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value2() {
        return getDay();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getMonth();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getYear();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getMemberNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getMemberTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getOrganizationNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value10() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value2(Integer value) {
        setDay(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value3(Integer value) {
        setMonth(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value4(Integer value) {
        setYear(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value5(Integer value) {
        setMemberNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value6(Integer value) {
        setMemberTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value7(Integer value) {
        setOrganizationNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value8(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value9(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord value10(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDayRecord values(String value1, Integer value2, Integer value3, Integer value4, Integer value5, Integer value6, Integer value7, String value8, Long value9, Timestamp value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IOrganizationLoginCountDay from) {
        setId(from.getId());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setMemberNum(from.getMemberNum());
        setMemberTime(from.getMemberTime());
        setOrganizationNum(from.getOrganizationNum());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IOrganizationLoginCountDay> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OrganizationLoginCountDayRecord
     */
    public OrganizationLoginCountDayRecord() {
        super(OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY);
    }

    /**
     * Create a detached, initialised OrganizationLoginCountDayRecord
     */
    public OrganizationLoginCountDayRecord(String id, Integer day, Integer month, Integer year, Integer memberNum, Integer memberTime, Integer organizationNum, String organizationId, Long createTime, Timestamp modifyDate) {
        super(OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY);

        set(0, id);
        set(1, day);
        set(2, month);
        set(3, year);
        set(4, memberNum);
        set(5, memberTime);
        set(6, organizationNum);
        set(7, organizationId);
        set(8, createTime);
        set(9, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.OrganizationLoginCountDayEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.OrganizationLoginCountDayEntity pojo = (com.zxy.product.log.jooq.tables.pojos.OrganizationLoginCountDayEntity)source;
        pojo.into(this);
        return true;
    }
}
