spring.application.name=file-deal-web-server
server.context-path=/api/v1/file-deal
server.port = 8084

#dubbo
dubbo.application.name=file-deal-web-server
dubbo.application.version=1
dubbo.registry.address=zookeeper://localhost:2181
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator

# redis
#cmutest9
spring.redis.cluster = false
spring.redis.cluster.nodes = ***********:6379
spring.redis.timeout = 10000
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3

# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1

# fastdfs
spring.fastdfs.connect-timeout = 30
spring.fastdfs.network-timeout = 60
spring.fastdfs.charset = utf-8
spring.fastdfs.tracker-servers = **************:10401
spring.fastdfs.tracker-http-port = 10402
spring.fastdfs.anti-steal-token = false
spring.fastdfs.secret-key = 123456
spring.fastdfs.max-total = 5
spring.fastdfs.max-idle = 5

#graphite
graphite.server=**************
graphite.port=30004


spring.rabbitmq.host=**************
spring.rabbitmq.port=30007
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.course.web.gensee.exchange=amq.topic
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1
