package com.zxy.product.filedeal.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.FormFile;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.entity.Attachment;
import com.zxy.product.system.content.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system-file")
public class SystemFileController {
    private final static Logger LOGGER = LoggerFactory.getLogger(SystemFileController.class);


    private final static long PIC_SIZE = 5242880;//5m
    private FileService fileService;
    private AttachmentResolver attachmentResolver;
    private static final List<String> VALID_EXTENSIONS = Arrays.asList("jpg", "jpeg", "png", "bmp", "gif", "ico");
    private final static String URLENCODEE_CONTENT_TYPE = "application/x-www-form-urlencoded; charset=UTF-8";


    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
        this.attachmentResolver = attachmentResolver;
    }

    @RequestMapping(value = "/upload", produces="text/html;charset=UTF-8")
    @Permitted
    @Param(name="")
    @FormFile(name="file", required = true, extension = "(?i)(jpg|jpeg|png|gif)")
    @JSON("*")
    public Map<String, Object> upload(RequestContext context){
        List<com.zxy.common.restful.multipart.Attachment> list = context.getAttachments("file");
        HttpServletRequest request = context.getRequest();
        String url = "";
        if("http".equals(request.getScheme())){
            url = request.getScheme()+"s://"+ request.getServerName()+"/";
        }else {
            url = request.getScheme()+"://"+ request.getServerName()+"/";
        }
        String[] filename = new String[list.size()];
        String[] contentType = new String[list.size()];
        String[] extention = new String[list.size()];
        String[] path = new String[list.size()];
        Long[] size = new Long[list.size()];
        for(int i = 0; i < list.size(); i++){
            com.zxy.common.restful.multipart.Attachment restfulAttachment = list.get(i);
            if (restfulAttachment.getSize() > PIC_SIZE) {
                throw new UnprocessableException(ErrorCode.RICH_TEXT_PIC_FILE_MAX_SIZE_ERROR);
            }
            filename[i] = restfulAttachment.getFilename();
            contentType[i] = restfulAttachment.getContentType();
            extention[i] = restfulAttachment.getExtension();
            path[i] = restfulAttachment.getPath();
            size[i] = restfulAttachment.getSize();
        }
        List<Attachment> result = fileService.insert(filename, contentType, extention, path, size);
        Map<String, Object> map = new HashMap<>();
        map.put("error", 0);
        map.put("url", url + result.get(0).getPath());
        return map;
    }

    @RequestMapping(value = "/material-upload", produces="text/html;charset=UTF-8")
    @Permitted(perms = "course-study/images-management")
    @Param(name="")
    @FormFile(name="file", required = true, extension = "(?i)(jpg|jpeg|png|gif)")
    @JSON("*")
    public Map<String, Object> materialUpload(RequestContext context){
        List<com.zxy.common.restful.multipart.Attachment> list = context.getAttachments("file");
        HttpServletRequest request = context.getRequest();
        String url = "";
        if("http".equals(request.getScheme())){
            url = request.getScheme()+"s://"+ request.getServerName()+"/";
        }else {
            url = request.getScheme()+"://"+ request.getServerName()+"/";
        }
        String[] filename = new String[list.size()];
        String[] contentType = new String[list.size()];
        String[] extention = new String[list.size()];
        String[] path = new String[list.size()];
        Long[] size = new Long[list.size()];
        for(int i = 0; i < list.size(); i++){
            com.zxy.common.restful.multipart.Attachment restfulAttachment = list.get(i);
            if (restfulAttachment.getSize() > PIC_SIZE) {
                throw new UnprocessableException(ErrorCode.RICH_TEXT_PIC_FILE_MAX_SIZE_ERROR);
            }
            filename[i] = restfulAttachment.getFilename();
            contentType[i] = restfulAttachment.getContentType();
            extention[i] = restfulAttachment.getExtension();
            path[i] = restfulAttachment.getPath();
            size[i] = restfulAttachment.getSize();
        }
        List<Attachment> result = fileService.insert(filename, contentType, extention, path, size);
        Map<String, Object> map = new HashMap<>();
        map.put("error", 0);
        map.put("id", result.get(0).getId());
        map.put("name", url + result.get(0).getFilename());
        map.put("url", url + result.get(0).getPath());
        return map;
    }

    /**
     * 新富文本框,返回格式
     * @param context
     * @return
     */
    @RequestMapping(value = "/new-upload", produces="text/html;charset=UTF-8", method = RequestMethod.POST)
    @Permitted
    @Param()
    @FormFile(name="file", required = true, extension = "(?i)(jpg|jpeg|png|gif)")
    @JSON("*.*")
    public Map<String, Object> newUpload(RequestContext context){
        Map<String, Object> map = new HashMap<>();
        try {
            List<com.zxy.common.restful.multipart.Attachment> list = context.getAttachments("file");
            HttpServletRequest request = context.getRequest();
            String url;
            if ("http".equals(request.getScheme())) {
                url = request.getScheme() + "s://" + request.getServerName() + "/";
            } else {
                url = request.getScheme() + "://" + request.getServerName() + "/";
            }
            String[] filename = new String[list.size()];
            String[] contentType = new String[list.size()];
            String[] extention = new String[list.size()];
            String[] path = new String[list.size()];
            Long[] size = new Long[list.size()];
            for (int i = 0; i < list.size(); i++) {
                com.zxy.common.restful.multipart.Attachment restfulAttachment = list.get(i);
                if (restfulAttachment.getSize() > PIC_SIZE) {
                    throw new UnprocessableException(ErrorCode.RICH_TEXT_PIC_FILE_MAX_SIZE_ERROR);
                }
                filename[i] = restfulAttachment.getFilename();
                contentType[i] = restfulAttachment.getContentType();
                extention[i] = restfulAttachment.getExtension();
                path[i] = restfulAttachment.getPath();
                size[i] = restfulAttachment.getSize();
            }
            List<Attachment> result = fileService.insert(filename, contentType, extention, path, size);
            map.put("errno", 0);
            map.put("data", ImmutableMap.of("url", url + result.get(0).getPath()));
        } catch (Exception e) {
            LOGGER.error("文件上传失败", e);
            map.put("errno", -1);
            map.put("message", "图片上传失败!");
            return map;
        }
        return map;
    }

    @RequestMapping("/download/{id}")
    @Param(name = "id", type = String.class, required = true)
    public Object download(RequestContext context) throws IOException {
        Optional<Attachment> optionalAttachment = fileService.get(context.get("id", String.class));
        if(optionalAttachment.isPresent()){
            Attachment a = optionalAttachment.get();
            com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
            fastDFSAttachment.setPath(a.getPath());
            InputStream inputStream = attachmentResolver.resolveToRead(fastDFSAttachment);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentLength(a.getSize());
            headers.set(HttpHeaders.CONTENT_TYPE, a.getContentType());

            InputStreamResource resource = new InputStreamResource(inputStream);
            ResponseEntity<InputStreamResource> response = new ResponseEntity<>(resource, headers, HttpStatus.OK);
            return response;

        };

        return new ResponseEntity<String>(HttpStatus.NOT_FOUND);
    }

    @RequestMapping(value = "stream", method = RequestMethod.POST)
    @JSON("*")
    public Map<String, Object> upload(HttpServletRequest request) throws IOException {

        @SuppressWarnings("unused")
        Long picSize = (Long) request.getAttribute("picSize");

        String fileName = System.currentTimeMillis() +".jpg";
        File temp = new File("temp/" + fileName);
        if (!temp.getParentFile().exists()) {
            temp.mkdirs();
        }
        ImageIO.write(ImageIO.read(request.getInputStream()), "jpg", temp);

        FileInputStream ins = new FileInputStream(temp);

        MultipartFile file = transferTo(ins, request.getContentType(), fileName, ins.available());
        com.zxy.common.restful.multipart.Attachment restfulAttachment = attachmentResolver.store(request, file, Optional.empty());
        String[] filename = new String[]{ restfulAttachment.getFilename() };
        String[] contentType = new String[]{ restfulAttachment.getContentType() };
        String[] extention = new String[]{ restfulAttachment.getExtension() };
        String[] path = new String[]{ restfulAttachment.getPath() };
        Long[] size = new Long[]{ restfulAttachment.getSize() };
        List<Attachment> result = fileService.insert(filename, contentType, extention, path, size);
        temp.delete();
        Map<String, Object> map = new HashMap<>();
        map.put("code", 200);

        map.put("msg", "success");
        map.put("imgId", result.get(0).getId());
        map.put("path", result.get(0).getPath());
        return map;
    }

    @SuppressWarnings("restriction")
    @RequestMapping(value = "/cropper-upload", produces="text/html;charset=UTF-8")
    @Permitted
    @JSON("code,msg,imgId,path")
    public Map<String, Object> cropperUpload(HttpServletRequest request) throws IOException {

        String fileName = System.currentTimeMillis() +".jpg";
        String imgBase64String = request.getParameter("image");

        InputStream ins = null;
        String header ="data:image";
        String[] imageArr = imgBase64String.split(",");
        if (imageArr[0].contains(header)) {
            imgBase64String = imageArr[1];
            sun.misc.BASE64Decoder decoder = new sun.misc.BASE64Decoder();
            byte[] decodedBytes = decoder.decodeBuffer(imgBase64String);
            ins = new ByteArrayInputStream(decodedBytes);
        }

        MultipartFile file = transferTo(ins, request.getContentType(), fileName, ins.available());
        if (!URLENCODEE_CONTENT_TYPE.equals(request.getContentType()) || !checkPicType(file.getOriginalFilename())) {
            throw new UnprocessableException(com.zxy.common.restful.validation.ErrorCode.FileTypeNotAllowed);
        }
        com.zxy.common.restful.multipart.Attachment restfulAttachment = attachmentResolver.store(request, file, Optional.empty());
        String[] filename = new String[]{ restfulAttachment.getFilename() };
        String[] contentType = new String[]{ restfulAttachment.getContentType() };
        String[] extention = new String[]{ restfulAttachment.getExtension() };
        String[] path = new String[]{ restfulAttachment.getPath() };
        Long[] size = new Long[]{ restfulAttachment.getSize() };
        List<Attachment> result = fileService.insert(filename, contentType, extention, path, size);
        Map<String, Object> map = new HashMap<>();
        map.put("code", 200);

        map.put("msg", "success");
        map.put("imgId", result.get(0).getId());
        map.put("path", result.get(0).getPath());
        return map;
    }

    @RequestMapping(value = "/simple-upload", produces="text/html;charset=UTF-8")
    @Permitted
    @FormFile(name="file", required = true, extension = "(?i)(jpg|jpeg|png|bmp|gif|ico)")
    @JSON("code,msg,imgId,path")
    public Map<String, Object> testUpload(HttpServletRequest request) throws IOException {

        MultipartHttpServletRequest multipartRequest =
                WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);

        Integer picSize = Integer.valueOf(request.getParameter("picSize"));
        MultipartFile file = multipartRequest.getFile("file");

        if (file.getInputStream().available() > picSize) {
            throw new UnprocessableException(com.zxy.common.restful.validation.ErrorCode.FileSizeExceeded);
        }
        if (!checkPicType(file.getOriginalFilename())) {
            throw new UnprocessableException(com.zxy.common.restful.validation.ErrorCode.FileTypeNotAllowed);
        }

        com.zxy.common.restful.multipart.Attachment restfulAttachment = attachmentResolver.store(request, file, Optional.empty());
        String[] filename = new String[]{ restfulAttachment.getFilename() };
        String[] contentType = new String[]{ restfulAttachment.getContentType() };
        String[] extention = new String[]{ restfulAttachment.getExtension() };
        String[] path = new String[]{ restfulAttachment.getPath() };
        Long[] size = new Long[]{ restfulAttachment.getSize() };
        List<Attachment> result = fileService.insert(filename, contentType, extention, path, size);
        Map<String, Object> map = new HashMap<>();
        map.put("code", 200);

        map.put("msg", "success");
        map.put("imgId", result.get(0).getId());
        map.put("path", result.get(0).getPath());
        return map;
    }


    @RequestMapping(value = "/simple-upload-avatar", produces="text/html;charset=UTF-8")
    @Permitted
    @FormFile(name="file", required = true, extension = "(?i)(jpg|jpeg|png)")
    @JSON("code,msg,imgId,path")
    public Map<String, Object> simpleUploadAvatar(HttpServletRequest request) throws IOException {

        MultipartHttpServletRequest multipartRequest =
                WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);

        Integer picSize = Integer.valueOf(request.getParameter("picSize"));
        MultipartFile file = multipartRequest.getFile("file");

        if (file.getInputStream().available() > picSize) {
            throw new UnprocessableException(com.zxy.common.restful.validation.ErrorCode.FileSizeExceeded);
        }
        if (!checkPicType(file.getOriginalFilename())) {
            throw new UnprocessableException(com.zxy.common.restful.validation.ErrorCode.FileTypeNotAllowed);
        }

        com.zxy.common.restful.multipart.Attachment restfulAttachment = attachmentResolver.store(request, file, Optional.empty());
        String[] filename = new String[]{ restfulAttachment.getFilename() };
        String[] contentType = new String[]{ restfulAttachment.getContentType() };
        String[] extention = new String[]{ restfulAttachment.getExtension() };
        String[] path = new String[]{ restfulAttachment.getPath() };
        Long[] size = new Long[]{ restfulAttachment.getSize() };
        List<Attachment> result = fileService.insert(filename, contentType, extention, path, size);
        Map<String, Object> map = new HashMap<>();
        map.put("code", 200);

        map.put("msg", "success");
        map.put("imgId", result.get(0).getId());
        map.put("path", result.get(0).getPath());
        return map;
    }

    /**
     * 重写MultipartFile实现， 将文件流转换成MultipartFile
     * @param inputStream
     * @param contentType
     * @param filename
     * @param size
     * @return
     */
    private MultipartFile transferTo(final InputStream inputStream, String contentType, String filename, int size) {
        return new MultipartFile() {

            @Override
            public String getName() {
                return filename;
            }

            @Override
            public String getOriginalFilename() {
                if (filename == null) {
                    // Should never happen.
                    return "";
                }

                // Check for Unix-style path
                int unixSep = filename.lastIndexOf("/");
                // Check for Windows-style path
                int winSep = filename.lastIndexOf("\\");
                // Cut off at latest possible point
                int pos = (winSep > unixSep ? winSep : unixSep);
                if (pos != -1)  {
                    // Any sort of path separator found...
                    return filename.substring(pos + 1);
                }
                else {
                    // A plain name
                    return filename;
                }
            }

            @Override
            public String getContentType() {
                return contentType;
            }

            @Override
            public boolean isEmpty() {
                return size == 0;
            }

            @Override
            public long getSize() {
                return size;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return  inputStream;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };
    }

    private boolean checkPicType(String name) {
        String extension = name.substring(name.lastIndexOf(".") + 1).toLowerCase();
        return VALID_EXTENSIONS.contains(extension);
    }
}
