package com.zxy.product.system.api.homeconfig;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.system.entity.HomeNav;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@RemoteService
public interface HomeNavService {

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<HomeNav> find(String homeConfigId);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<HomeNav> findByClientType(String homeConfigId,Integer clientType);

    @Transactional
    List<HomeNav> save(String homeConfigId, List<HomeNav> navList);
    
    List<HomeNav> findFromCache(String userId, String userToken, String homeConfigId, Optional<Integer> clientType);
    
    List<HomeNav> saveToCache(String userId, String userToken, String homeConfigId, List<HomeNav> navList);
    
    void saveAsFinal(String userId, String userToken, String homeConfigId);

}
