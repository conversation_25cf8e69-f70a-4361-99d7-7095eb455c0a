/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 消息发送记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMessageRecord extends Serializable {

    /**
     * Setter for <code>system.t_message_record.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_message_record.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>system.t_message_record.f_subject</code>. 主题
     */
    public void setSubject(String value);

    /**
     * Getter for <code>system.t_message_record.f_subject</code>. 主题
     */
    public String getSubject();

    /**
     * Setter for <code>system.t_message_record.f_content</code>. 消息内容
     */
    public void setContent(String value);

    /**
     * Getter for <code>system.t_message_record.f_content</code>. 消息内容
     */
    public String getContent();

    /**
     * Setter for <code>system.t_message_record.f_text_content</code>. 消息text内容
     */
    public void setTextContent(String value);

    /**
     * Getter for <code>system.t_message_record.f_text_content</code>. 消息text内容
     */
    public String getTextContent();

    /**
     * Setter for <code>system.t_message_record.f_sender_id</code>. 发件人id
     */
    public void setSenderId(String value);

    /**
     * Getter for <code>system.t_message_record.f_sender_id</code>. 发件人id
     */
    public String getSenderId();

    /**
     * Setter for <code>system.t_message_record.f_receiver_id</code>. 收件人id
     */
    public void setReceiverId(String value);

    /**
     * Getter for <code>system.t_message_record.f_receiver_id</code>. 收件人id
     */
    public String getReceiverId();

    /**
     * Setter for <code>system.t_message_record.f_type</code>. 消息类型 1站内消息 2邮件 3APP 4短信
     */
    public void setType(Integer value);

    /**
     * Getter for <code>system.t_message_record.f_type</code>. 消息类型 1站内消息 2邮件 3APP 4短信
     */
    public Integer getType();

    /**
     * Setter for <code>system.t_message_record.f_read_status</code>. 阅读状态：0未读(默认) 1已读
     */
    public void setReadStatus(Integer value);

    /**
     * Getter for <code>system.t_message_record.f_read_status</code>. 阅读状态：0未读(默认) 1已读
     */
    public Integer getReadStatus();

    /**
     * Setter for <code>system.t_message_record.f_business_id</code>. 主业务id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>system.t_message_record.f_business_id</code>. 主业务id
     */
    public String getBusinessId();

    /**
     * Setter for <code>system.t_message_record.f_business_code</code>. 主业务类型code
     */
    public void setBusinessCode(String value);

    /**
     * Getter for <code>system.t_message_record.f_business_code</code>. 主业务类型code
     */
    public String getBusinessCode();

    /**
     * Setter for <code>system.t_message_record.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>system.t_message_record.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>system.t_message_record.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>system.t_message_record.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>system.t_message_record.f_attr</code>. 消息属性:0消息通知 1@我
     */
    public void setAttr(Integer value);

    /**
     * Getter for <code>system.t_message_record.f_attr</code>. 消息属性:0消息通知 1@我
     */
    public Integer getAttr();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMessageRecord
     */
    public void from(com.zxy.product.system.jooq.tables.interfaces.IMessageRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMessageRecord
     */
    public <E extends com.zxy.product.system.jooq.tables.interfaces.IMessageRecord> E into(E into);
}
