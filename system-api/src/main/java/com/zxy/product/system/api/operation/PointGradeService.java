package com.zxy.product.system.api.operation;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.system.entity.PointGrade;
import com.zxy.product.system.entity.PointResultByMemberIdVo;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 积分等级配置相关业务接口
 * <AUTHOR>
 * @create 2023/12/13 11:08
 */
@RemoteService(timeout = 100000)
public interface PointGradeService {

    /**
     * 积分等级添加
     * @param gradeName
     * @param cover
     * @param pointGrade
     * @return
     */
    @Transactional
    PointGrade add(String gradeName, Optional<String> cover, Integer pointGrade);

    /**
     * 根据id修改积分等级列表
     * @param id
     * @param gradeName
     * @param cover
     * @param pointGrade
     * @return
     */
    @Transactional
    PointGrade updateById(String id, String gradeName, Optional<String> cover, Integer pointGrade);

    /**
     * 根据id删除积分等级列表
     * @param id
     * @return
     */
    @Transactional
    Boolean deleteById(String id);

    /**
     * 获取积分等级列表
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<PointGrade> list();


    /**
     * 根据历史积分获取积分等级,封面及下一级所需要的积分并赋值
     * @param resultEntity
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PointResultByMemberIdVo setGradeByPointHistory(PointResultByMemberIdVo resultEntity);

}
