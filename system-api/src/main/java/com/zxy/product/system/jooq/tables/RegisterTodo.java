/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.RegisterTodoRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;


/**
 * 注册待办表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RegisterTodo extends TableImpl<RegisterTodoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_register_todo</code>
     */
    public static final RegisterTodo REGISTER_TODO = new RegisterTodo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<RegisterTodoRecord> getRecordType() {
        return RegisterTodoRecord.class;
    }

    /**
     * The column <code>system.t_register_todo.f_id</code>.
     */
    public final TableField<RegisterTodoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>system.t_register_todo.t_register_type</code>. 注册类别(注册的申请类别)
     */
    public final TableField<RegisterTodoRecord, Integer> REGISTER_TYPE = createField("t_register_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "注册类别(注册的申请类别)");

    /**
     * The column <code>system.t_register_todo.f_register_id</code>. 注册记录id
     */
    public final TableField<RegisterTodoRecord, String> REGISTER_ID = createField("f_register_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "注册记录id");

    /**
     * The column <code>system.t_register_todo.f_register_organization_id</code>. 注册组织id
     */
    public final TableField<RegisterTodoRecord, String> REGISTER_ORGANIZATION_ID = createField("f_register_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "注册组织id");

    /**
     * The column <code>system.t_register_todo.f_register_member_name</code>. 注册用户名称
     */
    public final TableField<RegisterTodoRecord, String> REGISTER_MEMBER_NAME = createField("f_register_member_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "注册用户名称");

    /**
     * The column <code>system.t_register_todo.f_approve_member_id</code>. 审核人员id
     */
    public final TableField<RegisterTodoRecord, String> APPROVE_MEMBER_ID = createField("f_approve_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "审核人员id");

    /**
     * The column <code>system.t_register_todo.t_todo_status</code>. 状态(0 待处理 1已处理)
     */
    public final TableField<RegisterTodoRecord, Integer> TODO_STATUS = createField("t_todo_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "状态(0 待处理 1已处理)");

    /**
     * The column <code>system.t_register_todo.f_reason_id</code>. 申请理由
     */
    public final TableField<RegisterTodoRecord, String> REASON_ID = createField("f_reason_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "申请理由");

    /**
     * The column <code>system.t_register_todo.f_create_time</code>. 创建时间
     */
    public final TableField<RegisterTodoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>system.t_register_todo.f_modify_date</code>. 修改时间
     */
    public final TableField<RegisterTodoRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>system.t_register_todo</code> table reference
     */
    public RegisterTodo() {
        this("t_register_todo", null);
    }

    /**
     * Create an aliased <code>system.t_register_todo</code> table reference
     */
    public RegisterTodo(String alias) {
        this(alias, REGISTER_TODO);
    }

    private RegisterTodo(String alias, Table<RegisterTodoRecord> aliased) {
        this(alias, aliased, null);
    }

    private RegisterTodo(String alias, Table<RegisterTodoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "注册待办表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<RegisterTodoRecord> getPrimaryKey() {
        return Keys.KEY_T_REGISTER_TODO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<RegisterTodoRecord>> getKeys() {
        return Arrays.<UniqueKey<RegisterTodoRecord>>asList(Keys.KEY_T_REGISTER_TODO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RegisterTodo as(String alias) {
        return new RegisterTodo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public RegisterTodo rename(String name) {
        return new RegisterTodo(name, null);
    }
}
