/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.TopicObjectRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TopicObject extends TableImpl<TopicObjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_topic_object</code>
     */
    public static final TopicObject TOPIC_OBJECT = new TopicObject();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TopicObjectRecord> getRecordType() {
        return TopicObjectRecord.class;
    }

    /**
     * The column <code>system.t_topic_object.f_id</code>.
     */
    public final TableField<TopicObjectRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>system.t_topic_object.f_topic_id</code>. 话题ID
     */
    public final TableField<TopicObjectRecord, String> TOPIC_ID = createField("f_topic_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "话题ID");

    /**
     * The column <code>system.t_topic_object.f_item_id</code>. 关联ID
     */
    public final TableField<TopicObjectRecord, String> ITEM_ID = createField("f_item_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联ID");

    /**
     * The column <code>system.t_topic_object.f_type</code>. 关联类型（1人  2课程   3考试  4知识  5专题  6问题  7专家  8直播  9MOOC  10调研）
     */
    public final TableField<TopicObjectRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "关联类型（1人  2课程   3考试  4知识  5专题  6问题  7专家  8直播  9MOOC  10调研）");

    /**
     * The column <code>system.t_topic_object.f_create_time</code>. 创建时间
     */
    public final TableField<TopicObjectRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>system.t_topic_object</code> table reference
     */
    public TopicObject() {
        this("t_topic_object", null);
    }

    /**
     * Create an aliased <code>system.t_topic_object</code> table reference
     */
    public TopicObject(String alias) {
        this(alias, TOPIC_OBJECT);
    }

    private TopicObject(String alias, Table<TopicObjectRecord> aliased) {
        this(alias, aliased, null);
    }

    private TopicObject(String alias, Table<TopicObjectRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TopicObjectRecord> getPrimaryKey() {
        return Keys.KEY_T_TOPIC_OBJECT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TopicObjectRecord>> getKeys() {
        return Arrays.<UniqueKey<TopicObjectRecord>>asList(Keys.KEY_T_TOPIC_OBJECT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TopicObject as(String alias) {
        return new TopicObject(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TopicObject rename(String name) {
        return new TopicObject(name, null);
    }
}
