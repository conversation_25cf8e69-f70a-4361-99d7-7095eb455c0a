package com.zxy.product.system.api.homeconfig;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.HomeContent;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/2/25.
 */
@RemoteService
public interface HomeContentService {

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    PagedResult<HomeContent> findPages(int page, int pageSize, String moduleConfigId, Integer clientType);

    @Transactional
    List<HomeContent> insertBatch(String moduleConfigId, List<HomeContent> list);

    @Transactional
    String delete(String id);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<HomeContent> findList(String moduleConfigId, int size, Integer clientType, Optional<Integer> dataType);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    int getMaxSort(String moduleConfigId ,Integer clientType);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Optional<HomeContent> findOne(String moduleConfigId, String dataId);

    @Transactional
    String updateSortById(String id, int sort ,Integer clientType);

    @Transactional
    String updateImage(String id, String imageId,String imageIdPath , Integer clientType);

    /**
     * 清空自定义模块已配置的图片
     * @param moduleConfigId
     * @param clientType
     */
    @Transactional
    void deleteContentImage(String moduleConfigId, Optional<Integer> clientType);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	List<HomeContent> findMoreList(String moduleConfigId, Integer clientType);

    String updateImageToCache(String userId, String userToken, String contentId, String imageId, String imageIdPath,
			Integer clientType);

	String updateBasicInfoToCache(String userId, String userToken, String contentId, Optional<String> dataExt, Optional<String> dataName, Optional<String> url);

	String updateSortByIdToCache(String userId, String userToken, String contentId, int sort, Integer clientType, String moduleHomeConfigId);

	PagedResult<HomeContent> findPagesFromCache(String userId, String userToken, int page, int pageSize,
			String moduleConfigId, Integer clientType);

	List<HomeContent> insertBatchToCache(String userId, String userToken, String moduleConfigId,
			List<HomeContent> list);

	String deleteFromCache(String userId, String userToken, String id);

	void saveAsFinal(String userId, String userToken, String homeConfigId);

	List<HomeContent> findListFromCache(String userId, String userToken, String moduleConfigId, int size,
			Integer clientType,Optional<Integer> dataType);

    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
	String saveCheck(String userId, String userToken, String moduleConfigId, Integer clientType);
}

