package com.zxy.product.system.content;

import com.zxy.common.base.exception.Code;

/**
 * <AUTHOR>
 *
 */
public enum ErrorCode implements Code {


    //从course平移过来
    PublicationRestriction(5001, "开关开启,白天不允许发布"),
    OrganizationCodeUnique(10101, "organization code can not be repeat under the same organization"),
    ParentOrganizationRule(110101, "parent organization code should be inner or outer"),
    ParentOrganizationRule2(110102, "inner parent organization can not be outer and outer parent organization can not be inner"),
    CanNotFindOrganization(10102, "can not find organization"),
    CanNotDeleteOrganization(10103, "can not delete, cause this node has child node"),
    OrganizationNameUnique(10104, "organization name can not be repeat under the same organization"),
    OrganizationParentRule(10105, "organization level can not bigger than parent"),
    OrganizationSubRule(10106, "organization level can not smaller than sub"),
    OrganizationDomainNotFound(10107, "can not find organization by domain"),
    CanNotDeleteSinceRoleReleated(10108, "can not delete, cause this org has role releated"),
    CanNotDeleteSinceGrantReleated(10109, "can not delete, cause this node has grant releated"),
    OrganizationCodeNotFound(10110, "can not find organization by domain"),
    CanNotDeleteSinceBeyondGrant(10111, "can not delete, cause this org is beyond your grants"),
    CanNotUpdateSinceBeyondGrant(10112, "can not update, cause this org is beyond your grants"),
    CanNotDeleteSinceRootNode(10113, "can not delete, cause this org is root node"),
    QueryHasTooManyRecords(10114, "query has too many recoreds"),

    OrganizationNameRequired(10115, "organization name is required"),
    OrganizationNameTooLong(10116, "organization name too long"),
    OrganizationCodeRequired(10117, "organization name is required"),
	ParentOrganizationCodeRequired(110117, "parent organization name is required"),
    OrganizationLevelError(10118, "organization level error"),
    OrganizationStatusError(10119, "organization status error"),
    OrganizationCodeTooLong(10120, "organization code too long"),
	ParentOrganizationCodeTooLong(110120, "parent organization code too long"),
    OrganizationParentDisabled(10121, "parent organization is disabled"),
    OrganizationImportExceedMax(10122, "import count exceed max"),
    OrganizationImportTemplateError(10123, "import template not match"),
    OrganizationImportDataRepeat(10124, "import data repeat"),
    OrganizationCodeEqualsParentCode(10125, "organization code equals parentCode"),
    OrganizationParentNotExist(10126, "organization's parent is not exist"),
    OrganizationParentValidateFail(10127, "organization's parent valid fail"),
    OrganizationImportCodeExists(10128, "organization's parent valid fail"),
    OrganizationImportCodeNotMatch(10129, "organization's code not match"),
	ParentOrganizationImportCodeNotMatch(110129, "parent organization code not match"),
    InnerOuterOrganizationCantBeDelete(10130, "inner outer org can't be deleted"),
    CannotAddOrgInRootNode(10131, "can't add organization at root node"),
    AgentOrgCanOnlyBeOuter(10132, "agent org can only be outer"),
    OrganizationImportDEPTHERROR(10133, "organization's depth not match"),
    OrganizationImportError(10199, "organization import error"),
    DynamicSmsSendFail(10134, "dynamic sms send fail"),

    CommentInfoHasCommentReplys(10201, "current comment has replys"),
    CommentAccuseExistsForMember(10202, "exists accuse by member"),
    AccuseSourceNotExist(10203, "accuse source is not exist"), 	//举报的内容不存在
    CommentTooFrequest(10204, "comment too frequest"), 			//评论过于频繁
    CommentDeleteError(10205, "comment delete error"), 			//删除异常，非本人评论不能删除

    AudienceItemJsonError(10301, "json error"),

    SensitiveTypeCodeRequired(10401,"sensitive import is required"),
    SensitiveLengthNotMatch(10402,"sensitive length not match"),
    SensitiveExist(10403,"sensitive is exist"),
    SensitiveImportError(10404,"sensitive import template error"),

    RoleReferenced(10501,"role is referenced"),
    SubRoleReferenced(10502,"sub role is referenced"),
    RoleUpdateUnSupport(10503,"role update un support"),
    SubRoleNotExists(10504,"sub role not exists"),

    MenuInit(10601,"menu is inited"),
    MenuReleated(10602,"menu is releated"),

    TopicReferenced(10701,"the topic has been referenced"),
    TopicTypeCodeUnique(10702, "topic code must be unique"),
    TopicTypeReferenced(10703, "the topic type code has been referenced"),
    TopicNameHasBeenDefined(10704, "the topic name has been defined"),
    TopicTypeNameHasBeenDefined(10705, "the topic type name has been defined"),
    TopicTypeNotExists(10706, "the topic type not exists"),
    ParentTopicNotExists(10707, "the parent topic not exists"),
    SYNONYMS_TOPIC_MORE_THEN_50(10708, "最多可关联50个同义词标签"),
    RELATED_TOPIC_MORE_THEN_50(10709,"最多可关联50个关联词标签"),
    TopicNotExists(10710, "the topic not exists"),


    GrantBeyondOfMe(10801, "the member's grant is bigger than me"),
    GrantRoleRepeat(10802, "grant role repeat"),

    FeedbackAttachmentError(10901, "feedback attachments upload error"),

    RICH_TEXT_PIC_FILE_MAX_SIZE_ERROR(990001, "rick text pic file max size error"),
    Unauthorized(1, "unauthorized"),
    CertificateTemplateHasBeenUsedAndCannotBeDeleted(11001, "Certificate template has been used by subject and cannot be delete"),
    CertificateTemplateHasBeenUsedAndCannotBeModified(11002, "Certificate template has been used by subject and cannot be modified"),
    InvalidType(12000,"this type is invalid"),
    CommentInfoNotExist(12001,"this comment is not exist"),
    CERTIFICATE_TEMPLATE_NOT_EXISTS(11005, "Certificate template not exists"),

    NumIsExist(13001,"phone section num is exist"),

    SensitiveTypeRequired(13002,"sensitive type import is required"),
    SensitiveTypeNotMatch(13003,"sensitive type not match"),
    TheOwningLabelDoesNotSelectItself(13004,"The owning label does not select itself"),
    /**
     * 后台运营管理
     */
    RepeatToAdd(15001,"请勿重复添加!"),
    AddContentNonentity(15002,"添加的内容id不存在!"),
    GtMaxNumber(15003,"大于最大数量限制!"),
    DeleteContentNonentity(15004,"删除内容不存在!"),
    ContentNotBlacklist(15005,"内容不在黑名单中!"),
    MoveContentNonentity(15006,"移动内容不存在!"),

    HotWordIsExisted(16001,"hot word is existed"),
    HandSortIsRepeat(16002,"hand sort is repeat"),
    AppClientHasLogined(90902,"client has logined"),

    FileIdNotNull(16011,"fileId can't null!"),
    ExcelNoRows(16012, "Excel no rows!"),
    CheckRowTooLong(16013, "rows is to long"),
    InvalidExcelTemplate(16014, "Invalid excel template"),

    AskBarOrRedBoat(17001, "红船审核和问吧审核只能打开一个"),

    RepeatAdd(18001,"虚拟空间重复添加!"),
    OrganizationsAannotVirtualSpace(18002,"该组织不能创建虚拟空间!"),

    POINT_GREADE_NOT_REPEAT(19001,"重复分数禁止添加!"),
    POINT_GREADE_NOT_EXISTS(19002,"当前积分等级对象不存在!"),
    POINT_DETAIL_PRODUCT_NOT_NULL(19003,"商品参数不正确!"),
    POINT_DETAIL_POINT_NOT_NULL(19004,"积分参数不正确!"),
    POINT_DETAIL_TYPE_NOT_NULL(19005,"类型参数不正确!"),
    POINT_DETAIL_PRODUCT_ALREADY_PAID(19006,"商品已购买!"),
    POINT_BALANCE_NOT_ENOUGH(19007,"积分余额不足!"),

    HomeSaveFail(14002,"save home config failed"),
    NameDuplication(14007,"名字重复"),
    TheNumberOfPromotionReleasesExceeded5(14008,"首页推广发布数量超过5个"),
    TheNumberOfPromotionReleasesExceeded5Two(14010,"课程推广发布数量超过5个"),
    TheNumberOfPromotionReleasesExceeded5Three(14011,"导航推广发布数量超过5个"),
    TheParametersOfThePageToWhichTheNavigationPromotionBelongsDoNotExist(14009,"导航推广所属页面参数不存在"),


    pointBannerMostFive(17313, "point_banner_most_five"),

    /**
     * 课程导航1级目录不能超过8
     */
    CourseDirecotoryExceedSize(14001, "course directory size cannot bigger than 8"),

    commentDoesNotExist(14003, "comment does not exist"),

    DASHBOARD_HOT_WORD_NOT_EXIST(14004, "月度热词不存在"),
    DASHBOARD_HOT_WORD_YEAR_NOT_EXIST(14005, "年度热词不存在"),

    //智能图片7006
    PICTURE_CATEGORY_MAX_LENGTH_LIMIT(700601, "超出颜色分类限制"),
    METARIAL_EXIST_DELETE_REFUSED(700602, "存在关联素材，不允许删除"),
    STYLE_CONTENT_ERROR(700603, "样式内容解析错误，请检查!"),
    METARIAL_NOT_EXIST(700604, "素材不存在!"),

    /**
     * 积分导入相关
     */
    MemberNotExist(14100, "用户不存在"),
    MemberNameIsnull(14101, "用户name不能为空"),
    PointIsnull(14102, "积分为空"),
    PointIsBad(14103, "积分不符合规则"),
    remarkIsTooLong(14104, "备注不能超过300字"),

    UserIsNotPermitted(30120,"您暂无该资源浏览权限，去看看其他资源吧"),
    WrongTissueType(30220,"当前组织不是三级组织"),
    organize(30221,"组织存在"),
    OrganizationStausError(30225, "组织有效期结束时间不能小于当前时间；设置了有效期后才可以启用"),
    DuplicateName(30222,"该分组名称已存在"),
    ExtraLong(30223,"超度超长"),
    RelatedData(30224,"所选分组中有关联虚拟空间无法删除。可以删除关联的虚拟空间在进行分组删除。"),

    SubmissionsIsEmpty(400001,"提交内容为空"),

    CertifyMaxCount(400011,"最大添加50个"),
    /**首页文件上传失败，即将重试*/
    HomeFileUploadFailedAndWillBeRetriedSoon(15001,"HomeFileUploadFailedAndWillBeRetriedSoon"),
    AccountConfigurationFieldsRequired(400002,"4A账号映射配置缺少参数"),
    AccountConfigurationExist(400003,"当前账号映射配置已存在"),
    AccountConfigurationStateInvalid(400004,"账号映射配置状态无效"),
    EXCEL_ERROR(14201, "模板错误，请导入正确模板"),
    ImportNullFile(14202, "导入文档不能为空"),
    EXCEL_NOTSUPPORT(14203, "文档不符合规范"),
    MEMBER_ERROR(14204, "员工编码填写错误"),
    ORG_CODE_ERROR(14205, "组织编码填写错误"),
    VALID_DATE_ERROR(14206, "授权有效期填写错误"),
    GRANT_REPEAT(14207, "授权重复"),
    OPERATOR_TYPE_ERROR(14208, "数据格式错误"),
    DATE_ERROR(14209, "数据异常"),
    DATE_TWO_ERROR(14210, "未知情况"),
    ;
//The account configuration field is missing parameters

    private final int code;
    private final String desc;


    ErrorCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    public static ErrorCode getByCode(int code) {
        for(ErrorCode ec : ErrorCode.values()) {
        	if (ec.getCode() == code) {
        		return ec;
        	}
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }
}
