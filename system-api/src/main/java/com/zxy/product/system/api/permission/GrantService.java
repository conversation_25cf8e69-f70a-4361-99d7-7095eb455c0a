package com.zxy.product.system.api.permission;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.*;
import org.jooq.Condition;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


/**
 * 授权服务
 *
 * @user tianjun
 * @date 16/6/15
 */
@RemoteService(timeout = 300000)
public interface GrantService {

    int AUTO_CHILD_NO = 0;
    int AUTO_CHILD_YES = 1;
    int AUTO_CHILD_OTHER = 2;

    public static final String GRANT_MENU_URI = "system/grant"; // 授权菜单uri

    /**
     * 添加授权
     *
     * @param roleIds
     *            角色
     * @param memberIds
     *            用户
     * @param organizationIds
     *            节点
     * @param operatorTypes
     *            授权 操作类型
     * @param notifyType
     * @return
     */
    @Transactional
    List<Grant> insert(String memberId, String organizationId, String roleIds, String memberIds, String organizationIds, Optional<String> indeterminates, Optional<String>  childFind ,
                 String operatorTypes, Optional<Integer> validType, Optional<Long> validDate, Optional<Integer> notifyFlag, Optional<String> notifyType);

    /**
     * 删除授权
     * @param grantId
     * @param currentUserId
     * @param currentUserFullName
     * @return
     */
    @Transactional
    Integer delete(String grantId, String currentUserId, String currentUserFullName);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Grant> findGrantMembers(String grantId);

    /**
     * 删除授权
     * @param grantId
     * @return
     */
    @Transactional
    Integer deleteGrantMember(String grantMemberId);

    /**
     * 查询授权记录
     *
     * @param grantId
     *            父节点
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Grant> getOptional(String grantId);

    /**
     * 查询授权记录
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Grant> getOptional(String grantId, Optional<String> memberId);

    /**
     * 查询授权记录
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Grant> getByMemberId(String grantId, String memberId);

    /**
     * 查询授权订单
     * tianjun
     * @param optional
     * @param nodeId
     * @param type
     * @param rootOrganizationId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Grant> find(int page, int pageSize, Optional<String> nodeId, Optional<String> roleName, Optional<Long> start, Optional<Long> end, Optional<String> operatorType,
                            String memberId, Integer contain, Optional<Integer> type, String rootOrganizationId);
    /**
     *查询某个授权的授权人
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Member> findGrantMembers(String memberId, int page, int pageSize, String grantId);


    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Grant find(String grantId);


//    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
//    int findDetailCount(String grantId);

//    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
//    List<GrantDetail> findDetail(String grantId);

//    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
//    List<GrantDetail> findDetail(String grantId, int page, int pageSize);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<GrantDetail> findDetail(int page, int pageSize, Condition... coditions);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    int findDetailCount(Condition... coditions);

    /**
     * 获取被授权的组织 (左侧菜单)
     * 金康
     * @return
     */
    /**
     * * 此方法慎用，如需使用先联系技术经理！！！
     * *（该方法会导致查询超时，dubbo传输超时，可以使用 grantService.findGrantOrganizationByUri 替代）
     */
    @Deprecated
    List<Organization> findGrantedOrganization(String memberId, String uri, Optional<Integer> level, Optional<String> name, Optional<String> code, Optional<String> excludeId, Optional<String> organizationId, Optional<String> operatorType, Optional<Boolean> allStatus);
    /**
     * 获取被授权的组织 (左侧菜单)
     * 重写方法，只是修改了返回值
     * 尝试解决dubbo传输超时
     * @return
     */
    List<String> findGrantedOrganizationIds(String memberId, String uri, Optional<Integer> level, Optional<String> name, Optional<String> code, Optional<String> excludeId, Optional<String> organizationId, Optional<String> operatorType, Optional<Boolean> allStatus);

    List<Organization> findGrantedOrganizationAll(String memberId, Optional<String> uri, Optional<Integer> level, Optional<String> name, Optional<String> code, Optional<String> excludeId, Optional<String> organizationId, Optional<String> operatorType, Optional<Boolean> allStatus);

    /**
     * 获取用户被授权的顶级组织
     * contain	//是否包含子级【0：不包含；1：包含】
     * @return
     */
    List<String> findGrantedTopOrganization(String memberId, String uri, Integer contain);

    /**
     * 查询用户是否拥有某个组织ID的权限
     * @param memberId
     * @param uri
     * @param organizationIds
     * @return
     */
    List<String> findHavingGrantedOrganization(String memberId, String uri, String...organizationIds);
    /**
     * 查询用户是否拥有某个组织ID的权限
     */
    List<String> findHavingGrantedOrganization(String memberId, String uri, List<String> operatorTypes, String organizationId);

    /**
     * 获取被授权的组织(包括5及以下节点)fix ask-bar bug
     * TianC
     * @return
     */
    List<String> findGrantedOrganizationAll(String memberId, String uri, Optional<String> organizationId, Optional<String> operatorType);

    /**
     * 获取被授权的组织以及这些组织的父级组织
     */
    List<Organization> findGrantedOrganizationWithParent(String memberId, String uri);

    /**
     * 获取被授权的组织(分页)
     * 金康
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Organization> findGrantedOrganization(int page, int pageSize, String memberId, String uri, Optional<String> id, Optional<String> name, Optional<Integer> level, Optional<Integer> specLevel, Optional<String> code);

    /**
     * 根据用户和角色查询授权点
     * tianjun
     * @param memberId
     * @param roleId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findOrganizationByMemberAndRole(String memberId, String roleId);

    /**
     *  修改授权
     * @param operatorTypes 操作类型
     * @param organizationId 所属节点
     * @param roleIds 所选择的角色
     * @param memberIds 所选择的用户
     * @param organizationIds 所选择的节点
     * @param indeterminates 所选择的半选中节点
     * @param chindFinds 所选择的自动发现节点
     * @param notifyType
     * @param currentMemberName
     * @return
     */
    @Transactional
    Grant update(String currentUserId, String grantId, String organizationId, String roleIds, String memberIds, String organizationIds, Optional<String> indeterminates,
                 Optional<String> chindFinds,
                 String operatorType, Optional<Integer> validType, Optional<Long> validDate, Optional<Integer> notifyFlag, Optional<String> notifyType,
                 String currentMemberName);

    /**
     *  修改授权
     * @param operatorTypes 操作类型
     * @param memberId 所选择的用户
     * @param organizationId 所属节点
     * @param roleIds 所选择的角色
     * @param organizationIds 所选择的节点
     * @param indeterminates 所选择的半选中节点
     * @param chindFinds 所选择的自动发现节点
     * @param notifyType
     * @param currentMemberName
     * @return
     */
    @Transactional
    Grant addMember(String currentUserId, String grantId, String organizationId, String roleIds, String memberIds, String organizationIds, Optional<String> indeterminates,
                    Optional<String> chindFinds,
                    String operatorType, Optional<Integer> validType, Optional<Long> validDate, Optional<Integer> notifyFlag, Optional<String> notifyType,
                    String currentMemberName);

    /**
     * 查询该用户下的所有权限
     * @param memberId
     * @return map(key:organizationId  value:operatorTypes)
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, String> findAll(String memberId);

    /**
     *  2016-09-19
     *  tianjun
     *  选择角色,节点之后 查出能操作的操作类型
     * @param memberId
     * @param roleId
     * @param organizationIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String findOperatorType(String memberId,String roleId, String[] organizationIds, String[] seconds);

    /**
     * 2016-09-20
     * tianjun
     * 根据用户名,菜单路径,查询出所有授权的节点的最大权限
     * <a href='http://192.168.1.198/zxy/system/wikis/home'>wiki</a>
     * @param memberId
     * @param meunUrl
     * @return map(org, operator)
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String ,String> findOrganizationOperatorType(String memberId, String meunUrl);

    /**
     * 清除用户授权
     */
    @Transactional
    Integer clearMemberGrant(String memberId);

    /** 查询用户被授权的次数 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Integer countGrantMemberByMemberId(String memberId);

    /** 判断当前用户,对于某一菜单,是否拥有最高结点的权限 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	boolean judegOwnedCompanyNode(String memberId, String uri);

    /** 比较用户memberId和当前登陆用户的权限,如果比当前用户小,返回true,否则返回false */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    boolean checkGrants(String memberId, String currentUserId);

    /** 查询用户的授权组织,主要给受众对象使用 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
	List<Organization> findSimpleGrantedOrganization(String memberId, String uri, Optional<String> name, Optional<String> code, Boolean supportMore);

    /**
     * 查询登录用户的授权组织 约课权限配置导入校验使用(目前场景是只查询depth为1和3的组织)
     * supportMore 默认false
     * depth 默认 (1,3)
     * @param memberId
     * @param uri
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findSimpleGrantedOrganizationForGb(String memberId, String uri);

    /** 当菜单新增时,补全用户授权 */
    @Transactional
    Map<String, Object> fullAdminRoleGrantWhenMenuAdd(String menuIds);

    /**
     * 根据组织id获取被授权的父级组织
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findGrantedOrganizationWithParent(String memberId, String uri, String organizationId);

    /**
     * 根据菜单URI和组织ID查询授权过的管理员ID
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String[] findGrantedMemberByUriAndOrganization(String uri, String organizationId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findGrantedOrganization(String memberId, String uri, Optional<String> organizationId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    void findGrantedOrganizationNoReturn(String memberId, String uri, Optional<String> organizationId);

    /**
     * 去除组织排序
     *
     * @param memberId
     * @param uri
     * @param organizationId
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    HashSet<String> findGrantedOrganization2(String memberId, String uri, Optional<String> organizationId);

    /**
     * 根据组织id获取被授权的父级组织
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<GrantOrganization> findGrantOrganizationByMemberAndUri(String memberId, String uri);

    /**
     * 新增授权 获取操作类型
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String getOperatorTypes(String memberId,String roleId);

     /**
      * 管理员的授权时所选角色 对应的组织节点的Path
      * @param memberId 用户id
      * @param roleId   角色Id
      * @return
      */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, Set<String>> findGrantOrganizationByRoleId(String memberId, String roleId);

    /**
     * 管理员的授权组织节点的Path
     * @param memberId 用户id
     * @param roleId   角色Id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, Set<String>> findGrantOrganizations(String memberId, String roleId, String rootOrganizationId);

    /**
     *  获取用户管理节点，返回两种类型的管理节点 1.组织（包含子节点）  2.组织
     *  返回存储结构{"includeKey", ["1,","1,10001,","1,10002,"], "notIncludeKey", ["1","10002"]}
     * @param memberId
     * @param uri
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, Set<String>> findGrantOrganizationByUri(String memberId, String uri, String rootOrganizationId);

    /**
     * 是否拥有标签菜单权限
     * @param uri 标签uri
     * @param memberId 当前登录用户id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    int isGrantTopic(String uri,String memberId);

    /**
     * 获取拥有菜单权限的成员
     * @param uri 标签uri
     * @param orgPath 机构路径
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<String> findMemberByOrganizationAndUri(String orgPath, String uri);

    /**
     * 查询当前用户权限内组织数据
     * @param currentUserId
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    List<String> getMemberGrantsByOrgIds(String currentUserId, String uri, List<String> orgIds);

    /**
     * 根据orgPath获取所有MemberId
     * @param uri 标签uri
     * @param orgPath 机构路径
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String findMemberByOrgPathAndUri(String orgPath, String uri);


    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<String> getAllMemberIds();

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
     String findGrantMemberFullNames(String grantId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, String> findPaasOperatorType(String memberId, String menuId, String rootOrganizationId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<Grant> findMemberPage(int page, int pageSize, Optional<String> nodeId, Optional<String> roleName, Optional<Long> start, Optional<Long> end,
                                      Optional<String> operatorType, String memberId, Integer contain, Optional<Integer> type, Optional<String> roleId,
                                      Optional<String> memberIdOptional, Optional<String> content, Optional<String> grantId, Optional<String> authorizeOrgId,
                                      Integer contains, Optional<String> memberName, Optional<String> fullName);

    @Transactional
    Boolean fixGrant();

    @Transactional
    Boolean deleteGrantMemberByGrantId(String grantId, String grantMemberId, String currentUserId, String currentUserFullName);

    /**
     * 查询人员信息
     * @param memberNames
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Member> findByMemberNames(List<String> memberNames);
}