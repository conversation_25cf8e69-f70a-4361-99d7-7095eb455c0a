/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 积分轮播配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IPointBanner extends Serializable {

    /**
     * Setter for <code>system.t_point_banner.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_point_banner.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>system.t_point_banner.f_banner_name</code>. Banner名称
     */
    public void setBannerName(String value);

    /**
     * Getter for <code>system.t_point_banner.f_banner_name</code>. Banner名称
     */
    public String getBannerName();

    /**
     * Setter for <code>system.t_point_banner.f_pc_link_id</code>. PC封面url
     */
    public void setPcLinkId(String value);

    /**
     * Getter for <code>system.t_point_banner.f_pc_link_id</code>. PC封面url
     */
    public String getPcLinkId();

    /**
     * Setter for <code>system.t_point_banner.f_app_link_id</code>. APP封面url
     */
    public void setAppLinkId(String value);

    /**
     * Getter for <code>system.t_point_banner.f_app_link_id</code>. APP封面url
     */
    public String getAppLinkId();

    /**
     * Setter for <code>system.t_point_banner.f_link_type</code>. 连接类型
     */
    public void setLinkType(Integer value);

    /**
     * Getter for <code>system.t_point_banner.f_link_type</code>. 连接类型
     */
    public Integer getLinkType();

    /**
     * Setter for <code>system.t_point_banner.f_link_address</code>. 点击后的跳转链接
     */
    public void setLinkAddress(String value);

    /**
     * Getter for <code>system.t_point_banner.f_link_address</code>. 点击后的跳转链接
     */
    public String getLinkAddress();

    /**
     * Setter for <code>system.t_point_banner.f_banner_state</code>. Banner状态(1-未发布,2-已发布)
     */
    public void setBannerState(Integer value);

    /**
     * Getter for <code>system.t_point_banner.f_banner_state</code>. Banner状态(1-未发布,2-已发布)
     */
    public Integer getBannerState();

    /**
     * Setter for <code>system.t_point_banner.f_pc_image_path</code>. PC封面地址
     */
    public void setPcImagePath(String value);

    /**
     * Getter for <code>system.t_point_banner.f_pc_image_path</code>. PC封面地址
     */
    public String getPcImagePath();

    /**
     * Setter for <code>system.t_point_banner.f_app_image_path</code>. app封面地址
     */
    public void setAppImagePath(String value);

    /**
     * Getter for <code>system.t_point_banner.f_app_image_path</code>. app封面地址
     */
    public String getAppImagePath();

    /**
     * Setter for <code>system.t_point_banner.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>system.t_point_banner.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>system.t_point_banner.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>system.t_point_banner.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>system.t_point_banner.f_delete_flag</code>. 删除标识(1-未删除,2-已删除)
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>system.t_point_banner.f_delete_flag</code>. 删除标识(1-未删除,2-已删除)
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>system.t_point_banner.f_content</code>. 文案
     */
    public void setContent(String value);

    /**
     * Getter for <code>system.t_point_banner.f_content</code>. 文案
     */
    public String getContent();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IPointBanner
     */
    public void from(IPointBanner from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IPointBanner
     */
    public <E extends IPointBanner> E into(E into);
}
