/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 首页广告表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IHomeAdvertisement extends Serializable {

    /**
     * Setter for <code>system.t_home_advertisement.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>system.t_home_advertisement.f_title</code>. 标题
     */
    public void setTitle(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_title</code>. 标题
     */
    public String getTitle();

    /**
     * Setter for <code>system.t_home_advertisement.f_module_config_id</code>. 配置模块ID
     */
    public void setModuleConfigId(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_module_config_id</code>. 配置模块ID
     */
    public String getModuleConfigId();

    /**
     * Setter for <code>system.t_home_advertisement.f_pc_image</code>. pc图片
     */
    public void setPcImage(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_pc_image</code>. pc图片
     */
    public String getPcImage();

    /**
     * Setter for <code>system.t_home_advertisement.f_app_image</code>. app图片
     */
    public void setAppImage(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_app_image</code>. app图片
     */
    public String getAppImage();

    /**
     * Setter for <code>system.t_home_advertisement.f_client_type</code>. 类型：0 全部 1 pc 2 app
     */
    public void setClientType(Integer value);

    /**
     * Getter for <code>system.t_home_advertisement.f_client_type</code>. 类型：0 全部 1 pc 2 app
     */
    public Integer getClientType();

    /**
     * Setter for <code>system.t_home_advertisement.f_link_type</code>. url类型
     */
    public void setLinkType(Integer value);

    /**
     * Getter for <code>system.t_home_advertisement.f_link_type</code>. url类型
     */
    public Integer getLinkType();

    /**
     * Setter for <code>system.t_home_advertisement.f_link_address</code>. url连接地址
     */
    public void setLinkAddress(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_link_address</code>. url连接地址
     */
    public String getLinkAddress();

    /**
     * Setter for <code>system.t_home_advertisement.f_content</code>. 内容
     */
    public void setContent(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_content</code>. 内容
     */
    public String getContent();

    /**
     * Setter for <code>system.t_home_advertisement.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>system.t_home_advertisement.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>system.t_home_advertisement.f_state</code>. 状态：0 未发布 1 已发布
     */
    public void setState(Integer value);

    /**
     * Getter for <code>system.t_home_advertisement.f_state</code>. 状态：0 未发布 1 已发布
     */
    public Integer getState();

    /**
     * Setter for <code>system.t_home_advertisement.f_sort</code>.
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>system.t_home_advertisement.f_sort</code>.
     */
    public Integer getSort();

    /**
     * Setter for <code>system.t_home_advertisement.f_business_type</code>.
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>system.t_home_advertisement.f_business_type</code>.
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>system.t_home_advertisement.f_business_id</code>.
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_business_id</code>.
     */
    public String getBusinessId();

    /**
     * Setter for <code>system.t_home_advertisement.f_pc_image_path</code>. pc图片地址
     */
    public void setPcImagePath(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_pc_image_path</code>. pc图片地址
     */
    public String getPcImagePath();

    /**
     * Setter for <code>system.t_home_advertisement.f_app_image_path</code>. app图片地址
     */
    public void setAppImagePath(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_app_image_path</code>. app图片地址
     */
    public String getAppImagePath();

    /**
     * Setter for <code>system.t_home_advertisement.f_copywriting</code>. 文案
     */
    public void setCopywriting(String value);

    /**
     * Getter for <code>system.t_home_advertisement.f_copywriting</code>. 文案
     */
    public String getCopywriting();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IHomeAdvertisement
     */
    public void from(IHomeAdvertisement from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IHomeAdvertisement
     */
    public <E extends IHomeAdvertisement> E into(E into);
}
