package com.zxy.product.system.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.zxy.product.system.jooq.tables.pojos.GrantEntity;

/**
 * Created by keeley on 16/7/27.
 */
public class Grant extends GrantEntity {

    private static final long serialVersionUID = 7581974508462864109L;

    public static final String URI = "system/grant";
    public static final String OPERATOR_TYPE_ALL = "0";
    public static final String OPERATOR_TYPE_EDIT = "2";
    /**
     * 三天有效期
     */
    public static final Integer VALID_TYPE_THREE_DAY = 0;
    /**
     * 自定义有效期
     */
    public static final Integer VALID_TYPE_CUSTOM = 5;
    /**
     * 永久有效
     */
    public static final Integer VALID_TYPE_FOREVER = 6;
    public static final Integer NOTIFY_NO = 0;
    public static final Integer NOTIFY_YES = 1;
    public static final Integer REVOKE_NOTIFY_NO = 0;

    /**
     * 导入限制条数
     */
    public static final Integer TEMPLATE_DATA_LIMIT = 5000;
    public static final String TEMPLATE_MEMBER_NAME_CN = "员工编码（必填）";
    public static final String TEMPLATE_OPERATORTYPES_CN = "授权类型（必填，完全控制或可查看、可编辑、可删除、其他）";
    public static final String TEMPLATE_ORGANIZATION_CODE_CN = "授权节点组织编码（必填）";
    public static final String TEMPLATE_CHILDFLAG_CN = "是否包含子节点（必填，是/否）";
    public static final String TEMPLATE_ORGANIZATION_CN = "授权归属部门组织编码（必填）";
    public static final String TEMPLATE_VALIDDATE_CN = "授权有效期（非必填，yyyy-mm-dd）";

    private int row;
    private List<RowError> errors = new ArrayList<>();

    private List<Member> members;
    private Organization organization;
    private List<Organization> organizations;
    private Role role;
    private Integer memberSize;
    private Integer organizationSize;
    private boolean owned;
    private String memberId;
    private String memberFullName;
    private String memberName;
    private String roleName;
    private Member member;
    private String grantMemberId;
    private String organizationCode;// 归属部门
    private String organizationCodes;// 授权节点
    private String childFlag;// 是否包含子部门
    private String valid;

    public String getGrantMemberId() {
        return grantMemberId;
    }

    public void setGrantMemberId(String grantMemberId) {
        this.grantMemberId = grantMemberId;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getMemberFullName() {
        return memberFullName;
    }

    public void setMemberFullName(String memberFullName) {
        this.memberFullName = memberFullName;
    }

    public boolean getOwned() {
		return owned;
	}

	public void setOwned(boolean owned) {
		this.owned = owned;
	}

	public List<Member> getMembers() {
        return members;
    }

    public void setMembers(List<Member> members) {
        this.members = members;
    }

    public List<Organization> getOrganizations() {
        return organizations;
    }

    public void setOrganizations(List<Organization> organizations) {
        this.organizations = organizations;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Integer getMemberSize() {
        return memberSize;
    }

    public void setMemberSize(Integer memberSize) {
        this.memberSize = memberSize;
    }

    public Integer getOrganizationSize() {
        return organizationSize;
    }

    public void setOrganizationSize(Integer organizationSize) {
        this.organizationSize = organizationSize;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public String getOrganizationCodes() {
        return organizationCodes;
    }

    public void setOrganizationCodes(String organizationCodes) {
        this.organizationCodes = organizationCodes;
    }

    public String getChildFlag() {
        return childFlag;
    }

    public void setChildFlag(String childFlag) {
        this.childFlag = childFlag;
    }

    public String getValid() {
        return valid;
    }

    public void setValid(String valid) {
        this.valid = valid;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }

    // 定义权限代码到描述的映射关系
    private static final Map<Integer, String> PERMISSION_MAP = new HashMap<>();
    static {
        PERMISSION_MAP.put(0, "完全控制");
        PERMISSION_MAP.put(1, "可读");
        PERMISSION_MAP.put(2, "可编辑");
        PERMISSION_MAP.put(3, "可删除");
        PERMISSION_MAP.put(4, "其他");
    }

    // 字符串类型入参处理
    public static String convertPermissionTypes(String typesStr) {
        if (typesStr == null || typesStr.isEmpty()) {
            return "";
        }

        List<Integer> types = Arrays.stream(typesStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        return convertPermissionTypes(types);
    }

    public static String convertPermissionTypes(List<Integer> types) {
        if (types == null || types.isEmpty()) {
            return "";
        }
        // 特殊情况：包含0直接返回“完全控制”
        if (types.contains(0)) {
            return "完全控制";
        }

        return types.stream()
                .sorted() // 排序确保拼接顺序一致（如1,2和2,1都按1,2处理）
                .map(PERMISSION_MAP::get) // 直接用基础映射转换
                .filter(Objects::nonNull) // 过滤无效值
                .collect(Collectors.joining(",")); // 拼接结果
    }
}
