/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.AdvertisementConfigImageRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * APP广告页配置图片关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AdvertisementConfigImage extends TableImpl<AdvertisementConfigImageRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_advertisement_config_image</code>
     */
    public static final AdvertisementConfigImage ADVERTISEMENT_CONFIG_IMAGE = new AdvertisementConfigImage();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AdvertisementConfigImageRecord> getRecordType() {
        return AdvertisementConfigImageRecord.class;
    }

    /**
     * The column <code>system.t_advertisement_config_image.f_id</code>.
     */
    public final TableField<AdvertisementConfigImageRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>system.t_advertisement_config_image.f_name</code>. 名称
     */
    public final TableField<AdvertisementConfigImageRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(60), this, "名称");

    /**
     * The column <code>system.t_advertisement_config_image.f_advertisement_config_id</code>. APP广告页配置表ID
     */
    public final TableField<AdvertisementConfigImageRecord, String> ADVERTISEMENT_CONFIG_ID = createField("f_advertisement_config_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "APP广告页配置表ID");

    /**
     * The column <code>system.t_advertisement_config_image.f_image_path</code>. 图片地址
     */
    public final TableField<AdvertisementConfigImageRecord, String> IMAGE_PATH = createField("f_image_path", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "图片地址");

    /**
     * The column <code>system.t_advertisement_config_image.f_sort</code>. 序号
     */
    public final TableField<AdvertisementConfigImageRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "序号");

    /**
     * The column <code>system.t_advertisement_config_image.f_create_time</code>. 创建时间
     */
    public final TableField<AdvertisementConfigImageRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>system.t_advertisement_config_image.f_link_type</code>. url类型
     */
    public final TableField<AdvertisementConfigImageRecord, Integer> LINK_TYPE = createField("f_link_type", org.jooq.impl.SQLDataType.INTEGER, this, "url类型");

    /**
     * The column <code>system.t_advertisement_config_image.f_link_address</code>. url地址
     */
    public final TableField<AdvertisementConfigImageRecord, String> LINK_ADDRESS = createField("f_link_address", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "url地址");

    /**
     * The column <code>system.t_advertisement_config_image.f_content</code>. 内容
     */
    public final TableField<AdvertisementConfigImageRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB, this, "内容");

    /**
     * The column <code>system.t_advertisement_config_image.f_business_type</code>.
     */
    public final TableField<AdvertisementConfigImageRecord, String> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "");

    /**
     * The column <code>system.t_advertisement_config_image.f_business_id</code>.
     */
    public final TableField<AdvertisementConfigImageRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "");

    /**
     * Create a <code>system.t_advertisement_config_image</code> table reference
     */
    public AdvertisementConfigImage() {
        this("t_advertisement_config_image", null);
    }

    /**
     * Create an aliased <code>system.t_advertisement_config_image</code> table reference
     */
    public AdvertisementConfigImage(String alias) {
        this(alias, ADVERTISEMENT_CONFIG_IMAGE);
    }

    private AdvertisementConfigImage(String alias, Table<AdvertisementConfigImageRecord> aliased) {
        this(alias, aliased, null);
    }

    private AdvertisementConfigImage(String alias, Table<AdvertisementConfigImageRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "APP广告页配置图片关联表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AdvertisementConfigImageRecord> getPrimaryKey() {
        return Keys.KEY_T_ADVERTISEMENT_CONFIG_IMAGE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AdvertisementConfigImageRecord>> getKeys() {
        return Arrays.<UniqueKey<AdvertisementConfigImageRecord>>asList(Keys.KEY_T_ADVERTISEMENT_CONFIG_IMAGE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AdvertisementConfigImage as(String alias) {
        return new AdvertisementConfigImage(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AdvertisementConfigImage rename(String name) {
        return new AdvertisementConfigImage(name, null);
    }
}
