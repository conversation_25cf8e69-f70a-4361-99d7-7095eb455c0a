/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.SiteRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Site extends TableImpl<SiteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_site</code>
     */
    public static final Site SITE = new Site();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SiteRecord> getRecordType() {
        return SiteRecord.class;
    }

    /**
     * The column <code>system.t_site.f_id</code>.
     */
    public final TableField<SiteRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>system.t_site.f_name</code>. 名称
     */
    public final TableField<SiteRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "名称");

    /**
     * The column <code>system.t_site.f_remark</code>. 备注
     */
    public final TableField<SiteRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(3000), this, "备注");

    /**
     * The column <code>system.t_site.f_domain</code>. 域名
     */
    public final TableField<SiteRecord, String> DOMAIN = createField("f_domain", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "域名");

    /**
     * The column <code>system.t_site.f_create_time</code>. 创建时间
     */
    public final TableField<SiteRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>system.t_site</code> table reference
     */
    public Site() {
        this("t_site", null);
    }

    /**
     * Create an aliased <code>system.t_site</code> table reference
     */
    public Site(String alias) {
        this(alias, SITE);
    }

    private Site(String alias, Table<SiteRecord> aliased) {
        this(alias, aliased, null);
    }

    private Site(String alias, Table<SiteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SiteRecord> getPrimaryKey() {
        return Keys.KEY_T_SITE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SiteRecord>> getKeys() {
        return Arrays.<UniqueKey<SiteRecord>>asList(Keys.KEY_T_SITE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Site as(String alias) {
        return new Site(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Site rename(String name) {
        return new Site(name, null);
    }
}
