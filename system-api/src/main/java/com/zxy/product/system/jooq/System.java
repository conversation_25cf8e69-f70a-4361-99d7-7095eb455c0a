/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.system.jooq;


import com.zxy.product.system.jooq.tables.*;
import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

import javax.annotation.Generated;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class System extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system</code>
     */
    public static final System SYSTEM_SCHEMA = new System();

    /**
     * 活动推荐表
     */
    public final ActivityRecommend ACTIVITY_RECOMMEND = com.zxy.product.system.jooq.tables.ActivityRecommend.ACTIVITY_RECOMMEND;

    /**
     * 历史管理员名单
     */
    public final AdminMemberHistory ADMIN_MEMBER_HISTORY = com.zxy.product.system.jooq.tables.AdminMemberHistory.ADMIN_MEMBER_HISTORY;

    /**
     * 广告表
     */
    public final Advertisement ADVERTISEMENT = com.zxy.product.system.jooq.tables.Advertisement.ADVERTISEMENT;

    /**
     * APP广告页配置
     */
    public final AdvertisementConfig ADVERTISEMENT_CONFIG = com.zxy.product.system.jooq.tables.AdvertisementConfig.ADVERTISEMENT_CONFIG;

    /**
     * APP广告页配置图片关联表
     */
    public final AdvertisementConfigImage ADVERTISEMENT_CONFIG_IMAGE = com.zxy.product.system.jooq.tables.AdvertisementConfigImage.ADVERTISEMENT_CONFIG_IMAGE;

    /**
     * 运营公告
     */
    public final Announcement ANNOUNCEMENT = com.zxy.product.system.jooq.tables.Announcement.ANNOUNCEMENT;

    /**
     * 运营公告详情表
     */
    public final AnnouncementDetail ANNOUNCEMENT_DETAIL = com.zxy.product.system.jooq.tables.AnnouncementDetail.ANNOUNCEMENT_DETAIL;

    /**
     * APP规则配置表
     */
    public final AppRuleConfig APP_RULE_CONFIG = com.zxy.product.system.jooq.tables.AppRuleConfig.APP_RULE_CONFIG;

    /**
     * 受众项
     */
    public final AudienceItem AUDIENCE_ITEM = com.zxy.product.system.jooq.tables.AudienceItem.AUDIENCE_ITEM;

    /**
     * 受众人表
     */
    public final AudienceMember AUDIENCE_MEMBER = com.zxy.product.system.jooq.tables.AudienceMember.AUDIENCE_MEMBER;

    /**
     * 受众对象表
     */
    public final AudienceObject AUDIENCE_OBJECT = com.zxy.product.system.jooq.tables.AudienceObject.AUDIENCE_OBJECT;

    /**
     * 运营管理后台
     */
    public final BackgroundOperationManage BACKGROUND_OPERATION_MANAGE = com.zxy.product.system.jooq.tables.BackgroundOperationManage.BACKGROUND_OPERATION_MANAGE;

    /**
     * 言论黑名单
     */
    public final Blacklist BLACKLIST = com.zxy.product.system.jooq.tables.Blacklist.BLACKLIST;

    /**
     * 画布尺寸表
     */
    public final CanvasSize CANVAS_SIZE = com.zxy.product.system.jooq.tables.CanvasSize.CANVAS_SIZE;

    /**
     * 证书配置表
     */
    public final CertificateConfig CERTIFICATE_CONFIG = com.zxy.product.system.jooq.tables.CertificateConfig.CERTIFICATE_CONFIG;

    /**
     * 个人证书记录
     */
    public final CertificateRecord CERTIFICATE_RECORD = com.zxy.product.system.jooq.tables.CertificateRecord.CERTIFICATE_RECORD;

    /**
     * 证书模板表
     */
    public final CertificateTemplate CERTIFICATE_TEMPLATE = com.zxy.product.system.jooq.tables.CertificateTemplate.CERTIFICATE_TEMPLATE;

    /**
     * 用户收藏表
     */
    public final Collect COLLECT = com.zxy.product.system.jooq.tables.Collect.COLLECT;

    /**
     * 讨论区举报记录表
     */
    public final CommentAccuse COMMENT_ACCUSE = com.zxy.product.system.jooq.tables.CommentAccuse.COMMENT_ACCUSE;

    /**
     * The table <code>system.t_comment_info</code>.
     */
    public final CommentInfo COMMENT_INFO = com.zxy.product.system.jooq.tables.CommentInfo.COMMENT_INFO;

    /**
     * The table <code>system.t_comment_praise</code>.
     */
    public final CommentPraise COMMENT_PRAISE = com.zxy.product.system.jooq.tables.CommentPraise.COMMENT_PRAISE;

    /**
     * The table <code>system.t_comment_reply</code>.
     */
    public final CommentReply COMMENT_REPLY = com.zxy.product.system.jooq.tables.CommentReply.COMMENT_REPLY;

    /**
     * 虚拟空间上级分享的课程
     */
    public final CourseVirtualSpace COURSE_VIRTUAL_SPACE = com.zxy.product.system.jooq.tables.CourseVirtualSpace.COURSE_VIRTUAL_SPACE;

    /**
     * 封面栏目配置项表
     */
    public final CoverPattern COVER_PATTERN = com.zxy.product.system.jooq.tables.CoverPattern.COVER_PATTERN;

    /**
     * 驾驶舱配置表
     */
    public final DashboardConfig DASHBOARD_CONFIG = com.zxy.product.system.jooq.tables.DashboardConfig.DASHBOARD_CONFIG;

    /**
     * 热词管理-数据大屏
     */
    public final DashboardHotWord DASHBOARD_HOT_WORD = com.zxy.product.system.jooq.tables.DashboardHotWord.DASHBOARD_HOT_WORD;

    /**
     * 热词管理-数据大屏(近一年)
     */
    public final DashboardHotWordYear DASHBOARD_HOT_WORD_YEAR = com.zxy.product.system.jooq.tables.DashboardHotWordYear.DASHBOARD_HOT_WORD_YEAR;

    /**
     * 数据权限配置
     */
    public final DataPermission DATA_PERMISSION = com.zxy.product.system.jooq.tables.DataPermission.DATA_PERMISSION;

    /**
     * The table <code>system.t_data_security_config</code>.
     */
    public final DataSecurityConfig DATA_SECURITY_CONFIG = com.zxy.product.system.jooq.tables.DataSecurityConfig.DATA_SECURITY_CONFIG;

    /**
     * The table <code>system.t_delete_data_system</code>.
     */
    public final DeleteDataSystem DELETE_DATA_SYSTEM = com.zxy.product.system.jooq.tables.DeleteDataSystem.DELETE_DATA_SYSTEM;

    /**
     * 反馈表
     */
    public final Feedback FEEDBACK = com.zxy.product.system.jooq.tables.Feedback.FEEDBACK;

    /**
     * 反馈附件表
     */
    public final FeedbackAttachment FEEDBACK_ATTACHMENT = com.zxy.product.system.jooq.tables.FeedbackAttachment.FEEDBACK_ATTACHMENT;

    /**
     * The table <code>system.t_grant</code>.
     */
    public final Grant GRANT = com.zxy.product.system.jooq.tables.Grant.GRANT;

    /**
     * The table <code>system.t_grant_detail</code>.
     */
    public final GrantDetail GRANT_DETAIL = com.zxy.product.system.jooq.tables.GrantDetail.GRANT_DETAIL;

    /**
     * 单个人独立的授权
     */
    public final GrantDetailCustom GRANT_DETAIL_CUSTOM = com.zxy.product.system.jooq.tables.GrantDetailCustom.GRANT_DETAIL_CUSTOM;

    /**
     * 授权记录
     */
    public final GrantHistory GRANT_HISTORY = com.zxy.product.system.jooq.tables.GrantHistory.GRANT_HISTORY;

    /**
     * The table <code>system.t_grant_member</code>.
     */
    public final GrantMember GRANT_MEMBER = com.zxy.product.system.jooq.tables.GrantMember.GRANT_MEMBER;

    /**
     * The table <code>system.t_grant_organization</code>.
     */
    public final GrantOrganization GRANT_ORGANIZATION = com.zxy.product.system.jooq.tables.GrantOrganization.GRANT_ORGANIZATION;

    /**
     * 首页广告表
     */
    public final HomeAdvertisement HOME_ADVERTISEMENT = com.zxy.product.system.jooq.tables.HomeAdvertisement.HOME_ADVERTISEMENT;

    /**
     * The table <code>system.t_home_config</code>.
     */
    public final HomeConfig HOME_CONFIG = com.zxy.product.system.jooq.tables.HomeConfig.HOME_CONFIG;

    /**
     * 首页动态内容展示
     */
    public final HomeContent HOME_CONTENT = com.zxy.product.system.jooq.tables.HomeContent.HOME_CONTENT;

    /**
     * 首页动态内容图片关联表
     */
    public final HomeContentImage HOME_CONTENT_IMAGE = com.zxy.product.system.jooq.tables.HomeContentImage.HOME_CONTENT_IMAGE;

    /**
     * 首页动态内容展示-支持树形数据
     */
    public final HomeContentTree HOME_CONTENT_TREE = com.zxy.product.system.jooq.tables.HomeContentTree.HOME_CONTENT_TREE;

    /**
     * The table <code>system.t_home_footer</code>.
     */
    public final HomeFooter HOME_FOOTER = com.zxy.product.system.jooq.tables.HomeFooter.HOME_FOOTER;

    /**
     * The table <code>system.t_home_module</code>.
     */
    public final HomeModule HOME_MODULE = com.zxy.product.system.jooq.tables.HomeModule.HOME_MODULE;

    /**
     * The table <code>system.t_home_module_config</code>.
     */
    public final HomeModuleConfig HOME_MODULE_CONFIG = com.zxy.product.system.jooq.tables.HomeModuleConfig.HOME_MODULE_CONFIG;

    /**
     * The table <code>system.t_home_nav</code>.
     */
    public final HomeNav HOME_NAV = com.zxy.product.system.jooq.tables.HomeNav.HOME_NAV;

    /**
     * 首页新闻资讯表
     */
    public final HomeNews HOME_NEWS = com.zxy.product.system.jooq.tables.HomeNews.HOME_NEWS;

    /**
     * The table <code>system.t_home_person_panel</code>.
     */
    public final HomePersonPanel HOME_PERSON_PANEL = com.zxy.product.system.jooq.tables.HomePersonPanel.HOME_PERSON_PANEL;

    /**
     * 积分规则维度表
     */
    public final IntegralAspect INTEGRAL_ASPECT = com.zxy.product.system.jooq.tables.IntegralAspect.INTEGRAL_ASPECT;

    /**
     * 用户积分详情表
     */
    public final IntegralDetail INTEGRAL_DETAIL = com.zxy.product.system.jooq.tables.IntegralDetail.INTEGRAL_DETAIL;

    /**
     * 积分等级配置表
     */
    public final IntegralGrade INTEGRAL_GRADE = com.zxy.product.system.jooq.tables.IntegralGrade.INTEGRAL_GRADE;

    /**
     * 用户积分结果表
     */
    public final IntegralResult INTEGRAL_RESULT = com.zxy.product.system.jooq.tables.IntegralResult.INTEGRAL_RESULT;

    /**
     * 积分规则配置表
     */
    public final IntegralRule INTEGRAL_RULE = com.zxy.product.system.jooq.tables.IntegralRule.INTEGRAL_RULE;

    /**
     * 智能配图日志表
     */
    public final IntelligentCoverLog INTELLIGENT_COVER_LOG = com.zxy.product.system.jooq.tables.IntelligentCoverLog.INTELLIGENT_COVER_LOG;

    /**
     * 内部开关
     */
    public final InternalSwitch INTERNAL_SWITCH = com.zxy.product.system.jooq.tables.InternalSwitch.INTERNAL_SWITCH;

    /**
     * 虚拟空间链接表
     */
    public final LinkVirtualSpace LINK_VIRTUAL_SPACE = com.zxy.product.system.jooq.tables.LinkVirtualSpace.LINK_VIRTUAL_SPACE;

    /**
     * 素材表
     */
    public final Material MATERIAL = com.zxy.product.system.jooq.tables.Material.MATERIAL;

    /**
     * The table <code>system.t_member</code>.
     */
    public final Member MEMBER = com.zxy.product.system.jooq.tables.Member.MEMBER;

    /**
     * The table <code>system.t_menu</code>.
     */
    public final Menu MENU = com.zxy.product.system.jooq.tables.Menu.MENU;

    /**
     * APP推送记录表
     */
    public final MessageAppRecord MESSAGE_APP_RECORD = com.zxy.product.system.jooq.tables.MessageAppRecord.MESSAGE_APP_RECORD;

    /**
     * @我消息通知
     */
    public final MessageAtMe MESSAGE_AT_ME = com.zxy.product.system.jooq.tables.MessageAtMe.MESSAGE_AT_ME;

    /**
     * 邮件发送记录表
     */
    public final MessageEmailRecord MESSAGE_EMAIL_RECORD = com.zxy.product.system.jooq.tables.MessageEmailRecord.MESSAGE_EMAIL_RECORD;

    /**
     * 站内信发送记录表
     */
    public final MessageNoticeRecord MESSAGE_NOTICE_RECORD = com.zxy.product.system.jooq.tables.MessageNoticeRecord.MESSAGE_NOTICE_RECORD;

    /**
     * 消息发送记录表
     */
    public final MessageRecord MESSAGE_RECORD = com.zxy.product.system.jooq.tables.MessageRecord.MESSAGE_RECORD;

    /**
     * 短信发送记录表
     */
    public final MessageSmsRecord MESSAGE_SMS_RECORD = com.zxy.product.system.jooq.tables.MessageSmsRecord.MESSAGE_SMS_RECORD;

    /**
     * 消息模板表
     */
    public final MessageTemplate MESSAGE_TEMPLATE = com.zxy.product.system.jooq.tables.MessageTemplate.MESSAGE_TEMPLATE;

    /**
     * The table <code>system.t_module_group</code>.
     */
    public final ModuleGroup MODULE_GROUP = com.zxy.product.system.jooq.tables.ModuleGroup.MODULE_GROUP;

    /**
     * The table <code>system.t_module_item</code>.
     */
    public final ModuleItem MODULE_ITEM = com.zxy.product.system.jooq.tables.ModuleItem.MODULE_ITEM;

    /**
     * 消息推送表
     */
    public final Msg MSG = com.zxy.product.system.jooq.tables.Msg.MSG;

    /**
     * 员工消息数量统计
     */
    public final MsgCount MSG_COUNT = com.zxy.product.system.jooq.tables.MsgCount.MSG_COUNT;

    /**
     * 员工消息数量项
     */
    public final MsgCountItem MSG_COUNT_ITEM = com.zxy.product.system.jooq.tables.MsgCountItem.MSG_COUNT_ITEM;

    /**
     * 消息推送详情
     */
    public final MsgDetail MSG_DETAIL = com.zxy.product.system.jooq.tables.MsgDetail.MSG_DETAIL;

    /**
     * 消息阅读表
     */
    public final MsgReader MSG_READER = com.zxy.product.system.jooq.tables.MsgReader.MSG_READER;

    /**
     * 新闻表
     */
    public final News NEWS = com.zxy.product.system.jooq.tables.News.NEWS;

    /**
     * 新闻类别配置
     */
    public final NewsCategory NEWS_CATEGORY = com.zxy.product.system.jooq.tables.NewsCategory.NEWS_CATEGORY;

    /**
     * The table <code>system.t_organization</code>.
     */
    public final Organization ORGANIZATION = com.zxy.product.system.jooq.tables.Organization.ORGANIZATION;

    /**
     * The table <code>system.t_organization_detail</code>.
     */
    public final OrganizationDetail ORGANIZATION_DETAIL = com.zxy.product.system.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * 网大-人力资源省公司映射表
     */
    public final OrganizationHrMapping ORGANIZATION_HR_MAPPING = com.zxy.product.system.jooq.tables.OrganizationHrMapping.ORGANIZATION_HR_MAPPING;

    /**
     * 页面方案配置表
     */
    public final PagePlanConfig PAGE_PLAN_CONFIG = com.zxy.product.system.jooq.tables.PagePlanConfig.PAGE_PLAN_CONFIG;

    /**
     * 方案和页面关联表
     */
    public final PageRelation PAGE_RELATION = com.zxy.product.system.jooq.tables.PageRelation.PAGE_RELATION;

    /**
     * 党组织（原样同步党建云数据）
     */
    public final PartyOrganization PARTY_ORGANIZATION = com.zxy.product.system.jooq.tables.PartyOrganization.PARTY_ORGANIZATION;

    /**
     * The table <code>system.t_person_center</code>.
     */
    public final PersonCenter PERSON_CENTER = com.zxy.product.system.jooq.tables.PersonCenter.PERSON_CENTER;

    /**
     * 图片分类表
     */
    public final PictureCategory PICTURE_CATEGORY = com.zxy.product.system.jooq.tables.PictureCategory.PICTURE_CATEGORY;

    /**
     * The table <code>system.t_point_adjustment</code>.
     */
    public final PointAdjustment POINT_ADJUSTMENT = com.zxy.product.system.jooq.tables.PointAdjustment.POINT_ADJUSTMENT;

    /**
     * 积分轮播配置表
     */
    public final PointBanner POINT_BANNER = com.zxy.product.system.jooq.tables.PointBanner.POINT_BANNER;

    /**
     * 每日任务表
     */
    public final PointDailyQuest POINT_DAILY_QUEST = com.zxy.product.system.jooq.tables.PointDailyQuest.POINT_DAILY_QUEST;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_1 POINT_DETAIL_1 = com.zxy.product.system.jooq.tables.PointDetail_1.POINT_DETAIL_1;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_10 POINT_DETAIL_10 = com.zxy.product.system.jooq.tables.PointDetail_10.POINT_DETAIL_10;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_2 POINT_DETAIL_2 = com.zxy.product.system.jooq.tables.PointDetail_2.POINT_DETAIL_2;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_3 POINT_DETAIL_3 = com.zxy.product.system.jooq.tables.PointDetail_3.POINT_DETAIL_3;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_4 POINT_DETAIL_4 = com.zxy.product.system.jooq.tables.PointDetail_4.POINT_DETAIL_4;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_5 POINT_DETAIL_5 = com.zxy.product.system.jooq.tables.PointDetail_5.POINT_DETAIL_5;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_6 POINT_DETAIL_6 = com.zxy.product.system.jooq.tables.PointDetail_6.POINT_DETAIL_6;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_7 POINT_DETAIL_7 = com.zxy.product.system.jooq.tables.PointDetail_7.POINT_DETAIL_7;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_8 POINT_DETAIL_8 = com.zxy.product.system.jooq.tables.PointDetail_8.POINT_DETAIL_8;

    /**
     * 用户积分详情流水表
     */
    public final PointDetail_9 POINT_DETAIL_9 = com.zxy.product.system.jooq.tables.PointDetail_9.POINT_DETAIL_9;

    /**
     * 积分等级配置表
     */
    public final PointGrade POINT_GRADE = com.zxy.product.system.jooq.tables.PointGrade.POINT_GRADE;

    /**
     * 历史任务表
     */
    public final PointHistoryQuest POINT_HISTORY_QUEST = com.zxy.product.system.jooq.tables.PointHistoryQuest.POINT_HISTORY_QUEST;

    /**
     * 积分消费明细表
     */
    public final PointOrderDetail POINT_ORDER_DETAIL = com.zxy.product.system.jooq.tables.PointOrderDetail.POINT_ORDER_DETAIL;

    /**
     * 用户积分结果表
     */
    public final PointResult POINT_RESULT = com.zxy.product.system.jooq.tables.PointResult.POINT_RESULT;

    /**
     * 积分规则配置表
     */
    public final PointRule POINT_RULE = com.zxy.product.system.jooq.tables.PointRule.POINT_RULE;

    /**
     * 月度积分收入支出表
     */
    public final PointTotalYear POINT_TOTAL_YEAR = com.zxy.product.system.jooq.tables.PointTotalYear.POINT_TOTAL_YEAR;

    /**
     * The table <code>system.t_promotional_popup</code>.
     */
    public final PromotionalPopup PROMOTIONAL_POPUP = com.zxy.product.system.jooq.tables.PromotionalPopup.PROMOTIONAL_POPUP;

    /**
     * The table <code>system.t_role</code>.
     */
    public final Role ROLE = com.zxy.product.system.jooq.tables.Role.ROLE;

    /**
     * The table <code>system.t_role_menu</code>.
     */
    public final RoleMenu ROLE_MENU = com.zxy.product.system.jooq.tables.RoleMenu.ROLE_MENU;

    /**
     * The table <code>system.t_rule_config</code>.
     */
    public final RuleConfig RULE_CONFIG = com.zxy.product.system.jooq.tables.RuleConfig.RULE_CONFIG;

    /**
     * 红船附加审核范围配置表
     */
    public final RuleRedShipConfig RULE_RED_SHIP_CONFIG = com.zxy.product.system.jooq.tables.RuleRedShipConfig.RULE_RED_SHIP_CONFIG;

    /**
     * 防录屏黑名单表
     */
    public final ScreenshotGuardBlackList SCREENSHOT_GUARD_BLACK_LIST = com.zxy.product.system.jooq.tables.ScreenshotGuardBlackList.SCREENSHOT_GUARD_BLACK_LIST;

    /**
     * 防录屏白名单表
     */
    public final ScreenshotGuardWhiteList SCREENSHOT_GUARD_WHITE_LIST = com.zxy.product.system.jooq.tables.ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST;

    /**
     * 敏感词表
     */
    public final Sensitive SENSITIVE = com.zxy.product.system.jooq.tables.Sensitive.SENSITIVE;

    /**
     * 敏感词操作记录表
     */
    public final SensitiveLog SENSITIVE_LOG = com.zxy.product.system.jooq.tables.SensitiveLog.SENSITIVE_LOG;

    /**
     * The table <code>system.t_setting</code>.
     */
    public final Setting SETTING = com.zxy.product.system.jooq.tables.Setting.SETTING;

    /**
     * 分享模板表
     */
    public final Share SHARE = com.zxy.product.system.jooq.tables.Share.SHARE;

    /**
     * The table <code>system.t_site</code>.
     */
    public final Site SITE = com.zxy.product.system.jooq.tables.Site.SITE;

    /**
     * 首页样式配置
     */
    public final SkinConfig SKIN_CONFIG = com.zxy.product.system.jooq.tables.SkinConfig.SKIN_CONFIG;

    /**
     * 首页样式皮肤包
     */
    public final SkinPackage SKIN_PACKAGE = com.zxy.product.system.jooq.tables.SkinPackage.SKIN_PACKAGE;

    /**
     * 言论审核表
     */
    public final SpeechAudit SPEECH_AUDIT = com.zxy.product.system.jooq.tables.SpeechAudit.SPEECH_AUDIT;

    /**
     * 言论控制表
     */
    public final SpeechSet SPEECH_SET = com.zxy.product.system.jooq.tables.SpeechSet.SPEECH_SET;

    /**
     * 样式表
     */
    public final Style STYLE = com.zxy.product.system.jooq.tables.Style.STYLE;

    /**
     * The table <code>system.t_topic</code>.
     */
    public final Topic TOPIC = com.zxy.product.system.jooq.tables.Topic.TOPIC;

    /**
     * The table <code>system.t_topic_approval</code>.
     */
    public final TopicApproval TOPIC_APPROVAL = com.zxy.product.system.jooq.tables.TopicApproval.TOPIC_APPROVAL;

    /**
     * The table <code>system.t_topic_expert</code>.
     */
    public final TopicExpert TOPIC_EXPERT = com.zxy.product.system.jooq.tables.TopicExpert.TOPIC_EXPERT;

    /**
     * The table <code>system.t_topic_manager</code>.
     */
    public final TopicManager TOPIC_MANAGER = com.zxy.product.system.jooq.tables.TopicManager.TOPIC_MANAGER;

    /**
     * The table <code>system.t_topic_object</code>.
     */
    public final TopicObject TOPIC_OBJECT = com.zxy.product.system.jooq.tables.TopicObject.TOPIC_OBJECT;

    /**
     * 话题排行榜
     */
    public final TopicRank TOPIC_RANK = com.zxy.product.system.jooq.tables.TopicRank.TOPIC_RANK;

    /**
     * 话题关联表
     */
    public final TopicRelation TOPIC_RELATION = com.zxy.product.system.jooq.tables.TopicRelation.TOPIC_RELATION;

    /**
     * The table <code>system.t_topic_statistics</code>.
     */
    public final TopicStatistics TOPIC_STATISTICS = com.zxy.product.system.jooq.tables.TopicStatistics.TOPIC_STATISTICS;

    /**
     * The table <code>system.t_topic_type</code>.
     */
    public final TopicType TOPIC_TYPE = com.zxy.product.system.jooq.tables.TopicType.TOPIC_TYPE;

    /**
     * The table <code>system.t_user_behavior</code>.
     */
    public final UserBehavior USER_BEHAVIOR = com.zxy.product.system.jooq.tables.UserBehavior.USER_BEHAVIOR;

    /**
     * 虚拟空间分组
     */
    public final VirtualSpaceGrouping VIRTUAL_SPACE_GROUPING = com.zxy.product.system.jooq.tables.VirtualSpaceGrouping.VIRTUAL_SPACE_GROUPING;

    /**
     * 虚拟空间分组详情
     */
    public final VirtualSpaceGroupingDetails VIRTUAL_SPACE_GROUPING_DETAILS = com.zxy.product.system.jooq.tables.VirtualSpaceGroupingDetails.VIRTUAL_SPACE_GROUPING_DETAILS;


    /**
     * 虚拟空间-最近访问
     */
    public final RecentVisits RECENT_VISITS = com.zxy.product.system.jooq.tables.RecentVisits.RECENT_VISITS;

    /**
     * 金库账号映射表
     */
    public final VaultAuthAccountMapping VAULT_AUTH_ACCOUNT_MAPPING = com.zxy.product.system.jooq.tables.VaultAuthAccountMapping.VAULT_AUTH_ACCOUNT_MAPPING;

    /**
     * 组织主管设置表
     */
    public final OrganizationSupervisor ORGANIZATION_SUPERVISOR = com.zxy.product.system.jooq.tables.OrganizationSupervisor.ORGANIZATION_SUPERVISOR;

    /**
     * 注册待办表
     */
    public final RegisterTodo REGISTER_TODO = com.zxy.product.system.jooq.tables.RegisterTodo.REGISTER_TODO;


    /**
     * 首页相关CDN文件地址
     */
    public final HomeCdn HOME_CDN = com.zxy.product.system.jooq.tables.HomeCdn.HOME_CDN;


    /**
     * 首页认证中心
     */
    public final HomeCertify HOME_CERTIFY = com.zxy.product.system.jooq.tables.HomeCertify.HOME_CERTIFY;


    /**
     * 专题素材和banner素材关联表
     */
    public final MaterialBannerSubjectRelation MATERIAL_BANNER_SUBJECT_RELATION = com.zxy.product.system.jooq.tables.MaterialBannerSubjectRelation.MATERIAL_BANNER_SUBJECT_RELATION;

    /**
     * 首页专家工作室配置表
     */
    public final HomeStudio HOME_STUDIO = com.zxy.product.system.jooq.tables.HomeStudio.HOME_STUDIO;

    /**
     * 系统banner行为统计
     */
    public final SystemBannerCountDay SYSTEM_BANNER_COUNT_DAY = com.zxy.product.system.jooq.tables.SystemBannerCountDay.SYSTEM_BANNER_COUNT_DAY;

    /**
     * 系统banner行为收集表
     */
    public final SystemBannerLog SYSTEM_BANNER_LOG = com.zxy.product.system.jooq.tables.SystemBannerLog.SYSTEM_BANNER_LOG;

    /**
     * 系统组件行为统计
     */
    public final SystemComponentCountDay SYSTEM_COMPONENT_COUNT_DAY = com.zxy.product.system.jooq.tables.SystemComponentCountDay.SYSTEM_COMPONENT_COUNT_DAY;

    /**
     * 系统组件行为收集表
     */
    public final SystemComponentLog SYSTEM_COMPONENT_LOG = com.zxy.product.system.jooq.tables.SystemComponentLog.SYSTEM_COMPONENT_LOG;

    /**
     * 首页互动交流配置表
     */
    public final HomeInteractiveCommunication HOME_INTERACTIVE_COMMUNICATION = com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION;

    /**
     * No further instances allowed
     */
    private System() {
        super("system", null);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        List result = new ArrayList();
        result.addAll(getTables0());
        return result;
    }

    private final List<Table<?>> getTables0() {
        return Arrays.<Table<?>>asList(
                ActivityRecommend.ACTIVITY_RECOMMEND,
                AdminMemberHistory.ADMIN_MEMBER_HISTORY,
                Advertisement.ADVERTISEMENT,
                AdvertisementConfig.ADVERTISEMENT_CONFIG,
                AdvertisementConfigImage.ADVERTISEMENT_CONFIG_IMAGE,
                Announcement.ANNOUNCEMENT,
                AnnouncementDetail.ANNOUNCEMENT_DETAIL,
                AppRuleConfig.APP_RULE_CONFIG,
                AudienceItem.AUDIENCE_ITEM,
                AudienceMember.AUDIENCE_MEMBER,
                AudienceObject.AUDIENCE_OBJECT,
                BackgroundOperationManage.BACKGROUND_OPERATION_MANAGE,
                Blacklist.BLACKLIST,
                CanvasSize.CANVAS_SIZE,
                CertificateConfig.CERTIFICATE_CONFIG,
                CertificateRecord.CERTIFICATE_RECORD,
                CertificateTemplate.CERTIFICATE_TEMPLATE,
                Collect.COLLECT,
                CommentAccuse.COMMENT_ACCUSE,
                CommentInfo.COMMENT_INFO,
                CommentPraise.COMMENT_PRAISE,
                CommentReply.COMMENT_REPLY,
                CourseVirtualSpace.COURSE_VIRTUAL_SPACE,
                CoverPattern.COVER_PATTERN,
                DashboardConfig.DASHBOARD_CONFIG,
                DashboardHotWord.DASHBOARD_HOT_WORD,
                DashboardHotWordYear.DASHBOARD_HOT_WORD_YEAR,
                DataPermission.DATA_PERMISSION,
                DataSecurityConfig.DATA_SECURITY_CONFIG,
                DeleteDataSystem.DELETE_DATA_SYSTEM,
                Feedback.FEEDBACK,
                FeedbackAttachment.FEEDBACK_ATTACHMENT,
                Grant.GRANT,
                GrantDetail.GRANT_DETAIL,
                GrantDetailCustom.GRANT_DETAIL_CUSTOM,
                GrantHistory.GRANT_HISTORY,
                GrantMember.GRANT_MEMBER,
                GrantOrganization.GRANT_ORGANIZATION,
                HomeAdvertisement.HOME_ADVERTISEMENT,
                HomeConfig.HOME_CONFIG,
                HomeContent.HOME_CONTENT,
                HomeContentImage.HOME_CONTENT_IMAGE,
                HomeContentTree.HOME_CONTENT_TREE,
                HomeFooter.HOME_FOOTER,
                HomeModule.HOME_MODULE,
                HomeModuleConfig.HOME_MODULE_CONFIG,
                HomeNav.HOME_NAV,
                HomeNews.HOME_NEWS,
                HomePersonPanel.HOME_PERSON_PANEL,
                IntegralAspect.INTEGRAL_ASPECT,
                IntegralDetail.INTEGRAL_DETAIL,
                IntegralGrade.INTEGRAL_GRADE,
                IntegralResult.INTEGRAL_RESULT,
                IntegralRule.INTEGRAL_RULE,
                IntelligentCoverLog.INTELLIGENT_COVER_LOG,
                InternalSwitch.INTERNAL_SWITCH,
                LinkVirtualSpace.LINK_VIRTUAL_SPACE,
                Material.MATERIAL,
                Member.MEMBER,
                Menu.MENU,
                MessageAppRecord.MESSAGE_APP_RECORD,
                MessageAtMe.MESSAGE_AT_ME,
                MessageEmailRecord.MESSAGE_EMAIL_RECORD,
                MessageNoticeRecord.MESSAGE_NOTICE_RECORD,
                MessageRecord.MESSAGE_RECORD,
                MessageSmsRecord.MESSAGE_SMS_RECORD,
                MessageTemplate.MESSAGE_TEMPLATE,
                ModuleGroup.MODULE_GROUP,
                ModuleItem.MODULE_ITEM,
                Msg.MSG,
                MsgCount.MSG_COUNT,
                MsgCountItem.MSG_COUNT_ITEM,
                MsgDetail.MSG_DETAIL,
                MsgReader.MSG_READER,
                News.NEWS,
                NewsCategory.NEWS_CATEGORY,
                Organization.ORGANIZATION,
                OrganizationDetail.ORGANIZATION_DETAIL,
                OrganizationHrMapping.ORGANIZATION_HR_MAPPING,
                PagePlanConfig.PAGE_PLAN_CONFIG,
                PageRelation.PAGE_RELATION,
                PartyOrganization.PARTY_ORGANIZATION,
                PersonCenter.PERSON_CENTER,
                PictureCategory.PICTURE_CATEGORY,
                PointAdjustment.POINT_ADJUSTMENT,
                PointBanner.POINT_BANNER,
                PointDailyQuest.POINT_DAILY_QUEST,
                PointDetail_1.POINT_DETAIL_1,
                PointDetail_10.POINT_DETAIL_10,
                PointDetail_2.POINT_DETAIL_2,
                PointDetail_3.POINT_DETAIL_3,
                PointDetail_4.POINT_DETAIL_4,
                PointDetail_5.POINT_DETAIL_5,
                PointDetail_6.POINT_DETAIL_6,
                PointDetail_7.POINT_DETAIL_7,
                PointDetail_8.POINT_DETAIL_8,
                PointDetail_9.POINT_DETAIL_9,
                PointGrade.POINT_GRADE,
                PointHistoryQuest.POINT_HISTORY_QUEST,
                PointOrderDetail.POINT_ORDER_DETAIL,
                PointResult.POINT_RESULT,
                PointRule.POINT_RULE,
                PointTotalYear.POINT_TOTAL_YEAR,
                PromotionalPopup.PROMOTIONAL_POPUP,
                Role.ROLE,
                RoleMenu.ROLE_MENU,
                RuleConfig.RULE_CONFIG,
                RuleRedShipConfig.RULE_RED_SHIP_CONFIG,
                ScreenshotGuardBlackList.SCREENSHOT_GUARD_BLACK_LIST,
                ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST,
                Sensitive.SENSITIVE,
                SensitiveLog.SENSITIVE_LOG,
                Setting.SETTING,
                Share.SHARE,
                Site.SITE,
                SkinConfig.SKIN_CONFIG,
                SkinPackage.SKIN_PACKAGE,
                SpeechAudit.SPEECH_AUDIT,
                SpeechSet.SPEECH_SET,
                Style.STYLE,
                Topic.TOPIC,
                TopicApproval.TOPIC_APPROVAL,
                TopicExpert.TOPIC_EXPERT,
                TopicManager.TOPIC_MANAGER,
                TopicObject.TOPIC_OBJECT,
                TopicRank.TOPIC_RANK,
                TopicRelation.TOPIC_RELATION,
                TopicStatistics.TOPIC_STATISTICS,
                TopicType.TOPIC_TYPE,
                UserBehavior.USER_BEHAVIOR,
                RecentVisits.RECENT_VISITS,
                VirtualSpaceGrouping.VIRTUAL_SPACE_GROUPING,
                VirtualSpaceGroupingDetails.VIRTUAL_SPACE_GROUPING_DETAILS,
                VaultAuthAccountMapping.VAULT_AUTH_ACCOUNT_MAPPING,
                OrganizationSupervisor.ORGANIZATION_SUPERVISOR,
                RegisterTodo.REGISTER_TODO,
                HomeCdn.HOME_CDN,
                ConfigCdn.CONFIG_CDN,
                ActivityRecommend.ACTIVITY_RECOMMEND,
                PagePlanConfig.PAGE_PLAN_CONFIG,
                PageRelation.PAGE_RELATION,
                Advertisement.ADVERTISEMENT,
                AdvertisementConfig.ADVERTISEMENT_CONFIG,
                AdvertisementConfigImage.ADVERTISEMENT_CONFIG_IMAGE,
                Announcement.ANNOUNCEMENT,
                AnnouncementDetail.ANNOUNCEMENT_DETAIL,
                AudienceItem.AUDIENCE_ITEM,
                AudienceMember.AUDIENCE_MEMBER,
                AudienceObject.AUDIENCE_OBJECT,
                CertificateConfig.CERTIFICATE_CONFIG,
                CertificateRecord.CERTIFICATE_RECORD,
                CertificateTemplate.CERTIFICATE_TEMPLATE,
                Collect.COLLECT,
                CommentAccuse.COMMENT_ACCUSE,
                CommentInfo.COMMENT_INFO,
                CommentPraise.COMMENT_PRAISE,
                CommentReply.COMMENT_REPLY,
                DataPermission.DATA_PERMISSION,
                DataSecurityConfig.DATA_SECURITY_CONFIG,
                DashboardHotWord.DASHBOARD_HOT_WORD,
                Feedback.FEEDBACK,
                FeedbackAttachment.FEEDBACK_ATTACHMENT,
                Grant.GRANT,
                GrantDetail.GRANT_DETAIL,
                GrantMember.GRANT_MEMBER,
                GrantOrganization.GRANT_ORGANIZATION,
                HomeAdvertisement.HOME_ADVERTISEMENT,
                HomeConfig.HOME_CONFIG,
                HomeContent.HOME_CONTENT,
                HomeContentTree.HOME_CONTENT_TREE,
                HomeContentImage.HOME_CONTENT_IMAGE,
                HomeFooter.HOME_FOOTER,
                HomeModule.HOME_MODULE,
                HomeModuleConfig.HOME_MODULE_CONFIG,
                HomeNav.HOME_NAV,
                HomeNews.HOME_NEWS,
                HomePersonPanel.HOME_PERSON_PANEL,
                IntegralAspect.INTEGRAL_ASPECT,
                IntegralDetail.INTEGRAL_DETAIL,
                IntegralGrade.INTEGRAL_GRADE,
                IntegralResult.INTEGRAL_RESULT,
                IntegralRule.INTEGRAL_RULE,
                Member.MEMBER,
                Menu.MENU,
                MessageAppRecord.MESSAGE_APP_RECORD,
                MessageAtMe.MESSAGE_AT_ME,
                MessageEmailRecord.MESSAGE_EMAIL_RECORD,
                MessageNoticeRecord.MESSAGE_NOTICE_RECORD,
                MessageRecord.MESSAGE_RECORD,
                MessageSmsRecord.MESSAGE_SMS_RECORD,
                MessageTemplate.MESSAGE_TEMPLATE,
                ModuleGroup.MODULE_GROUP,
                ModuleItem.MODULE_ITEM,
                Msg.MSG,
                MsgCount.MSG_COUNT,
                MsgCountItem.MSG_COUNT_ITEM,
                MsgDetail.MSG_DETAIL,
                MsgReader.MSG_READER,
                News.NEWS,
                NewsCategory.NEWS_CATEGORY,
                Organization.ORGANIZATION,
                OrganizationDetail.ORGANIZATION_DETAIL,
                OrganizationHrMapping.ORGANIZATION_HR_MAPPING,
                PartyOrganization.PARTY_ORGANIZATION,
                PersonCenter.PERSON_CENTER,
                Role.ROLE,
                RoleMenu.ROLE_MENU,
                RuleConfig.RULE_CONFIG,
                Sensitive.SENSITIVE,
                SensitiveLog.SENSITIVE_LOG,
                Setting.SETTING,
                Share.SHARE,
                Site.SITE,
                SkinConfig.SKIN_CONFIG,
                SkinPackage.SKIN_PACKAGE,
                SpeechAudit.SPEECH_AUDIT,
                SpeechSet.SPEECH_SET,
                Topic.TOPIC,
                TopicApproval.TOPIC_APPROVAL,
                TopicExpert.TOPIC_EXPERT,
                TopicManager.TOPIC_MANAGER,
                TopicObject.TOPIC_OBJECT,
                TopicRank.TOPIC_RANK,
                TopicStatistics.TOPIC_STATISTICS,
                TopicType.TOPIC_TYPE,
                ScreenshotGuardBlackList.SCREENSHOT_GUARD_BLACK_LIST,
                ScreenshotGuardWhiteList.SCREENSHOT_GUARD_WHITE_LIST,
                Blacklist.BLACKLIST,
                AppRuleConfig.APP_RULE_CONFIG,
                UserBehavior.USER_BEHAVIOR,
                BackgroundOperationManage.BACKGROUND_OPERATION_MANAGE,
                CourseVirtualSpace.COURSE_VIRTUAL_SPACE,
                DeleteDataSystem.DELETE_DATA_SYSTEM,
                InternalSwitch.INTERNAL_SWITCH,
                PromotionalPopup.PROMOTIONAL_POPUP,
                CanvasSize.CANVAS_SIZE,
                CoverPattern.COVER_PATTERN,
                Material.MATERIAL,
                PictureCategory.PICTURE_CATEGORY,
                Style.STYLE,
                DashboardHotWordYear.DASHBOARD_HOT_WORD_YEAR,
                PointBanner.POINT_BANNER,
                PointDailyQuest.POINT_DAILY_QUEST,
                PointDetail_1.POINT_DETAIL_1,
                PointDetail_10.POINT_DETAIL_10,
                PointDetail_2.POINT_DETAIL_2,
                PointDetail_3.POINT_DETAIL_3,
                PointDetail_4.POINT_DETAIL_4,
                PointDetail_5.POINT_DETAIL_5,
                PointDetail_6.POINT_DETAIL_6,
                PointDetail_7.POINT_DETAIL_7,
                PointDetail_8.POINT_DETAIL_8,
                PointDetail_9.POINT_DETAIL_9,
                PointGrade.POINT_GRADE,
                PointResult.POINT_RESULT,
                PointRule.POINT_RULE,
                PointTotalYear.POINT_TOTAL_YEAR,
                PointOrderDetail.POINT_ORDER_DETAIL,
                PointAdjustment.POINT_ADJUSTMENT,
                PointHistoryQuest.POINT_HISTORY_QUEST,
                RuleRedShipConfig.RULE_RED_SHIP_CONFIG,
                HomeCdn.HOME_CDN,
                ConfigCdn.CONFIG_CDN,
                HomeCertify.HOME_CERTIFY,
                MaterialBannerSubjectRelation.MATERIAL_BANNER_SUBJECT_RELATION,
                HomeStudio.HOME_STUDIO,
                SystemBannerCountDay.SYSTEM_BANNER_COUNT_DAY,
                SystemBannerLog.SYSTEM_BANNER_LOG,
                SystemComponentCountDay.SYSTEM_COMPONENT_COUNT_DAY,
                SystemComponentLog.SYSTEM_COMPONENT_LOG,
                HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION
        );


    }
}
