package com.zxy.product.system.entity;


import com.zxy.product.system.jooq.tables.pojos.OrganizationEntity;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class Organization extends OrganizationEntity {

	private static final long serialVersionUID = 4661050558792921560L;

	public static final Integer LEVEL_GROUP = 1; // 管理
	public static final Integer LEVEL_HEAD = 2; // 公司
	public static final Integer LEVEL_BRANCH = 3; // 分公司
	public static final Integer LEVEL_DEPARTMENT = 4; // 部门

	public static final Integer TYPE_INNER = 1; // 内部组织
	public static final Integer TYPE_OUTER = 2; // 外部组织
	public static final Integer TYPE_ALL = 3; // 所有组织

	public static final Integer STATUS_ENABLED = 1;
	public static final Integer STATUS_DISABLED = 2;

	public static final Integer CHILD_FIND_NO = 0;
	public static final Integer CHILD_FIND_YES = 1;
	public static final String CHILD_FIND_YES_STR = "1";
	public static final String CHILD_FIND_NO_STR = "0";

	public static final int ADD = 1; // 新增
	public static final int UPDATE = 0; // 修改
	public static final int DELETE = -1; // 删除

	public static final String ROOT_ORGANIZATION = "1";
	public static final String INTERNAL_ORGANIZATION = "10000001";
	public static final String INCLUDE_KEY = "includeKey";
	public static final String NOT_INCLUDE_KEY = "notIncludeKey";

	public static final Integer DEPTH_ONE = 1; // 第一级
	public static final Integer DEPTH_TWO = 2; // 第二级
	public static final Integer DEPTH_THREE = 3; // 第三级

	/**
	 * 1=有虚拟空间
	 */
	public static final int VIRTUAL_STATUS_OK=1;

	public static final Integer STATUS_DISABLE = 1; // 禁用
	public static final Integer STATUS_ENBELE = 0; // 启用
    private Integer index; // 序号,用于生成excel时产生序号

	private String childFind; //1 勾选发现 2 半选择状态
	private String organizationName; //所属机构名称
	private String parentName;
	private Integer parentLevel;
	private String companyName;
	private boolean isParent;
	private String parentCode;
	private String syncItemOrganizationId;
	private String ihrItemId;	// ihr同步的itemId,用于回传同步状态

	/**当前组织Id对应的首页版本*/
	private String version;

	public String getVersion() { return version; }

	public void setVersion(String version) { this.version = version; }

	/**
	 * 1=有虚拟空间
	 */
	private Integer virtualStatus;

	private String operatorTypes;
	private List<OrganizationSupervisor> organizationSupervisors;


	public List<OrganizationSupervisor> getOrganizationSupervisors() {
		return organizationSupervisors;
	}

	public void setOrganizationSupervisors(List<OrganizationSupervisor> organizationSupervisors) {
		this.organizationSupervisors = organizationSupervisors;
	}



	public String getOperatorTypes() {
		return operatorTypes;
	}

	public void setOperatorTypes(String operatorTypes) {
		this.operatorTypes = operatorTypes;
	}

	public Integer getVirtualStatus() {
		return virtualStatus;
	}

	public void setVirtualStatus(Integer virtualStatus) {
		this.virtualStatus = virtualStatus;
	}

	public String getIhrItemId() {
		return ihrItemId;
	}

	public void setIhrItemId(String ihrItemId) {
		this.ihrItemId = ihrItemId;
	}

	public String getSyncItemOrganizationId() {
		return syncItemOrganizationId;
	}

	public void setSyncItemOrganizationId(String syncItemOrganizationId) {
		this.syncItemOrganizationId = syncItemOrganizationId;
	}

	private List<Organization> children;



	public boolean getIsParent() {
		return isParent;
	}

	public void setIsParent(boolean isParent) {
		this.isParent = isParent;
	}

	public String getChildFind() {
		return childFind;
	}

	public void setChildFind(String childFind) {
		this.childFind = childFind;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getParentName() {
		return parentName;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public Integer getParentLevel() {
		return parentLevel;
	}

	public void setParentLevel(Integer parentLevel) {
		this.parentLevel = parentLevel;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getParentCode() {
		return parentCode;
	}

	public void setParentCode(String parentCode) {
		this.parentCode = parentCode;
	}

	@Override
	public int hashCode() {
		if (this.getId() == null) {
			return 0;
		}
		final int prime = 31;
		int result = 1;
		result = prime * result + getId().hashCode();
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		if (getId() == null) {
			return false;
		}
		Organization other = (Organization) obj;
		if (!getId().equals(other.getId())) {
			return false;
		}
		return true;
	}

	public List<Organization> getChildren() {
		return children;
	}

	public void setChildren(List<Organization> children) {
		this.children = children;
	}

/*
	public Integer sordeByLevel(){
		return super.getLevel();
    }*/



}
