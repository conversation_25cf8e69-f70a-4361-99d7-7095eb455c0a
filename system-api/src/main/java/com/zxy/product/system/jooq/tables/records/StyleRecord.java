/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.records;


import com.zxy.product.system.jooq.tables.Style;
import com.zxy.product.system.jooq.tables.interfaces.IStyle;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record16;
import org.jooq.Row16;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 样式表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StyleRecord extends UpdatableRecordImpl<StyleRecord> implements Record16<String, String, String, Integer, Integer, String, String, Integer, String, String, String, String, Long, Timestamp, Integer, String>, IStyle {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>system.t_style.f_id</code>. 主键id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>system.t_style.f_id</code>. 主键id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>system.t_style.f_code</code>. 样式编码
     */
    @Override
    public void setCode(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>system.t_style.f_code</code>. 样式编码
     */
    @Override
    public String getCode() {
        return (String) get(1);
    }

    /**
     * Setter for <code>system.t_style.f_name</code>. 名称
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>system.t_style.f_name</code>. 名称
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>system.t_style.f_title_min</code>. 标题最小长度
     */
    @Override
    public void setTitleMin(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>system.t_style.f_title_min</code>. 标题最小长度
     */
    @Override
    public Integer getTitleMin() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>system.t_style.f_title_max</code>. 标题最大长度
     */
    @Override
    public void setTitleMax(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>system.t_style.f_title_max</code>. 标题最大长度
     */
    @Override
    public Integer getTitleMax() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>system.t_style.f_size_id</code>. 关联尺寸id
     */
    @Override
    public void setSizeId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>system.t_style.f_size_id</code>. 关联尺寸id
     */
    @Override
    public String getSizeId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>system.t_style.f_content</code>. 内容
     */
    @Override
    public void setContent(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>system.t_style.f_content</code>. 内容
     */
    @Override
    public String getContent() {
        return (String) get(6);
    }

    /**
     * Setter for <code>system.t_style.f_material_num</code>. 素材数量
     */
    @Override
    public void setMaterialNum(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>system.t_style.f_material_num</code>. 素材数量
     */
    @Override
    public Integer getMaterialNum() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>system.t_style.f_default_attachment_id</code>. 默认附件id
     */
    @Override
    public void setDefaultAttachmentId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>system.t_style.f_default_attachment_id</code>. 默认附件id
     */
    @Override
    public String getDefaultAttachmentId() {
        return (String) get(8);
    }

    /**
     * Setter for <code>system.t_style.f_default_path</code>. 默认附件路径
     */
    @Override
    public void setDefaultPath(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>system.t_style.f_default_path</code>. 默认附件路径
     */
    @Override
    public String getDefaultPath() {
        return (String) get(9);
    }

    /**
     * Setter for <code>system.t_style.f_organization_id</code>. 组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>system.t_style.f_organization_id</code>. 组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(10);
    }

    /**
     * Setter for <code>system.t_style.f_create_member_id</code>. 提交人
     */
    @Override
    public void setCreateMemberId(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>system.t_style.f_create_member_id</code>. 提交人
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(11);
    }

    /**
     * Setter for <code>system.t_style.f_create_time</code>. 提交时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>system.t_style.f_create_time</code>. 提交时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>system.t_style.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(13, value);
    }

    /**
     * Getter for <code>system.t_style.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(13);
    }

    /**
     * Setter for <code>system.t_style.f_delete_flag</code>. 0正常,1删除
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>system.t_style.f_delete_flag</code>. 0正常,1删除
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>system.t_style.f_company_id</code>. 企业id
     */
    @Override
    public void setCompanyId(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>system.t_style.f_company_id</code>. 企业id
     */
    @Override
    public String getCompanyId() {
        return (String) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record16 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row16<String, String, String, Integer, Integer, String, String, Integer, String, String, String, String, Long, Timestamp, Integer, String> fieldsRow() {
        return (Row16) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row16<String, String, String, Integer, Integer, String, String, Integer, String, String, String, String, Long, Timestamp, Integer, String> valuesRow() {
        return (Row16) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return Style.STYLE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return Style.STYLE.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return Style.STYLE.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return Style.STYLE.TITLE_MIN;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return Style.STYLE.TITLE_MAX;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return Style.STYLE.SIZE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return Style.STYLE.CONTENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return Style.STYLE.MATERIAL_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return Style.STYLE.DEFAULT_ATTACHMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return Style.STYLE.DEFAULT_PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return Style.STYLE.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return Style.STYLE.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field13() {
        return Style.STYLE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field14() {
        return Style.STYLE.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field15() {
        return Style.STYLE.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field16() {
        return Style.STYLE.COMPANY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getTitleMin();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getTitleMax();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getSizeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getContent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getMaterialNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getDefaultAttachmentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getDefaultPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value13() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value14() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value15() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value16() {
        return getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value2(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value3(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value4(Integer value) {
        setTitleMin(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value5(Integer value) {
        setTitleMax(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value6(String value) {
        setSizeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value7(String value) {
        setContent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value8(Integer value) {
        setMaterialNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value9(String value) {
        setDefaultAttachmentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value10(String value) {
        setDefaultPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value11(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value12(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value13(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value14(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value15(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord value16(String value) {
        setCompanyId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StyleRecord values(String value1, String value2, String value3, Integer value4, Integer value5, String value6, String value7, Integer value8, String value9, String value10, String value11, String value12, Long value13, Timestamp value14, Integer value15, String value16) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStyle from) {
        setId(from.getId());
        setCode(from.getCode());
        setName(from.getName());
        setTitleMin(from.getTitleMin());
        setTitleMax(from.getTitleMax());
        setSizeId(from.getSizeId());
        setContent(from.getContent());
        setMaterialNum(from.getMaterialNum());
        setDefaultAttachmentId(from.getDefaultAttachmentId());
        setDefaultPath(from.getDefaultPath());
        setOrganizationId(from.getOrganizationId());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setDeleteFlag(from.getDeleteFlag());
        setCompanyId(from.getCompanyId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStyle> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StyleRecord
     */
    public StyleRecord() {
        super(Style.STYLE);
    }

    /**
     * Create a detached, initialised StyleRecord
     */
    public StyleRecord(String id, String code, String name, Integer titleMin, Integer titleMax, String sizeId, String content, Integer materialNum, String defaultAttachmentId, String defaultPath, String organizationId, String createMemberId, Long createTime, Timestamp modifyDate, Integer deleteFlag, String companyId) {
        super(Style.STYLE);

        set(0, id);
        set(1, code);
        set(2, name);
        set(3, titleMin);
        set(4, titleMax);
        set(5, sizeId);
        set(6, content);
        set(7, materialNum);
        set(8, defaultAttachmentId);
        set(9, defaultPath);
        set(10, organizationId);
        set(11, createMemberId);
        set(12, createTime);
        set(13, modifyDate);
        set(14, deleteFlag);
        set(15, companyId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.system.jooq.tables.pojos.StyleEntity)) {
            return false;
        }
        com.zxy.product.system.jooq.tables.pojos.StyleEntity pojo = (com.zxy.product.system.jooq.tables.pojos.StyleEntity)source;
        pojo.into(this);
        return true;
    }
}
