package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.AppRuleConfigEntity;

/**
 * <AUTHOR>
 */
public class AppRuleConfig extends AppRuleConfigEntity {
    private static final long serialVersionUID = 5678687674719905942L;

    public static final Integer STATUS_ENABLED = 1;
    /**
     * 开启
     */
    public static final String STATUS_OPEN = "1";
    /**
     * 关闭
     */
    public static final String STATUS_CLOSE = "0";

    /**
     * 默认状态
     */
    public static final Integer DEFAULT_VALUE = 1;

    public enum KEY {
        /**
         * 默认音频播放
         */
        AUDIO_PLAYBACK,
        /**
         * APP0元20G免流量ico
         */
        MENU_20G_ICON,
        /**
         * 智能推荐开关
         */
        INTELLIGENT_RECOMMENDATION,
        /**
         * 息屏播放
         */
        OFF_SCREEN_PLAYBACK

    }

}
