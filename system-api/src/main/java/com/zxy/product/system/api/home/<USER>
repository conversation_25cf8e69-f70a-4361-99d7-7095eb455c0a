package com.zxy.product.system.api.home;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.system.domain.vo.CustomizeVO;
import com.zxy.product.system.domain.vo.RuleConfigVO;
import com.zxy.product.system.domain.vo.home.Interactive.InteractiveParentVO;
import com.zxy.product.system.domain.vo.home.big.banner.BigBannerParentVO;
import com.zxy.product.system.domain.vo.home.category.course.CategoryCourseParentVO;
import com.zxy.product.system.domain.vo.home.course.navigation.CourseNavigationParentVO;
import com.zxy.product.system.domain.vo.home.customize.CustomizeParentVO;
import com.zxy.product.system.domain.vo.home.featured.content.FeaturedContentParentVO;
import com.zxy.product.system.domain.vo.home.mini.banner.MiniBannerParentVO;
import com.zxy.product.system.domain.vo.home.news.NewsParentVO;
import com.zxy.product.system.entity.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 十万——重写首页系统Service
 * <AUTHOR>
 * @date 2024年07月31日 11:30
 */
@RemoteService
public  interface HomeSystemService {

    /**
     * 查询首页配置项信息
     * @param cfgId 首页主配置Id
     * @param clientType 客户端类型
     * @return 首页配置项信息
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<HomeModuleConfig> homeModuleCfg(String cfgId, Integer clientType);

    /**
     * 首页：新闻资讯
     * @param homeCfgId 首页新闻资讯配置Id
     * @param categoryIdOpt 分类id
     * @param stateOpt 状态 0 未发布 1 已发布
     * @param clientType 客户端类型 0全部 1PC 2App
     * @return 首页新闻资讯对象VO
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends NewsParentVO>  newsCfg(String homeCfgId, Optional<String> categoryIdOpt,
                                          Optional<Integer> stateOpt, Integer clientType);

    /**
     * 首页：课程导航
     * @param homeCfgId 首页课程导航配置Id
     * @param typeOpt 类型
     * @param clientType 客户端类型 1PC 2App 0全部
     * @return 首页课程导航对象VO
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends CourseNavigationParentVO> courseNavigationCfg(String homeCfgId, Optional<String> typeOpt, Integer clientType);

    /**
     * 首页：互动学习
     * @param cfgId 首页互动学习配置Id
     * @param size 从DB获取的数据长度
     * @param clientType 客户端类型 1 PC 2 App 0 全部
     * @param dataTypeOpt 数据类型
     * @return 首页互动学习对象VO
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends InteractiveParentVO>interactionCfg(String cfgId, Integer size, Integer clientType,
                                                      Optional<Integer> dataTypeOpt);

    /**
     * 首页：BIG_BANNER（新）
     * @param homeCfgId 首页BIG_BANNER配置Id
     * @param clientType 客户端类型
     * @param sizeOpt 从DB获取的数据长度
     * @return 首页BIG_BANNER（新）对象VO
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends BigBannerParentVO> bigBannerCfg(String homeCfgId, Integer clientType,
                                                   Optional<Integer> sizeOpt);

    /**
     * 首页：精选内容
     * @param cfgId 首页精选内容配置Id
     * @param size 从DB获取的数据长度
     * @param clientType 客户端类型
     * @return 首页精选内容对象VO
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends FeaturedContentParentVO> featuredContentCfg(String cfgId, Integer size, Integer clientType);

    /**
     * 首页：分类课程
     * @param cfgId 首页分类课程模块配置Id
     * @param typeOpt 类型
     * @param clientType 客户端类型 1 PC 2 App 0 全部
     * @return 首页分类课程对象VO
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends CategoryCourseParentVO> categoryCourseCfg(String cfgId, Optional<String> typeOpt,Integer clientType);

    /**
     * 首页：MIMI_BANNER（新）
     * @param cfgId 首页MIMI_BANNER（新）配置Id
     * @param clientType 客户端类型
     * @param sizeOpt 从DB获取的数据长度
     * @return 首页MIMI_BANNER（新）对象VO
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends MiniBannerParentVO> miniBannerCfg(String cfgId, Integer clientType,
                                                      Optional<Integer> sizeOpt);


    /**
     * 首页：自定义
     * @param cfgId 首页自定义配置Id
     * @param size 从DB获取的数据长度
     * @param clientType 客户端类型
     * @return 首页自定义对象VO
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends CustomizeParentVO> customizeCfg(String cfgId, Integer size, Integer clientType);

    /**
     * 自定义属性查询（精选内容 && 自定义模块 的图片地址）
     * @param cfgId 首页配置项主Id
     * @param clientType 客户端类型 1PC 2App
     * @return 查询自定义数据集合 /find-by-ids接口的数据返回值
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String,List<CustomizeVO>> customizeCourseSelect(String cfgId, Integer clientType);

    /**
     * 自定义属性查询（资源图片地址）
     * @param cfgId 首页配置项主Id
     * @param clientType 客户端类型 1PC 2App
     * @return 查询自定义数据集合 /find-by-ids接口的数据返回值
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String,List<CustomizeVO>> customizePathSelect(String cfgId,Integer clientType);

    /**
     * 查询系统配置数据集合
     * @return 系统配置数据集合
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<RuleConfigVO> ruleConfigCollect();

    /**
     * 根据配置项key实时查询数据库系统配置项
     * @param key 查询系统配置项的KEY
     * @return 出参的RULE_CONFIG对象
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    RuleConfig singleRuleConfig(String key);

    /**
     * 查询分类课程
     */
    @Transactional(readOnly = true)
    List<String> specialClassifiedCoursesSelect(String cfgId);

    /**
     * 查询分类课程模块Id
     * * @param cfgId
     */
    @Transactional(readOnly = true)
    String doClassifiedCourses(String cfgId);
}
