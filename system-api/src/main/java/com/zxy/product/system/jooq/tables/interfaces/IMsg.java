/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 消息推送表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMsg extends Serializable {

    /**
     * Setter for <code>system.t_msg.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_msg.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>system.t_msg.f_title</code>. 消息标题
     */
    public void setTitle(String value);

    /**
     * Getter for <code>system.t_msg.f_title</code>. 消息标题
     */
    public String getTitle();

    /**
     * Setter for <code>system.t_msg.f_content</code>. 内容
     */
    public void setContent(String value);

    /**
     * Getter for <code>system.t_msg.f_content</code>. 内容
     */
    public String getContent();

    /**
     * Setter for <code>system.t_msg.f_url</code>. 链接地址
     */
    public void setUrl(String value);

    /**
     * Getter for <code>system.t_msg.f_url</code>. 链接地址
     */
    public String getUrl();

    /**
     * Setter for <code>system.t_msg.f_text_content</code>. 内容纯文本
     */
    public void setTextContent(String value);

    /**
     * Getter for <code>system.t_msg.f_text_content</code>. 内容纯文本
     */
    public String getTextContent();

    /**
     * Setter for <code>system.t_msg.f_type</code>. 消息类型 1站内消息 2邮件 3APP 4短信
     */
    public void setType(Integer value);

    /**
     * Getter for <code>system.t_msg.f_type</code>. 消息类型 1站内消息 2邮件 3APP 4短信
     */
    public Integer getType();

    /**
     * Setter for <code>system.t_msg.f_status</code>. 状态：0草稿(默认) 1发布
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>system.t_msg.f_status</code>. 状态：0草稿(默认) 1发布
     */
    public Integer getStatus();

    /**
     * Setter for <code>system.t_msg.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>system.t_msg.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>system.t_msg.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>system.t_msg.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>system.t_msg.f_member_id</code>. 创建人id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>system.t_msg.f_member_id</code>. 创建人id
     */
    public String getMemberId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMsg
     */
    public void from(com.zxy.product.system.jooq.tables.interfaces.IMsg from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMsg
     */
    public <E extends com.zxy.product.system.jooq.tables.interfaces.IMsg> E into(E into);
}
