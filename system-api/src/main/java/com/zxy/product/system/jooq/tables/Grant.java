/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.GrantRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Grant extends TableImpl<GrantRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_grant</code>
     */
    public static final Grant GRANT = new Grant();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GrantRecord> getRecordType() {
        return GrantRecord.class;
    }

    /**
     * The column <code>system.t_grant.f_id</code>. ID
     */
    public final TableField<GrantRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>system.t_grant.f_organization_id</code>. 组织ID
     */
    public final TableField<GrantRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织ID");

    /**
     * The column <code>system.t_grant.f_operator_types</code>. 操作类型
     */
    public final TableField<GrantRecord, String> OPERATOR_TYPES = createField("f_operator_types", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "操作类型 ");

    /**
     * The column <code>system.t_grant.f_role_id</code>. 角色ID
     */
    public final TableField<GrantRecord, String> ROLE_ID = createField("f_role_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "角色ID");

    /**
     * The column <code>system.t_grant.f_create_member_id</code>. 创建人id
     */
    public final TableField<GrantRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人id");

    /**
     * The column <code>system.t_grant.f_create_time</code>. 创建时间
     */
    public final TableField<GrantRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>system.t_grant.f_valid_type</code>. 授权有效期类型 0三天 1一个月 2三个月 3半年 4一年 5自定义 6永久有效
     */
    public final TableField<GrantRecord, Integer> VALID_TYPE = createField("f_valid_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("6", org.jooq.impl.SQLDataType.INTEGER)), this, "授权有效期类型 0三天 1一个月 2三个月 3半年 4一年 5自定义 6永久有效");

    /**
     * The column <code>system.t_grant.f_valid_date</code>. 授权有效期
     */
    public final TableField<GrantRecord, Long> VALID_DATE = createField("f_valid_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "授权有效期");

    /**
     * The column <code>system.t_grant.f_notify_flag</code>. 到期提醒 0否 1是
     */
    public final TableField<GrantRecord, Integer> NOTIFY_FLAG = createField("f_notify_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "到期提醒 0否 1是");

    /**
     * The column <code>system.t_grant.f_notify_type</code>. 提醒类型 1站内消息 2邮件 3APP 4短信
     */
    public final TableField<GrantRecord, String> NOTIFY_TYPE = createField("f_notify_type", org.jooq.impl.SQLDataType.VARCHAR(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "提醒类型 1站内消息 2邮件 3APP 4短信");

    /**
     * The column <code>system.t_grant.f_revoke_notify_flag</code>. 撤销提醒 0未提醒 1已提醒
     */
    public final TableField<GrantRecord, Integer> REVOKE_NOTIFY_FLAG = createField("f_revoke_notify_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "撤销提醒 0未提醒 1已提醒");

    /**
     * Create a <code>system.t_grant</code> table reference
     */
    public Grant() {
        this("t_grant", null);
    }

    /**
     * Create an aliased <code>system.t_grant</code> table reference
     */
    public Grant(String alias) {
        this(alias, GRANT);
    }

    private Grant(String alias, Table<GrantRecord> aliased) {
        this(alias, aliased, null);
    }

    private Grant(String alias, Table<GrantRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    @Override
    public UniqueKey<GrantRecord> getPrimaryKey() {
        return Keys.KEY_T_GRANT_PRIMARY;
    }

    @Override
    public List<UniqueKey<GrantRecord>> getKeys() {
        return Arrays.<UniqueKey<GrantRecord>>asList(Keys.KEY_T_GRANT_PRIMARY);
    }

    @Override
    public Grant as(String alias) {
        return new Grant(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Grant rename(String name) {
        return new Grant(name, null);
    }
}
