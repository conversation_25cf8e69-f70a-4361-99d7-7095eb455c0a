package com.zxy.product.system.api.permission;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.system.entity.Organization;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


@RemoteService(timeout = 300000)
public interface OrganizationService {
    // 组织菜单uri
    public static final String ORGANIZATION_URI = "human/organization";

    // 组织导出缓存key
    public static final String EXPORT_ORGANIZATIONS_CACHE_KEY = "exportOrganizationsCacheKey#";

    /** 添加组织 */
    @Transactional
    Organization insert(Optional<String> id, String name, Optional<String> shortName, String code, String parentId, Integer cmccLevel, Integer level, Optional<Integer> order, Optional<String> cmccAttribute, Optional<String> cmccCategory, Integer status, Optional<String> extentionParams, Optional<Integer> type,Optional<Long> vaildStartTime,Optional<Long> vaildEndTime,Optional<String> supervisorIds);

    /**
     * 批量添加组织
     * @param organizations 欲添加的组织,记得一定要把基础信息验证好
     * @param options 可选参数,为扩展预留,没有可填入Optional.empty()
     * @return
     */
    @Transactional
    Map<String, List<Organization>> batchInsert(List<Organization> organizations, String memberId, Optional<Map<String, Object>> options);

    /** mis数据 添加组织 */
    @Transactional
    Organization hrSyncInsert(Optional<String> id, String name, Optional<String> shortName, String code, String parentId, Integer cmccLevel, Integer level, Optional<Integer> order, Optional<String> cmccAttribute, Optional<String> cmccCategory, Integer status, Optional<String> extentionParams, Optional<String> misCode);

    /** 更新组织  level:节点级别 */
    @Transactional
    Organization update(String id, String name, Optional<String> shortName, String code, String parentId, Integer cmccLevel, Integer level, Optional<Integer> order, Optional<String> cmccAttribute, Optional<String> cmccCategory, Integer status, Optional<String> extentionParams,Optional<Long> vaildStartTime,Optional<Long> vaildEndTime,Optional<String> supervisorIds);

    /** mis数据 更新组织  */
    @Transactional
    Organization hrSyncUpdate(String id, String name, Optional<String> shortName, String code, String parentId, Integer cmccLevel, Integer level, Optional<Integer> order, Optional<String> cmccAttribute, Optional<String> cmccCategory, Integer status, Optional<String> extentionParams, String misCode);

    /** 根据id查询节点 返回包含是否是父组织字段 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Organization> getById(String id);

    /** 根据id查询节点 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Organization> get(String id);

    /** 获取用户被授权的组织(实时查询,不使用grant_detail和organization_detail) */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findOrgList(String memberId, String uri, Optional<Boolean> supportMore, Optional<Integer> level, Optional<Boolean> asParent, Optional<String> name, Optional<String> code, Optional<Integer> type, Optional<String> excludeId, Optional<String> organizationId, Optional<String> operatorType, Optional<Boolean> allStatus);

    /** 查询用户被授权的组织,使用grant_detail, ps:组织选择器使用 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findGrantedOrganization(String memberId, String uri, Optional<String> name, Optional<String> code, Optional<Integer> type, Optional<String> operatorType, Optional<Boolean> supportMore);

    /** 查询用户在一个菜单下,所有被授权的组织,硬查询,不使用grant_detail */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findMemberGrantedOrganization(String memberId, String uri, boolean nameComposite, Optional<Integer> depth, Optional<Integer> type);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findMemberGrantedOrganization(String memberId, String uri, boolean nameComposite, Optional<Integer> depth);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findMemberGrantedOrganization(String memberId, String uri, boolean nameComposite);

    /**
     * 只提供f_code和f_name字段
     * @param memberId
     * @param uri
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    public List<Organization> findMemberGrantedOrganization(String memberId, String uri, Optional<String> orgCode);

    /**
     * 缓存方式查询授权组织
     * @param memberId
     * @param uri
     * @param type
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    public List<Organization> findMemberGrantedOrganizationWithCache(String memberId, String uri, Optional<Integer> type);

    /**
     * 缓存方式查询授权组织NoReturn
     * @param memberId
     * @param uri
     * @param type
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    public void findMemberGrantedOrganizationWithCacheNoReturn(String memberId, String uri, Optional<Integer> type);

    /**
     * 缓存方式查询授权组织
     * @param memberId
     * @param uri
     * @param organizationId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    public List<Organization> findMemberGrantedOrganizationWithCache(String memberId, String uri, String organizationId);

    /**
     * 缓存方式查询授权组织NoReturn
     * @param memberId
     * @param uri
     * @param organizationId
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    public void findMemberGrantedOrganizationWithCacheNoReturn(String memberId, String uri, String organizationId);

    /** 根据id查询组织基本信息(没有连表查询) */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Organization> getBasic(String id);

    /** 根据id删除节点 */
    @Transactional
    int delete(String id);

    /** 查询该组织下的的数量 ,name:组织名称,code:组织代码 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    int count(String organizationId, Optional<String> name, Optional<String> code, Optional<String> id);

    /** 根据域名查询组织(从site->org) */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Organization> getByDomain(String domain);

    /** 获取organizationId父组织中最近的一个组织,level:级别 (传多个值表示或者关系) */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Organization getCompanyOrganization(String id, Integer... level);

    /** 根据组织编码查询组织 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Organization> getByCode(String code);

    /** 根据组织编码查询当前人的组织 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Organization> getByCode(String memberId, String code);

    /** 查询当前用户被授权过的所有组织 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> getByMember(String memberId);

    /**
     * 获取当前id下面最大的级别<br>
     * (比如知学云公司下有北京分公司、深圳分公司，北京分公司有研发部、测试部，如果传知学云id得到结果为分公司级别，如果传深圳分公司，则得到的结果为部门级别)
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Integer> getMaxLevelOrganizationById(String id);

    /** 判断system组织是否关联 , true代表关联*/
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    boolean judgeOrgReleated(String id);

    /** 查询当前用户整个公司的组织或者机构,默认查询机构级别,没有被禁用的
     * @param rootId
     * @param supportMore
     * @modifyDate 2017-11-14
     * @modifyUser chengzhi
     *
     * */
    List<Organization> findOrgsWithNoGrant(String rootId, Optional<Integer> depthLimit, Optional<String> organizationId, Optional<String> name,
                                           Optional<String> code, Optional<Boolean> isCompany, Optional<Boolean> isAll, Boolean supportMore,Optional<Integer> type);

    /** 查询当前人在该角色下的授权组织 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findOrgsByMemberIdAndRoleId(String memberId, String roleId);

    /** 查询当前组织的父组织 ,level:查询小于等于该级别的组织*/
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findParent(String id, Integer level);
    /**
     * 根据人查询当前所属集团ID
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Organization getCompanyOrganizationWithLevel2ByMemberId(String memberId);

    /** 根据MIS省公司简称查询组织，只用于同步数据 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<Organization> getByMisCode(String misCode, Integer depth);

    /** 判断当前用户,对于组织organizationId,是否拥有operator的操作权限 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    boolean checkGrants(int operator, String memberId, String organizationId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findExportOrganization(String memberId, Optional<String> name, Optional<String> code, Optional<Integer> level, Optional<Integer> type);

    /** 根据条件查询当前组织的父组织的数量(level:查询小于等于该级别的组织, status:父组织的状态)  */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    int countParent(String id, Optional<Integer> level, Optional<Integer> status);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findTemplateOrganization(String memberId);

    @Transactional
    boolean initOrganizationDepth();

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findChildren(String parentId, Optional<Boolean> onlyCompany);

    /**
     * 查询当前uri该用户的最大权限
     * @param memberId
     * @param uri
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Organization findMaxGrantOriganization(String memberId,String uri);

    /** 通过组织编码 和 misCode 获取该省内的组织    */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<com.zxy.product.system.entity.Organization> getByCodeAndMisCode(String organizationCode, String misCode);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    int countByMisCode(String rootOrganizationId, Optional<String> name, Optional<String> code, Optional<String> id, Optional<String> misCode);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> getByCodes(Collection<String> codes);

    /**
     * 根据组织id查询id对应的区号
     * @param orgIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, String> getAreasByOrgIds(List<String> orgIds);
    /**
     * 根据组织id查询id对应的区号
     * @param orgIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, String> getAreasByOrgIds(String... orgIds);


    /** mis 批量同步*/
    @Transactional
    void syncBatchInsert(List<Organization> organizations, String version);

    @Transactional
    void syncBatchUpdate(List<Organization> list, String version);

    /** mis 同步更新 */
    @Transactional
    Organization misUpdate(String id, String name, Optional<String> shortName, String code, String parentId, Integer status);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    public String getOrganizationIdWithDepth5(String orgId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findAndExportOrganization(String memberId, Optional<String> name, Optional<String> code, Optional<Integer> level, Optional<Integer> type);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findAndExportOrganizationInExam(String memberId, Optional<String> name, Optional<String> code, Optional<Integer> level, Optional<Integer> type);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<com.zxy.product.system.entity.Organization> findOrganizationByIds(List<String> parentIds);

    /**
     * 获取角色授权组织
     * @param currentUserId
     * @param uri
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findGrantOrgsByMemberIdAndUri(String currentUserId, String uri);

    /**
     * 由角色获取授权组织
     * @param memberId
     * @param roleId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findGrantOrgsByMemberIdAndroleId(String memberId, String roleId);

    /**
     * 培训计划  导出模板
     * @param memberId
     * @param uri
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findAllOrgsByMemberIdAndUri(String memberId, String uri);

    /**
     * 权限管理 新增和修改 选择授权组织
     * @param currentUserId 管理员id
     * @param roleId 角色id
     * @param supportMore 是否显示子组织
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findOrganizationByRoleId(String currentUserId, Optional<String> roleId,
                                                Optional<Boolean> supportMore, Map<String, Set<String>> grantOrganizationMap,
                                                Optional<String> name, Optional<String> code);

    /**
     * 组织管理导出组织
     * @param cacheKey 数据量太大，dubbo传输慢，使用redis存取，此为cacheKey
     * @param memberId
     * @param name
     * @param code
     * @param level
     * @param type
     * @param grantOrganizationMap
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    void exportOrganizationByGrantPath(String cacheKey, String memberId, Optional<String> name, Optional<String> code,
                                       Optional<Integer> level, Optional<Integer> type, Map<String, Set<String>> grantOrganizationMap);

    /**
     * 组织管理导出组织,获取父组织名称
     * @param memberId
     * @param name
     * @param code
     * @param level
     * @param type
     * @param grantOrganizationMap
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, String> findParentOrganizationName(String memberId, Optional<String> name, Optional<String> code,
                                                   Optional<Integer> level, Optional<Integer> type, Map<String, Set<String>> grantOrganizationMap);

    /**
     * 导入职务优化
     * @param grantOrganizationMap
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> findOrganizationByGrantPath(Map<String, Set<String>> grantOrganizationMap);


    /** ihr 批量新增*/
    @Transactional
    void ihrSyncBatchInsert(List<Organization> list, String fileName);

    /** ihr 批量修改*/
    @Transactional
    void ihrSyncBatchUpdate(List<Organization> list, String fileName, Integer updateType);

    /** ihr 批量禁用*/
    @Transactional
    void ihrSyncBatchDisable(List<Organization> list, String fileName, Integer updateType);

    /**
     * 人工智能---部门初始化数据
     * @param start
     * @param limit
     * @return
     */
    @Transactional(readOnly = true)
    List<String> getAllOrgIds(int start,int limit);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    void findMemberGrantedOrganizationNo(String memberId, String uri, boolean nameComposite);

    /**
     * 查询组织基本详情（批量）
     * @param ids
     * @return
     */
    @Transactional(readOnly = true)
    List<Organization> getOrganizationByIds(List<String> ids);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String getRootOrganizationId(String id);

    @Transactional(readOnly = true)
    List<Organization> findOrgListVirtualSpace(String currentUserId, String uri, Optional<Boolean> supportMore, Optional<Integer> level, Optional<Boolean> asParent, Optional<String> name, Optional<String> code, Optional<Integer> type, Optional<String> excludeId, Optional<Boolean> allStatus);


    /**
     * 通过指定id 获取子组织
     *
     * @param parentId
     * @return java.util.List<com.zxy.product.system.entity.Organization>
     * @throws
     * @method getOrganizationByParentId
     * <AUTHOR> Qian
     * @version 1.0
     * @date 2024/12/10 18:04
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> getOrganizationByParentId(String parentId,Integer depth);


    /**
     * 根据id获取组织信息
     *
     * @param id id
     * @return com.zxy.product.system.entity.Organization
     * @throws
     * @method get
     * <AUTHOR> Qian
     * @version 1.0
     * @date 2024/12/12 14:19
     */
    Organization getByPriId(String id);


    /***
     * 处理外部组织过期数据
     */
    @Transactional(rollbackFor = Exception.class)
    void handleOrganizationVaildDate();

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Organization> getByMemberOrganization(String memberId, String uri, boolean nameComposite, Optional<Integer> depth, Optional<Integer> type);
}
