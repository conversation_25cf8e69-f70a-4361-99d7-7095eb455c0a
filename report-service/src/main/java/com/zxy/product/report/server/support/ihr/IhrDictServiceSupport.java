package com.zxy.product.report.server.support.ihr;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.ihr.IhrDictService;
import com.zxy.product.report.entity.IhrDict;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.zxy.product.report.jooq.Tables.IHR_DICT;

@Service
public class IhrDictServiceSupport implements IhrDictService {

    private CommonDao<IhrDict> dao;

    @Autowired
    public void setDao(CommonDao<IhrDict> dao) {
        this.dao = dao;
    }

    @Override
    public Map<String, String> find(Integer key) {
        return dao.execute(x->x.select(IHR_DICT.CODE,IHR_DICT.VALUE).from(IHR_DICT)
                .where(IHR_DICT.KEY.eq(key))).fetchMap(IHR_DICT.CODE,IHR_DICT.VALUE);
    }
}
