package com.zxy.product.report.server.config;

import com.alibaba.fastjson.JSON;
import org.apache.tomcat.jdbc.pool.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Component
public class DataSourceMonitor {
    private static final Logger logger= LoggerFactory.getLogger(DataSourceMonitor.class);


    private DataSource dataSource;

    @Autowired
    public void setDataSource(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @PostConstruct
    public Map<String, Object> getPoolInfo() throws Exception {
        Map<String, Object> info = new HashMap<>();

        if (dataSource instanceof DataSource) {
            // Tomcat JDBC Pool
            DataSource tomcatDs = dataSource;
            info.put("type", "Tomcat JDBC Pool");
            info.put("active", tomcatDs.getActive());
            info.put("idle", tomcatDs.getIdle());
            info.put("maxActive", tomcatDs.getMaxActive());
            info.put("maxIdle", tomcatDs.getMaxIdle());
            info.put("initialSize", tomcatDs.getInitialSize());
        } else if (dataSource.getClass().getName().startsWith("com.zaxxer.hikari")) {
            // HikariCP
            Object hikariPool = invokeMethod(dataSource, "getHikariPoolMXBean");
            info.put("type", "HikariCP");
            info.put("active", invokeMethod(hikariPool, "getActiveConnections"));
            info.put("idle", invokeMethod(hikariPool, "getIdleConnections"));
            info.put("max", invokeMethod(hikariPool, "getMaximumPoolSize"));
            info.put("total", invokeMethod(hikariPool, "getTotalConnections"));
        } else if (dataSource.getClass().getName().contains("dbcp2")) {
            // Apache DBCP2
            info.put("type", "DBCP2");
            info.put("active", invokeMethod(dataSource, "getNumActive"));
            info.put("idle", invokeMethod(dataSource, "getNumIdle"));
            info.put("max", invokeMethod(dataSource, "getMaxTotal"));
        } else {
            throw new IllegalStateException("Unsupported DataSource type: " + dataSource.getClass());
        }
        logger.error("当前数据源信息{}", JSON.toJSONString(info));
        return info;
    }

    private Object invokeMethod(Object target, String methodName) throws Exception {
        Method method = target.getClass().getMethod(methodName);
        return method.invoke(target);
    }
}
