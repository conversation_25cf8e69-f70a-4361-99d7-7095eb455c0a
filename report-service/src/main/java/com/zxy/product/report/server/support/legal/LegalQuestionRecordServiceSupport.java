package com.zxy.product.report.server.support.legal;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.legal.LegalQuestionRecordService;
import com.zxy.product.report.content.ErrorCode;
import com.zxy.product.report.entity.LegalManager;
import com.zxy.product.report.entity.LegalQuestion;
import com.zxy.product.report.entity.LegalQuestionAttr;
import com.zxy.product.report.entity.LegalQuestionRecord;
import org.jooq.tools.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zxy.product.report.jooq.Tables.*;

/**
 * <AUTHOR>
 * @date 2019/12/26
 */
@Service
public class LegalQuestionRecordServiceSupport implements LegalQuestionRecordService {

    private CommonDao<LegalQuestionRecord> recordDao;
    private CommonDao<LegalManager> managerDao;
    private CommonDao<LegalQuestion> questionDao;
    private CommonDao<LegalQuestionAttr> attrDao;

    @Autowired
    public void setRecordDao(CommonDao<LegalQuestionRecord> recordDao) {
        this.recordDao = recordDao;
    }

    @Autowired
    public void setManagerDao(CommonDao<LegalManager> managerDao) {
        this.managerDao = managerDao;
    }

    @Autowired
    public void setQuestionDao(CommonDao<LegalQuestion> questionDao) {
        this.questionDao = questionDao;
    }

    @Autowired
    public void setAttrDao(CommonDao<LegalQuestionAttr> attrDao) {
        this.attrDao = attrDao;
    }

    @Override
    public List<LegalQuestionRecord> save(String memberId, List<LegalQuestionRecord> records) {

        // 获取管理员信息
        LegalManager legalManager = managerDao.fetchOne(LEGAL_MANAGER.MEMBER_ID.eq(memberId),
                LEGAL_MANAGER.COMPANY_TYPE.in(LegalManager.COMPANY_TYPE_MAJOR,LegalManager.COMPANY_TYPE_PROVINCE))
                .orElseThrow(()->new UnprocessableException(ErrorCode.LegalManagerNotExists));

        // 未开始和进行中才可以保存
        if (legalManager.getStatus() != null && legalManager.getStatus() > LegalManager.STATUS_ING) {
            throw new UnprocessableException(ErrorCode.LegalManagerStatusException);
        }

        // 获取问题ids
        Set<String> questionIds = records.stream().map(LegalQuestionRecord::getQuestionId).collect(Collectors.toSet());

        // 查找已存在的记录
        List<LegalQuestionRecord> existsRecords = recordDao.fetch(LEGAL_QUESTION_RECORD.MEMBER_ID.eq(memberId),
                LEGAL_QUESTION_RECORD.QUESTION_ID.in(questionIds));
        Set<String> existsQuestionId = existsRecords.stream().map(LegalQuestionRecord::getQuestionId).collect(Collectors.toSet());

        // 获取要插入的作答记录
        List<LegalQuestionRecord> insertRecords = records.stream().filter(r -> !existsQuestionId.contains(r.getQuestionId()))
                .collect(Collectors.toList());
        // 获取要更新的作答记录
        Map<String, LegalQuestionRecord> updateRecordMap = records.stream().filter(r -> existsQuestionId.contains(r.getQuestionId()))
                .collect(Collectors.toMap(LegalQuestionRecord::getQuestionId, r -> r, (r1, r2) -> r2));

        // 新增作答记录
        insertRecords.forEach(r -> {
            r.forInsert();
            r.setMemberId(memberId);
        });
        recordDao.insert(insertRecords);

        // 修改作答记录
        existsRecords.forEach(r -> {
            LegalQuestionRecord ur = updateRecordMap.get(r.getQuestionId());
            r.setAnswer(ur.getAnswer());
            r.setQuestionAttrId(ur.getQuestionAttrId());
            r.setDesc(ur.getDesc());
            r.setAttachmentId(ur.getAttachmentId());
            r.setAttachmentName(ur.getAttachmentName());
        });
        recordDao.update(existsRecords);

        // 更新管理员状态
        legalManager.setStatus(LegalManager.STATUS_ING);
        legalManager.setStartTime(System.currentTimeMillis());
        managerDao.update(legalManager);

        return records;
    }

    @Override
    public void compute(String memberId) {

        // 获取管理员信息
        LegalManager legalManager = managerDao.fetchOne(LEGAL_MANAGER.MEMBER_ID.eq(memberId),
                LEGAL_MANAGER.COMPANY_TYPE.in(LegalManager.COMPANY_TYPE_MAJOR,LegalManager.COMPANY_TYPE_PROVINCE))
                .orElseThrow(()->new UnprocessableException(ErrorCode.LegalManagerNotExists));

        // 查询所有题目
        List<LegalQuestion> questions = questionDao.fetch(LEGAL_QUESTION.COMPANY_TYPE.eq(legalManager.getCompanyType()));

        // 获取题目ids条件
        Set<String> questionIds = questions.stream().map(LegalQuestion::getId).collect(Collectors.toSet());

        // 查询当前用户的作答记录
        List<LegalQuestionRecord> records = recordDao.fetch(LEGAL_QUESTION_RECORD.QUESTION_ID.in(questionIds),LEGAL_QUESTION_RECORD.MEMBER_ID.eq(memberId));

        // 已答题目数量不等于题目数量，问卷未完成，questionId和memberId作唯一索引，不存在重复作答记录
        if (questions.size() != records.size()) {
            throw new UnprocessableException(ErrorCode.LegalQuestionNotCompleted);
        }

        // 获取选项及对应分数
 /*        Map<String, Integer> attrMap = attrDao.fetch(LEGAL_QUESTION_ATTR.QUESTION_ID.in(questionIds))
                .stream().collect(Collectors.toMap(LegalQuestionAttr::getId, LegalQuestionAttr::getScore)); */

        // 拿作答记录和选项对比,计算分数 // TODO
        double totalScore = 0;
        for (LegalQuestionRecord record : records) {
            /* if (!StringUtils.isEmpty(record.getQuestionAttrId())) {
                Integer score = MapUtils.getInteger(attrMap, record.getQuestionAttrId(), 0);
                record.setScore(score);
                totalScore += score;
            } */
            // CMU-6558 2023年法治移动评价体系:保存分数的时候暂时保存在answer中 为了在提交的时候再计算分数,而不是直接放到score中
            double score = Double.parseDouble(Optional.ofNullable(record.getAnswer()).orElse("0"));
            record.setScore(score);
            totalScore += score;
        }
        // 更新作答记录的分数
        recordDao.update(records);

        // 更新管理员状态及总分
        legalManager.setSubmitTime(System.currentTimeMillis());
        legalManager.setScore(totalScore);
        legalManager.setStatus(LegalManager.STATUS_FINISH);
        managerDao.update(legalManager);
    }
}
