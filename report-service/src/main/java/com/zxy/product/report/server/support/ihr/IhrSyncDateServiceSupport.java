package com.zxy.product.report.server.support.ihr;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.ihr.IhrSyncDateService;
import com.zxy.product.report.entity.IhrSyncDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zxy.product.report.jooq.tables.IhrSyncDate.IHR_SYNC_DATE;

@Service
public class IhrSyncDateServiceSupport implements IhrSyncDateService {

	private CommonDao<IhrSyncDate> dao;

	@Autowired
	public void setDao(CommonDao<IhrSyncDate> dao) {
		this.dao = dao;
	}

	@Override
	public List<IhrSyncDate> findBySyncDate(String syncDate) {
		return dao.fetch(IHR_SYNC_DATE.SYNC_DATE.eq(syncDate));
	}

	@Override
	public IhrSyncDate insert(String syncDate) {
        IhrSyncDate ihrSyncDate = new IhrSyncDate();
        ihrSyncDate.forInsert();
        ihrSyncDate.setSyncDate(syncDate);
		return dao.insert(ihrSyncDate);
	}

}
