package com.zxy.product.report.server.support;
import static com.zxy.product.report.jooq.tables.ExamResult.EXAM_RESULT;
import static com.zxy.product.report.jooq.tables.Member.MEMBER;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.report.api.ExamResultService;
import com.zxy.product.report.entity.ExamResult;
import com.zxy.product.report.entity.Member;

@Service
public class ExamResultServiceSupport implements ExamResultService{
    private CommonDao<ExamResult> examResultDao;
    private CommonDao<Member> memberDao;

    @Autowired
    public void setExamResultDao(CommonDao<ExamResult> examResultDao) {
        this.examResultDao = examResultDao;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }


    @Override
    public List<ExamResult> findExamResult(String memberId) {
        String name = memberDao.execute(e ->
        e.select(MEMBER.NAME).from(MEMBER)
        .where(MEMBER.ID.eq(memberId)).fetchOne(MEMBER.NAME));
            List<ExamResult> examResultList = examResultDao.execute(x -> {
                return x.select(Fields.start()
                        .add(EXAM_RESULT)
                        .end())
                        .from(EXAM_RESULT)
                        .where(EXAM_RESULT.MEMBER_NAME.eq(name))
                        .orderBy(EXAM_RESULT.CREATE_TIME.desc())
                        .fetchInto(ExamResult.class);
            });
            return examResultList;
    }

}
