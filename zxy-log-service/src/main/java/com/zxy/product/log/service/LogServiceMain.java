package com.zxy.product.log.service;

import com.zxy.common.dao.spring.CommonDaoConfig;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.service.config.*;
import org.jooq.Schema;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 *
 */
//@SpringBootApplication
@Configuration
@ComponentScan
@EnableTransactionManagement
@Import({
        RpcConfig.class,
        RPCClientConfig.class,
        RPCServerConfig.class,
        CommonDaoConfig.class,
        MessageConfig.class,
        MongoConfig.class,
        TransactionConfig.class,
        RabbitAutoConfiguration.class,
        TransactionAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        JooqAutoConfiguration.class,
        CacheConfig.class
})
public class LogServiceMain {

    @Bean
    public Schema schema() {
        return ZxyLog.ZXY_LOG_SCHEMA; // jOOQ生成代码的根目录下与数据库同名的类
    }

    public static void main(String[] args) throws Exception {
        SpringApplication.run(LogServiceMain.class, args);
        synchronized (LogServiceMain.class) {
            LogServiceMain.class.wait();
        }
    }

}
