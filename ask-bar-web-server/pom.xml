<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zxy.product</groupId>
		<artifactId>ask-bar</artifactId>
		<version>cmu-9.6.0</version>
	</parent>
	<artifactId>ask-bar-web-server</artifactId>

	<dependencies>

		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>web-server-parent</artifactId>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>redis.clients</groupId>
					<artifactId>jedis</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.3</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>ask-bar-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>system-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>human-resource-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>course-study-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>org.csource</groupId>
			<artifactId>fastdfs-client-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>2.1.4</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>ooxml-schemas</artifactId>
			<version>1.4</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<dependencies>
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>springloaded</artifactId>
						<version>1.2.3.RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>
