package com.zxy.product.askbar.web.controller;

import com.zxy.product.system.content.SystemConstant;
import com.zxy.product.system.content.SwitchEnum;
import com.zxy.product.askbar.web.config.SwitchConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.askbar.api.TrendService;
import com.zxy.product.askbar.entity.Member;
import com.zxy.product.askbar.entity.Trend;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 动态
 * <AUTHOR>
 * @date 2017年8月10日 下午3:07:00
 *
 */
@Controller
@RequestMapping("/trend")
public class TrendController {

    private TrendService trendService;

    @Autowired
    public void setTrendService(TrendService trendService) {
        this.trendService = trendService;
    }

    /** 问吧首页-全部动态 */
    @RequestMapping(value="/trends-all", method = RequestMethod.GET)
    @Param(name = "start", type = Integer.class , required = true)
    @Param(name = "pageSize", type = Integer.class , required = true)
    @JSON("recordCount")
    @JSON("items.(id,businessId,businessType,questionId,discussId,createMemberId,createTime,rootOrganizationId)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)") // 创建人
    @JSON("items.question.(id,type,title,contentTxt,status,auditStatus,img,accuseStatus,essenceStatus,topStatus,browseNum,attentionNum,praiseNum,discussNum,shareNum,attentionNum,isCreator,isManager,createTime,lastModifyTime,shareTitle,shareObjectId,shareType)")// 内容：问题、文章、分享
    @JSON("items.question.questionTopics.topic.(id,name,typeId,group,isBarTopic)")// 内容关联话题
    @JSON("items.discuss.(id,type,contentText,auditStatus、,img,praiseNum,replyNum,isCreator,createTime)") // 讨论
    @JSON("items.attention.(id)") // 当前用户对内容的关注记录
    @JSON("items.praise.(id)") // 点赞记录
    public PagedResult<Trend> findTrendsAll(RequestContext requestContext, Subject<Member> subject) {

        //内部配置排序开关
        Boolean AskBarHomeOrderFlag = SwitchConfig.getSwitchStatus(SwitchEnum.AskBarHomeOrderSwitch);

        return trendService.findTrendsAll(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                subject.getCurrentUserId(),
                SystemConstant.ROOT_ORGANIZATION_ID,
                AskBarHomeOrderFlag);
    }
    /** 问吧首页-全部动态 */
    @RequestMapping(value="/trends-all-security", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class , required = true)
    @Param(name = "pageSize", type = Integer.class , required = true)
    @JSON("recordCount")
    @JSON("items.(id,businessId,businessType,questionId,discussId,createMemberId,createTime,rootOrganizationId)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)") // 创建人
    @JSON("items.question.(id,type,title,contentTxt,status,auditStatus,img,accuseStatus,essenceStatus,topStatus,browseNum,attentionNum,praiseNum,discussNum,shareNum,attentionNum,isCreator,isManager,createTime,lastModifyTime,shareTitle,shareObjectId,shareType)")// 内容：问题、文章、分享
    @JSON("items.question.questionTopics.topic.(id,name,typeId,group,isBarTopic)")// 内容关联话题
    @JSON("items.discuss.(id,type,contentText,auditStatus、,img,praiseNum,replyNum,isCreator,createTime)") // 讨论
    @JSON("items.attention.(id)") // 当前用户对内容的关注记录
    @JSON("items.praise.(id)") // 点赞记录
    public PagedResult<Trend> findSecurityTrendsAll(RequestContext requestContext, Subject<Member> subject) throws Exception {
        PagedResult<Trend> trendsAll = findTrendsAll(requestContext, subject);
        List<Trend> items = trendsAll.getItems();
        for (Trend item : items) {
            Member createMember = item.getCreateMember();
            if (Objects.nonNull(createMember)){
                createMember.setFullName(com.zxy.common.restful.util.Encrypt.aesEncrypt(Optional.ofNullable(createMember.getFullName()).orElse("")  ,"d8cg8gVakEq9Agup"));
            }
        }
        return PagedResult.create(trendsAll.getRecordCount(), items);
    }

    /** 问吧首页-与我相关 */
    @RequestMapping(value="/trends-mine", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class , required = true)
    @Param(name = "pageSize", type = Integer.class , required = true)
    @JSON("recordCount")
    @JSON("items.(id,businessId,businessType,questionId,discussId,createMemberId,createTime,rootOrganizationId)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)") // 创建人
    @JSON("items.question.(id,type,title,contentTxt,status,auditStatus,img,accuseStatus,essenceStatus,browseNum,attentionNum,praiseNum,discussNum,shareNum,attentionNum,isCreator,isManager,createTime,lastModifyTime,shareTitle,shareObjectId,shareType)")// 内容：问题、文章、分享
    @JSON("items.question.questionTopics.topic.(id,name,typeId,group,isBarTopic)")// 内容关联话题
    @JSON("items.discuss.(id,type,contentText,auditStatus,img,accuseStatus,praiseNum,replyNum,isCreator,createTime)") // 讨论
    @JSON("items.attention.(id)") // 当前用户对内容的关注记录
    @JSON("items.praise.(id)") // 点赞记录
    public PagedResult<Trend> findTrendsMine(RequestContext requestContext, Subject<Member> subject) {
        return trendService.findTrendsMine(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                subject.getCurrentUserId(),
                subject.getCurrentUser().getRootOrganization().getId());
    }

    /** 问吧首页-与我相关安全版本 */
    @RequestMapping(value="/trends-mine-security", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class , required = true)
    @Param(name = "pageSize", type = Integer.class , required = true)
    @JSON("recordCount")
    @JSON("items.(id,businessId,businessType,questionId,discussId,createMemberId,createTime,rootOrganizationId)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)") // 创建人
    @JSON("items.question.(id,type,title,contentTxt,status,auditStatus,img,accuseStatus,essenceStatus,browseNum,attentionNum,praiseNum,discussNum,shareNum,attentionNum,isCreator,isManager,createTime,lastModifyTime,shareTitle,shareObjectId,shareType)")// 内容：问题、文章、分享
    @JSON("items.question.questionTopics.topic.(id,name,typeId,group,isBarTopic)")// 内容关联话题
    @JSON("items.discuss.(id,type,contentText,auditStatus,img,accuseStatus,praiseNum,replyNum,isCreator,createTime)") // 讨论
    @JSON("items.attention.(id)") // 当前用户对内容的关注记录
    @JSON("items.praise.(id)") // 点赞记录
    public PagedResult<Trend> findSecurityTrendsMine(RequestContext requestContext, Subject<Member> subject) throws Exception {
        PagedResult<Trend> trendsMine = findTrendsMine(requestContext, subject);
        List<Trend> items = trendsMine.getItems();
        for (Trend item : items) {
            Member createMember = item.getCreateMember();
            if (Objects.nonNull(createMember)){
                createMember.setFullName(com.zxy.common.restful.util.Encrypt.aesEncrypt(Optional.ofNullable(createMember.getFullName()).orElse(""), "d8cg8gVakEq9Agup"));
            }
        }
        return PagedResult.create(trendsMine.getRecordCount(), items);
    }

    /** 问吧首页-文章分享 */
    @RequestMapping(value="/trends-article", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class , required = true)
    @Param(name = "pageSize", type = Integer.class , required = true)
    @JSON("recordCount")
    @JSON("items.(id,businessId,businessType,questionId,discussId,createMemberId,createTime,rootOrganizationId)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)")
    @JSON("items.question.(id,type,title,contentTxt,status,auditStatus,img,accuseStatus,essenceStatus,topStatus,browseNum,attentionNum,praiseNum,discussNum,shareNum,attentionNum,isCreator,isManager,createTime,lastModifyTime)")
    @JSON("items.question.questionTopics.topic.(id,name,typeId,group,isBarTopic)") // 内容：问题、文章、分享
    @JSON("items.attention.(id)") // 当前用户对内容的关注记录
    @JSON("items.praise.(id)") // 点赞记录
    public PagedResult<Trend> findTrendsArticle(RequestContext requestContext, Subject<Member> subject) {
        return trendService.findTrendsArticle(
                requestContext.getInteger("start"),
                requestContext.getInteger("pageSize"),
                subject.getCurrentUserId(),
                subject.getCurrentUser().getRootOrganization().getId());
    }

    /**
     * 查询指定话题下的最新动态
     */
    @RequestMapping(value = "/trends-topic", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "topicId", required = true)
    @JSON("recordCount")
    @JSON("items.(id,businessId,businessType,createTime)")
    @JSON("items.question.(id,type,title,contentTxt,status,auditStatus,img,accuseStatus,essenceStatus,topStatus,topicTopStatus,browseNum,attentionNum,praiseNum,discussNum,shareNum,attentionNum,isCreator,isManager,createTime,lastModifyTime,shareObjectId,shareType,shareTitle)")
    @JSON("items.discuss.(id,type,contentText,auditStatus,img,praiseNum,replyNum,isCreator,createTime)") // 讨论
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)")
    @JSON("items.question.questionTopics.topic.(id,name,group,isBarTopic)")
    @JSON("items.attention.(id)")
    @JSON("items.praise.(id)")
    public PagedResult<Trend> findTrend(RequestContext context, Subject<Member> subject) {
        return trendService.findTrendByTopicId(context.getInteger("start"),
                                               context.getInteger("pageSize"),
                                               context.getString("topicId"),
                                               subject.getCurrentUser().getRootOrganization().getId(),
                                               subject.getCurrentUserId());
    }

    /**
     * 查询指定话题下的文章分享
     */
    @RequestMapping(value = "/article-topic", method = RequestMethod.GET)
    @Permitted
    @Param(name = "start", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "topicId", required = true)
    @JSON("recordCount")
    @JSON("items.(id,businessId,businessType,questionId,discussId,createMemberId,createTime,rootOrganizationId)")
    @JSON("items.question.(id,type,title,contentTxt,status,auditStatus,img,accuseStatus,essenceStatus,topStatus,topicTopStatus,browseNum,attentionNum,praiseNum,discussNum,shareNum,attentionNum,isCreator,isManager,createTime,lastModifyTime, topStatus)")
    @JSON("items.createMember.(id,fullName,headPortrait,headPortraitPath,isExpert)")
    @JSON("items.question.questionTopics.topic.(id,name,typeId,group,isBarTopic)")
    @JSON("items.attention.(id)")
    @JSON("items.praise.(id)")
    public PagedResult<Trend> findArticleAndShare(RequestContext context, Subject<Member> subject) {
        return trendService.findArticleByTopicId(context.getInteger("start"),
                                                 context.getInteger("pageSize"),
                                                 context.getString("topicId"),
                                                 subject.getCurrentUser().getRootOrganization().getId(),
                                                 subject.getCurrentUserId());
    }

}
