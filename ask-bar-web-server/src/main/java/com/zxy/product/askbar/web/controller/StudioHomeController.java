package com.zxy.product.askbar.web.controller;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.askbar.api.*;
import com.zxy.product.askbar.entity.*;
import com.zxy.product.askbar.vo.ExpertStudiosVO;
import com.zxy.product.askbar.web.vo.ArticleVo;
import com.zxy.product.askbar.web.vo.PageVo;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.GenseeWebCastService;
import com.zxy.product.course.api.KnowledgeService;
import com.zxy.product.course.api.course.CourseInfoStudioService;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.GenseeWebCast;
import com.zxy.product.course.entity.KnowledgeInfo;
import com.zxy.product.human.api.FileService;
import com.zxy.product.system.api.collect.CollectService;
import com.zxy.product.system.api.comment.CommentInfoService;
import com.zxy.product.system.entity.CommentInfo;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zxy.product.askbar.web.util.SecurePathCdnUtils.generateSecurePathCdn;

/**
 * <AUTHOR>
 * @date 2021/10/25
 */

@Controller
@RequestMapping("/studio/home")
public class StudioHomeController {

    private static final Integer HOT_EXPIRE_TIME = 10 * 60;
    private static final Integer HOT_DEFAULT_PAGE = 1;
    private static final Integer HOT_DEFAULT_PAGE_SIZE = 3;
    private static final String STR_COMMA = ",";
    private static final Integer CLIENT_PC = 1;
    private static final Integer CLIENT_APP = 2;

    private Cache cache;
    private Cache communicationCache;
    private Cache articleCache;
    private StudioContentService studioContentService;
    private StudioService studioService;

    private CollectService collectService;
    private StudioAttentionService studioAttentionService;
    private QuestionService questionService;//文章
    private StudioDiscussService studioDiscussService;//讨论

    private GenseeWebCastService genseeWebCastService;//直播
    private CourseInfoService courseInfoService;//课程
    private KnowledgeService knowledgeService;//知识
    private FileService fileService;
    private CommentInfoService commentInfoService;
    private StudioMemberService studioMemberService;
    private CourseInfoStudioService courseInfoStudioService;
    private StudioQusetionService studioQusetionService;
    private QuestionTopicService questionTopicService;

    @Autowired
    public void setCourseInfoStudioService(CourseInfoStudioService courseInfoStudioService) {
        this.courseInfoStudioService = courseInfoStudioService;
    }

    @Autowired
    public void setStudioMemberService(StudioMemberService studioMemberService) {
        this.studioMemberService = studioMemberService;
    }

    @Autowired
    public void setCommentInfoService(CommentInfoService commentInfoService) {
        this.commentInfoService = commentInfoService;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setStudioAttentionService(StudioAttentionService studioAttentionService) {
        this.studioAttentionService = studioAttentionService;
    }

    @Autowired
    public void setQuestionService(QuestionService questionService) {
        this.questionService = questionService;
    }

    @Autowired
    public void setStudioDiscussService(StudioDiscussService studioDiscussService) {
        this.studioDiscussService = studioDiscussService;
    }

    @Autowired
    public void setGenseeWebCastService(GenseeWebCastService genseeWebCastService) {
        this.genseeWebCastService = genseeWebCastService;
    }

    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setKnowledgeService(KnowledgeService knowledgeService) {
        this.knowledgeService = knowledgeService;
    }

    @Autowired
    public void setCollectService(CollectService collectService) {
        this.collectService = collectService;
    }

    @Autowired
    public void setStudioService(StudioService studioService) {
        this.studioService = studioService;
    }

    @Autowired
    public void setStudioContentService(StudioContentService studioContentService) {
        this.studioContentService = studioContentService;
    }

    @Autowired
    public void setCache(CacheService cacheService) {
        cache = cacheService.create("ask-bar", "ask-bar-web-server");
        communicationCache = cacheService.create("ask-bar", "communication-list");
        articleCache = cacheService.create("ask-bar", "studio-article-list");
    }

    @Autowired
    public void setStudioQusetionService(StudioQusetionService studioQusetionService) {
        this.studioQusetionService = studioQusetionService;
    }

    @Autowired
    public void setQuestionTopicService(QuestionTopicService questionTopicService) {
        this.questionTopicService = questionTopicService;
    }

    @RequestMapping(value = "/hot", method = RequestMethod.GET)
    @Param(name = "types", required = true)
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @Param(name = "clientType", type = Integer.class, value = "1-pc,2-app")
    @Permitted
    @JSON("items.(businessId,businessType,popular,businessName,cover,type,createTime)")
    @JSON("more")
    public PageVo<StudioContent> hot(RequestContext context) {
        List<Integer> types = Arrays.stream(context.getString("types").split(STR_COMMA))
                .map(Integer::valueOf).collect(Collectors.toList());
        Integer page = context.getOptionalInteger("page").orElse(HOT_DEFAULT_PAGE);
        Integer pageSize = context.getOptionalInteger("pageSize").orElse(HOT_DEFAULT_PAGE_SIZE);
        Integer clientType = context.getOptionalInteger("clientType").orElse(CLIENT_PC);

        PagedResult<StudioContent> pagedResult;
        if (HOT_DEFAULT_PAGE.equals(page)) {
            String cacheKey = StudioContent.buildHotCacheKey(types.toString(), pageSize, clientType);
            pagedResult =
                    cache.get(cacheKey, () -> getHot(types, page, pageSize, clientType), HOT_EXPIRE_TIME);
        } else {
            pagedResult = getHot(types, page, pageSize, clientType);
        }

        return PageVo.create(pagedResult.getRecordCount(), pagedResult.getItems());
    }

    @RequestMapping(value = "/my-attention-num", method = RequestMethod.GET)
    @JSON("*")
    public Map<String, Integer> myAttentionNum(Subject<Member> subject) {
        return ImmutableMap.of("data", studioService.getMyAttentionNum(subject.getCurrentUserId()));
    }

    @RequestMapping(value = "/my-collection-num", method = RequestMethod.GET)
    @JSON("*")
    public Map<String, Integer> myCollectionNum(Subject<Member> subject) {
        return ImmutableMap.of("data", collectService.getMyCollectionNum(subject.getCurrentUserId()));
    }
    /**
     * 进入管理端权限按钮判断
     * 返回最近发布的工作室
     */
    @RequestMapping(value = "/permission", method = RequestMethod.GET)
    @JSON("id")
    @Permitted
    public Map<String, String> checkPermission(Subject<Member> subject) {
        return ImmutableMap.of("id", studioService.checkPermission(subject.getCurrentUserId()));
    }

    /**
     * 学员端查看工作室详情
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("topics.(id,name)")
    @JSON("id,name,property,positionalTitle,topics,maxim,introduction,introductionTxt,coverId,coverPath,portraitId,portraitPath,contentNum,popular,createTime," +
            "adminName,attentionNum,praiseNum,topics,attention")
    @Permitted
    public Studio getStudioInfo(RequestContext context, Subject<Member> subject) {
        String studioId = context.getString("id");
        Studio studio = studioService.get(studioId);
        studio.setPortraitPath(generateSecurePathCdn(studio.getPortraitPath()));
        studio.setCoverPath(generateSecurePathCdn(studio.getCoverPath()));
        Map<String, List<Topic>> topicMapByIds = studioService.getTopicMapByIds(Arrays.asList(studio.getId()));
        studio.setTopics(topicMapByIds.get(studioId));
        studio.setAttention(StudioAttention.ATTENTION_NO);
        studioAttentionService.getByMemberAndStudioId(studioId, subject.getCurrentUserId()).ifPresent(r -> {
            studio.setAttention(StudioAttention.ATTENTION_YES);
        });
        return studio;
    }

    @RequestMapping(value = "/studio-member/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @Permitted
    @JSON("memberList.(id,masterFlag,member,organization)")
    @JSON("memberList.organization.(id,name,code)")
    @JSON("memberList.member.(id,name,fullName,headPortrait,headPortraitPath)")
    @JSON("adminMember.(id,masterFlag,member,organization)")
    @JSON("adminMember.member.(id,name,fullName,headPortrait,headPortraitPath)")
    @JSON("adminMember.organization.(id, name, code)")
    @JSON("id,memberList,adminMember")
    public Studio getStudioMember(RequestContext context, Subject<Member> subject) {
        String id = context.getString("id");
        Studio studio = studioService.get(id);
        List<StudioMember> members = studioMemberService.getStudioMemberById(id);
        studio.setId(id);
        members.stream().filter(me -> StudioMember.IS_ADMIN == me.getMasterFlag()).findFirst()
                .ifPresent(studio::setAdminMember);
        studio.setMemberList(members.stream().filter(me -> StudioMember.IS_ADMIN != me.getMasterFlag())
                .collect(Collectors.toList()));
        return studio;
    }

    /**
     * 专家工作室首页
     *
     * @param context
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("more")
    @JSON("recordCount")
    @JSON("items.topics.(id,name,createTime)")
    @JSON("items.organization.(id,name)")
    @JSON("items.newArticle.(id,studioId,businessId,businessName,businessType,createTime)")
    @JSON("items.hotContent.(id,studioId,businessId,businessName,businessType,genseeStatus,status)")
    @JSON("items.(id,name,property,positionalTitle,topics,maxim,introduction,coverId,coverPath,portraitId,portraitPath,contentNum,popular,createTime," +
            "adminName,attentionNum,topics,attention)")
    @Permitted
    public PageVo<Studio> findHomeStudio(RequestContext context) {

        Integer page = context.getInteger("page");
        Integer pageSize = context.getInteger("pageSize");

        PagedResult<Studio> pagedResult;
        if (Studio.PAGE == (page)) {
            String cacheKey = Studio.homeStudioCacheKey();
            pagedResult =
                    cache.get(cacheKey, () ->  pageResult(page, pageSize), 60 * 60 * 2);
        } else {
            pagedResult = pageResult(page, pageSize);
        }
        List<Studio> studioList = pagedResult.getItems();
        if (CollectionUtils.isEmpty(studioList)) {
            return PageVo.createCount(0, studioList);
        }
        return  PageVo.typeConversion(pagedResult, pageSize);
    }
    /**
     * 专家工作室首页
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/security", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("more")
    @JSON("recordCount")
    @JSON("items.topics.(id,name,createTime)")
    @JSON("items.organization.(id,name)")
    @JSON("items.newArticle.(id,studioId,businessId,businessName,businessType,createTime)")
    @JSON("items.hotContent.(id,studioId,businessId,businessName,businessType,genseeStatus,status)")
    @JSON("items.(id,name,property,positionalTitle,topics,maxim,introduction,coverId,coverPath,portraitId,portraitPath,contentNum,popular,createTime," +
            "adminName,attentionNum,topics,attention)")
    @Permitted
    public PageVo<Studio> findSecurityHomeStudio(RequestContext context) throws Exception {
        PageVo<Studio> homeStudio = findHomeStudio(context);
        List<Studio> items = homeStudio.getItems();
        for (Studio item : items) {
            item.setAdminName(com.zxy.common.restful.util.Encrypt.aesEncrypt(Optional.ofNullable(item.getAdminName()).orElse(""), "d8cg8gVakEq9Agup"));
        }
        return PageVo.create(homeStudio.getRecordCount(), items);
    }

    /**
     *重写首页专家工作室Page列表（反腐倡廉  20240317）
     *
     * @param requestContext 请求上下文
     * @return 重写首页专家工作室Page列表
     */
    @RequestMapping(method = RequestMethod.GET,value = "/expert-studios-page")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("recordCount")
    @JSON("items.(id,adminName,topicName,coverPath)")
    @Permitted
    public PageVo<ExpertStudiosVO> expertStudiosPage(RequestContext requestContext){
        Integer page = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        PagedResult<ExpertStudiosVO> result = Optional.of(page)
                .filter(ew1 -> Objects.equals(Studio.PAGE, ew1))
                .map(ew2 -> {
                    String cacheKey = ExpertStudiosVO.homeExpertStudiosVO();
                    return cache.get(cacheKey, () -> studioService.expertStudiosPage(ew2, pageSize), ExpertStudiosVO.EXPIRE_TIME);
                }).orElseGet(() -> studioService.expertStudiosPage(page, pageSize));
        List<ExpertStudiosVO> expertStudiosCollect = result.getItems();
        return Optional.ofNullable(expertStudiosCollect)
                .filter(CollectionUtils::isEmpty)
                .map(ew2->PageVo.createCount(0, ew2))
                .orElseGet(()->PageVo.typeConversion(result, pageSize));
    }


    private PagedResult<Studio> pageResult(Integer page, Integer pageSize) {
        PagedResult<Studio> result = studioService.findHomeStudio(page, pageSize);
        List<String> studioIds = result.getItems().stream().map(Studio::getId).collect(Collectors.toList());
        Map<String, List<StudioContent>> hotContent = studioService.getHotContentMapByIds(studioIds);
        List<String> genseeIds = hotContent.values().stream()
                .flatMap(Collection::stream)
                .filter(con -> StudioContent.BUSINESS_TYPE_LIVE.equals(con.getBusinessType()))
                .map(StudioContent::getBusinessId)
                .collect(Collectors.toList());
        Map<String, Integer> genseeMap = genseeWebCastService.findContentByIds(genseeIds).stream()
                .collect(Collectors.toMap(GenseeWebCast::getId, GenseeWebCast::getStatus, (p, q) -> p));
        result.getItems().forEach(s->s.setHotContent(hotContent.getOrDefault(s.getId(), Lists.newArrayList()).stream()
                .peek(con->{
                    Integer genseeStatus = genseeMap.get(con.getBusinessId());
                    con.setGenseeStatus(genseeStatus);
                    con.setStatus(genseeStatus);
                }).collect(Collectors.toList())));
        return result;
    }

    private PagedResult<StudioContent> getHot(List<Integer> types, Integer page, Integer pageSize, Integer clientType) {
        PagedResult<StudioContent> pagedResult = studioContentService.getHot(types, page, pageSize);
        if (CLIENT_PC.equals(clientType)) {
            // pc不需要查询封面
            return pagedResult;
        }
        //分离数据
        List<String> liveList = Lists.newArrayList();
        List<String> courseList = Lists.newArrayList();
        List<String> articleList = Lists.newArrayList();
        List<String> docList = Lists.newArrayList();
        List<String> discussList = Lists.newArrayList();
        pagedResult.getItems().forEach(r -> {
            String businessId = r.getBusinessId();
            Integer businessType = r.getBusinessType();
            if (StudioContent.BUSINESS_TYPE_LIVE.equals(businessType)) {
                liveList.add(businessId);
            } else if (StudioContent.BUSINESS_TYPE_COURSE.equals(businessType)||StudioContent.BUSINESS_TYPE_CITATION_COURSE ==businessType) {
                courseList.add(businessId);
            } else if (StudioContent.BUSINESS_TYPE_ARTICLE.equals(businessType)||StudioContent.BUSINESS_TYPE_CITATION_ARTICLE == businessType) {
                articleList.add(businessId);
            } else if (StudioContent.BUSINESS_TYPE_DOC.equals(businessType)||StudioContent.BUSINESS_TYPE_CITATION_DOC == businessType) {
                docList.add(businessId);
            } else if (StudioContent.BUSINESS_TYPE_DISCUSS.equals(businessType)) {
                discussList.add(businessId);
            }
        });
        //获取封面
        Function<List<String>, Boolean> nonEmpty = list -> !CollectionUtils.isEmpty(list);
        Map<String, String> coverMap = Maps.newHashMap();
        Map<String, Integer> fileTypeMap = Maps.newHashMap();
        if (nonEmpty.apply(liveList)) {
            genseeWebCastService.getCoverPaths(liveList).forEach(r -> {
                coverMap.put(r.getId(), r.getCoverPath());
            });
        }
        if (nonEmpty.apply(courseList)) {
            courseInfoService.getCoverPaths(courseList).forEach(r -> {
                coverMap.put(r.getId(), r.getCoverPath());
            });
        }
        if (nonEmpty.apply(articleList)) {
            Map<String, String> map = questionService.getImgs(articleList)
                    .stream().filter(r -> Objects.nonNull(r.getImg()))
                    .collect(Collectors.toMap(Question::getId, Question::getImg));//路径
            coverMap.putAll(map);
        }
        if (nonEmpty.apply(docList)) {
            Map<String, Integer> type = knowledgeService.getTypes(docList)
                    .stream().filter(r -> Objects.nonNull(r.getType()))
                    .collect(Collectors.toMap(KnowledgeInfo::getId, KnowledgeInfo::getType));
            fileTypeMap.putAll(type);
        }
        if (nonEmpty.apply(discussList)) {
            Map<String, String> map = studioDiscussService.getCovers(discussList)
                    .stream().filter(r -> Objects.nonNull(r.getCoverPath()))
                    .collect(Collectors.toMap(StudioDiscuss::getId, StudioDiscuss::getCoverPath));
            coverMap.putAll(map);
        }
        //封装
        pagedResult.getItems().forEach(r -> {
            Integer businessType = r.getBusinessType();
            String businessId = r.getBusinessId();
            if (StudioContent.BUSINESS_TYPE_DOC.equals(businessType)) {
                r.setType(fileTypeMap.get(businessId));
            } else {
                r.setCover(coverMap.get(businessId));
            }
        });

        return pagedResult;
    }


    /**
     * 问答选择工作室
     * @param context
     * @return
     */
    @RequestMapping(value = "/select", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")        // 工作室名称
    @Param(name = "adminName")   //管理员名称
    @Param(name = "property", type = Integer.class)   //工作室属性
    @JSON("more")
    @JSON("items.topics.(id,name)")
    @JSON("items.(id,name,coverId,coverPath,portraitId,portraitPath,adminName,topics,property)")
    @Permitted
    public PageVo<Studio> select(RequestContext context) {

        PagedResult<Studio> result = studioService.select(context.getInteger("page"), context.getInteger("pageSize"),
                context.getOptionalString("adminName"), Optional.empty(),
                context.getOptionalString("name"), context.getOptionalString("property"));
        return PageVo.create(result.getRecordCount(),result.getItems());

    }

    /**
     * 工作室首页课程
     * @param context
     * @return
     */
    @RequestMapping(value = "/find-all-course", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("more")
    @JSON("recordCount")
    @JSON("items.(type,studio,collectId,discussNum,content,collectNum)")
    @JSON("items.studio.(*)")
    @JSON("items.content.(id,name,type,createTime,topicNames,releaseTime,source,status,cover,coverPath,beginDate,endDate,description,visits,avgScore,url,descriptionText,descriptionApp,integral,collectionCount)")
    @Permitted
    public PageVo<StudioContent> findHomeCourse(RequestContext context, Subject<Member> subject) {
        Integer pageSize = context.getInteger("pageSize");
        String currentUserId = subject.getCurrentUserId();

        List<Integer> types = new ArrayList<>();
        types.add(StudioContent.BUSINESS_TYPE_CITATION_COURSE);
        types.add(StudioContent.BUSINESS_TYPE_COURSE);
        List<String> ids = studioContentService.getByHideIds(types);

        //String currentUserId = "1";
        PagedResult<CourseInfo> pageInfo = courseInfoStudioService.findHomeCourse(
                currentUserId, context.getInteger("page"),
                pageSize, new ArrayList<>());
        List<CourseInfo> originalList = pageInfo.getItems();

        List<CourseInfo> list;
        if (ids.isEmpty()) {
            // ids为空时不过滤，保留原始列表
            list = originalList;
        } else {
            // ids不为空时执行过滤
            list = originalList.stream()
                    .filter(r -> !ids.contains(r.getId()))
                    .collect(Collectors.toList());
        }

        pageInfo = PagedResult.create(list.size(), list);
        //查询引用课程
        // 查询工作室发布的引用课程
        Map<Integer, List<String>> map = studioContentService.getBussinessId(Lists.newArrayList(StudioContent.BUSINESS_TYPE_CITATION_COURSE));
        List<String> citationIds = map.get(StudioContent.BUSINESS_TYPE_CITATION_COURSE);
        if (!CollectionUtils.isEmpty(citationIds)) {
            PagedResult<CourseInfo> citationPageInfo = courseInfoStudioService.findHomeCourse(
                    currentUserId, context.getInteger("page"),
                    pageSize, citationIds);
            List<CourseInfo> citationList = citationPageInfo.getItems();

            //1.有自建，有引用
            if (!CollectionUtils.isEmpty(list)&&!CollectionUtils.isEmpty(citationList)) {
                list.addAll(citationList);
                pageInfo =  PagedResult.create(pageInfo.getRecordCount() + citationList.size(), list);
            }

            //2.没有自建，只有引用
             if (CollectionUtils.isEmpty(list)&&!CollectionUtils.isEmpty(citationList)) {
                pageInfo = citationPageInfo;
                list = new ArrayList<>();
                list.addAll(citationList);
            }

        }



        if (CollectionUtils.isEmpty(list)) {
            return PageVo.create(0,new ArrayList<>());
        }
        PageVo<CourseInfo> pageVo = PageVo.typeConversion(pageInfo, pageSize);
        List<CourseInfo> infoList = pageVo.getItems();
        List<String> courseIds = infoList.stream().map(CourseInfo::getId).collect(Collectors.toList());
        // 查询收藏数
//        Map<String, Integer> numMap = collectService.getCollectionNumMap(courseIds);
        // 查询评论数
        Map<String, Integer> commentMap = commentInfoService.getCommentNumMap(courseIds);

        // 查询工作室信息
        Map<String, Studio> studios = studioService.getStudioByBusinessIds(courseIds);

        // 过滤掉在studios中不存在的courseId
//        courseIds = courseIds.stream()
//                .filter(courseId -> studios.containsKey(courseId))
//                .collect(Collectors.toList());

        // 查询是否已经收藏
        Map<String, String> collectMap = collectService.getIdMap(courseIds, currentUserId);
        List<StudioContent> result = infoList.stream().map(course -> {
            StudioContent<CourseInfo> content = new StudioContent();
            String courseId = course.getId();
            content.setType(StudioContent.BUSINESS_TYPE_COURSE);
            //设置引用类型
            if(!CollectionUtils.isEmpty(citationIds)&&citationIds.contains(courseId)){
                content.setType(StudioContent.BUSINESS_TYPE_CITATION_COURSE);
            }
            content.setStudio(studios.getOrDefault(courseId, new Studio()));
            content.setContent(course);
            content.setCollectNum(course.getCollectionCount());
            content.setDiscussNum(commentMap.getOrDefault(courseId, 0));
            content.setCollectId(collectMap.get(courseId));
            return content;
        }).collect(Collectors.toList());
        return PageVo.page(pageVo.getRecordCount(),result,pageVo.getMore());
    }

    @RequestMapping(value = "/get-article", method = RequestMethod.GET)
    @Param(name = "articleType", type = Integer.class, required = true)
    @JSON("(id,title,contentTxt,createTime,businessType)")
    @JSON("studio.(id,name,portraitId,portraitPath)")
    @JSON("topics.(id,name)")
    public List<ArticleVo> getHomeDoc(RequestContext context) {
        List<ArticleVo> articleVos = articleCache.get(context.getInteger("articleType")+"",()-> {
            List<StudioContent> contents = studioContentService.getByArticleType(context.getInteger("articleType"));
            List<String> articleIds = contents.stream().map(StudioContent::getBusinessId).collect(Collectors.toList());
            // 文章信息
            List<Question> questionList = questionService.getByIds(articleIds);
            List<KnowledgeInfo> KnowledgeInfos = knowledgeService.getByIds(Lists.newArrayList(articleIds));
            // 文章关联标签
            Map<String, List<Topic>> questionTopicsMap = questionTopicService.getDistinctQuestionTopicInfoList(articleIds)
                    .stream().collect(Collectors.groupingBy(Topic::getBusinessId));
            // 工作室信息
            Map<String, Studio> studioInfoMap = contents.stream().map(StudioContent::getStudio)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(Studio::getId, Function.identity(), (k1, k2) -> k1));
            //创建时间
            Map<String, Long> createTimeMap = contents.stream()
                    .collect(Collectors.toMap(StudioContent::getBusinessId, StudioContent::getCreateTime, (k1, k2) -> k1));
            Map<String, String> studioIdMap = Maps.newHashMap();
            Map<String, Integer> contentMap = Maps.newHashMap();
            contents.forEach(s -> {
                String businessId = s.getBusinessId();
                String studioId = s.getStudioId();
                Integer businessType = s.getBusinessType();
                studioIdMap.put(businessId, studioId);
                contentMap.put(businessId, businessType);
            });

            List<ArticleVo> vos = new ArrayList<>();
            if (!ObjectUtils.isEmpty(questionList)) {
                questionList.forEach(r -> {
                    ArticleVo vo = new ArticleVo();
                    vo.setId(r.getId());
                    vo.setTitle(r.getTitle());
                    vo.setStudio(studioInfoMap.get(studioIdMap.get(r.getId())));
                    vo.setTopics(questionTopicsMap.get(r.getId()));
                    vo.setCreateTime(createTimeMap.get(r.getId()));
                    vo.setContentTxt(r.getContentTxt());
                    vo.setBusinessType(contentMap.get(r.getId()));
                    vos.add(vo);
                });
            }
            if (!ObjectUtils.isEmpty(KnowledgeInfos)){
                KnowledgeInfos.forEach(r->{
                    ArticleVo vo = new ArticleVo();
                    vo.setId(r.getId());
                    vo.setTitle(r.getName());
                    vo.setStudio(studioInfoMap.get(studioIdMap.get(r.getId())));
                    vo.setTopics(questionTopicsMap.get(r.getId()));
                    vo.setCreateTime(createTimeMap.get(r.getId()));
                    vo.setContentTxt(r.getDescription());
                    vo.setBusinessType(contentMap.get(r.getId()));
                    vos.add(vo);
                });
            }

            // 对 vos 进行排序
            vos.sort(Comparator.comparing(ArticleVo::getCreateTime).reversed());

            return vos;


        },60*10);
        articleVos.forEach(articleVo -> {
            Studio studio = articleVo.getStudio();
            if (studio != null && studio.getPortraitPath() != null && !studio.getPortraitPath().contains("?auth_key")) {
                studio.setPortraitPath(generateSecurePathCdn(studio.getPortraitPath()));
                articleVo.setStudio(studio);
            }
        });
        return articleVos;
    }

    /**
     * 工作室首页课程
     * @param context
     * @return
     */
    @RequestMapping(value = "/studio-course", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "studioId", required = true)
    @JSON("more")
    @JSON("items.(type,studio,collectNum,collectId,discussNum,content)")
    @JSON("items.studio.(*)")
    @JSON("items.content.(id,name,type,createTime,topicNames,releaseTime,source,status,cover,coverPath,beginDate,endDate,description,visits,avgScore,url,descriptionText,descriptionApp,integral,collectionCount)")
    @Permitted
    public PageVo<StudioContent> findStudioCourse(RequestContext context, Subject<Member> subject) {
        Integer pageSize = context.getInteger("pageSize");
        String studioId = context.getString("studioId");
        String currentUserId = subject.getCurrentUserId();
        //String currentUserId = "1";
        // 查询工作室发布的所有课程
        Map<Integer, List<String>> map = studioContentService.getBussinessId(studioId,
                Lists.newArrayList(StudioContent.BUSINESS_TYPE_COURSE,StudioContent.BUSINESS_TYPE_CITATION_COURSE));
        List<String> bussinessIds = new ArrayList<>();
        if(!CollectionUtils.isEmpty(map.get(StudioContent.BUSINESS_TYPE_CITATION_COURSE))){
            bussinessIds.addAll(map.get(StudioContent.BUSINESS_TYPE_CITATION_COURSE));
        }
        if(!CollectionUtils.isEmpty(map.get(StudioContent.BUSINESS_TYPE_COURSE))){
            bussinessIds.addAll(map.get(StudioContent.BUSINESS_TYPE_COURSE));
        }

        if (CollectionUtils.isEmpty(map)&&CollectionUtils.isEmpty(bussinessIds)) {
            return PageVo.create(0,new ArrayList<>());
        }
        PagedResult<CourseInfo> pageInfo = courseInfoStudioService.findHomeCourse(
                currentUserId, context.getInteger("page"),
                pageSize, bussinessIds);
        List<CourseInfo> list = pageInfo.getItems();
        if (CollectionUtils.isEmpty(list)) {
            return PageVo.create(0,new ArrayList<>());
        }
        PageVo<CourseInfo> pageVo = PageVo.typeConversion(pageInfo, pageSize);
        List<CourseInfo> infoList = pageVo.getItems();
        List<String> courseIds = infoList.stream().map(CourseInfo::getId).collect(Collectors.toList());
        // 查询收藏数
//        Map<String, Integer> numMap = collectService.getCollectionNumMap(courseIds);
        // 查询评论数
        Map<String, Integer> commentMap = commentInfoService.getCommentNumMap(courseIds);
        // 查询工作室信息
        Studio studio = studioService.get(studioId);
        studio.setPortraitPath(generateSecurePathCdn(studio.getPortraitPath()));
        studio.setCoverPath(generateSecurePathCdn(studio.getCoverPath()));
        // 查询是否已经收藏
        Map<String, String> collectMap = collectService.getIdMap(courseIds, currentUserId);
        List<StudioContent> result = infoList.stream().map(course -> {
            StudioContent<CourseInfo> content = new StudioContent();
            String courseId = course.getId();
            content.setType(StudioContent.BUSINESS_TYPE_COURSE);
            content.setStudio(studio);
            course.setCoverPath(generateSecurePathCdn(course.getCoverPath()));
            content.setContent(course);
            content.setCollectNum(course.getCollectionCount());
            content.setDiscussNum(commentMap.getOrDefault(courseId, 0));
            content.setCollectId(collectMap.get(courseId));
            return content;
        }).collect(Collectors.toList());
        return PageVo.page(pageVo.getRecordCount(),result,pageVo.getMore());
    }


    /**
     * 专家工作室首页 热门课程 随机取“视频类型”为“课程”的4门课
     * @return
     */
    @RequestMapping(value = "/hot-course", method = RequestMethod.GET)
    @JSON("content.(id,name,cover)")
    @JSON("studio.(id,name,portraitId,portraitPath)")
    public List<StudioContent> hotCourse() {
        List<StudioContent> studioContents = articleCache.get("hot-course",()->{
            List<StudioContent> contents = studioContentService.getByCourseType(StudioContent.COURSE_TYPE_COURSE);
            Map<String, Studio> studioMap = contents.stream().collect(Collectors.toMap(StudioContent::getBusinessId,
                    StudioContent::getStudio, (k1, k2) -> k1));
            List<String> ids = contents.stream().map(StudioContent::getBusinessId).collect(Collectors.toList());
            List<CourseInfo> courseInfoList = courseInfoService.findCourse("", ids);
            List<StudioContent> result = courseInfoList.stream().map(course -> {
                StudioContent<CourseInfo> content = new StudioContent();
                String courseId = course.getId();
                course.setCover(course.getCoverPath());
                content.setStudio(studioMap.get(courseId));
                content.setContent(course);
                return content;
            }).collect(Collectors.toList());
            return result;
        },60*10);
        studioContents.forEach(studioContent -> {
            CourseInfo cs = (CourseInfo) studioContent.getContent();
            if(cs!=null) cs.setCover(generateSecurePathCdn(cs.getCoverPath()));
            Studio studio = studioContent.getStudio();
            if(studio!=null) studio.setPortraitPath(generateSecurePathCdn(studio.getPortraitPath()));
            studioContent.setContent(cs);
            studioContent.setStudio(studio);
        });
        return studioContents;
    }

    /**
     * 工作室首页答疑交流.
     * @return
     */
    @RequestMapping(value = "/communication", method = RequestMethod.GET)
    @JSON("id,studio,title,introduction,startTime,createTime,type,fileType,contentTxt,createMemberName," +
            "headPortraitPath,answerTxt,answerTime,answer")
    @JSON("studio.(id,name,portraitId,portraitPath)")
    public List<ArticleVo> getCommunication() {
        List<ArticleVo> articleVoList = communicationCache.get("getCommunication", () -> {
            List<CommentInfo> commentInfos = commentInfoService.getStudioComment();
            List<StudioQuestion> studioQuestions = studioQusetionService.getNewAnswer();
            List<String> discussIds = commentInfos.stream().map(CommentInfo::getBusinessId).collect(Collectors.toList());

            Map<String, CommentInfo> commentMap = commentInfos.stream()
                    .collect(Collectors.toMap(CommentInfo::getBusinessId, Function.identity(), (k1, k2) -> k1));
            // 获取主体
            List<StudioDiscuss> discussList = studioDiscussService.getByIdsWithStudio(discussIds);

            ArrayList<ArticleVo> voList = Lists.newArrayList();

            // 组装信息 讨论
            discussList.forEach(r -> {
                String id = r.getId();
                ArticleVo vo = new ArticleVo();
                vo.setId(id);
                vo.setTitle(r.getTitle());
                vo.setStudio(r.getStudio());
                vo.setIntroduction(r.getIntroduction());
                vo.setCreateTime(r.getCreateTime());
                vo.setAnswerTime(commentMap.get(id).getCreateTime());
                vo.setAnswerTxt(commentMap.get(id).getContentText());
                vo.setAnswer(commentMap.get(id).getMemberName());
                vo.setType(StudioContent.BUSINESS_TYPE_DISCUSS);
                voList.add(vo);
            });

            //问答
            studioQuestions.forEach(r -> {
                String id = r.getId();
                ArticleVo vo = new ArticleVo();
                vo.setId(id);
                vo.setTitle(r.getTitle());
                vo.setContent(r.getContent());
                vo.setContentTxt(r.getContentTxt());
                vo.setStudio(r.getStudio());
                vo.setType(StudioContent.BUSINESS_TYPE_QUESTION);
                vo.setCreateMemberId(r.getCreateMember().getId());
                vo.setCreateMemberName(r.getCreateMember().getFullName());
                vo.setCreateTime(r.getCreateTime());
                vo.setAnswerTime(r.getAnswerTime()); //根据回复时间排序
                vo.setAnswerTxt(r.getAnswerTxt());
                vo.setHeadPortraitPath(r.getCreateMember().getHeadPortraitPath());
                vo.setAnswer(r.getAnswerMember().getFullName());
                voList.add(vo);
            });
            List<ArticleVo> articleVos =
                    voList.stream().sorted(Comparator.comparingLong(ArticleVo::getAnswerTime).reversed()).collect(Collectors.toList());
            return articleVos.subList(0, 2);
        }, 60*60*24);
        articleVoList.forEach(articleVo -> {
            Studio studio = articleVo.getStudio();
            if(studio!=null) studio.setPortraitPath(generateSecurePathCdn(studio.getPortraitPath()));
            articleVo.setHeadPortraitPath(generateSecurePathCdn(articleVo.getHeadPortraitPath()));
            articleVo.setStudio(studio);
        });
        return articleVoList;
    }

    @RequestMapping(value = "/get-doc", method = RequestMethod.GET)
    @JSON("(id,name,browseCount, type,description)")
    @JSON("createMember.(id,fullName)")
    public List<KnowledgeInfo> getDoc() {
        return articleCache.get("get-doc",()->{
            //只查五条
            PagedResult<StudioContent> pagedResult = studioContentService.getPageByTypes(1, 6, Optional.empty(),
                    Lists.newArrayList(StudioContent.BUSINESS_TYPE_DOC, StudioContent.BUSINESS_TYPE_CITATION_DOC));

            List<String> docIds = pagedResult.getItems().stream().map(StudioContent::getBusinessId).collect(Collectors.toList());
            // 文档信息，已有收藏数、评分，缺少讨论数
            List<KnowledgeInfo> knowledgeInfoList = knowledgeService.getNewByStudio(docIds);
            return knowledgeInfoList;
        },60*10);
    }

    @RequestMapping(value = "/get-studio", method = RequestMethod.GET)
//    @Param(name = "property", type = Integer.class, required = true)
    @JSON("*")
    @JSON("*.topics.(id,name,createTime)")
    @JSON("*.newArticle.(id,studioId,businessId,businessName,businessType,createTime)")
    @JSON("*.hotContent.(id,studioId,businessId,businessName,businessType,genseeStatus,status)")
    @JSON("*.organization.(id,name)")
    @JSON("*.(id,name,property,positionalTitle,topics,maxim,introduction,coverId,coverPath,portraitId,portraitPath," +
            "contentNum,popular,createTime," +
            "adminName,attentionNum,topics,attention)")
    public Map<String, Object> getPageStudio() {
        Map<String, Object> resultMap =  articleCache.get("page-studio",()->{
            List<Studio> scientistStudios = studioService.getStudios(Studio.CHIEF_SCIENTIST);
            List<Studio> expertStudios = studioService.getStudios(Studio.CHIEF_EXPERT);
            List<Studio> provincialStudios = studioService.getStudios(Studio.PROVINCIAL_EXPERT);
            List<Studio> studios = Lists.newArrayList();
            studios.addAll(scientistStudios);
            studios.addAll(expertStudios);
            studios.addAll(provincialStudios);
            List<String> studioIds = provincialStudios.stream().map(Studio::getId).collect(Collectors.toList());
            Map<String, List<StudioContent>> hotContent = studioService.getHotContentMapByIds(studioIds);
            List<String> genseeIds = hotContent.values().stream()
                    .flatMap(Collection::stream)
                    .filter(con -> StudioContent.BUSINESS_TYPE_LIVE.equals(con.getBusinessType()))
                    .map(StudioContent::getBusinessId)
                    .collect(Collectors.toList());
            Map<String, Integer> genseeMap = genseeWebCastService.findContentByIds(genseeIds).stream()
                    .collect(Collectors.toMap(GenseeWebCast::getId, GenseeWebCast::getStatus, (p, q) -> p));
            studios.forEach(s->s.setHotContent(hotContent.getOrDefault(s.getId(), Lists.newArrayList()).stream()
                    .peek(con->{
                        Integer genseeStatus = genseeMap.get(con.getBusinessId());
                        con.setGenseeStatus(genseeStatus);
                        con.setStatus(genseeStatus);
                    }).collect(Collectors.toList())));
            Map<String, Object> reqMap = Maps.newHashMap();
            reqMap.put("scientistStudios", studios.stream().filter(f -> f.getProperty().equals(Studio.CHIEF_SCIENTIST)).collect(Collectors.toList()));
            reqMap.put("expertStudios", studios.stream().filter(f -> f.getProperty().equals(Studio.CHIEF_EXPERT)).collect(Collectors.toList()));
            reqMap.put("provincialStudios", studios.stream().filter(f -> f.getProperty().equals(Studio.PROVINCIAL_EXPERT)).collect(Collectors.toList()));
            return reqMap;
        },60*10);
        for (Map.Entry<String,Object> entry : resultMap.entrySet()) {
            List<Studio> valueList = (List<Studio>) entry.getValue();
            for (Studio value : valueList) {
                value.setPortraitPath(generateSecurePathCdn(value.getPortraitPath()));
                value.setCoverPath(generateSecurePathCdn(value.getCoverPath()));
            }
        }
        return resultMap;
    }

    private PagedResult<CourseInfo> buildContentPage(List<CourseInfo> studioList, Integer pageSize) {
        return PagedResult.create(studioList.size() > pageSize?1:0, studioList.stream().limit(pageSize).collect(Collectors.toList()));
    }
}
