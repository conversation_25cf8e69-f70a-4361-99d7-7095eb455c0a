/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.askbar.jooq;


import com.zxy.product.askbar.jooq.tables.*;
import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

import javax.annotation.Generated;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
        value = {
                "http://www.jooq.org",
                "jOOQ version:3.9.6"
        },
        comments = "This class is generated by jOOQ"
)
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class AskBar extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>ask-bar</code>
     */
    public static final AskBar ASK_BAR_SCHEMA = new AskBar();

    /**
     * 问题-专家表
     */
    public final QuestionExpert QUESTION_EXPERT = com.zxy.product.askbar.jooq.tables.QuestionExpert.QUESTION_EXPERT;

    /**
     * Faq库对应答案表
     */
    public final FaqAnswerInfo FAQ_ANSWER_INFO = com.zxy.product.askbar.jooq.tables.FaqAnswerInfo.FAQ_ANSWER_INFO;

    /**
     * The table <code>问题日志/code>.
     */
    public final FaqQuestionLog FAQ_QUESTION_LOG = com.zxy.product.askbar.jooq.tables.FaqQuestionLog.FAQ_QUESTION_LOG;

    /**
     * FAQ库管理表
     */
    public final FaqInfo FAQ_INFO = com.zxy.product.askbar.jooq.tables.FaqInfo.FAQ_INFO;
    /**
     * 举报记录表
     */
    public final Accuse ACCUSE = com.zxy.product.askbar.jooq.tables.Accuse.ACCUSE;

    /**
     * 附件表
     */
    public final Attachment ATTACHMENT = com.zxy.product.askbar.jooq.tables.Attachment.ATTACHMENT;

    /**
     * 关注表
     */
    public final Attention ATTENTION = com.zxy.product.askbar.jooq.tables.Attention.ATTENTION;

    /**
     * 审核表
     */
    public final Audit AUDIT = com.zxy.product.askbar.jooq.tables.Audit.AUDIT;

    /**
     * 讨论表
     */
    public final Discuss DISCUSS = com.zxy.product.askbar.jooq.tables.Discuss.DISCUSS;

    /**
     * 专家表
     */
    public final Expert EXPERT = com.zxy.product.askbar.jooq.tables.Expert.EXPERT;

    /**
     * 专家审核表
     */
    public final ExpertAudit EXPERT_AUDIT = com.zxy.product.askbar.jooq.tables.ExpertAudit.EXPERT_AUDIT;

    /**
     * 专家资质
     */
    public final ExpertQualifications EXPERT_QUALIFICATIONS =
            com.zxy.product.askbar.jooq.tables.ExpertQualifications.EXPERT_QUALIFICATIONS;

    /**
     * 专家话题关联表
     */
    public final ExpertTopic EXPERT_TOPIC = com.zxy.product.askbar.jooq.tables.ExpertTopic.EXPERT_TOPIC;

    /**
     * The table <code>ask-bar.t_grant_detail</code>.
     */
    public final GrantDetail GRANT_DETAIL = com.zxy.product.askbar.jooq.tables.GrantDetail.GRANT_DETAIL;

    /**
     * The table <code>ask-bar.t_member</code>.
     */
    public final Member MEMBER = com.zxy.product.askbar.jooq.tables.Member.MEMBER;

    /**
     * The table <code>ask-bar.t_organization</code>.
     */
    public final Organization ORGANIZATION = com.zxy.product.askbar.jooq.tables.Organization.ORGANIZATION;

    /**
     * The table <code>ask-bar.t_organization_detail</code>.
     */
    public final OrganizationDetail ORGANIZATION_DETAIL =
            com.zxy.product.askbar.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * 点赞表
     */
    public final Praise PRAISE = com.zxy.product.askbar.jooq.tables.Praise.PRAISE;

    /**
     * 问题表
     */
    public final Question QUESTION = com.zxy.product.askbar.jooq.tables.Question.QUESTION;

    /**
     * 热门问题月度排行榜
     */
    public final QuestionMonthList QUESTION_MONTH_LIST =
            com.zxy.product.askbar.jooq.tables.QuestionMonthList.QUESTION_MONTH_LIST;

    /**
     * 问题话题关联表
     */
    public final QuestionTopic QUESTION_TOPIC = com.zxy.product.askbar.jooq.tables.QuestionTopic.QUESTION_TOPIC;

    /**
     * The table <code>ask-bar.t_topic</code>.
     */
    public final Topic TOPIC = com.zxy.product.askbar.jooq.tables.Topic.TOPIC;

    /**
     * The table <code>ask-bar.t_topic_manager</code>.
     */
    public final TopicManager TOPIC_MANAGER = com.zxy.product.askbar.jooq.tables.TopicManager.TOPIC_MANAGER;

    /**
     * 动态表
     */
    public final Trend TREND = com.zxy.product.askbar.jooq.tables.Trend.TREND;

    /**
     * 问题分类表
     */
    public final FaqCategory FAQ_CATEGORY = com.zxy.product.askbar.jooq.tables.FaqCategory.FAQ_CATEGORY;

    /**
     * 口令配置表
     */
    public final FaqCommand FAQ_COMMAND = com.zxy.product.askbar.jooq.tables.FaqCommand.FAQ_COMMAND;

    /**
     * 口令关键词
     */
    public final FaqKey FAQ_KEY = com.zxy.product.askbar.jooq.tables.FaqKey.FAQ_KEY;

    /**
     * 员工直通车问题表
     */
    public final DirectTrainQuestion DIRECT_TRAIN_QUESTION =
            com.zxy.product.askbar.jooq.tables.DirectTrainQuestion.DIRECT_TRAIN_QUESTION;

    /**
     * 专家工作室表
     */
    public final Studio STUDIO = com.zxy.product.askbar.jooq.tables.Studio.STUDIO;

    /**
     * 专家工作室人员关注表
     */
    public final StudioAttention STUDIO_ATTENTION = com.zxy.product.askbar.jooq.tables.StudioAttention.STUDIO_ATTENTION;

    /**
     * 专家工作室内容关联表
     */
    public final StudioContent STUDIO_CONTENT = com.zxy.product.askbar.jooq.tables.StudioContent.STUDIO_CONTENT;

    /**
     * 专家工作室内容审核表
     */
    public final StudioContentAudit STUDIO_CONTENT_AUDIT =
            com.zxy.product.askbar.jooq.tables.StudioContentAudit.STUDIO_CONTENT_AUDIT;

    /**
     * 专家工作室讨论表
     */
    public final StudioDiscuss STUDIO_DISCUSS = com.zxy.product.askbar.jooq.tables.StudioDiscuss.STUDIO_DISCUSS;

    /**
     * 专家工作室人员关联表（成员）
     */
    public final StudioMember STUDIO_MEMBER = com.zxy.product.askbar.jooq.tables.StudioMember.STUDIO_MEMBER;

    /**
     * 专家工作室问答表
     */
    public final StudioQuestion STUDIO_QUESTION = com.zxy.product.askbar.jooq.tables.StudioQuestion.STUDIO_QUESTION;

    /**
     * 专家工作室标签关联表（研究方向）
     */
    public final StudioTopic STUDIO_TOPIC = com.zxy.product.askbar.jooq.tables.StudioTopic.STUDIO_TOPIC;

    /**
     * 相似问
     */
    public final SimilarAsk SIMILAR_ASK = com.zxy.product.askbar.jooq.tables.SimilarAsk.SIMILAR_ASK;


    /**
     * No further instances allowed
     */
    private AskBar() {
        super("ask-bar", null);
    }


    /**
     * The table <code>ask-bar.t_chatting_records</code>.
     */
    public final ChattingRecords CHATTING_RECORDS = com.zxy.product.askbar.jooq.tables.ChattingRecords.CHATTING_RECORDS;

    /**
     * 问题评价表
     */
    public final IssueEvaluate ISSUE_EVALUATE = com.zxy.product.askbar.jooq.tables.IssueEvaluate.ISSUE_EVALUATE;

    /**
     * 虚拟空间专家表
     */
    public final ExpertVirtualSpace EXPERT_VIRTUAL_SPACE = com.zxy.product.askbar.jooq.tables.ExpertVirtualSpace.EXPERT_VIRTUAL_SPACE;
    /**
     * {@inheritDoc}
     */
    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        List result = new ArrayList();
        result.addAll(getTables0());
        return result;
    }

    private final List<Table<?>> getTables0() {
        return Arrays.<Table<?>>asList(
                QuestionExpert.QUESTION_EXPERT,
                FaqAnswerInfo.FAQ_ANSWER_INFO,
                FaqQuestionLog.FAQ_QUESTION_LOG,
                FaqInfo.FAQ_INFO,
                Accuse.ACCUSE,
                Attachment.ATTACHMENT,
                Attention.ATTENTION,
                Audit.AUDIT,
                Discuss.DISCUSS,
                Expert.EXPERT,
                ExpertAudit.EXPERT_AUDIT,
                ExpertQualifications.EXPERT_QUALIFICATIONS,
                ExpertTopic.EXPERT_TOPIC,
                GrantDetail.GRANT_DETAIL,
                Member.MEMBER,
                Organization.ORGANIZATION,
                OrganizationDetail.ORGANIZATION_DETAIL,
                Praise.PRAISE,
                Question.QUESTION,
                QuestionMonthList.QUESTION_MONTH_LIST,
                QuestionTopic.QUESTION_TOPIC,
                Topic.TOPIC,
                TopicManager.TOPIC_MANAGER,
                Trend.TREND,
                FaqCategory.FAQ_CATEGORY,
                FaqKey.FAQ_KEY,
                FaqCommand.FAQ_COMMAND,
                DirectTrainQuestion.DIRECT_TRAIN_QUESTION,
                ChattingRecords.CHATTING_RECORDS,
                Studio.STUDIO,
                StudioAttention.STUDIO_ATTENTION,
                StudioContent.STUDIO_CONTENT,
                StudioContentAudit.STUDIO_CONTENT_AUDIT,
                StudioDiscuss.STUDIO_DISCUSS,
                StudioMember.STUDIO_MEMBER,
                StudioQuestion.STUDIO_QUESTION,
                StudioTopic.STUDIO_TOPIC,
                SimilarAsk.SIMILAR_ASK,
                IssueEvaluate.ISSUE_EVALUATE,
                ExpertVirtualSpace.EXPERT_VIRTUAL_SPACE
                );
    }
}
