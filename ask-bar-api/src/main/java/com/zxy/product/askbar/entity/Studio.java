package com.zxy.product.askbar.entity;


import com.zxy.product.askbar.jooq.tables.pojos.StudioEntity;

import java.io.Serializable;
import java.util.List;

public class Studio extends StudioEntity {

    public static final String MENU_URL = "studio/studio-manage";
    //0：禁用，1：启用
    public static final int STATUS_DISABLE = 0;
    public static final int STATUS_ENABLE = 1;

    //0：隐藏，1：显示
    public static final int HIDE_DISABLE = 0;
    public static final int HIDE_ENABLE = 1;

    // 推荐状态 1：推荐
    public static final int RECOMMEND_NO = 0;
    public static final int RECOMMEND_YES = 1;

    //1：首席科学家 2：首席专家 3：省级专家 4：其他专家
    public static final int CHIEF_SCIENTIST = 1;
    public static final int CHIEF_EXPERT = 2;
    public static final int PROVINCIAL_EXPERT = 3;
    public static final int OTHER_EXPERTS = 4;

    // 是否删除(0：否,1：是)
    public static final Integer DELETE_FLAG_YES = 1;
    public static final Integer DELETE_FLAG_NO = 0;

    public static final String SEPARATOR = ",";
    public static final int MEMBER_MAX_SIZE = 15;
    public static final int PAGE = 1;
    public static final int PAGE_SIZE = 10;
    public static final int MAX_RECOMMEND = 10;
    // 首页工作室过期时间
    public static final int EXPIRE_TIME = 5 * 60;
    private String organizationName;
    private String adminName;
    private Long attentionNum; // 关注
    private Long praiseNum; // 点赞
    private Long collectNum;//收藏数
    private Long browseNum;//浏览数
    private Integer attention;//是否已关注：0否，1是
    //研究方向
    private List<Topic> topics;
    private List<StudioMember> memberList;
    private Praise praise;
    private Boolean enableFlag; // 是否可用
    private StudioMember adminMember;

    public Long getCollectNum() {
        return collectNum;
    }

    public void setCollectNum(Long collectNum) {
        this.collectNum = collectNum;
    }

    public Long getBrowseNum() {
        return browseNum;
    }

    public void setBrowseNum(Long browseNum) {
        this.browseNum = browseNum;
    }

    public Integer getAttention() {
        return attention;
    }

    public void setAttention(Integer attention) {
        this.attention = attention;
    }

    public StudioMember getAdminMember() {
        return adminMember;
    }

    public void setAdminMember(StudioMember adminMember) {
        this.adminMember = adminMember;
    }

    public List<StudioMember> getMemberList() {
        return memberList;
    }

    public void setMemberList(List<StudioMember> memberList) {
        this.memberList = memberList;
    }

    public Boolean getEnableFlag() {
        return enableFlag;
    }

    public void setEnableFlag(Boolean enableFlag) {
        this.enableFlag = enableFlag;
    }

    public Praise getPraise() {
        return praise;
    }

    public void setPraise(Praise praise) {
        this.praise = praise;
    }

    public List<Topic> getTopics() {
        return topics;
    }

    public void setTopics(List<Topic> topics) {
        this.topics = topics;
    }

    public Long getAttentionNum() {
        return attentionNum;
    }

    public void setAttentionNum(Long attentionNum) {
        this.attentionNum = attentionNum;
    }

    public Long getPraiseNum() {
        return praiseNum;
    }

    public void setPraiseNum(Long praiseNum) {
        this.praiseNum = praiseNum;
    }

    private Organization organization;
    // 最新发布的文章
    private StudioContent newArticle;
    // 热门内容
    private List<StudioContent> hotContent;

    public List<StudioContent> getHotContent() {
        return hotContent;
    }

    public void setHotContent(List<StudioContent> hotContent) {
        this.hotContent = hotContent;
    }

    public StudioContent getNewArticle() {
        return newArticle;
    }

    public void setNewArticle(StudioContent newArticle) {
        this.newArticle = newArticle;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }


    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    /**
     * 首页专家工作室——标签名称（十万）
     */
    private String topicName;

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    /**
     * 工作室首页缓存
     */
    public static String homeStudioCacheKey() {
        return String.format("studio#page=%d#pageSize=%d", PAGE, PAGE_SIZE);
    }


    /**首页配置块：专家工作室*/
    public static class StudioExpertChild implements Serializable {
        private static final long serialVersionUID = 9213887626214336558L;
        /**首页配置块：专家工作室Id*/
        private String id;
        /**首页配置块：专家工作室讲师姓名*/
        private String adminName;
        /**首页配置块：专家工作室讲师图片Path*/
        private String coverPath;
        /**首页配置块：专家工作室专家对应*/
        private String topicName;
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getAdminName() { return adminName; }
        public void setAdminName(String adminName) { this.adminName = adminName; }
        public String getCoverPath() { return coverPath; }
        public void setCoverPath(String coverPath) { this.coverPath = coverPath; }
        public String getTopicName() { return topicName; }
        public void setTopicName(String topicName) { this.topicName = topicName; }
        @Override
        public String toString() {
            return "StudioExpertChild{" +
                    "id='" + id + '\'' +
                    ", adminName='" + adminName + '\'' +
                    ", coverPath='" + coverPath + '\'' +
                    ", topicName='" + topicName + '\'' +
                    '}';
        }
    }

    /**首页配置块：专家工作室成员属性（标签与专家相关）*/
    public static class StudioTopicAttribute implements Serializable {
        private static final long serialVersionUID = -8486878836334145007L;
        /**首页配置块：专家工作室成员属性（标签与专家相关）——主键*/
        private String studioId;
        /**首页配置块：专家工作室成员属性（标签与专家相关）——创建时间*/
        private Long createTime;
        /**首页配置块：专家工作室成员属性（标签与专家相关）——标签*/
        private TopicAttribute topicAttribute;
        public String getStudioId() { return studioId; }
        public void setStudioId(String studioId) { this.studioId = studioId; }
        public Long getCreateTime() { return createTime; }
        public void setCreateTime(Long createTime) { this.createTime = createTime; }
        public TopicAttribute getTopicAttribute() { return topicAttribute; }
        public void setTopicAttribute(TopicAttribute topicAttribute) { this.topicAttribute = topicAttribute; }
        @Override
        public String toString() {
            return "StudioTopicAttribute{" +
                    "studioId='" + studioId + '\'' +
                    ", createTime=" + createTime +
                    '}';
        }
    }

    /**首页配置块：专家工作室成员属性（标签相关）*/
    public static class TopicAttribute implements Serializable {
        private static final long serialVersionUID = 3688162729866185710L;
        /**标签Id*/
        private String id;
        /**标签名称*/
        private String name;
        /**标签创建时间*/
        private Long createTime;
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Long getCreateTime() { return createTime; }
        public void setCreateTime(Long createTime) { this.createTime = createTime; }
        @Override
        public String toString() {
            return "TopicAttribute{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    ", createTime=" + createTime +
                    '}';
        }
    }

    public enum ClientType {
        ADMIN(0),     // 管理端
        STUDENT(1);   // 学员端

        private final int value;

        ClientType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

}

