/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.askbar.jooq.tables;


import com.zxy.product.askbar.jooq.AskBar;
import com.zxy.product.askbar.jooq.Keys;
import com.zxy.product.askbar.jooq.tables.records.MemberRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Member extends TableImpl<MemberRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>ask-bar.t_member</code>
     */
    public static final Member MEMBER = new Member();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MemberRecord> getRecordType() {
        return MemberRecord.class;
    }

    /**
     * The column <code>ask-bar.t_member.f_id</code>. ID
     */
    public final TableField<MemberRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>ask-bar.t_member.f_name</code>. 人员名称
     */
    public final TableField<MemberRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(20), this, "人员名称");

    /**
     * The column <code>ask-bar.t_member.f_password</code>. 密码
     */
    public final TableField<MemberRecord, String> PASSWORD = createField("f_password", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "密码");

    /**
     * The column <code>ask-bar.t_member.f_organization_id</code>. 组织ID
     */
    public final TableField<MemberRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "组织ID");

    /**
     * The column <code>ask-bar.t_member.f_create_time</code>. 创建时间
     */
    public final TableField<MemberRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>ask-bar.t_member.f_full_name</code>. 姓名
     */
    public final TableField<MemberRecord, String> FULL_NAME = createField("f_full_name", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "姓名");

    /**
     * The column <code>ask-bar.t_member.f_ihr_code</code>. ihr新员工编码
     */
    public final TableField<MemberRecord, String> IHR_CODE = createField("f_ihr_code", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "ihr新员工编码");

    /**
     * The column <code>ask-bar.t_member.f_major_position_id</code>. 职位
     */
    public final TableField<MemberRecord, String> MAJOR_POSITION_ID = createField("f_major_position_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "职位");

    /**
     * The column <code>ask-bar.t_member.f_job_id</code>. ihr新职务id
     */
    public final TableField<MemberRecord, String> JOB_ID = createField("f_job_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "ihr新职务id");

    /**
     * The column <code>ask-bar.t_member.f_email</code>.
     */
    public final TableField<MemberRecord, String> EMAIL = createField("f_email", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "");

    /**
     * The column <code>ask-bar.t_member.f_phone_number</code>. 手机号
     */
    public final TableField<MemberRecord, String> PHONE_NUMBER = createField("f_phone_number", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "手机号");

    /**
     * The column <code>ask-bar.t_member.f_status</code>. 人员状态
     */
    public final TableField<MemberRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "人员状态");

    /**
     * The column <code>ask-bar.t_member.f_sex</code>. 性别
     */
    public final TableField<MemberRecord, Integer> SEX = createField("f_sex", org.jooq.impl.SQLDataType.INTEGER, this, "性别");

    /**
     * The column <code>ask-bar.t_member.f_head_portrait</code>. 用户头像id
     */
    public final TableField<MemberRecord, String> HEAD_PORTRAIT = createField("f_head_portrait", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户头像id");

    /**
     * The column <code>ask-bar.t_member.f_major_position_name</code>.
     */
    public final TableField<MemberRecord, String> MAJOR_POSITION_NAME = createField("f_major_position_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "");

    /**
     * The column <code>ask-bar.t_member.f_head_portrait_path</code>. 头像路径
     */
    public final TableField<MemberRecord, String> HEAD_PORTRAIT_PATH = createField("f_head_portrait_path", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "头像路径");

    /**
     * The column <code>ask-bar.t_member.f_company_id</code>. 表示离当前用户所在组织的最近的4层内组织id
     */
    public final TableField<MemberRecord, String> COMPANY_ID = createField("f_company_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "表示离当前用户所在组织的最近的4层内组织id");

    /**
     * Create a <code>ask-bar.t_member</code> table reference
     */
    public Member() {
        this("t_member", null);
    }

    /**
     * Create an aliased <code>ask-bar.t_member</code> table reference
     */
    public Member(String alias) {
        this(alias, MEMBER);
    }

    private Member(String alias, Table<MemberRecord> aliased) {
        this(alias, aliased, null);
    }

    private Member(String alias, Table<MemberRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return AskBar.ASK_BAR_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MemberRecord> getPrimaryKey() {
        return Keys.KEY_T_MEMBER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MemberRecord>> getKeys() {
        return Arrays.<UniqueKey<MemberRecord>>asList(Keys.KEY_T_MEMBER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Member as(String alias) {
        return new Member(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Member rename(String name) {
        return new Member(name, null);
    }
}
