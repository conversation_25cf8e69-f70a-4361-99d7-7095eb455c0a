package com.zxy.product.askbar.entity;

import com.zxy.product.askbar.jooq.tables.pojos.AccuseEntity;

public class Accuse extends AccuseEntity {

    private static final long serialVersionUID = -3858570382549080584L;

    // 举报业务类型:1问题 2文章 3分享 4讨论 5回复
    public static final int BUSINESS_TYPE_QUESTION = 1;
    public static final int BUSINESS_TYPE_ARTICLE = 2;
    public static final int BUSINESS_TYPE_SHARE = 3;
    public static final int BUSINESS_TYPE_DISCUSS = 4;
    public static final int BUSINESS_TYPE_REPLY = 5;
    // 审核状态:0待审核 1通过 2拒绝
    public static final int AUDIT_STATUS_WAIT = 0;
    public static final int AUDIT_STATUS_PASS = 1;
    public static final int AUDIT_STATUS_REJECT = 2;

    private Member accuseMember;//举报人

    public Member getAccuseMember() {
        return accuseMember;
    }

    public void setAccuseMember(Member accuseMember) {
        this.accuseMember = accuseMember;
    }
}
