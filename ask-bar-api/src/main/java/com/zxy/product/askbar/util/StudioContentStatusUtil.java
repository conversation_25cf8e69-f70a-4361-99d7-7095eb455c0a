package com.zxy.product.askbar.util;

import com.zxy.product.askbar.entity.StudioContent;
import com.zxy.product.askbar.entity.StudioContentAudit;
import org.jooq.Condition;
import org.jooq.impl.DSL;

import java.util.Optional;

import static com.zxy.product.askbar.jooq.Tables.STUDIO_CONTENT;
import static com.zxy.product.askbar.jooq.Tables.STUDIO_CONTENT_AUDIT;

/**
 * <AUTHOR>
 * @date 2021/11/2
 */
public class StudioContentStatusUtil {

    // 业务代码使用状态
    private static final int B_STATUS_WAIT = 1;
    private static final int B_STATUS_REFUSE = 2;
    private static final int B_STATUS_RELEASE = 3;
    private static final int B_STATUS_NOT_RELEASE = 4;
    private static final int B_STATUS_RELEASE_NOW  = 5;

    public static Integer parseStatus(Optional<Integer> inputStatus, Integer status, Integer auditStatus) {
        if (inputStatus.isPresent() && inputStatus.get().equals(B_STATUS_NOT_RELEASE)) {
            return B_STATUS_NOT_RELEASE;
        }
        if (StudioContentAudit.AUDIT_STATUS_WAIT.equals(auditStatus)) {
            return B_STATUS_WAIT;
        }
        if (StudioContentAudit.AUDIT_STATUS_REFUSE.equals(auditStatus)) {
            return B_STATUS_REFUSE;
        }
        if (StudioContent.STATUS_RELEASE.equals(status)) {
            return B_STATUS_RELEASE;
        }
        if (StudioContent.STATUS_NOT_RELEASE.equals(status)) {
            return B_STATUS_NOT_RELEASE;
        }
        if (StudioContent.STATUS_PUBLISHING.equals(status)) {
            return B_STATUS_RELEASE_NOW;
        }
        return null;
    }

    public static Condition buildConditionByStatus(Integer status) {
        // 1待审核，2已拒绝，3已发布，4未发布
        switch (status) {
            case B_STATUS_WAIT:
                return STUDIO_CONTENT_AUDIT.AUDIT_STATUS.eq(StudioContentAudit.AUDIT_STATUS_WAIT);
            case B_STATUS_REFUSE:
                return STUDIO_CONTENT_AUDIT.AUDIT_STATUS.eq(StudioContentAudit.AUDIT_STATUS_REFUSE);
                //未发布状态 不包含已拒绝和待审核
            case B_STATUS_NOT_RELEASE:
                return STUDIO_CONTENT.STATUS.eq(StudioContent.STATUS_NOT_RELEASE).and(STUDIO_CONTENT_AUDIT.AUDIT_STATUS.isNull());
            case B_STATUS_RELEASE:
                return STUDIO_CONTENT.STATUS.eq(StudioContent.STATUS_RELEASE);
            case B_STATUS_RELEASE_NOW:
                return STUDIO_CONTENT.STATUS.eq(StudioContent.STATUS_PUBLISHING);
            default:
                return DSL.trueCondition();
        }
    }
}
