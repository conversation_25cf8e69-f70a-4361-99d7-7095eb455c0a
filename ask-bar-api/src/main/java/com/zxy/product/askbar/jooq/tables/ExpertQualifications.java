/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.askbar.jooq.tables;


import com.zxy.product.askbar.jooq.AskBar;
import com.zxy.product.askbar.jooq.Keys;
import com.zxy.product.askbar.jooq.tables.records.ExpertQualificationsRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 专家资质
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExpertQualifications extends TableImpl<ExpertQualificationsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>ask-bar.t_expert_qualifications</code>
     */
    public static final ExpertQualifications EXPERT_QUALIFICATIONS = new ExpertQualifications();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExpertQualificationsRecord> getRecordType() {
        return ExpertQualificationsRecord.class;
    }

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_id</code>. 主键id
     */
    public final TableField<ExpertQualificationsRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键id");

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_content</code>. 内容
     */
    public final TableField<ExpertQualificationsRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB, this, "内容");

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_attach_id</code>. 附件id
     */
    public final TableField<ExpertQualificationsRecord, String> ATTACH_ID = createField("f_attach_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "附件id");

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_attach_name</code>. 附件名称
     */
    public final TableField<ExpertQualificationsRecord, String> ATTACH_NAME = createField("f_attach_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "附件名称");

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_attach_path</code>. 附件地址
     */
    public final TableField<ExpertQualificationsRecord, String> ATTACH_PATH = createField("f_attach_path", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "附件地址");

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_attach_type</code>. 附件类型: 0-视频 1-音频 2-word 3-pdf 4-excel 5-ppt 6-epub 7-txt 8-压缩包(rar,zip)
     */
    public final TableField<ExpertQualificationsRecord, Integer> ATTACH_TYPE = createField("f_attach_type", org.jooq.impl.SQLDataType.INTEGER, this, "附件类型: 0-视频 1-音频 2-word 3-pdf 4-excel 5-ppt 6-epub 7-txt 8-压缩包(rar,zip)");

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_attach_content_type</code>. 附件content-type
     */
    public final TableField<ExpertQualificationsRecord, String> ATTACH_CONTENT_TYPE = createField("f_attach_content_type", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "附件content-type");

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_organization_id</code>. 组织id
     */
    public final TableField<ExpertQualificationsRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织id");

    /**
     * The column <code>ask-bar.t_expert_qualifications.f_create_time</code>. 修改时间
     */
    public final TableField<ExpertQualificationsRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "修改时间");

    /**
     * Create a <code>ask-bar.t_expert_qualifications</code> table reference
     */
    public ExpertQualifications() {
        this("t_expert_qualifications", null);
    }

    /**
     * Create an aliased <code>ask-bar.t_expert_qualifications</code> table reference
     */
    public ExpertQualifications(String alias) {
        this(alias, EXPERT_QUALIFICATIONS);
    }

    private ExpertQualifications(String alias, Table<ExpertQualificationsRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExpertQualifications(String alias, Table<ExpertQualificationsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "专家资质");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return AskBar.ASK_BAR_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExpertQualificationsRecord> getPrimaryKey() {
        return Keys.KEY_T_EXPERT_QUALIFICATIONS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExpertQualificationsRecord>> getKeys() {
        return Arrays.<UniqueKey<ExpertQualificationsRecord>>asList(Keys.KEY_T_EXPERT_QUALIFICATIONS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExpertQualifications as(String alias) {
        return new ExpertQualifications(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExpertQualifications rename(String name) {
        return new ExpertQualifications(name, null);
    }
}
