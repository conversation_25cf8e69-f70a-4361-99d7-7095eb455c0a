package com.zxy.product.askbar.entity;

import com.zxy.product.askbar.jooq.tables.pojos.FaqInfoEntity;

/**
 * @ProjectName: ask-bar
 * @Package: com.zxy.product.askbar.entity
 * @ClassName: FaqInfo
 * @Author: futzh
 * @Description: 问答管理实体类
 * @Date: 2020/11/25 9:51
 * @Version: 1.0
 */
public class FaqInfo extends FaqInfoEntity {

    private static final long serialVersionUID = 669763827584092610L;

    public static Integer IS_PUBLISHED = 1;

    public static Integer IS_NOT_PUBLISH = 0;

    public static Integer IS_ANSWERED = 1;

    public static Integer IS_NOT_ANSWER = 0;

    private String answer;

    private String typeName;

    private String memberFullName; //提问人

    private String memberName;//提问人编号

    public String getMemberFullName() {
        return memberFullName;
    }

    public void setMemberFullName(String memberFullName) {
        this.memberFullName = memberFullName;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }
}
