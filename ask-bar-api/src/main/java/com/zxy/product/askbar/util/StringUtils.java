package com.zxy.product.askbar.util;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Optional;

/**
 * Created by keeley on 16/10/17.
 */
public interface StringUtils {
    static long dateString2OptionalLong(String t) {
        return Date.from(LocalDate.parse(t).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }
    static Optional<Long> dateString2OptionalLong(Optional<String> value) {
        return value.map(StringUtils::dateString2OptionalLong);
    }
    static String long2Date(Long date){
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(date));
    }
    static boolean blank(String text){
        if(text==null|| "".equals(text)){
            return true;
        }else {
            return false;
        }
    }
}
