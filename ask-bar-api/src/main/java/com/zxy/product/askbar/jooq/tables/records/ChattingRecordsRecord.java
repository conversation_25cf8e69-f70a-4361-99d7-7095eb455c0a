/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.askbar.jooq.tables.records;


import com.zxy.product.askbar.jooq.tables.ChattingRecords;
import com.zxy.product.askbar.jooq.tables.interfaces.IChattingRecords;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChattingRecordsRecord extends UpdatableRecordImpl<ChattingRecordsRecord> implements Record13<String, String, String, Integer, Long, Integer, Integer, String, Integer, Integer, String, String, String>, IChattingRecords {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_text</code>. 问题以及答案
     */
    @Override
    public void setText(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_text</code>. 问题以及答案
     */
    @Override
    public String getText() {
        return (String) get(2);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_role</code>. 角色:1客服,2用户
     */
    @Override
    public void setRole(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_role</code>. 角色:1客服,2用户
     */
    @Override
    public Integer getRole() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_creation_time</code>. 创建时间
     */
    @Override
    public void setCreationTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_creation_time</code>. 创建时间
     */
    @Override
    public Long getCreationTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_order</code>. 问题顺序
     */
    @Override
    public void setOrder(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_order</code>. 问题顺序
     */
    @Override
    public Integer getOrder() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_evaluate_type</code>. 评价,1=赞,2=踩
     */
    @Override
    public void setEvaluateType(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_evaluate_type</code>. 评价,1=赞,2=踩
     */
    @Override
    public Integer getEvaluateType() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_suggest</code>. 多轮对话
     */
    @Override
    public void setSuggest(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_suggest</code>. 多轮对话
     */
    @Override
    public String getSuggest() {
        return (String) get(7);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_text_type</code>. 文本类型 1=文本,2=图片
     */
    @Override
    public void setTextType(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_text_type</code>. 文本类型 1=文本,2=图片
     */
    @Override
    public Integer getTextType() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_guided_dialogue</code>. 是否是引导式对话1=是
     */
    @Override
    public void setGuidedDialogue(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_guided_dialogue</code>. 是否是引导式对话1=是
     */
    @Override
    public Integer getGuidedDialogue() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_img_type</code>. 图片类型
     */
    @Override
    public void setImgType(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_img_type</code>. 图片类型
     */
    @Override
    public String getImgType() {
        return (String) get(10);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_img_url</code>. 图片地址
     */
    @Override
    public void setImgUrl(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_img_url</code>. 图片地址
     */
    @Override
    public String getImgUrl() {
        return (String) get(11);
    }

    /**
     * Setter for <code>ask-bar.t_chatting_records.f_data</code>.
     */
    @Override
    public void setData(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>ask-bar.t_chatting_records.f_data</code>.
     */
    @Override
    public String getData() {
        return (String) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, String, String, Integer, Long, Integer, Integer, String, Integer, Integer, String, String, String> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, String, String, Integer, Long, Integer, Integer, String, Integer, Integer, String, String, String> valuesRow() {
        return (Row13) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ChattingRecords.CHATTING_RECORDS.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ChattingRecords.CHATTING_RECORDS.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ChattingRecords.CHATTING_RECORDS.TEXT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return ChattingRecords.CHATTING_RECORDS.ROLE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return ChattingRecords.CHATTING_RECORDS.CREATION_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return ChattingRecords.CHATTING_RECORDS.ORDER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return ChattingRecords.CHATTING_RECORDS.EVALUATE_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return ChattingRecords.CHATTING_RECORDS.SUGGEST;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return ChattingRecords.CHATTING_RECORDS.TEXT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return ChattingRecords.CHATTING_RECORDS.GUIDED_DIALOGUE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return ChattingRecords.CHATTING_RECORDS.IMG_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return ChattingRecords.CHATTING_RECORDS.IMG_URL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field13() {
        return ChattingRecords.CHATTING_RECORDS.DATA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getText();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getRole();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreationTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getOrder();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getEvaluateType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getSuggest();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getTextType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getGuidedDialogue();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getImgType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getImgUrl();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value13() {
        return getData();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value3(String value) {
        setText(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value4(Integer value) {
        setRole(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value5(Long value) {
        setCreationTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value6(Integer value) {
        setOrder(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value7(Integer value) {
        setEvaluateType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value8(String value) {
        setSuggest(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value9(Integer value) {
        setTextType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value10(Integer value) {
        setGuidedDialogue(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value11(String value) {
        setImgType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value12(String value) {
        setImgUrl(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord value13(String value) {
        setData(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChattingRecordsRecord values(String value1, String value2, String value3, Integer value4, Long value5, Integer value6, Integer value7, String value8, Integer value9, Integer value10, String value11, String value12, String value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IChattingRecords from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setText(from.getText());
        setRole(from.getRole());
        setCreationTime(from.getCreationTime());
        setOrder(from.getOrder());
        setEvaluateType(from.getEvaluateType());
        setSuggest(from.getSuggest());
        setTextType(from.getTextType());
        setGuidedDialogue(from.getGuidedDialogue());
        setImgType(from.getImgType());
        setImgUrl(from.getImgUrl());
        setData(from.getData());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IChattingRecords> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ChattingRecordsRecord
     */
    public ChattingRecordsRecord() {
        super(ChattingRecords.CHATTING_RECORDS);
    }

    /**
     * Create a detached, initialised ChattingRecordsRecord
     */
    public ChattingRecordsRecord(String id, String memberId, String text, Integer role, Long creationTime, Integer order, Integer evaluateType, String suggest, Integer textType, Integer guidedDialogue, String imgType, String imgUrl, String data) {
        super(ChattingRecords.CHATTING_RECORDS);

        set(0, id);
        set(1, memberId);
        set(2, text);
        set(3, role);
        set(4, creationTime);
        set(5, order);
        set(6, evaluateType);
        set(7, suggest);
        set(8, textType);
        set(9, guidedDialogue);
        set(10, imgType);
        set(11, imgUrl);
        set(12, data);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.askbar.jooq.tables.pojos.ChattingRecordsEntity)) {
            return false;
        }
        com.zxy.product.askbar.jooq.tables.pojos.ChattingRecordsEntity pojo = (com.zxy.product.askbar.jooq.tables.pojos.ChattingRecordsEntity)source;
        pojo.into(this);
        return true;
    }
}
