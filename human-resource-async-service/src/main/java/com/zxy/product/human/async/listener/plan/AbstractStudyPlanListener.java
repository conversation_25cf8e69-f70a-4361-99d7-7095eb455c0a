package com.zxy.product.human.async.listener.plan;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.course.entity.AudienceItem;
import com.zxy.product.course.entity.AudienceObject;
import com.zxy.product.human.api.plan.StudyPlanService;
import com.zxy.product.human.content.MessageHeaderContent;
import com.zxy.product.human.content.MessageTypeContent;
import com.zxy.product.human.content.RedisKeys;
import com.zxy.product.human.entity.StudyPlan;
import com.zxy.product.human.util.DateUtil;
import org.jooq.*;
import org.jooq.impl.TableImpl;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Classname AbstractStudyPlanListener
 * @Description TODO
 * @Date 2022/6/27 14:49
 * @Created by ykn
 */
public abstract class AbstractStudyPlanListener extends AbstractMessageListener {

    private static final Logger logger = LoggerFactory.getLogger(AbstractStudyPlanListener.class);
    public static final String BUSINESS_TYPE_SUBJECT_STUDY_PLAN = "8";

    protected static final Integer PAGE_SIZE = 100;//单次查询受众数量

    private Cache cache;
    private Cache courseStudyPlanCache;
    private CommonDao<StudyPlan.StudyPlan_00> dao;
    private RedissonClient redissonClient;
    protected StudyPlanService studyPlanService;
    private TransactionTemplate template;
    private DSLContext dslContext;

    @Autowired
    public void setDao(CommonDao<StudyPlan.StudyPlan_00> dao) {
        this.dao = dao;
    }

    @Autowired
    private void setDslContext(DSLContext dslContext){
        this.dslContext=dslContext;
    }

    @Autowired
    public void setCacheService(CacheService cacheService){
        this.cache=cacheService.create("course-study-plan","check-mq");
        this.courseStudyPlanCache=cacheService.create("course-study-plan","check-business");
    }

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }
    @Autowired
    public void setStudyPlanService(StudyPlanService studyPlanService) {
        this.studyPlanService = studyPlanService;
    }
    @Autowired
    public void setTemplate(TransactionTemplate template) {
        this.template = template;
    }

    @Override
    protected void onMessage(Message message) {
        int messageType = message.getType();
        logger.info("学习计划当前消息体{}",message);
        switch (messageType) {
            case MessageTypeContent.COURSE_STUDY_PLAN_CONFIG_INSERT:
            case com.zxy.product.train.content.MessageTypeContent.TRAIN_STUDY_PLAN_CONFIG_INSERT:
            case com.zxy.product.exam.content.MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_INSERT:
            case com.zxy.product.exam.content.MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_ACTIVITY_INSERT:
//            case MessageTypeContent.MEMBERS_STUDY_PLAN_UPDATE:
                handleInsert(message);
                break;
            case MessageTypeContent.COURSE_STUDY_PLAN_CONFIG_UPDATE:
            case com.zxy.product.exam.content.MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_UPDATE:
                handleConfigUpdate(message);
                break;
            case MessageTypeContent.COURSE_STUDY_PLAN_CONFIG_REVOKE:
            case com.zxy.product.exam.content.MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_REVOKE:
            case com.zxy.product.exam.content.MessageTypeContent.EXAM_STUDY_PLAN_CONFIG_ACTIVITY_REVOKE:
            case com.zxy.product.train.content.MessageTypeContent.TRAIN_STUDY_PLAN_CONFIG_REVOKE:
                handleDelete(message);
                break;
            case com.zxy.product.exam.content.MessageTypeContent.EXAM_STUDY_PLAN_UPDATE:
            case com.zxy.product.exam.content.MessageTypeContent.EXAM_ACTIVITY_STUDY_PLAN_UPDATE:
            case com.zxy.product.train.content.MessageTypeContent.TRAIN_STUDY_PLAN_UPDATE:
            case MessageTypeContent.COURSE_STUDY_PLAN_UPDATE:
                handleUpdate(message);
                break;
            case MessageTypeContent.COURSE_STUDY_PLAN_CONFIG_HANDLE_FAIL:
            case MessageTypeContent.EXAM_STUDY_PLAN_HANDLE_FAIL:
            case MessageTypeContent.EXAM_ACTIVITY_STUDY_PLAN_HANDLE_FAIL:
            case MessageTypeContent.TRAIN_STUDY_PLAN_HANDLE_FAIL:
                processHandleFail(message);
                break;
            default: break;
        }
    }

    /**
     * 新增学习计划
     * @param message 消息体数据
     */
    private void handleInsert(Message message) {
        String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
        if(!Strings.isNullOrEmpty(businessId)){
            String value = cache.get(businessId, String.class);
            if(!Strings.isNullOrEmpty(value)){
                logger.error("新增学习计划推送过于频繁，请稍后重试，当前业务Id{}",businessId);
                return;
            }
            cache.set(businessId,"processing",2*3600);
            this.processStudyPlanInsert(message);
            cache.clear(businessId);
            courseStudyPlanCache.clear(businessId);
        }
    }

    /**
     * 更新学习计划
     * @param message 消息体数据
     */
    private void handleConfigUpdate(Message message) {
        String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
        if(!Strings.isNullOrEmpty(businessId)){
            String value = cache.get(businessId, String.class);
            if(!Strings.isNullOrEmpty(value)){
                logger.error("更新学习计划推送过于频繁，请稍后重试，当前业务Id{}",businessId);
                return;
            }
            cache.set(businessId,"processing",2*3600);
            this.processStudyPlanConfigUpdate(message);
            cache.clear(businessId);
            courseStudyPlanCache.clear(businessId);
        }
    }

    private void handleDelete(Message message) {
        String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
        RLock lock = redissonClient.getLock(String.join("#", RedisKeys.STUDY_PLAN_DELETE_LOCK, businessId));
        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                this.processStudyPlanDelete(message);
            }
        } catch (Exception e) {
            logger.error("学习计划撤销失败 businessId:{},错误:{}",businessId, e);
        } finally {
            courseStudyPlanCache.clear(businessId);
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    private void handleUpdate(Message message) {
        String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        try {
                this.processStudyPlanUpdate(message);
        } catch (Exception e) {
            logger.error("学习计划更新失败 businessId:{},memberId:{},错误:{}",businessId,memberId,e);
        }
    }



    protected abstract void processStudyPlanInsert(Message message);

    protected abstract void processStudyPlanConfigUpdate(Message message);

    protected abstract void processStudyPlanDelete(Message message);

    protected abstract void processStudyPlanUpdate(Message message);

    protected abstract void processHandleFail(Message message);

    protected void update(String memberId, String businessId, Optional<Long> completeTime,Optional<Integer> finishStatus,Optional<Integer> rate){
        studyPlanService.update(memberId,businessId,completeTime,finishStatus,rate);
    }

    /**
     * 根据业务id 批量更改相关配置
     * @param businessId 业务id
     * @param businessName 业务名称
     * @param applicationStartTime 报名开始时间
     * @param applicationEndTime 报名结束时间
     * @param startTime 开始时间
     * @param deadLine 截止时间
     * @param remindStatus 提醒状态
     * @param remindType 提示类型
     * @param remindTime 提醒时间
     */
    protected void batchUpdateByBusinessId(String businessId, Optional<String> businessName, Optional<Long> applicationStartTime, Optional<Long> applicationEndTime, Optional<Long> startTime, Optional<Long> deadLine, Optional<Integer> remindStatus, Optional<String> remindType, Optional<Long> remindTime) {
        logger.info("根据业务id 批量更新开始:{}", DateUtil.dateLongToString(Instant.now().toEpochMilli(),DateUtil.YYYY_MM_DD_HH_MM_SS));
        template.execute(new TransactionCallbackWithoutResult(){
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                Map<String, StudyPlan.JooqFields> allJooqs = StudyPlan.getAllStudyPlanTable();
                allJooqs.values().forEach(jooq->{
                    int offset = 0;
                    Integer sum = dao.execute(dsl -> dsl.selectCount().from(jooq.table).where(jooq.BUSINESS_ID.eq(businessId)).fetchOne().value1());
                    while (offset < sum) {
                        int finalOffset = offset;
                        List<String> ids = dao.execute(dsl -> dsl.select(jooq.ID, jooq.MEMBER_ID).from(jooq.table).where(jooq.BUSINESS_ID.eq(businessId)).limit(finalOffset, PAGE_SIZE).fetch(jooq.ID));
                        dao.execute(dsl-> {
                            UpdateSetMoreStep<?> update = dsl.update(jooq.table).set(jooq.BUSINESS_ID, businessId);
                            businessName.ifPresent(v->update.set(jooq.BUSINESS_NAME,v));
                            applicationStartTime.ifPresent(v->update.set(jooq.APPLICANT_START_TIME,v));
                            applicationEndTime.ifPresent(v->update.set(jooq.APPLICANT_END_TIME,v));
                            startTime.ifPresent(v->update.set(jooq.START_TIME,v));
                            deadLine.ifPresent(v->update.set(jooq.DEAD_LINE,v));
                            remindStatus.ifPresent(v->update.set(jooq.REMIND_STATUS,v));
                            remindType.ifPresent(v->update.set(jooq.REMIND_TYPE,v));
                            remindTime.ifPresent(v->update.set(jooq.REMIND_TIME,v));
                            return update.where(jooq.ID.in(ids)).execute();
                        });
                        offset += PAGE_SIZE;
                    }
                });
            }
        });
        logger.info("根据业务id 批量更新结束:{}", DateUtil.dateLongToString(Instant.now().toEpochMilli(),DateUtil.YYYY_MM_DD_HH_MM_SS));
    }


    protected StudyPlan initializeStudyPlan(String memberId, String businessId, Integer businessType, String businessName, Integer rate, Long applicantStartTime, Long applicantEndTime, Long startTime, Long endTime,String remindType, Long remindTime) {
        StudyPlan studyPlan = new StudyPlan();
        studyPlan.forInsert();
        studyPlan.setMemberId(memberId);//人员id
        studyPlan.setBusinessId(businessId);
        studyPlan.setBusinessType(businessType);//业务类型
        studyPlan.setBusinessName(businessName);//业务名称
        studyPlan.setRequiredCompleteRate(rate);
        studyPlan.setFinishStatus(StudyPlan.RATE_COMPLETE.equals(rate)?StudyPlan.FINISH_STATUS_DONE:StudyPlan.FINISH_STATUS_UN_FINISH);//当完成率为100时 设置为完成
        studyPlan.setSourceType(StudyPlan.SOURCE_TYPE_ORG);//什么方式推送
        studyPlan.setApplicationStartTime(applicantStartTime);
        studyPlan.setApplicationEndTime(applicantEndTime);
        studyPlan.setStartTime(startTime);//开始时间
        studyPlan.setDeadLine(endTime);//结束时间
        studyPlan.setRemindStatus(Objects.isNull(remindType)?StudyPlan.REMIND_STATUS_NONEXISTENCE:StudyPlan.REMIND_STATUS_NO);// 未提醒
        studyPlan.setRemindType(remindType);//推送方式
        studyPlan.setRemindTime(remindTime);//提醒时间
        return studyPlan;
    }

    /**
     * 将受众按用户id取模分组
     *
     * @param memberIds 受众集合
     * @return 分组数据 key：table values 用户id集合
     */
    protected Map<? extends TableImpl<?>, List<String>> groupAudience(List<String> memberIds) {
        return memberIds.stream()
                .collect(Collectors.groupingBy(StudyPlan::choiceTable));

    }

    protected List<String> findExistedMemberIds(List<String> memberIds,String businessId) {
        Map<? extends TableImpl<?>, List<String>> tableMap = this.groupAudience(memberIds);
        List<String> existedMemberIds = Lists.newArrayList();
        tableMap.forEach((table, ids) -> {
            List<String> existedIds = dao.execute(dsl -> {
                Field<String> memberIdField = table.field("f_member_id", String.class);
                Field<String> businessIdField = table.field("f_business_id", String.class);
                return dsl.select(memberIdField)
                        .from(table)
                        .where(businessIdField.eq(businessId),memberIdField.in(ids))
                        .fetch(memberIdField);
            });
            existedMemberIds.addAll(existedIds);
        });
        return existedMemberIds;
    }

    /**
     * 根据人员id查询以及存在的businessId
     * @param memberIds
     * @return
     */
    protected List<String> findExistedBusinessIds(List<String> memberIds) {
        Map<? extends TableImpl<?>, List<String>> tableMap = this.groupAudience(memberIds);
        List<String> existedBusinessIds = Lists.newArrayList();
        tableMap.forEach((table, ids) -> {
            List<String> existedIds = dao.execute(dsl -> {
                Field<String> memberIdField = table.field("f_member_id", String.class);
                Field<String> businessIdField = table.field("f_business_id", String.class);
                return dsl.selectDistinct(businessIdField)
                        .from(table)
                        .where(memberIdField.in(ids))
                        .fetch(businessIdField);
            });
            existedBusinessIds.addAll(existedIds);
        });
        return existedBusinessIds;
    }

    /**
     * 查询所有表中存在的业务id
     * @param businessIds    业务id集合
     * @return
     */
    protected List<StudyPlan> findExistedBusinessId(List<String> businessIds) {
        Map<String, StudyPlan.JooqFields> tableMap = StudyPlan.getAllStudyPlanTable();
        List<StudyPlan> existedBusinessIds = Lists.newArrayList();
        tableMap.forEach((k, table) -> {
            List<StudyPlan> existedIds = dao.execute(dsl -> {
                return dsl.selectDistinct(table.getFields()
                                )
                        .from(table.table)
                        .where(table.BUSINESS_ID.in(businessIds))
                        .groupBy(table.BUSINESS_ID)
                        .fetch(r -> this.recordTranslate2StudyPlan(table, r));
            });
            existedBusinessIds.addAll(existedIds);
        });
        return existedBusinessIds;
    }

    private StudyPlan recordTranslate2StudyPlan(StudyPlan.JooqFields studyPlanJooq, Record r) {
        StudyPlan info = new StudyPlan();
        info.setId(r.getValue(studyPlanJooq.ID));
        info.setMemberId(r.getValue(studyPlanJooq.MEMBER_ID));
        info.setBusinessId(r.getValue(studyPlanJooq.BUSINESS_ID));
        info.setBusinessName(r.getValue(studyPlanJooq.BUSINESS_NAME));
        info.setBusinessType(r.getValue(studyPlanJooq.BUSINESS_TYPE));
        info.setRequiredCompleteRate(r.getValue(studyPlanJooq.REQUIRED_COMPLETE_RATE));
        info.setFinishStatus(r.getValue(studyPlanJooq.FINISH_STATUS));
        info.setSourceType(r.getValue(studyPlanJooq.SOURCE_TYPE));
        info.setApplicationStartTime(r.getValue(studyPlanJooq.APPLICANT_START_TIME));
        info.setApplicationEndTime(r.getValue(studyPlanJooq.APPLICANT_END_TIME));
        info.setStartTime(r.getValue(studyPlanJooq.START_TIME));
        info.setDeadLine(r.getValue(studyPlanJooq.DEAD_LINE));
        info.setCompleteTime(r.getValue(studyPlanJooq.COMPLETE_TIME));
        info.setRemindTime(r.getValue(studyPlanJooq.REMIND_TIME));
        info.setRemindType(r.getValue(studyPlanJooq.REMIND_TYPE));
        info.setRemindStatus(r.getValue(studyPlanJooq.REMIND_STATUS));
        info.setCreateTime(r.getValue(studyPlanJooq.CREATE_TIME));
        return info;
    }


    /**
     * 分页批量删除
     * @param businessId     业务Id
     */
    protected AtomicReference<Integer> batchDelete(String businessId) {
        AtomicReference<Integer> atomicReference = new AtomicReference<>(0);
        return template.execute((ew1)->{
            Map<String, StudyPlan.JooqFields> allStudyPlanTable = StudyPlan.getAllStudyPlanTable();
            allStudyPlanTable.forEach((k, table) -> {
                int totalDeleteCount = 0;
                int sum = countStudyPlan(businessId, table);
                int deleteCount;
                do {
                    deleteCount = deleteStudyPlanByPage(businessId, table);

                } while ( sum> 0 && deleteCount > 0);
                atomicReference.updateAndGet(v -> v + totalDeleteCount);
            });
            return atomicReference;
        });
    }

    /**
     *  查询分表数据量
     * @param businessId 业务id
     * @param jooq 分表
     * @return
     */
    private Integer countStudyPlan(String businessId, StudyPlan.JooqFields jooq) {
        return dao.execute(dsl -> dsl.selectCount()
                .from(jooq.table)
                .where()
                .and(jooq.BUSINESS_ID.eq(businessId)).fetchOne().value1());
    }

    /**
     * 根据业务id批量删除学习计划
     * @param businessId 业务Id
     * @param jooq 分表
     * @return 学习计划数量
     */
    private Integer deleteStudyPlanByPage(String businessId, StudyPlan.JooqFields jooq) {
        return dao.execute(dsl -> {
            List<String> ids = dsl.select(Fields.start().add(jooq.ID.as("id")).end())
                    .from(jooq.table)
                    .where(jooq.BUSINESS_ID.eq(businessId))
                    .limit(PAGE_SIZE)
                    .fetch(jooq.ID.as("id"));
            return dsl.delete(jooq.table).where(jooq.ID.in(ids)).execute();
        });
    }
    /**
     * 批量删除
     * @param businessId     关联id
     * @param memberIds      用户ids
     */
    protected void batchDelete(String businessId, List<String> memberIds) {
        Map<? extends TableImpl<?>, List<String>> tableMap = this.groupAudience(memberIds);
        tableMap.forEach((table,ids)->{
            dao.execute(dsl-> dsl.delete(table)
                    .where(table.field("f_business_id",String.class).eq(businessId))
                    .and(table.field("f_member_id",String.class).in(ids))
                    .execute());
        });
    }

    /**
     * 批量删除
     * @param memberIds      用户ids
     */
    protected void batchDelete(List<String> memberIds, List<String> needDelete) {
        Map<? extends TableImpl<?>, List<String>> tableMap = this.groupAudience(memberIds);
        tableMap.forEach((table,ids)->{
            dao.execute(dsl-> dsl.delete(table)
                    .where()
                    .and(table.field("f_member_id",String.class).in(ids))
                    .and(table.field("f_business_id",String.class).in(needDelete))
                    .execute());
        });
    }

    /**
     * 批量新增
     *
     * @param studyPlans 新增学习计划集合
     */
    protected void batchInsert(List<StudyPlan> studyPlans) {
        List<UpdatableRecord<?>> updatableRecords = studyPlans.stream().map(StudyPlan::translate2UpdateRecord).collect(Collectors.toList());
        dao.execute(dsl -> dsl.batchInsert(updatableRecords).execute());
    }

    /*
     * 单个删除
     * @param businessId
     * @param memberId
     */
    protected void delete(String businessId, String memberId) {
        studyPlanService.deleteByBusinessIdAndMemberId(memberId, businessId);
    }

    /**
     * 场景:管理端撤销学习计划，删除所有相关业务Id的学习计划数据
     * 删除关联业务Id的所有学习计划表,包括t_study_plan_00到t_study_plan_04
     * @param businessId 业务Id
     * @param businessType 业务类型
     */
    protected void deleteJooqCollect(String businessId, String businessType){
        Map<String, StudyPlan.JooqFields> allJooqMap = StudyPlan.getAllStudyPlanTable();
        Integer integerType = BUSINESS_TYPE_SUBJECT_STUDY_PLAN.equals(businessType) ?
                AudienceObject.BUSINESS_TYPE_SUBJECT :
                Integer.valueOf(businessType);
        allJooqMap.values().forEach(ew1->{
            while (true){
                List<String> idCollect = this.singleJooqCollect( ew1, businessId, integerType);
                if(CollectionUtils.isEmpty(idCollect)){
                    break;
                }
                this.deleteSingleJooqCollect(ew1, idCollect,businessId);
            }
        });
    }

    /**
     * 查询单一学习计划JOOQ数据
     * @param jooq 学习计划JOOQ
     * @param businessId 业务Id
     * @param businessType 业务类型
     * @return 查询到的相关Id数据集合
     */
    private List<String> singleJooqCollect( StudyPlan.JooqFields jooq, String businessId, Integer businessType){
        int pageSize=1000;
        return dslContext.select(jooq.ID)
                .from(jooq.table)
                .where(jooq.BUSINESS_ID.eq(businessId))
                .and(jooq.BUSINESS_TYPE.eq(businessType))
                .limit(pageSize)
                .fetch(jooq.ID);
    }

    /**
     * 删除单一学习计划JOOQ数据
     * @param jooq jooq 学习计划JOOQ
     * @param idCollect 根据业务Id查询到的相关学习计划数据
     * @param businessId 业务Id
     */
    protected void deleteSingleJooqCollect(StudyPlan.JooqFields jooq, List<String> idCollect, String businessId) {
        logger.info("删除单一学习计划JOOQ数据，删除表名{},删除Id集合{}", jooq.table, idCollect.size());
        if (!CollectionUtils.isEmpty(idCollect)) {
            template.execute((ew1) -> {
                        int executeCount = dslContext.deleteFrom(jooq.table).where(jooq.ID.in(idCollect)).execute();
                        logger.info("删除单一学习计划JOOQ数据,业务Id{}，删除数据条数{}", businessId, executeCount);
                        return executeCount;
                    }
            );
        }
    }
}
