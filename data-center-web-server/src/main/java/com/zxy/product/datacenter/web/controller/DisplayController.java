package com.zxy.product.datacenter.web.controller;


import static com.zxy.product.datacenter.content.DataCenterConstant.THOUSAND_RATIO;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.security.Permitted;
import com.zxy.product.datacenter.api.service.demo.DisplayService;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据看板接口
 */
@RestController
@RequestMapping("/display")
public class DisplayController {

    private static final String STUDY_STATISTICS_CURRENT_YEAR_CACHE_KEY = "study-statistics-current-year";
    private static final String DATA_VIEW_PROVINCE_AND_OTHERS_CACHE_KEY = "data-view-province-and-others";
    private static final String LEARNING_TREND_CHART_CACHE_KEY = "learning-trend-chart";
    private static final String LOGIN_ACTIVITY_CACHE_KEY = "login-activity";
    private static final String RESOURCE_VIEW_CACHE_KEY = "resource-view";
    private static final String ACTIVITY_VIEW_CACHE_KEY = "activity-view";
    private static final String RESOURCE_TOP_CACHE_KEY = "resource-top";
    private static final String TRAINING_VIEW_CACHE_KEY = "training-view";
    private static final String MEMBER_LOGIN_DISTRIBUTION_CACHE_KEY = "member-login-distribution";
    private static final int EXPIRE_TIME = 24 * 60 * 60;
    private static final String UTF8_CHARSET_STR = "application/json;charset=utf-8";
    public static final DateTimeFormatter FORMATTER_YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DecimalFormat THOUSAND_FORMAT = new DecimalFormat("#.####");

    private DisplayService displayService;
    private Cache cache;

    @Value("#{'${member.political.ids}'.split(',')}")
    private Set<String> memberPoliticalIds;

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("datacenter", "display");
    }

    @Autowired
    public void setDisplayService(DisplayService displayService) {
        this.displayService = displayService;
    }

    /**
     * 全年累计学习情况
     */
    @RequestMapping(method = RequestMethod.GET, value = "/study-statistics/current-year", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    //@Permitted
    @ResponseBody
    public String findStudyStatisticsCurrentYear(RequestContext rc) {
        String orgId = rc.getString("orgId");
        return cache.get(STUDY_STATISTICS_CURRENT_YEAR_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> com.alibaba.fastjson.JSON.toJSONString(displayService.findStudyStatisticsCurrentYear(orgId)),
            EXPIRE_TIME);
    }

    /**
     * 下级单位登录学习情况
     */
    @RequestMapping(method = RequestMethod.GET, value = "/data-view/province-and-others", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    @Permitted
    @ResponseBody
    public String findDataViewProvinceAndOthers(RequestContext rc) {
        String orgId = rc.getString("orgId");
        return cache.get(DATA_VIEW_PROVINCE_AND_OTHERS_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> com.alibaba.fastjson.JSON.toJSONString(displayService.findChildStudyStatisticsCurrentYear(orgId)),
            EXPIRE_TIME);
    }

    /**
     * 学习趋势图
     */
    @RequestMapping(method = RequestMethod.GET, value = "/learning-trend-chart", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    @Permitted
    @ResponseBody
    public String findLearningTrendChart(RequestContext rc) {
        String orgId = rc.getString("orgId");
        return cache.get(LEARNING_TREND_CHART_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> com.alibaba.fastjson.JSON.toJSONString(ImmutableMap.of("5week", displayService.findLearningTrendChartWeek(orgId),
                "7day", displayService.findLearningTrendChartDay(orgId),
                "12month", displayService.findLearningTrendChartMonth(orgId))),
            EXPIRE_TIME);
    }

    /**
     * 登录活跃情况
     */
    @RequestMapping(method = RequestMethod.GET, value = "/login-activity", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    @Permitted
    @ResponseBody
    public String findLoginActivity(RequestContext rc) {
        String orgId = rc.getString("orgId");
        return cache.get(LOGIN_ACTIVITY_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> com.alibaba.fastjson.JSON.toJSONString(ImmutableMap.of("year", displayService.findLoginActivityYear(orgId),
                "5week", displayService.findLoginActivityWeek(orgId),
                "12month", displayService.findLoginActivityMonth(orgId),
                "7day", displayService.findLoginActivityDay(orgId))),
            EXPIRE_TIME);
    }

    /**
     * 人群登录分布
     */
    @RequestMapping(method = RequestMethod.GET, value = "/member-login-distribution", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    @Permitted
    @ResponseBody
    public String findMemberLoginDistribution(RequestContext rc) {
        String orgId = rc.getString("orgId");
        return cache.get(MEMBER_LOGIN_DISTRIBUTION_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> {
                double loginSum = displayService.findUserSum(orgId);
                // 男女比例
                List<Map<String, Object>> sexDatas = displayService.findSex(orgId);
                Double sexSum = sexDatas.stream().map(s ->  Double.parseDouble(s.get("loginMember").toString()))
                                        .reduce(Double::sum).orElse((double) 0);
                // 入职时间
                List<Map<String, Object>> entryTimeDatas = displayService.findEntryTime(orgId);
                double entryTimeSum = 0;
                if (!entryTimeDatas.isEmpty()) {
                    entryTimeSum =  Double.parseDouble(entryTimeDatas.get(0).get("year2_sum").toString()) +
                         Double.parseDouble(entryTimeDatas.get(0).get("year3_sum").toString()) +
                         Double.parseDouble(entryTimeDatas.get(0).get("year6_sum").toString()) +
                         Double.parseDouble(entryTimeDatas.get(0).get("year10_sum").toString());
                }
                // 政治面貌
                List<Map<String, Object>> politicalOutlookDatas = displayService.findPoliticalOutlook(orgId);
                Double politicalOutlookSum = politicalOutlookDatas.stream().map(s ->  Double.parseDouble(s.get("loginMember").toString()))
                                                                  .reduce(Double::sum).orElse((double) 0);
                List<Map<String, Object>> politicalOutlookResults = handlePoliticalOutlook(loginSum, politicalOutlookDatas, politicalOutlookSum,memberPoliticalIds);

                Map<String, Map<String, Object>> result = new HashMap<>();
                result.put("female", handleSex(loginSum, sexDatas, sexSum, 1));
                result.put("male", handleSex(loginSum, sexDatas, sexSum, 0));
                result.put("zeroToTwoYears", handleEntryTime(loginSum, entryTimeDatas, entryTimeSum, "year2_sum"));
                result.put("threeToFiveYears", handleEntryTime(loginSum, entryTimeDatas, entryTimeSum, "year3_sum"));
                result.put("sixToTenYears", handleEntryTime(loginSum, entryTimeDatas, entryTimeSum, "year6_sum"));
                result.put("moreThanTenYears", handleEntryTime(loginSum, entryTimeDatas, entryTimeSum, "year10_sum"));
                result.put("politicalOutlook", politicalOutlookResults.get(0));
                result.put("other", politicalOutlookResults.get(1));
                return com.alibaba.fastjson.JSON.toJSONString(result);
            },
            EXPIRE_TIME);
    }

    private List<Map<String, Object>> handlePoliticalOutlook(double loginSum, List<Map<String, Object>> politicalOutlookDatas, double politicalOutlookSum, Set<String> partySet) {
        List<Map<String, Object>> result = new ArrayList<>();
        double partySum = 0;
        double otherSum = 0;
        for (Map<String, Object> politicalOutlookData : politicalOutlookDatas) {
            Object politicalizationId = politicalOutlookData.get("politicalizationId");
            if (politicalizationId != null && partySet.contains(politicalizationId.toString())) {
                partySum += Double.parseDouble(politicalOutlookData.get("loginMember").toString());
            } else {
                otherSum += Double.parseDouble(politicalOutlookData.get("loginMember").toString());
            }
        }
        double dataSum = partySum + otherSum;

        HashMap<String, Object> partyMap = new HashMap<>();
        partyMap.put("loginMember", THOUSAND_FORMAT.format(partySum / THOUSAND_RATIO));
        partyMap.put("loginRatio", partySum / loginSum * 100);
        partyMap.put("pieCharRatio", partySum / dataSum * 100);
        partyMap.put("totalMember", THOUSAND_FORMAT.format(loginSum / THOUSAND_RATIO));
        result.add(partyMap);

        HashMap<String, Object> otherMap = new HashMap<>();
        otherMap.put("loginMember", THOUSAND_FORMAT.format(otherSum / THOUSAND_RATIO));
        otherMap.put("loginRatio", otherSum / loginSum * 100);
        otherMap.put("pieCharRatio", otherSum / dataSum * 100);
        otherMap.put("totalMember", THOUSAND_FORMAT.format(loginSum / THOUSAND_RATIO));
        result.add(otherMap);
        return result;
    }

    private Map<String, Object> handleEntryTime(double loginSum, List<Map<String, Object>> entryTimeDatas, double entryTimeSum, String key) {
        HashMap<String, Object> map = new HashMap<>();
        entryTimeDatas.forEach(data -> {
            map.put("totalMember", THOUSAND_FORMAT.format(loginSum / THOUSAND_RATIO));
            Object loginMember = data.get(key);
            if (loginMember != null) {
                map.put("loginMember", THOUSAND_FORMAT.format(Double.parseDouble(loginMember.toString()) / THOUSAND_RATIO));
                map.put("loginRatio", Double.parseDouble(loginMember.toString()) / loginSum * 100);
                map.put("pieCharRatio", Double.parseDouble(loginMember.toString()) / entryTimeSum * 100);
            } else {
                map.put("loginMember", 0);
                map.put("loginRatio", 0);
                map.put("pieCharRatio", 0);
            }
        });
        return map;
    }

    private HashMap<String, Object> handleSex(double loginSum, List<Map<String, Object>> sexDatas, double sexSum, int sexInt) {
        HashMap<String, Object> map = new HashMap<>();
        sexDatas.stream().filter(data -> Objects.equals(data.get("sex"), sexInt)).forEach(data -> {
            map.put("totalMember", THOUSAND_FORMAT.format(loginSum / THOUSAND_RATIO));
            Object loginMember = data.get("loginMember");
            if (loginMember != null) {
                map.put("loginMember", THOUSAND_FORMAT.format(Double.parseDouble(loginMember.toString()) / THOUSAND_RATIO));
                map.put("loginRatio", Double.parseDouble(loginMember.toString()) / loginSum * 100);
                map.put("pieCharRatio", Double.parseDouble(loginMember.toString()) / sexSum * 100);
            } else {
                map.put("loginMember", 0);
                map.put("loginRatio", 0);
                map.put("pieCharRatio", 0);
            }
        });
        return map;
    }

    /**
     * 资源情况
     */
    @RequestMapping(method = RequestMethod.GET, value = "/resource-view", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    @Permitted
    @ResponseBody
    public String findResourceView(RequestContext rc) {
        String orgId = rc.getString("orgId");
        return cache.get(RESOURCE_VIEW_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> com.alibaba.fastjson.JSON.toJSONString(displayService.findRecourseView(orgId)),
            EXPIRE_TIME);
    }

    /**
     * 活动情况
     */
    @RequestMapping(method = RequestMethod.GET, value = "/activity-view", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    @Permitted
    @ResponseBody
    public String findActivityView(RequestContext rc) {
        String orgId = rc.getString("orgId");
        return cache.get(ACTIVITY_VIEW_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> com.alibaba.fastjson.JSON.toJSONString(displayService.findActivityView(orgId)),
            EXPIRE_TIME);
    }

    /**
     * 热门学习
     */
    @RequestMapping(method = RequestMethod.GET, value = "/resource-top3", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    @Param(name = "orgPath", required = true)
    @Permitted
    @ResponseBody
    public String findResourceTop3(RequestContext rc) {
        String orgId = rc.getString("orgId");
        String orgPath = rc.getString("orgPath");
        return cache.get(RESOURCE_TOP_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> com.alibaba.fastjson.JSON.toJSONString(displayService.findResourceTop(orgId, orgPath)),
            EXPIRE_TIME);
    }

    /**
     * 培训班参与情况
     */
    @RequestMapping(method = RequestMethod.GET, value = "/training-view", produces = UTF8_CHARSET_STR)
    @Param(name = "orgId", required = true)
    @Permitted
    @ResponseBody
    public String findTrainingView(RequestContext rc) {
        String orgId = rc.getString("orgId");
        return cache.get(TRAINING_VIEW_CACHE_KEY + "#" + orgId + "#" + FORMATTER_YYYYMMDD.format(LocalDate.now()),
            () -> com.alibaba.fastjson.JSON.toJSONString(displayService.findTrainingView(orgId)),
            EXPIRE_TIME);
    }

}
