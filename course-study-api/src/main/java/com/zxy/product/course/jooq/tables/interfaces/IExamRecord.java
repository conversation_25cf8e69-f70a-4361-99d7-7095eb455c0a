/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IExamRecord extends Serializable {

    /**
     * Setter for <code>course-study.t_exam_record.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_exam_record.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_exam_record.f_member_id</code>. 员工id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_exam_record.f_member_id</code>. 员工id
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_exam_record.f_organization_id</code>. 所属组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>course-study.t_exam_record.f_organization_id</code>. 所属组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>course-study.t_exam_record.f_start_time</code>. 进入考试时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>course-study.t_exam_record.f_start_time</code>. 进入考试时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>course-study.t_exam_record.f_end_time</code>. 交卷截止时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>course-study.t_exam_record.f_end_time</code>. 交卷截止时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>course-study.t_exam_record.f_last_submit_time</code>. 最后提交时间
     */
    public void setLastSubmitTime(Long value);

    /**
     * Getter for <code>course-study.t_exam_record.f_last_submit_time</code>. 最后提交时间
     */
    public Long getLastSubmitTime();

    /**
     * Setter for <code>course-study.t_exam_record.f_score</code>. 成绩
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_score</code>. 成绩
     */
    public Integer getScore();

    /**
     * Setter for <code>course-study.t_exam_record.f_client_type</code>. 客户端类型 1:pc,2:app
     */
    public void setClientType(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_client_type</code>. 客户端类型 1:pc,2:app
     */
    public Integer getClientType();

    /**
     * Setter for <code>course-study.t_exam_record.f_status</code>. 状态  1：未开始，2：进行中，4：交卷异常，5:待评卷，6：及格，7：不及格 8已完成
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_status</code>. 状态  1：未开始，2：进行中，4：交卷异常，5:待评卷，6：及格，7：不及格 8已完成
     */
    public Integer getStatus();

    /**
     * Setter for <code>course-study.t_exam_record.f_exam_id</code>. 关联考试id
     */
    public void setExamId(String value);

    /**
     * Getter for <code>course-study.t_exam_record.f_exam_id</code>. 关联考试id
     */
    public String getExamId();

    /**
     * Setter for <code>course-study.t_exam_record.f_paper_instance_id</code>. 试卷实例id
     */
    public void setPaperInstanceId(String value);

    /**
     * Getter for <code>course-study.t_exam_record.f_paper_instance_id</code>. 试卷实例id
     */
    public String getPaperInstanceId();

    /**
     * Setter for <code>course-study.t_exam_record.f_exam_number</code>. 第几次考试
     */
    public void setExamNumber(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_exam_number</code>. 第几次考试
     */
    public Integer getExamNumber();

    /**
     * Setter for <code>course-study.t_exam_record.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_exam_record.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_exam_record.f_submit_time</code>. 交卷时间
     */
    public void setSubmitTime(Long value);

    /**
     * Getter for <code>course-study.t_exam_record.f_submit_time</code>. 交卷时间
     */
    public Long getSubmitTime();

    /**
     * Setter for <code>course-study.t_exam_record.f_duration</code>. 考试时长(分钟)
     */
    public void setDuration(Long value);

    /**
     * Getter for <code>course-study.t_exam_record.f_duration</code>. 考试时长(分钟)
     */
    public Long getDuration();

    /**
     * Setter for <code>course-study.t_exam_record.f_is_reset</code>. 是否重置
     */
    public void setIsReset(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_is_reset</code>. 是否重置
     */
    public Integer getIsReset();

    /**
     * Setter for <code>course-study.t_exam_record.f_is_current</code>. 0:非当前，1：当前
     */
    public void setIsCurrent(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_is_current</code>. 0:非当前，1：当前
     */
    public Integer getIsCurrent();

    /**
     * Setter for <code>course-study.t_exam_record.f_is_finished</code>. 是否完成
     */
    public void setIsFinished(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_is_finished</code>. 是否完成
     */
    public Integer getIsFinished();

    /**
     * Setter for <code>course-study.t_exam_record.f_exception_order</code>. 异常排序
     */
    public void setExceptionOrder(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_exception_order</code>. 异常排序
     */
    public Integer getExceptionOrder();

    /**
     * Setter for <code>course-study.t_exam_record.f_order_content</code>.
     */
    public void setOrderContent(String value);

    /**
     * Getter for <code>course-study.t_exam_record.f_order_content</code>.
     */
    public String getOrderContent();

    /**
     * Setter for <code>course-study.t_exam_record.f_exam_times</code>. 考试次数
     */
    public void setExamTimes(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_exam_times</code>. 考试次数
     */
    public Integer getExamTimes();

    /**
     * Setter for <code>course-study.t_exam_record.f_switch_times</code>. 切屏数
     */
    public void setSwitchTimes(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_switch_times</code>. 切屏数
     */
    public Integer getSwitchTimes();

    /**
     * Setter for <code>course-study.t_exam_record.f_personal_code</code>. 个人码
     */
    public void setPersonalCode(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_personal_code</code>. 个人码
     */
    public Integer getPersonalCode();

    /**
     * Setter for <code>course-study.t_exam_record.f_user_ip</code>. 用户ip
     */
    public void setUserIp(String value);

    /**
     * Getter for <code>course-study.t_exam_record.f_user_ip</code>. 用户ip
     */
    public String getUserIp();

    /**
     * Setter for <code>course-study.t_exam_record.f_no_answer_count</code>. 未答数
     */
    public void setNoAnswerCount(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_no_answer_count</code>. 未答数
     */
    public Integer getNoAnswerCount();

    /**
     * Setter for <code>course-study.t_exam_record.f_answered_count</code>. 已答数
     */
    public void setAnsweredCount(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_answered_count</code>. 已答数
     */
    public Integer getAnsweredCount();

    /**
     * Setter for <code>course-study.t_exam_record.f_client_version</code>. 终端型号
     */
    public void setClientVersion(String value);

    /**
     * Getter for <code>course-study.t_exam_record.f_client_version</code>. 终端型号
     */
    public String getClientVersion();

    /**
     * Setter for <code>course-study.t_exam_record.f_old_score</code>. 分数
     */
    public void setOldScore(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_old_score</code>. 分数
     */
    public Integer getOldScore();

    /**
     * Setter for <code>course-study.t_exam_record.f_old_status</code>. 状态
     */
    public void setOldStatus(Integer value);

    /**
     * Getter for <code>course-study.t_exam_record.f_old_status</code>. 状态
     */
    public Integer getOldStatus();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IExamRecord
     */
    public void from(IExamRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IExamRecord
     */
    public <E extends IExamRecord> E into(E into);
}
