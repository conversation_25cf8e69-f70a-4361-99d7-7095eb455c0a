/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IPartyStudySummaryMonth;

import javax.annotation.Generated;


/**
 * 党校学习数据汇总（按月）
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PartyStudySummaryMonthEntity extends BaseEntity implements IPartyStudySummaryMonth {

    private static final long serialVersionUID = 1L;

    private Integer month;
    private Long    studyTotalTime;
    private Integer studyMemberCount;

    public PartyStudySummaryMonthEntity() {}

    public PartyStudySummaryMonthEntity(PartyStudySummaryMonthEntity value) {
        this.month = value.month;
        this.studyTotalTime = value.studyTotalTime;
        this.studyMemberCount = value.studyMemberCount;
    }

    public PartyStudySummaryMonthEntity(
        String  id,
        Integer month,
        Long    studyTotalTime,
        Integer studyMemberCount,
        Long    createTime
    ) {
        super.setId(id);
        this.month = month;
        this.studyTotalTime = studyTotalTime;
        this.studyMemberCount = studyMemberCount;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Integer getMonth() {
        return this.month;
    }

    @Override
    public void setMonth(Integer month) {
        this.month = month;
    }

    @Override
    public Long getStudyTotalTime() {
        return this.studyTotalTime;
    }

    @Override
    public void setStudyTotalTime(Long studyTotalTime) {
        this.studyTotalTime = studyTotalTime;
    }

    @Override
    public Integer getStudyMemberCount() {
        return this.studyMemberCount;
    }

    @Override
    public void setStudyMemberCount(Integer studyMemberCount) {
        this.studyMemberCount = studyMemberCount;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("PartyStudySummaryMonthEntity (");

        sb.append(getId());
        sb.append(", ").append(month);
        sb.append(", ").append(studyTotalTime);
        sb.append(", ").append(studyMemberCount);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPartyStudySummaryMonth from) {
        setId(from.getId());
        setMonth(from.getMonth());
        setStudyTotalTime(from.getStudyTotalTime());
        setStudyMemberCount(from.getStudyMemberCount());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPartyStudySummaryMonth> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends PartyStudySummaryMonthEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.PartyStudySummaryMonthRecord r = new com.zxy.product.course.jooq.tables.records.PartyStudySummaryMonthRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.ID, record.getValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.MONTH) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.MONTH, record.getValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.MONTH));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.STUDY_TOTAL_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.STUDY_TOTAL_TIME, record.getValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.STUDY_TOTAL_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.STUDY_MEMBER_COUNT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.STUDY_MEMBER_COUNT, record.getValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.STUDY_MEMBER_COUNT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
