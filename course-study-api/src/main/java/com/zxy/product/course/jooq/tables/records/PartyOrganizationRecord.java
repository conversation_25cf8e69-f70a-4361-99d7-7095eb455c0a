/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.PartyOrganization;
import com.zxy.product.course.jooq.tables.interfaces.IPartyOrganization;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record20;
import org.jooq.Row20;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 党组织（原样同步党建云数据）
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PartyOrganizationRecord extends UpdatableRecordImpl<PartyOrganizationRecord> implements Record20<String, String, String, String, String, String, Integer, String, String, Integer, Long, Integer, Integer, String, String, String, Long, String, String, Integer>, IPartyOrganization {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_party_organization.f_id</code>. 党组织ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_id</code>. 党组织ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_uuid</code>. 党组织标识
     */
    @Override
    public void setUuid(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_uuid</code>. 党组织标识
     */
    @Override
    public String getUuid() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_org_name</code>. 党组织机构名称
     */
    @Override
    public void setOrgName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_org_name</code>. 党组织机构名称
     */
    @Override
    public String getOrgName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_org_full_name</code>. 党组织机构全称
     */
    @Override
    public void setOrgFullName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_org_full_name</code>. 党组织机构全称
     */
    @Override
    public String getOrgFullName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_code</code>. 党组织编码 
     */
    @Override
    public void setCode(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_code</code>. 党组织编码 
     */
    @Override
    public String getCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_org_type</code>. 党组织类型（选择：党组、党委、党总支、党支部）
     */
    @Override
    public void setOrgType(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_org_type</code>. 党组织类型（选择：党组、党委、党总支、党支部）
     */
    @Override
    public String getOrgType() {
        return (String) get(5);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_is_cancel</code>. 是否被取消
     */
    @Override
    public void setIsCancel(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_is_cancel</code>. 是否被取消
     */
    @Override
    public Integer getIsCancel() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_parent_id</code>. 上级党组织ID
     */
    @Override
    public void setParentId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_parent_id</code>. 上级党组织ID
     */
    @Override
    public String getParentId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_parents_path</code>. 所有上级组织ids,逗号拼接
     */
    @Override
    public void setParentsPath(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_parents_path</code>. 所有上级组织ids,逗号拼接
     */
    @Override
    public String getParentsPath() {
        return (String) get(8);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_taxis</code>. 排序
     */
    @Override
    public void setTaxis(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_taxis</code>. 排序
     */
    @Override
    public Integer getTaxis() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_last_edit_date</code>. 最后修改时间
     */
    @Override
    public void setLastEditDate(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_last_edit_date</code>. 最后修改时间
     */
    @Override
    public Long getLastEditDate() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_is_temporary</code>. 是否是临时组织
     */
    @Override
    public void setIsTemporary(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_is_temporary</code>. 是否是临时组织
     */
    @Override
    public Integer getIsTemporary() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_is_joint_branch</code>. 是否是联合支部
     */
    @Override
    public void setIsJointBranch(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_is_joint_branch</code>. 是否是联合支部
     */
    @Override
    public Integer getIsJointBranch() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_work_organization</code>. 单位名称
     */
    @Override
    public void setWorkOrganization(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_work_organization</code>. 单位名称
     */
    @Override
    public String getWorkOrganization() {
        return (String) get(13);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_work_organization_code</code>. 党组织所在单位代码
     */
    @Override
    public void setWorkOrganizationCode(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_work_organization_code</code>. 党组织所在单位代码
     */
    @Override
    public String getWorkOrganizationCode() {
        return (String) get(14);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_work_organization_type</code>. 党组织所在单位情况
     */
    @Override
    public void setWorkOrganizationType(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_work_organization_type</code>. 党组织所在单位情况
     */
    @Override
    public String getWorkOrganizationType() {
        return (String) get(15);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_org_found_time</code>. 成立时间
     */
    @Override
    public void setOrgFoundTime(Long value) {
        set(16, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_org_found_time</code>. 成立时间
     */
    @Override
    public Long getOrgFoundTime() {
        return (Long) get(16);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_org_short_name</code>. 组织简称
     */
    @Override
    public void setOrgShortName(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_org_short_name</code>. 组织简称
     */
    @Override
    public String getOrgShortName() {
        return (String) get(17);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_org_short_name_online_user</code>. 组织简称（在线人数，支部生活）显示
     */
    @Override
    public void setOrgShortNameOnlineUser(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_org_short_name_online_user</code>. 组织简称（在线人数，支部生活）显示
     */
    @Override
    public String getOrgShortNameOnlineUser() {
        return (String) get(18);
    }

    /**
     * Setter for <code>course-study.t_party_organization.f_is_dz_org</code>. 是否为党组办公室的支部
     */
    @Override
    public void setIsDzOrg(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>course-study.t_party_organization.f_is_dz_org</code>. 是否为党组办公室的支部
     */
    @Override
    public Integer getIsDzOrg() {
        return (Integer) get(19);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record20 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row20<String, String, String, String, String, String, Integer, String, String, Integer, Long, Integer, Integer, String, String, String, Long, String, String, Integer> fieldsRow() {
        return (Row20) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row20<String, String, String, String, String, String, Integer, String, String, Integer, Long, Integer, Integer, String, String, String, Long, String, String, Integer> valuesRow() {
        return (Row20) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return PartyOrganization.PARTY_ORGANIZATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return PartyOrganization.PARTY_ORGANIZATION.UUID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return PartyOrganization.PARTY_ORGANIZATION.ORG_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return PartyOrganization.PARTY_ORGANIZATION.ORG_FULL_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return PartyOrganization.PARTY_ORGANIZATION.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return PartyOrganization.PARTY_ORGANIZATION.ORG_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return PartyOrganization.PARTY_ORGANIZATION.IS_CANCEL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return PartyOrganization.PARTY_ORGANIZATION.PARENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return PartyOrganization.PARTY_ORGANIZATION.PARENTS_PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return PartyOrganization.PARTY_ORGANIZATION.TAXIS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field11() {
        return PartyOrganization.PARTY_ORGANIZATION.LAST_EDIT_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return PartyOrganization.PARTY_ORGANIZATION.IS_TEMPORARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field13() {
        return PartyOrganization.PARTY_ORGANIZATION.IS_JOINT_BRANCH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field14() {
        return PartyOrganization.PARTY_ORGANIZATION.WORK_ORGANIZATION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field15() {
        return PartyOrganization.PARTY_ORGANIZATION.WORK_ORGANIZATION_CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field16() {
        return PartyOrganization.PARTY_ORGANIZATION.WORK_ORGANIZATION_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field17() {
        return PartyOrganization.PARTY_ORGANIZATION.ORG_FOUND_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field18() {
        return PartyOrganization.PARTY_ORGANIZATION.ORG_SHORT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field19() {
        return PartyOrganization.PARTY_ORGANIZATION.ORG_SHORT_NAME_ONLINE_USER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field20() {
        return PartyOrganization.PARTY_ORGANIZATION.IS_DZ_ORG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getUuid();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getOrgName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getOrgFullName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getOrgType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getIsCancel();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getParentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getParentsPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getTaxis();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value11() {
        return getLastEditDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getIsTemporary();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value13() {
        return getIsJointBranch();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value14() {
        return getWorkOrganization();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value15() {
        return getWorkOrganizationCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value16() {
        return getWorkOrganizationType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value17() {
        return getOrgFoundTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value18() {
        return getOrgShortName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value19() {
        return getOrgShortNameOnlineUser();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value20() {
        return getIsDzOrg();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value2(String value) {
        setUuid(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value3(String value) {
        setOrgName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value4(String value) {
        setOrgFullName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value5(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value6(String value) {
        setOrgType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value7(Integer value) {
        setIsCancel(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value8(String value) {
        setParentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value9(String value) {
        setParentsPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value10(Integer value) {
        setTaxis(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value11(Long value) {
        setLastEditDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value12(Integer value) {
        setIsTemporary(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value13(Integer value) {
        setIsJointBranch(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value14(String value) {
        setWorkOrganization(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value15(String value) {
        setWorkOrganizationCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value16(String value) {
        setWorkOrganizationType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value17(Long value) {
        setOrgFoundTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value18(String value) {
        setOrgShortName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value19(String value) {
        setOrgShortNameOnlineUser(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord value20(Integer value) {
        setIsDzOrg(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PartyOrganizationRecord values(String value1, String value2, String value3, String value4, String value5, String value6, Integer value7, String value8, String value9, Integer value10, Long value11, Integer value12, Integer value13, String value14, String value15, String value16, Long value17, String value18, String value19, Integer value20) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPartyOrganization from) {
        setId(from.getId());
        setUuid(from.getUuid());
        setOrgName(from.getOrgName());
        setOrgFullName(from.getOrgFullName());
        setCode(from.getCode());
        setOrgType(from.getOrgType());
        setIsCancel(from.getIsCancel());
        setParentId(from.getParentId());
        setParentsPath(from.getParentsPath());
        setTaxis(from.getTaxis());
        setLastEditDate(from.getLastEditDate());
        setIsTemporary(from.getIsTemporary());
        setIsJointBranch(from.getIsJointBranch());
        setWorkOrganization(from.getWorkOrganization());
        setWorkOrganizationCode(from.getWorkOrganizationCode());
        setWorkOrganizationType(from.getWorkOrganizationType());
        setOrgFoundTime(from.getOrgFoundTime());
        setOrgShortName(from.getOrgShortName());
        setOrgShortNameOnlineUser(from.getOrgShortNameOnlineUser());
        setIsDzOrg(from.getIsDzOrg());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPartyOrganization> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PartyOrganizationRecord
     */
    public PartyOrganizationRecord() {
        super(PartyOrganization.PARTY_ORGANIZATION);
    }

    /**
     * Create a detached, initialised PartyOrganizationRecord
     */
    public PartyOrganizationRecord(String id, String uuid, String orgName, String orgFullName, String code, String orgType, Integer isCancel, String parentId, String parentsPath, Integer taxis, Long lastEditDate, Integer isTemporary, Integer isJointBranch, String workOrganization, String workOrganizationCode, String workOrganizationType, Long orgFoundTime, String orgShortName, String orgShortNameOnlineUser, Integer isDzOrg) {
        super(PartyOrganization.PARTY_ORGANIZATION);

        set(0, id);
        set(1, uuid);
        set(2, orgName);
        set(3, orgFullName);
        set(4, code);
        set(5, orgType);
        set(6, isCancel);
        set(7, parentId);
        set(8, parentsPath);
        set(9, taxis);
        set(10, lastEditDate);
        set(11, isTemporary);
        set(12, isJointBranch);
        set(13, workOrganization);
        set(14, workOrganizationCode);
        set(15, workOrganizationType);
        set(16, orgFoundTime);
        set(17, orgShortName);
        set(18, orgShortNameOnlineUser);
        set(19, isDzOrg);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.PartyOrganizationEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.PartyOrganizationEntity pojo = (com.zxy.product.course.jooq.tables.pojos.PartyOrganizationEntity)source;
        pojo.into(this);
        return true;
    }
}
