/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IAnnualBill_2022;

import javax.annotation.Generated;


/**
 * 2022年度账单
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnnualBill_2022Entity extends BaseEntity implements IAnnualBill_2022 {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private Integer loginTimes;
    private Integer studyTimeYear;
    private Integer studyFinishCourse;
    private Integer studyOrder;
    private Integer courseMaxDay;
    private Integer courseMaxCount;
    private String  maxCourseId;
    private String  maxCourseName;
    private Integer maxCourseTime;
    private String  maxSubjectId;
    private String  maxSubjectName;
    private Integer maxSubjectTime;
    private Long    lastTime;
    private String  lastCourseId;
    private String  lastCourseName;
    private Integer card;
    private Integer finish;

    public AnnualBill_2022Entity() {}

    public AnnualBill_2022Entity(AnnualBill_2022Entity value) {
        this.memberId = value.memberId;
        this.loginTimes = value.loginTimes;
        this.studyTimeYear = value.studyTimeYear;
        this.studyFinishCourse = value.studyFinishCourse;
        this.studyOrder = value.studyOrder;
        this.courseMaxDay = value.courseMaxDay;
        this.courseMaxCount = value.courseMaxCount;
        this.maxCourseId = value.maxCourseId;
        this.maxCourseName = value.maxCourseName;
        this.maxCourseTime = value.maxCourseTime;
        this.maxSubjectId = value.maxSubjectId;
        this.maxSubjectName = value.maxSubjectName;
        this.maxSubjectTime = value.maxSubjectTime;
        this.lastTime = value.lastTime;
        this.lastCourseId = value.lastCourseId;
        this.lastCourseName = value.lastCourseName;
        this.card = value.card;
        this.finish = value.finish;
    }

    public AnnualBill_2022Entity(
        String  id,
        Long    createTime,
        String  memberId,
        Integer loginTimes,
        Integer studyTimeYear,
        Integer studyFinishCourse,
        Integer studyOrder,
        Integer courseMaxDay,
        Integer courseMaxCount,
        String  maxCourseId,
        String  maxCourseName,
        Integer maxCourseTime,
        String  maxSubjectId,
        String  maxSubjectName,
        Integer maxSubjectTime,
        Long    lastTime,
        String  lastCourseId,
        String  lastCourseName,
        Integer card,
        Integer finish
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.memberId = memberId;
        this.loginTimes = loginTimes;
        this.studyTimeYear = studyTimeYear;
        this.studyFinishCourse = studyFinishCourse;
        this.studyOrder = studyOrder;
        this.courseMaxDay = courseMaxDay;
        this.courseMaxCount = courseMaxCount;
        this.maxCourseId = maxCourseId;
        this.maxCourseName = maxCourseName;
        this.maxCourseTime = maxCourseTime;
        this.maxSubjectId = maxSubjectId;
        this.maxSubjectName = maxSubjectName;
        this.maxSubjectTime = maxSubjectTime;
        this.lastTime = lastTime;
        this.lastCourseId = lastCourseId;
        this.lastCourseName = lastCourseName;
        this.card = card;
        this.finish = finish;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Integer getLoginTimes() {
        return this.loginTimes;
    }

    @Override
    public void setLoginTimes(Integer loginTimes) {
        this.loginTimes = loginTimes;
    }

    @Override
    public Integer getStudyTimeYear() {
        return this.studyTimeYear;
    }

    @Override
    public void setStudyTimeYear(Integer studyTimeYear) {
        this.studyTimeYear = studyTimeYear;
    }

    @Override
    public Integer getStudyFinishCourse() {
        return this.studyFinishCourse;
    }

    @Override
    public void setStudyFinishCourse(Integer studyFinishCourse) {
        this.studyFinishCourse = studyFinishCourse;
    }

    @Override
    public Integer getStudyOrder() {
        return this.studyOrder;
    }

    @Override
    public void setStudyOrder(Integer studyOrder) {
        this.studyOrder = studyOrder;
    }

    @Override
    public Integer getCourseMaxDay() {
        return this.courseMaxDay;
    }

    @Override
    public void setCourseMaxDay(Integer courseMaxDay) {
        this.courseMaxDay = courseMaxDay;
    }

    @Override
    public Integer getCourseMaxCount() {
        return this.courseMaxCount;
    }

    @Override
    public void setCourseMaxCount(Integer courseMaxCount) {
        this.courseMaxCount = courseMaxCount;
    }

    @Override
    public String getMaxCourseId() {
        return this.maxCourseId;
    }

    @Override
    public void setMaxCourseId(String maxCourseId) {
        this.maxCourseId = maxCourseId;
    }

    @Override
    public String getMaxCourseName() {
        return this.maxCourseName;
    }

    @Override
    public void setMaxCourseName(String maxCourseName) {
        this.maxCourseName = maxCourseName;
    }

    @Override
    public Integer getMaxCourseTime() {
        return this.maxCourseTime;
    }

    @Override
    public void setMaxCourseTime(Integer maxCourseTime) {
        this.maxCourseTime = maxCourseTime;
    }

    @Override
    public String getMaxSubjectId() {
        return this.maxSubjectId;
    }

    @Override
    public void setMaxSubjectId(String maxSubjectId) {
        this.maxSubjectId = maxSubjectId;
    }

    @Override
    public String getMaxSubjectName() {
        return this.maxSubjectName;
    }

    @Override
    public void setMaxSubjectName(String maxSubjectName) {
        this.maxSubjectName = maxSubjectName;
    }

    @Override
    public Integer getMaxSubjectTime() {
        return this.maxSubjectTime;
    }

    @Override
    public void setMaxSubjectTime(Integer maxSubjectTime) {
        this.maxSubjectTime = maxSubjectTime;
    }

    @Override
    public Long getLastTime() {
        return this.lastTime;
    }

    @Override
    public void setLastTime(Long lastTime) {
        this.lastTime = lastTime;
    }

    @Override
    public String getLastCourseId() {
        return this.lastCourseId;
    }

    @Override
    public void setLastCourseId(String lastCourseId) {
        this.lastCourseId = lastCourseId;
    }

    @Override
    public String getLastCourseName() {
        return this.lastCourseName;
    }

    @Override
    public void setLastCourseName(String lastCourseName) {
        this.lastCourseName = lastCourseName;
    }

    @Override
    public Integer getCard() {
        return this.card;
    }

    @Override
    public void setCard(Integer card) {
        this.card = card;
    }

    @Override
    public Integer getFinish() {
        return this.finish;
    }

    @Override
    public void setFinish(Integer finish) {
        this.finish = finish;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AnnualBill_2022Entity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(memberId);
        sb.append(", ").append(loginTimes);
        sb.append(", ").append(studyTimeYear);
        sb.append(", ").append(studyFinishCourse);
        sb.append(", ").append(studyOrder);
        sb.append(", ").append(courseMaxDay);
        sb.append(", ").append(courseMaxCount);
        sb.append(", ").append(maxCourseId);
        sb.append(", ").append(maxCourseName);
        sb.append(", ").append(maxCourseTime);
        sb.append(", ").append(maxSubjectId);
        sb.append(", ").append(maxSubjectName);
        sb.append(", ").append(maxSubjectTime);
        sb.append(", ").append(lastTime);
        sb.append(", ").append(lastCourseId);
        sb.append(", ").append(lastCourseName);
        sb.append(", ").append(card);
        sb.append(", ").append(finish);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAnnualBill_2022 from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setMemberId(from.getMemberId());
        setLoginTimes(from.getLoginTimes());
        setStudyTimeYear(from.getStudyTimeYear());
        setStudyFinishCourse(from.getStudyFinishCourse());
        setStudyOrder(from.getStudyOrder());
        setCourseMaxDay(from.getCourseMaxDay());
        setCourseMaxCount(from.getCourseMaxCount());
        setMaxCourseId(from.getMaxCourseId());
        setMaxCourseName(from.getMaxCourseName());
        setMaxCourseTime(from.getMaxCourseTime());
        setMaxSubjectId(from.getMaxSubjectId());
        setMaxSubjectName(from.getMaxSubjectName());
        setMaxSubjectTime(from.getMaxSubjectTime());
        setLastTime(from.getLastTime());
        setLastCourseId(from.getLastCourseId());
        setLastCourseName(from.getLastCourseName());
        setCard(from.getCard());
        setFinish(from.getFinish());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAnnualBill_2022> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends AnnualBill_2022Entity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.AnnualBill_2022Record r = new com.zxy.product.course.jooq.tables.records.AnnualBill_2022Record();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.ID, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LOGIN_TIMES.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LOGIN_TIMES, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LOGIN_TIMES));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_TIME_YEAR.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_TIME_YEAR, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_TIME_YEAR));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_FINISH_COURSE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_FINISH_COURSE, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_FINISH_COURSE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_ORDER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_ORDER, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.STUDY_ORDER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.COURSE_MAX_DAY.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.COURSE_MAX_DAY, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.COURSE_MAX_DAY));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.COURSE_MAX_COUNT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.COURSE_MAX_COUNT, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.COURSE_MAX_COUNT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_NAME, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_TIME, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_COURSE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_ID, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_NAME, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_TIME, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.MAX_SUBJECT_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_TIME, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_COURSE_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_COURSE_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_COURSE_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_COURSE_NAME, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.LAST_COURSE_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.CARD.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.CARD, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.CARD));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.FINISH.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.FINISH, record.getValue(com.zxy.product.course.jooq.tables.AnnualBill_2022.ANNUAL_BILL_2022.FINISH));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
