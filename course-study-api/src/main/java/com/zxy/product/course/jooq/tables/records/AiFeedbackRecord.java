/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.AiFeedback;
import com.zxy.product.course.jooq.tables.interfaces.IAiFeedback;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 数智导师 模型反馈表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AiFeedbackRecord extends UpdatableRecordImpl<AiFeedbackRecord> implements Record6<String, Integer, Integer, String, Integer, Timestamp>, IAiFeedback {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_ai_feedback.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_ai_feedback.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_ai_feedback.f_feedback_type</code>. 反馈类型
     */
    @Override
    public void setFeedbackType(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_ai_feedback.f_feedback_type</code>. 反馈类型
     */
    @Override
    public Integer getFeedbackType() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>course-study.t_ai_feedback.f_experience</code>. 反馈体验
     */
    @Override
    public void setExperience(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_ai_feedback.f_experience</code>. 反馈体验
     */
    @Override
    public Integer getExperience() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>course-study.t_ai_feedback.f_feedback_substance</code>. 反馈内容
     */
    @Override
    public void setFeedbackSubstance(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_ai_feedback.f_feedback_substance</code>. 反馈内容
     */
    @Override
    public String getFeedbackSubstance() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_ai_feedback.f_feekback_num</code>. 反馈次数（每个用户一个问题不能超过3次）
     */
    @Override
    public void setFeekbackNum(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_ai_feedback.f_feekback_num</code>. 反馈次数（每个用户一个问题不能超过3次）
     */
    @Override
    public Integer getFeekbackNum() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>course-study.t_ai_feedback.f_update_time</code>. 修改时间
     */
    @Override
    public void setUpdateTime(Timestamp value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_ai_feedback.f_update_time</code>. 修改时间
     */
    @Override
    public Timestamp getUpdateTime() {
        return (Timestamp) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, Integer, Integer, String, Integer, Timestamp> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, Integer, Integer, String, Integer, Timestamp> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return AiFeedback.AI_FEEDBACK.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field2() {
        return AiFeedback.AI_FEEDBACK.FEEDBACK_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return AiFeedback.AI_FEEDBACK.EXPERIENCE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return AiFeedback.AI_FEEDBACK.FEEDBACK_SUBSTANCE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return AiFeedback.AI_FEEDBACK.FEEKBACK_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field6() {
        return AiFeedback.AI_FEEDBACK.UPDATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value2() {
        return getFeedbackType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getExperience();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getFeedbackSubstance();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getFeekbackNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value6() {
        return getUpdateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AiFeedbackRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AiFeedbackRecord value2(Integer value) {
        setFeedbackType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AiFeedbackRecord value3(Integer value) {
        setExperience(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AiFeedbackRecord value4(String value) {
        setFeedbackSubstance(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AiFeedbackRecord value5(Integer value) {
        setFeekbackNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AiFeedbackRecord value6(Timestamp value) {
        setUpdateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AiFeedbackRecord values(String value1, Integer value2, Integer value3, String value4, Integer value5, Timestamp value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAiFeedback from) {
        setId(from.getId());
        setFeedbackType(from.getFeedbackType());
        setExperience(from.getExperience());
        setFeedbackSubstance(from.getFeedbackSubstance());
        setFeekbackNum(from.getFeekbackNum());
        setUpdateTime(from.getUpdateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAiFeedback> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AiFeedbackRecord
     */
    public AiFeedbackRecord() {
        super(AiFeedback.AI_FEEDBACK);
    }

    /**
     * Create a detached, initialised AiFeedbackRecord
     */
    public AiFeedbackRecord(String id, Integer feedbackType, Integer experience, String feedbackSubstance, Integer feekbackNum, Timestamp updateTime) {
        super(AiFeedback.AI_FEEDBACK);

        set(0, id);
        set(1, feedbackType);
        set(2, experience);
        set(3, feedbackSubstance);
        set(4, feekbackNum);
        set(5, updateTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.AiFeedbackEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.AiFeedbackEntity pojo = (com.zxy.product.course.jooq.tables.pojos.AiFeedbackEntity)source;
        pojo.into(this);
        return true;
    }
}
