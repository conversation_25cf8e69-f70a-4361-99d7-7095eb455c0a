package com.zxy.product.course.content;

/**
 * @Author: huqijun
 * @Date: 2020/3/24 20:30
 */
public class CommonConstant {
    private CommonConstant(){

    }

    public static final String CACHEKEY = "test#mq#";
    public static final String CACHEKEYCOUNT1 = "test#mq#count1#";
    public static final String CACHEKEYCOUNT2 = "test#mq#count2#";
    public static final String EXECUTE_TYPE_GET = "get";
    public static final String COUNT = "count";

    public static final String JOIN = "#";
    public static final String COLON = ":";
    public static final String SEPARATOR = "/";
    public static final String SEPARATOR_DOUBLE = "//";
    public static final String EQUAL = "=";
    public static final String PARAM_JOIN = "&";
    public static final String SEPARATE = ";";
    public static final String RUNG = "-";
    public static final String QUESTION = "?";
    public static final String MIDDLE_SPOT = "-";
    public static final Character CHAR_SPOT = '.';
    public static final String CONFIG_ID = "configId";
    public static final String AND = "&";
    public static final String SOURCE = "source";
    public static final String MIDDLE_BRACKET_LEFT = "【";
    public static final String MIDDLE_BRACKET_RIGHT = "】";
    public static final  String INFINITY = "无穷大";
    public static final  String EXCEL_SUFFIX = "xlsx";
    public static final String EXCEL_XLS = ".xls";

    public static final String EXCEL_XLSX = ".xlsx";


    /**
     * 用于课程更新完成规则变化记录日志
     */
    public static final String COURSE_UPDATE_AUDIT_KEY_PRE = "course_update_audit#";

    public static final int PAGE_COUNT_EXPIRE_TIME = 5;

    /**
     * 用于节更新时设置缓存，缓存在插入设计日志对比新旧值使用
     */
    public static final int UPDATE_SECTION_EXPIRE_TIME = 5;

    public static final int COURSE_SELECTOR_COUNT_EXPIRE_TIME = 30;

    public static final String ZERO_STR = "0";
    public static final String ONE_STR  = "1";
    public static final long NUM_1L = 1L;

    public static final int MINUS_ONE = -1;
    public static final long MINUS_ONE_LONG = -1L;
    public static final int ZERO = 0;

    public static final int ONE = 1;

    public static final Long ZERO_LONG = 0L;

    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int FIVE = 5;
    public static final int SIX = 6;
    public static final int SEVEN = 7;
    public static final int EIGHT = 8;
    public static final int NINE = 9;

    public static final int TEN = 10;
    public static final double TEN_DOUBLE = 10.0;
    public static final int ELEVEN = 11;
    public static final int TWELVE = 12;
    public static final int THIRTEEN = 13;
    public static final int FOURTEEN = 14;
    public static final int FIFTEEN = 15;
    public static final int SIXTEEN = 16;
    public static final int SEVENTEEN = 17;
    public static final int EIGHTEEN = 18;
    public static final int NINETEEN = 19;
    public static final int TWENTY = 20;
    public static final int TWENTY_ONE = 21;
    public static final int TWENTY_THREE = 23;
    public static final int TWENTY_FIVE = 25;
    public static final int TWENTY_SIX = 26;
    public static final int THIRTY = 30;
    public static final int THIRTY_TWO = 32;
    public static final int FORTY = 40;
    public static final int FORTY_FIVE = 45;
    public static final int FORTY_SIX = 46;
    public static final int FORTY_EIGHT = 48;
    public static final int FIFTY = 50;
    public static final int FIFTY_EIGHT = 58;
    public static final int FIFTY_NINE = 59;
    public static final int SIXTY = 60;
    public static final int SIXTY_FOUR = 64;
    public static final int EIGHTY = 80;
    public static final int NINETY_SIX = 96;
    public static final int ONE_HUNDRED = 100;
    public static final double ONE_HUNDRED_DOUBLE = 100.0;
    public static final int TWO_HUNDRED = 200;
    public static final int THREE_HUNDRED = 300;
    public static final long THREE_HUNDRED_LONG = 300L;
    public static final int FIVE_HUNDRED = 500;
    public static final long FIVE_HUNDRED_LONG = 500L;
    public static final int EIGHT_HUNDRED = 800;
    public static final int NINE_HUNDRED = 900;
    public static final int NINE_HUNDRED_AND_NINETY_NINE = 999;
    public static final int ONE_THOUSAND = 1000;
    public static final long ONE_THOUSAND_LONG = 1000L;
    public static final int FIVE_THOUSAND = 5000;
    public static final int TEN_THOUSAND = 10000;
    public static final int ONE_MILLION = 1000000;
    public static final int TEN_MILLION = 10000000;
    public static final int MAX_NANO_OF_SECOND = 999999999;
    public static final int ONE_HOUR_MILLI_SECONDS = 3600000;

    public static final double ZERO_D = 0.0d;

    /**
     * excel导入 单页数量
     */
    public static final int EXPORT_PAGE_SIZE = 500;
    /**
     * excel导入 单页数量
     */
    public static final int IMPORT_PAGE_SIZE = 200;

    /**
     * 批量操作最大数量
     */
    public static final int BATCH_MAX_SIZE_LIMIT = 500;

    /**
     * excel导入 错误信息提示最多入库条数
     */
    public static final int MAX_ERROR_MESSAGE_COUNT = 20;

    public static final char SEPARATOR_CHAR = ',';

    public static final String SEPARATOR_COMMA = ",";

    public static final String SEPARATOR_PERCENT = "%";

    public static final String EMPTY = "";
    public static final String BLANK_SPACE = " ";

    public static final String ZERO_RUNG = "-0-0-";

    public static final int BATCH_PAGE_SIZE = 500;

    public static final int MAX_PAGE_SIZE = 10000;
    
    public static final int ONE_MINUTE = 60;

    public static final int ONE_HOUR = 3600;

    public static final int EXPIRE_TIME = 60*60;

    public static final int CACHE_TIME_5M = 5 * 60;

    public static final int CACHE_TIME_10M = 10 * 60;

    public static final String DOT = ".";
    
    public static final String VERSION_10_5_0 = "10.5.0";
    
    public static final String BODY = "body";
    
    public static final String DEFAULT = "default";
    
    public static final String SAFARI = "Safari";
    
    public static final String CHROME = "Chrome";
    
    public static final String HEADER_IF_NONE_MATCH = "If-None-Match";
    
    public static final String HEADER_USER_AGENT = "User-Agent";
    
    public static final String STR_PUBLIC = "public";

    public static final String STR_ATTACHMENT = "attachment";

    public static final String AUTH_KEY = "auth_key";

    public static final int NUM_THOUSAND = 1000;
    
    public static final int SEC_ONE_DAY = 86400;
    public static final int HOUR_OF_DAY = 24;

    public static final int DAY_ONE_WEEK = 7;

    public static final int VIP_FLAG=1;

    public static final int DEFAULT_VALUE = -1;

    /**
     * 课程/专题名称最大长度
     */
    public static final int COURSE_NAME_LENGTH_MAX = 98;

    public static final String BEARER_PREFIX = "Bearer__";

    public static final String TWO_UNDERLINE = "__";

    /**
     * 复制后的课程/专题名
     */
    public static final String COURSE_NAME_COPY = "%s复制";
    /**
     * 正序
     */
    public static final String ASC = "asc";
    /**
     * 反序
     */
    public static final String DESC = "desc";

    public static final String OK = "ok";
    public static final String SUCCESS = "success";
    /**
     * 分院
     */
    public static final String HOME_CONFIG = "home-config";

    public static final String ACCESS_TOKEN = "access_token";

    /**
     * 有效时间标识
     */
    public static final String EXPIRE = "expires";

    /**
     * 知识付费开关默认值
     */
    public static final Boolean KNOWLEDGE_PAYMENT_ENABLE_DEFAULT = false;

    public static final Integer KNOWLEDGE_PAY_SWITCH_OPEN = 1;

    public static final String REFERER = "referer";

    /**
     * 直播操作动作
     */
    public static final String GENSEE_ACTION_REVOKE = "revoke";
    public static final String GENSEE_ACTION_RE_PUBLISH = "rePublish";
    public static final String GENSEE_ACTION_UPDATE = "update";

    public static final String MP3 = "mp3";
    public static final String MP4 = ".mp4";
    public static final String T_MP4 = "_t.mp4";

    public static final int CACHE_EXPIRATION_TIME_SECONDS = 15 * 60;
    // 缓存时间  1天
    public static final int CACHE_EXPIRATION_DAY_TIME = 24 * 3600;

    /**
     * 批量处理最大值50
     */
    public static final int BATCH_MAX_NUM = 50;
    /**
     * 中化数据回传常量
     */
    public static final String COMPANY_COURSE_PUSH_SWITCH = "COMPANY_COURSE_PUSH_SWITCH";
    public static final int CACHE_TIME_HOUR_12 = 12 * 60 * 60;
    public static final String COMPANY_COURSE_PUSH_SWITCH_KEY = "company-course-push-switch:%s";
    public static final String SWITCH_OPEN = "1";
    public static final String SWITCH_CLOSE = "0";
    public static final String OPEN = "open";
    public static final String NOT_FINISH_STATUS = "notFinishStatus";

    /**
     * 手动接口触发
     */
    public static final String TRIGGER_TYPE_HAND = "triggerTypeHand";

    /**
     * 归档开关
     */
    public static final String ARCHIVER_SWITCH = "switch";

    public static final String INCLUDE_KEY = "includeKey";
    public static final String NOT_INCLUDE_KEY = "notIncludeKey";
    public static final String FILE_ID = "fileId";
    public static final String FILE_PATH = "filePath";

    public static final int DEFAULT_BUFFER_SIZE = 1024;

    public static final String PDF = "pdf";

    /**
     * 全员受众类型中, 用户数据模板 - 企业id
     */
    public static final String ALL_AUDIENCE_FORMAT = "ZXY-%s";


    /**
     * es索引
     */
    public static final String ES_INDEX_EXTRACT_TEXT_CONTENT = "course-extract-text-content";

    /**
     * vtt文件名
     */
    public static final String VTT_FORMAT = "%s.vtt";

    public static final String A= "A";
    public static final String B= "B";
    public static final String C= "C";
    public static final String D= "D";
    public static final String E= "E";
    public static final String F= "F";
    public static final String G= "G";
    public static final String H= "H";
    public static final String I= "I";
    public static final String J= "J";
    public static final String K= "K";
    public static final String L= "L";
    public static final String M= "M";
    public static final String N= "N";
    public static final String O= "O";
    public static final String P= "P";
    public static final String Q= "Q";
    public static final String R= "R";
    public static final String S= "S";
    public static final String T= "T";
    public static final String U= "U";
    public static final String V= "V";
    public static final String W= "W";
    public static final String X= "X";
    public static final String Y= "Y";
    public static final String Z= "Z";


    /**
     * 模块
     */
    public static final String COURSE_SERVICE = "course-service";
}

