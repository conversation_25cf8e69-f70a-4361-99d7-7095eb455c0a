/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseSectionStudyLogJlRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyLogJl extends TableImpl<CourseSectionStudyLogJlRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_section_study_log_jl</code>
     */
    public static final CourseSectionStudyLogJl COURSE_SECTION_STUDY_LOG_JL = new CourseSectionStudyLogJl();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseSectionStudyLogJlRecord> getRecordType() {
        return CourseSectionStudyLogJlRecord.class;
    }

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_id</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_member_id</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_course_id</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_section_id</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> SECTION_ID = createField("f_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_section_scrom_id</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> SECTION_SCROM_ID = createField("f_section_scrom_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_client_type</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_completed_rate</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, Integer> COMPLETED_RATE = createField("f_completed_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_finish_status</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_study_time</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_create_time</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_commit_time</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, Long> COMMIT_TIME = createField("f_commit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_submit_text</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> SUBMIT_TEXT = createField("f_submit_text", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_audit_member_id</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> AUDIT_MEMBER_ID = createField("f_audit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_score</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_comments</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> COMMENTS = createField("f_comments", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_exam_status</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, Integer> EXAM_STATUS = createField("f_exam_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_jl.f_lesson_location</code>.
     */
    public final TableField<CourseSectionStudyLogJlRecord, String> LESSON_LOCATION = createField("f_lesson_location", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * Create a <code>course-study.t_course_section_study_log_jl</code> table reference
     */
    public CourseSectionStudyLogJl() {
        this("t_course_section_study_log_jl", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_section_study_log_jl</code> table reference
     */
    public CourseSectionStudyLogJl(String alias) {
        this(alias, COURSE_SECTION_STUDY_LOG_JL);
    }

    private CourseSectionStudyLogJl(String alias, Table<CourseSectionStudyLogJlRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseSectionStudyLogJl(String alias, Table<CourseSectionStudyLogJlRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseSectionStudyLogJlRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_SECTION_STUDY_LOG_JL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseSectionStudyLogJlRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseSectionStudyLogJlRecord>>asList(Keys.KEY_T_COURSE_SECTION_STUDY_LOG_JL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogJl as(String alias) {
        return new CourseSectionStudyLogJl(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseSectionStudyLogJl rename(String name) {
        return new CourseSectionStudyLogJl(name, null);
    }
}
