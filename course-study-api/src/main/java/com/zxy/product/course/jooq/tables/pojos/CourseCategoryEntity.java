/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseCategory;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 课程目录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseCategoryEntity extends BaseEntity implements ICourseCategory {

    private static final long serialVersionUID = 1L;

    private String    name;
    private String    parentId;
    private Integer   sequence;
    private String    code;
    private Integer   state;
    private Integer   hide;
    private String    path;
    private String    organizationId;
    private Timestamp modifyDate;

    public CourseCategoryEntity() {}

    public CourseCategoryEntity(CourseCategoryEntity value) {
        this.name = value.name;
        this.parentId = value.parentId;
        this.sequence = value.sequence;
        this.code = value.code;
        this.state = value.state;
        this.hide = value.hide;
        this.path = value.path;
        this.organizationId = value.organizationId;
        this.modifyDate = value.modifyDate;
    }

    public CourseCategoryEntity(
        String    id,
        Long      createTime,
        String    name,
        String    parentId,
        Integer   sequence,
        String    code,
        Integer   state,
        Integer   hide,
        String    path,
        String    organizationId,
        Timestamp modifyDate
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.name = name;
        this.parentId = parentId;
        this.sequence = sequence;
        this.code = code;
        this.state = state;
        this.hide = hide;
        this.path = path;
        this.organizationId = organizationId;
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getParentId() {
        return this.parentId;
    }

    @Override
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    @Override
    public Integer getSequence() {
        return this.sequence;
    }

    @Override
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public Integer getState() {
        return this.state;
    }

    @Override
    public void setState(Integer state) {
        this.state = state;
    }

    @Override
    public Integer getHide() {
        return this.hide;
    }

    @Override
    public void setHide(Integer hide) {
        this.hide = hide;
    }

    @Override
    public String getPath() {
        return this.path;
    }

    @Override
    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseCategoryEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(name);
        sb.append(", ").append(parentId);
        sb.append(", ").append(sequence);
        sb.append(", ").append(code);
        sb.append(", ").append(state);
        sb.append(", ").append(hide);
        sb.append(", ").append(path);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseCategory from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setName(from.getName());
        setParentId(from.getParentId());
        setSequence(from.getSequence());
        setCode(from.getCode());
        setState(from.getState());
        setHide(from.getHide());
        setPath(from.getPath());
        setOrganizationId(from.getOrganizationId());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseCategory> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseCategoryEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseCategoryRecord r = new com.zxy.product.course.jooq.tables.records.CourseCategoryRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.NAME, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.PARENT_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.PARENT_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.PARENT_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.SEQUENCE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.SEQUENCE, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.SEQUENCE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.CODE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.CODE, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.CODE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.STATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.STATE, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.STATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.HIDE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.HIDE, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.HIDE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.PATH) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.PATH, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.PATH));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.ORGANIZATION_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
