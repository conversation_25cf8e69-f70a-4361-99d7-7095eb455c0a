/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 课程打点表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseMark extends Serializable {

    /**
     * Setter for <code>course-study.t_course_mark.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_mark.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_mark.f_course_id</code>. 课程Id
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_course_mark.f_course_id</code>. 课程Id
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_course_mark.f_chapter_id</code>. 课程章Id
     */
    public void setChapterId(String value);

    /**
     * Getter for <code>course-study.t_course_mark.f_chapter_id</code>. 课程章Id
     */
    public String getChapterId();

    /**
     * Setter for <code>course-study.t_course_mark.f_section_id</code>. 课程节Id
     */
    public void setSectionId(String value);

    /**
     * Getter for <code>course-study.t_course_mark.f_section_id</code>. 课程节Id
     */
    public String getSectionId();

    /**
     * Setter for <code>course-study.t_course_mark.f_check_point_time</code>. 课程打点时间
     */
    public void setCheckPointTime(String value);

    /**
     * Getter for <code>course-study.t_course_mark.f_check_point_time</code>. 课程打点时间
     */
    public String getCheckPointTime();

    /**
     * Setter for <code>course-study.t_course_mark.f_topic_id</code>. 标签Id
     */
    public void setTopicId(String value);

    /**
     * Getter for <code>course-study.t_course_mark.f_topic_id</code>. 标签Id
     */
    public String getTopicId();

    /**
     * Setter for <code>course-study.t_course_mark.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_mark.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_mark.f_update_time</code>. 更新时间
     */
    public void setUpdateTime(Timestamp value);

    /**
     * Getter for <code>course-study.t_course_mark.f_update_time</code>. 更新时间
     */
    public Timestamp getUpdateTime();

    /**
     * Setter for <code>course-study.t_course_mark.f_member_id</code>. 用户Id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_mark.f_member_id</code>. 用户Id
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_course_mark.f_point_content</code>. 知识点书签内容
     */
    public void setPointContent(String value);

    /**
     * Getter for <code>course-study.t_course_mark.f_point_content</code>. 知识点书签内容
     */
    public String getPointContent();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseMark
     */
    public void from(ICourseMark from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseMark
     */
    public <E extends ICourseMark> E into(E into);
}
