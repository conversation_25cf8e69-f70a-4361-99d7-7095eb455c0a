/*
 * This file is generated by jO<PERSON>Q.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseSectionStudyLogSxDay;
import com.zxy.product.course.jooq.tables.interfaces.ICourseSectionStudyLogSxDay;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyLogSxDayRecord extends UpdatableRecordImpl<CourseSectionStudyLogSxDayRecord> implements Record12<String, String, String, Inte<PERSON>, <PERSON>te<PERSON>, <PERSON>te<PERSON>, <PERSON>te<PERSON>, Inte<PERSON>, Inte<PERSON>, <PERSON>, <PERSON>tamp, Integer>, ICourseSectionStudyLogSxDay {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_course_id</code>. 课程/专题id
     */
    @Override
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_course_id</code>. 课程/专题id
     */
    @Override
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_app_study_time</code>. app课程学习时长
     */
    @Override
    public void setAppStudyTime(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_app_study_time</code>. app课程学习时长
     */
    @Override
    public Integer getAppStudyTime() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_pc_study_time</code>. pc课程学习时长
     */
    @Override
    public void setPcStudyTime(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_pc_study_time</code>. pc课程学习时长
     */
    @Override
    public Integer getPcStudyTime() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_study_time</code>. 当天学习总时长
     */
    @Override
    public void setStudyTime(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_study_time</code>. 当天学习总时长
     */
    @Override
    public Integer getStudyTime() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_day</code>. 日
     */
    @Override
    public void setDay(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_day</code>. 日
     */
    @Override
    public Integer getDay() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_month</code>. 月
     */
    @Override
    public void setMonth(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_month</code>. 月
     */
    @Override
    public Integer getMonth() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_year</code>. 年
     */
    @Override
    public void setYear(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_year</code>. 年
     */
    @Override
    public Integer getYear() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(10);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_log_sx_day.f_study_num</code>. 学习次数
     */
    @Override
    public void setStudyNum(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_log_sx_day.f_study_num</code>. 学习次数
     */
    @Override
    public Integer getStudyNum() {
        return (Integer) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long, Timestamp, Integer> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long, Timestamp, Integer> valuesRow() {
        return (Row12) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.APP_STUDY_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.PC_STUDY_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.STUDY_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.DAY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MONTH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.YEAR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field11() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.STUDY_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getAppStudyTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getPcStudyTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getStudyTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getDay();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getMonth();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getYear();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value11() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getStudyNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value3(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value4(Integer value) {
        setAppStudyTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value5(Integer value) {
        setPcStudyTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value6(Integer value) {
        setStudyTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value7(Integer value) {
        setDay(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value8(Integer value) {
        setMonth(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value9(Integer value) {
        setYear(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value10(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value11(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord value12(Integer value) {
        setStudyNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogSxDayRecord values(String value1, String value2, String value3, Integer value4, Integer value5, Integer value6, Integer value7, Integer value8, Integer value9, Long value10, Timestamp value11, Integer value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseSectionStudyLogSxDay from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setAppStudyTime(from.getAppStudyTime());
        setPcStudyTime(from.getPcStudyTime());
        setStudyTime(from.getStudyTime());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setStudyNum(from.getStudyNum());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseSectionStudyLogSxDay> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseSectionStudyLogSxDayRecord
     */
    public CourseSectionStudyLogSxDayRecord() {
        super(CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY);
    }

    /**
     * Create a detached, initialised CourseSectionStudyLogSxDayRecord
     */
    public CourseSectionStudyLogSxDayRecord(String id, String memberId, String courseId, Integer appStudyTime, Integer pcStudyTime, Integer studyTime, Integer day, Integer month, Integer year, Long createTime, Timestamp modifyDate, Integer studyNum) {
        super(CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY);

        set(0, id);
        set(1, memberId);
        set(2, courseId);
        set(3, appStudyTime);
        set(4, pcStudyTime);
        set(5, studyTime);
        set(6, day);
        set(7, month);
        set(8, year);
        set(9, createTime);
        set(10, modifyDate);
        set(11, studyNum);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyLogSxDayEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyLogSxDayEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyLogSxDayEntity)source;
        pojo.into(this);
        return true;
    }
}
