/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.ThematicChapterRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 专题班主题章节
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ThematicChapter extends TableImpl<ThematicChapterRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_thematic_chapter</code>
     */
    public static final ThematicChapter THEMATIC_CHAPTER = new ThematicChapter();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ThematicChapterRecord> getRecordType() {
        return ThematicChapterRecord.class;
    }

    /**
     * The column <code>course-study.t_thematic_chapter.f_id</code>. ID
     */
    public final TableField<ThematicChapterRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>course-study.t_thematic_chapter.f_thematic_id</code>. 专题ID
     */
    public final TableField<ThematicChapterRecord, String> THEMATIC_ID = createField("f_thematic_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "专题ID");

    /**
     * The column <code>course-study.t_thematic_chapter.f_title</code>. f_title
     */
    public final TableField<ThematicChapterRecord, String> TITLE = createField("f_title", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "f_title");

    /**
     * The column <code>course-study.t_thematic_chapter.f_name</code>.
     */
    public final TableField<ThematicChapterRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_thematic_chapter.f_sequence</code>. 章节序列
     */
    public final TableField<ThematicChapterRecord, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "章节序列");

    /**
     * The column <code>course-study.t_thematic_chapter.f_create_time</code>. 创建时间
     */
    public final TableField<ThematicChapterRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_thematic_chapter.f_version_id</code>. 版本ID
     */
    public final TableField<ThematicChapterRecord, String> VERSION_ID = createField("f_version_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "版本ID");

    /**
     * The column <code>course-study.t_thematic_chapter.f_learn_sequence</code>. 按顺序完成 0 没顺序 1 按顺序学习
     */
    public final TableField<ThematicChapterRecord, Integer> LEARN_SEQUENCE = createField("f_learn_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "按顺序完成 0 没顺序 1 按顺序学习");

    /**
     * The column <code>course-study.t_thematic_chapter.f_order_type</code>. 未使用
     */
    public final TableField<ThematicChapterRecord, Integer> ORDER_TYPE = createField("f_order_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "未使用");

    /**
     * The column <code>course-study.t_thematic_chapter.f_in_order</code>. 未使用
     */
    public final TableField<ThematicChapterRecord, Integer> IN_ORDER = createField("f_in_order", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "未使用");

    /**
     * The column <code>course-study.t_thematic_chapter.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    public final TableField<ThematicChapterRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "删除标识 0 未删除， 1 已删除");

    /**
     * The column <code>course-study.t_thematic_chapter.f_is_cloud</code>. 是否云课程，0/null 否，1是
     */
    public final TableField<ThematicChapterRecord, Integer> IS_CLOUD = createField("f_is_cloud", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否云课程，0/null 否，1是");

    /**
     * The column <code>course-study.t_thematic_chapter.f_module</code>. 模块
     */
    public final TableField<ThematicChapterRecord, Integer> MODULE = createField("f_module", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "模块");

    /**
     * Create a <code>course-study.t_thematic_chapter</code> table reference
     */
    public ThematicChapter() {
        this("t_thematic_chapter", null);
    }

    /**
     * Create an aliased <code>course-study.t_thematic_chapter</code> table reference
     */
    public ThematicChapter(String alias) {
        this(alias, THEMATIC_CHAPTER);
    }

    private ThematicChapter(String alias, Table<ThematicChapterRecord> aliased) {
        this(alias, aliased, null);
    }

    private ThematicChapter(String alias, Table<ThematicChapterRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "专题班主题章节");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ThematicChapterRecord> getPrimaryKey() {
        return Keys.KEY_T_THEMATIC_CHAPTER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ThematicChapterRecord>> getKeys() {
        return Arrays.<UniqueKey<ThematicChapterRecord>>asList(Keys.KEY_T_THEMATIC_CHAPTER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ThematicChapter as(String alias) {
        return new ThematicChapter(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ThematicChapter rename(String name) {
        return new ThematicChapter(name, null);
    }
}
