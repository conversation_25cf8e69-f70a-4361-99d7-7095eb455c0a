package com.zxy.product.course.api.course;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseSectionStudyProgress;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.mongodb.CourseStudyLog;
import java.util.List;
import java.util.Optional;
import org.jooq.impl.TableImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 * @date 2017/4/25
 * https://gitlab.zhixueyun.com/zxy/zxy-student-frontend/wikis/course-process
 */
@RemoteService(timeout = 50000)
public interface CourseProcessService {
    String COURSE_PROGRESS_PRE_UPDATE_SET = "COURSE_PROGRESS_PRE_UPDATE_SET";
    String COURSE_PROGRESS_PRE_UPDATE_MAP = "COURSE_PROGRESS_PRE_UPDATE_MAP";

    /**
     * 开始学习
     * @param memberId
     * @param sectionId
     * @param clientType
     * @return
     */
    @Transactional
    CourseStudyLog startSectionLog(String memberId, String sectionId, Integer clientType);
    @Transactional
    CourseStudyLog startSectionLog(String memberId, String sectionId, Integer clientType,Long createTime, String path);

    /**
     * 提交doc学习进度
     * @param log
     * @param location
     * @param table
     * @return
     */
    @Transactional
    CourseSectionStudyProgress docProcess(CourseStudyLog log,Optional<String> location, Optional<Integer> studyTime, Integer configMaxTime, String path);

    /**
     * 提交media进度
     * @param log
     * @param studyTime
     * @param location
     * @param configMaxTime
     * @param table
     * @return
     */
    @Transactional
    CourseSectionStudyProgress mediaProcess(CourseStudyLog log,Integer studyTime,Integer sumTime,Integer location,Integer configProcess, Integer configPlay, Integer configMaxTime, String path);

    /**
     * scorm日志id
     * @param scormLogId
     * @return
     */
    @Transactional
    CourseSectionStudyProgress scormProcess(String scormLogId);
    /**
     *  计算课程进度
     * @param memberId
     * @param courseId
     * @return
     */
    @Transactional
    CourseStudyProgress updateCourseStudyProgress(String memberId, String courseId, Boolean isCourse);
    @Transactional
    void updateMediaLength(String sectionId, Integer videoTotalTime);
    @Transactional
    void updateCourseStudyAsyn(String courseId,String memberId);
    @Transactional
    CourseStudyLog startScormLog(String currentUserId,String sectionId, String scormId, Integer clientType, String path);

    @Transactional
    int batchStudyProgress(List<CourseStudyLog> items,String organizationId);

    @Transactional
    void updateCourseStudyCache();

    /**
     * 清除处理过学习进度的redis
     * @param memberId
     * @param courseId
     */
    void deleteCourseStudyRedis(String memberId,String courseId);
    /**
     * 新增专题log
     * @param memberId
     * @param subjectId
     * @param sectionId
     * @param finishStatus
     * @param studyTime
     */
    @Transactional
    void insertSubjectLog(String memberId, String subjectId, String sectionId, int finishStatus, int studyTime, Long createTime);

    @Transactional
    Integer filterStudyTime(CourseStudyLog log, Integer studyTime,Optional<Integer> resourceTimeOptional, Integer configMaxTime, TableImpl<?> table);

    @Transactional
	CourseStudyLog insertSubLog(String memberId, Integer clientType, CourseChapterSection section, String path);

    @Transactional
	void updateLastStudy(CourseStudyLog log);

    @Transactional
	CourseStudyLog findCourseSectionLog(String logId, String path);

    @Transactional
	void insertOrUpdateCourseSectionLogDay(String memberId, String courseId, String date, Integer clientType,
			int studyTime);



    /**
     *课程时长统计表与流水表不统一,以log流水为标准
     */
    void repairCourseProgressTotalTimeByLog();

    /**
     *课程时长统计表与流水表不统一,以分表log流水为标准
     */
    void repairCourseProgressTotalTimeBySplitLog();

    /**
     *专题中同一门课程时长重复记录问题
     */
    void repairSubjectRepeatCourseRecord();

    /**
     * 单窗口播放限制
     * @param courseId
     * @param currentUserId
     * @param remove  是否删除当前课程缓存 1:移除
     * @param type  1:课程ID  0:章节ID
     * @return
     */
  Integer onlyOneVideoPlayLimit(String courseId, String currentUserId, Integer remove,  Integer type);

    /**
     * 单窗口播放限制，按照logId判断
     */
  Integer onlyOneVideoPlayLimitLog(String logId, String currentUserId);


  /**
   * 播放视频重新设置缓存.
   * @param id
   * @param currentUserId
   */
  void onlyOneVideoPlaySetCache(String id, String currentUserId);

  /**
   * APP学习时长超过24小时修复.
   */
  void repairAppStudyTotalTimeMoreThan24Hours();

  /**
   * APP学习时长超过24小时LOG交叉问题处理.
   */
  void repairAppStudyTotalTimeConfuse();

  /**
   * APP学习时长超过24小时将修复后流水记录合并更新人课天时长信息.
   */
  void repairSomeDayStudyTimeAppStudyTotalTimeMoreThan24Hours();
}
