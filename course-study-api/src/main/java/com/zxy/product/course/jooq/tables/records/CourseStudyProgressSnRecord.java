/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseStudyProgressSn;
import com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgressSn;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProgressSnRecord extends UpdatableRecordImpl<CourseStudyProgressSnRecord> implements ICourseStudyProgressSn {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_member_id</code>.
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_member_id</code>.
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_course_id</code>.
     */
    @Override
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_course_id</code>.
     */
    @Override
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_begin_time</code>.
     */
    @Override
    public void setBeginTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_begin_time</code>.
     */
    @Override
    public Long getBeginTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_type</code>.
     */
    @Override
    public void setType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_type</code>.
     */
    @Override
    public Integer getType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_is_required</code>.
     */
    @Override
    public void setIsRequired(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_is_required</code>.
     */
    @Override
    public Integer getIsRequired() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_finish_status</code>.
     */
    @Override
    public void setFinishStatus(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_finish_status</code>.
     */
    @Override
    public Integer getFinishStatus() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_finish_time</code>.
     */
    @Override
    public void setFinishTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_finish_time</code>.
     */
    @Override
    public Long getFinishTime() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_study_total_time</code>.
     */
    @Override
    public void setStudyTotalTime(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_study_total_time</code>.
     */
    @Override
    public Integer getStudyTotalTime() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_register_time</code>.
     */
    @Override
    public void setRegisterTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_register_time</code>.
     */
    @Override
    public Long getRegisterTime() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_last_access_time</code>.
     */
    @Override
    public void setLastAccessTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_last_access_time</code>.
     */
    @Override
    public Long getLastAccessTime() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(11);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.t_course_version_id</code>.
     */
    @Override
    public void setCourseVersionId(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.t_course_version_id</code>.
     */
    @Override
    public String getCourseVersionId() {
        return (String) get(12);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_mark_member_id</code>.
     */
    @Override
    public void setMarkMemberId(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_mark_member_id</code>.
     */
    @Override
    public String getMarkMemberId() {
        return (String) get(13);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_mark_time</code>.
     */
    @Override
    public void setMarkTime(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_mark_time</code>.
     */
    @Override
    public Long getMarkTime() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_completed_rate</code>.
     */
    @Override
    public void setCompletedRate(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_completed_rate</code>.
     */
    @Override
    public Integer getCompletedRate() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_current_chapter_id</code>.
     */
    @Override
    public void setCurrentChapterId(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_current_chapter_id</code>.
     */
    @Override
    public String getCurrentChapterId() {
        return (String) get(16);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_current_section_id</code>.
     */
    @Override
    public void setCurrentSectionId(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_current_section_id</code>.
     */
    @Override
    public String getCurrentSectionId() {
        return (String) get(17);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_push_id</code>.
     */
    @Override
    public void setPushId(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_push_id</code>.
     */
    @Override
    public String getPushId() {
        return (String) get(18);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_visits</code>.
     */
    @Override
    public void setVisits(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_visits</code>.
     */
    @Override
    public Integer getVisits() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_last_modify_time</code>.
     */
    @Override
    public void setLastModifyTime(Long value) {
        set(20, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_last_modify_time</code>.
     */
    @Override
    public Long getLastModifyTime() {
        return (Long) get(20);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(21, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(21);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_subject_finish_time</code>. 专题必修课完成时间
     */
    @Override
    public void setSubjectFinishTime(Long value) {
        set(22, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_subject_finish_time</code>. 专题必修课完成时间
     */
    @Override
    public Long getSubjectFinishTime() {
        return (Long) get(22);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_completion_times</code>. 完成次数
     */
    @Override
    public void setCompletionTimes(Integer value) {
        set(23, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_completion_times</code>. 完成次数
     */
    @Override
    public Integer getCompletionTimes() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress_sn.f_latest_completion_time</code>. 最新完成时间
     */
    @Override
    public void setLatestCompletionTime(Long value) {
        set(24, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress_sn.f_latest_completion_time</code>. 最新完成时间
     */
    @Override
    public Long getLatestCompletionTime() {
        return (Long) get(24);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseStudyProgressSn from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setBeginTime(from.getBeginTime());
        setType(from.getType());
        setIsRequired(from.getIsRequired());
        setFinishStatus(from.getFinishStatus());
        setFinishTime(from.getFinishTime());
        setStudyTotalTime(from.getStudyTotalTime());
        setRegisterTime(from.getRegisterTime());
        setLastAccessTime(from.getLastAccessTime());
        setCreateTime(from.getCreateTime());
        setCourseVersionId(from.getCourseVersionId());
        setMarkMemberId(from.getMarkMemberId());
        setMarkTime(from.getMarkTime());
        setCompletedRate(from.getCompletedRate());
        setCurrentChapterId(from.getCurrentChapterId());
        setCurrentSectionId(from.getCurrentSectionId());
        setPushId(from.getPushId());
        setVisits(from.getVisits());
        setLastModifyTime(from.getLastModifyTime());
        setModifyDate(from.getModifyDate());
        setSubjectFinishTime(from.getSubjectFinishTime());
        setCompletionTimes(from.getCompletionTimes());
        setLatestCompletionTime(from.getLatestCompletionTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseStudyProgressSn> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseStudyProgressSnRecord
     */
    public CourseStudyProgressSnRecord() {
        super(CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN);
    }

    /**
     * Create a detached, initialised CourseStudyProgressSnRecord
     */
    public CourseStudyProgressSnRecord(String id, String memberId, String courseId, Long beginTime, Integer type, Integer isRequired, Integer finishStatus, Long finishTime, Integer studyTotalTime, Long registerTime, Long lastAccessTime, Long createTime, String courseVersionId, String markMemberId, Long markTime, Integer completedRate, String currentChapterId, String currentSectionId, String pushId, Integer visits, Long lastModifyTime, Timestamp modifyDate, Long subjectFinishTime, Integer completionTimes, Long latestCompletionTime) {
        super(CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN);

        set(0, id);
        set(1, memberId);
        set(2, courseId);
        set(3, beginTime);
        set(4, type);
        set(5, isRequired);
        set(6, finishStatus);
        set(7, finishTime);
        set(8, studyTotalTime);
        set(9, registerTime);
        set(10, lastAccessTime);
        set(11, createTime);
        set(12, courseVersionId);
        set(13, markMemberId);
        set(14, markTime);
        set(15, completedRate);
        set(16, currentChapterId);
        set(17, currentSectionId);
        set(18, pushId);
        set(19, visits);
        set(20, lastModifyTime);
        set(21, modifyDate);
        set(22, subjectFinishTime);
        set(23, completionTimes);
        set(24, latestCompletionTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressSnEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressSnEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressSnEntity)source;
        pojo.into(this);
        return true;
    }
}
