package com.zxy.product.course.api.model.mentor;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.dto.model.mentor.*;
import com.zxy.product.course.entity.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 数智导师Service
 * <AUTHOR>
 * @date 2024年04月23日 16:37
 */
@RemoteService
public interface ModelMentorService {

    /**
     * 编辑课程Or专题的模型问答
     * @param exampleDTO 模型问答入参DTO
     * @return 编辑结果
     */
    @Transactional(rollbackFor = Exception.class)
    AiPresetExample editPresetExample(PresetExampleDTO exampleDTO);

    /**
     * 根据主键查询模型问答详情
     * @param id 模型问答主键
     * @return 模型问答详情
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    AiPresetExample singlePresetExample(String id);

    /**
     * 资源Id随机返回用户的可能想问
     *
     * @param resourceId    资源Id（课程Or专题Id）
     * @param operationType 操作类型(OperationTypeEnum枚举类)
     * @param resourceType
     * @return 用户预设想问数据集
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    List<AiPresetExample> presetMayWantAsk(String resourceId, Integer operationType, Optional<Integer> resourceType);

    /**
     * 数智导师问答PageE数据
     *
     * @param page 当前页
     * @param pageSize 每页展示数据条数
     * @param presetQuestion 预设问题
     * @param resourceName 资源名称 （专题名称Or课程名称）
     * @param resourceType 资源类型（专题Or课程）
     * @param questionType 问题类型
     * @param resourceNameOrder 资源名称排序（非必填）
     * @param updateTimeOrder 更新时间排序（非必填）
     * @return 数智导师问答Page数据
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    PagedResult<AiPresetExample> examplePage(Integer page, Integer pageSize,
                                             Optional<String> presetQuestion, Optional<String> resourceName,
                                             Optional<Integer> resourceType, Optional<Integer> questionType,
                                             Optional<Integer> resourceNameOrder,
                                             Optional<Integer> updateTimeOrder,Integer defaultOrder);

    /**
     * 数智导师反馈Page数据
     * @param page 当前页
     * @param pageSize 每页展示数据条数
     * @param question 问题内容
     * @param resourceName 资源名称（专题名称Or课程名称）
     * @param resourceType（专题Or课程）
     * @return 数智导师反馈Page数据
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    PagedResult<AiFeedback> feedbackPage(Integer page, Integer pageSize,
                                         Optional<String> question,
                                         Optional<String> resourceName,
                                         Optional<Integer> resourceType);

    /**
     * 主键查询数智导师反馈详情
     * @param id 数智导师反馈主键
     * @return 数智导师反馈详情
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    AiFeedback singleFeedback(String id);

    /**
     * 数智导师用户提问
     * @param mentorDTO 数智导师用户提问入参DTO
     * @return 数智导师用户提问出参VO
     */
    @Transactional(rollbackFor = Exception.class)
    AiMentor mentorMemberAsk(MentorDTO mentorDTO);

    /**
     * 数智导师用户重新生成答案
     * @param editAnswerDTO 数智导师用户修正答案入参DTO
     * @return 数智导师用户重新生成答案出参VO
     */
    @Transactional(rollbackFor = Exception.class)
    AiMentor memberAgainAsk(EditAnswerDTO editAnswerDTO);

    /**
     * 数智导师用户反馈
     * @param feedbackDTO 数智导师用户反馈入参DTO
     * @return 数智导师出参VO
     */
    @Transactional(rollbackFor = Exception.class)
    AiMentor mentorMemberFeedback(FeedbackDTO feedbackDTO);

    /**
     * 数智导师用户修改反馈
     * @param feedbackDTO 数智导师用户反馈入参DTO
     * @return 数智导师出参VO
     */
    @Transactional(rollbackFor = Exception.class)
    AiMentor mentorEditFeedback(FeedbackDTO feedbackDTO);

    /**
     * 数智导师聊天数据
     * @param chatQueryDTO 数智导师聊天入参DTO
     * @return 数智导师聊天出参VO
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    List<AiMentor> mentorChatBoxCollect(ChatQueryDTO chatQueryDTO);

    /**
     * 数智导师V2：课件笔记Page列表页
     * @param page 当前页
     * @param pageSize 每页展示数据条数
     * @param courseNameOpt 条件查询课程名称（非必填）
     * @param summaryContentOpt 条件查询笔记内容（非必填）
     * @return 数智导师V2课件笔记Page列表页
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CoursewareNote> coursewareNotePage(Integer page, Integer pageSize,
                                            Optional<String> courseNameOpt,
                                            Optional<String> summaryContentOpt);

    /**
     * 数智导师V2：根据Id查询课件笔记
     * @param id 课件笔记Id
     * @return 根据Id查询到的课件笔记数据
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    CoursewareNote singleCoursewareNote(String id);


    /**
     * 数智导师V2：查询当前学员端某个课件笔记的回显版本
     * @param courseId 课程Id
     * @param sectionId 课件Id
     * @return 查询当前学员端某个课件笔记的回显版本
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    Map<String,Object> currentWareNote(String courseId, String sectionId);

    /**
     * 数智导师V2：切换课件笔记版本
     * @param wareNoteId 课件笔记Id
     * @return 切换课件笔记版本出参
     */
    @Transactional(rollbackFor = Exception.class)
    Map<String,String> switchWareNote(String wareNoteId);


    /**
     * 数智导师V2：课件笔记送审
     * @param wareNoteId 课件笔记Id
     * @return 送审的课件笔记Map信息
     */
    @Transactional(rollbackFor = Exception.class)
    Map<String,String> sendWareNoteAudit(String wareNoteId);


    /**
     * 数智导师V2：审核课件笔记
     * @param id 课件笔记审核Id
     * @param memberId 当前操作用户Id
     * @param auditStatus 审核状态
     * @param auditOpinionOpt 审核意见（非必填）
     * @return 审核的课件笔记对象
     */
    @Transactional(rollbackFor = Exception.class)
    Map<String,String> auditWareNote(String id , String memberId, Integer auditStatus,
                                      Optional<String> auditOpinionOpt);

    /**
     * 数智导师V2：修正课件笔记
     * @param id 课件笔记Id
     * @param memberId 当前登录的用户Id
     * @param summaryContent 笔记摘要
     * @param noteVersionJson 课件笔记版本集合JSON串
     * @return 修正课件笔记
     */
    @Transactional(rollbackFor = Exception.class)
    Map<String,String> editCoursewareNote(String id,String memberId,
                                      String summaryContent, String noteVersionJson);

    /**
     * 数智导师V2：课件笔记审核Page列表数据
     * @param page 当前页
     * @param pageSize 每页展示数据条数
     * @param courseNameOpt 条件查询课程名称（非必填）
     * @param summaryContentOpt 条件查询课件摘要（非必填）
     * @return 课件笔记审核Page列表数据
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CoursewareNoteAudit> wareNoteAuditPage(Integer page, Integer pageSize,
                                                   Optional<String> courseNameOpt,
                                                   Optional<String> summaryContentOpt);

    /**
     * 数智导师V2：根据Id查询课件笔记审核详情
     * @param id 课件笔记审核Id
     * @return 课件笔记审核数据
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    CoursewareNoteAudit singleWareNoteAudit(String id);

    /**
     * 数智导师V2：课程笔记Page列表页
     * @param page              当前页
     * @param pageSize          每页展示数据条数
     * @param courseNameOpt     条件查询课程名称（非必填）
     * @param summaryContentOpt 条件查询课程摘要（非必填）
     * @param businessTypeOpt
     * @return 课程笔记Page列表页
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseMainNote> courseMainNotePage(Integer page, Integer pageSize,
                                                   Optional<String> courseNameOpt,
                                                   Optional<String> summaryContentOpt, Optional<Integer> businessTypeOpt);


    /**
     * 数智导师V2：根据Id查询课程笔记详情
     * @param id 课程笔记Id
     * @return 根据Id查询课程笔记详情
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    CourseMainNote singleCourseMainNote(String id);

    /**
     * 数智导师V2：查询课程笔记回显版本以及版本信息集合
     * @param courseId 课程Id
     * @return 查询课程笔记回显版本以及版本信息集合
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String,Object> currentMainNote(String courseId);

    /**
     * 数智导师V2：切换课程笔记版本
     *
     * @param mainNoteId        课程笔记Id
     * @param currentMainNoteId
     * @return 切换课程笔记版本
     */
    @Transactional(rollbackFor = Exception.class)
    Map<String, String> switchMainNote(String mainNoteId, String currentMainNoteId);

    /**
     * 课程笔记送审
     * @param mainNoteId 课程笔记Id
     * @return 送审的课程笔记Map信息
     */
    @Transactional(rollbackFor = Exception.class)
    Map<String,String> sendMainNoteAudit(String mainNoteId);

    /**
     * 数智导师V2：审核课程笔记
     * @param id 课程笔记审核Id
     * @param memberId 用户Id
     * @param auditStatus 审核状态
     * @param auditOpinionOpt 审核内容
     * @return 审核课程笔记后出参
     */
    @Transactional(rollbackFor = Exception.class)
    Map<String,String> auditMainNote(String id , String memberId, Integer auditStatus,
                                     Optional<String> auditOpinionOpt);

    /**
     * 数智导师V2：修正课程笔记（V3需要补充管理员修改课件笔记后推送九天的业务逻辑）
     * @param id 课件笔记Id
     * @param memberId 当前登录的用户Id
     * @param summaryContent 笔记摘要
     * @param mainVersionJson 课件笔记版本集合JSON串
     * @return 出参
     */
    @Transactional(rollbackFor = Exception.class)
    Map<String,String> editCourseMainNote(String id, String memberId,
                                      String summaryContent, String mainVersionJson);


    /**
     * 数智导师V2：课程笔记审核Page列表数据
     * @param page 当前页
     * @param pageSize 每页展示数据条数
     * @param courseNameOpt 条件查询课程名称（非必填）
     * @param summaryContentOpt 条件查询课件摘要（非必填）
     * @return 课程笔记审核Page列表数据
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    PagedResult<CourseMainNoteAudit> mainNoteAuditPage(Integer page, Integer pageSize,
                                                       Optional<String> courseNameOpt,
                                                       Optional<String> summaryContentOpt);

    /**
     * 数智导师V2：根据Id查询课程笔记审核详情
     * @param id 课件笔记审核Id
     * @return 课程笔记审核详情数据
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    CourseMainNoteAudit singleMainNoteAudit(String id);

    /**
     * 数智导师V2：学员端查询课件摘要And 知识点相关内容
     * @param courseId 课程Id
     * @param sectionId 小节Id
     * @return 课件摘要And 知识点相关内容
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<SmartNoteDTO.Content> smartNoteOfCourse(String courseId,String sectionId);

    /**
     * 数智导师V2：学员端查询课程摘要And知识点相关内容
     * @param courseId 课程Id
     * @return 课程摘要And知识点相关内容
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    CourseMainNote mainNoteOfCourse(String courseId);

    /**
     * 同步课程的模型问答
     * @param orderingExample 数智导师V2猜你想问即将落盘的JSON串
     * @param courseId 课程Id
     */
    @Transactional(rollbackFor = Exception.class)
    void synchronizePresetExample(String orderingExample, String courseId,String resourceType);

    /**
     * 同步课程笔记
     * @param orderingNote 数智导师V2课程笔记即将落盘的JSON串
     * @param courseId 课程Id
     * @param memberId 用户Id
     */
    @Transactional(rollbackFor = Exception.class)
    void synchronizeMainNote(String orderingNote,String courseId, String memberId);

    /**
     * 同步课程知识点
     * @param orderingNoteKnowledge 数智导师V2课程笔记知识点即将罗盘的JSON串
     * @param mainNoteId 课程笔记Id
     */
    @Transactional(rollbackFor = Exception.class)
    void synchronizeMainNoteKnowledge(String orderingNoteKnowledge, String mainNoteId);


    /**
     * 同步课件笔记
     * @param orderingNote 数智导师V2课件笔记即将落盘的JSON串
     * @param courseId 课程Id
     * @param sectionId 章节Id
     * @param memberId 用户Id
     */
    @Transactional(rollbackFor = Exception.class)
    void synchronizeWareNote(String orderingNote,String courseId, String sectionId,String memberId);

    /**
     * 同步课件知识点
     * @param orderingNoteKnowledge 数智导师V2课件知识点即将落盘的JSON串
     * @param wareNoteId 课件笔记Id
     */
    @Transactional(rollbackFor = Exception.class)
    void synchronizeWareNoteKnowledge(String orderingNoteKnowledge, String wareNoteId);

    /**
     * 备用：转化小节Id（三方数据中以1,2,3标识数据Id，需要根据下标转化成Mysql中的主键）
     * @param courseId 课程Id
     * @return 根据课程Id查询小节相关信息
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<ConvertDTO> convertPrimaryKey(String courseId);

    /**
     * 改变课程状态
     * @param courseId 课程Id
     * @param mentorState 课件笔记状态 （0无需生成 1未生成 2待审核  3未审核通过  4已审核通过  5开启数智导师  6开启无摘要数智导师）
     */
    @Transactional(rollbackFor = Exception.class)
    void changeCourseState(String courseId, Integer mentorState);

    /**
     * 数智导师V2（优化）:添加预设问题（这里只能添加通用问题）
     *
     * @param presetQuestion 预设问题（通用问题）
     * @param presetAnswer   预设答案（通用答案）
     * @param resourceType
     */
    @Transactional(rollbackFor = Exception.class)
    void addPresetExample(String presetQuestion, String presetAnswer, Optional<Integer> resourceType);

    /**
     * 数智导师V2（优化）：删除预设问题（这里只能删除通用问题）
     * @param id 预设问题（通用问题Id）
     */
    @Transactional(rollbackFor = Exception.class)
    void delPresetExample(String id);

    @Transactional(rollbackFor = Exception.class)
    Optional<CourseMainNote>  findCourseMainNoteByCourseId(String courseId);

    @Transactional
    void insertCourseMainNote(CourseMainNote courseMainNote);

    @Transactional
    void insertAiPresetExampleList(List<AiPresetExample> aiPresetExampleList);

    @Transactional
    void insertCourseMainNoteVersionList(List<CourseMainNoteVersion> courseMainNoteVersionList);

    @Transactional
    void deleteCourseMainNote(String id);

    @Transactional
    void deleteCourseMainNoteVersion(String courseMainNoteId);

    @Transactional
    void deleteAiPresetExample(String id);

    @Transactional
    String synchronizeMainNote(String orderingNote, String courseId, String number, Integer businessType);

    void editReleaseStatus(String courseMainNoteId, Integer releaseStatus,String memberId);

    void editSwitchMentor(String courseId, Integer status, Integer type);

    PagedResult<CourseMainNote> courseMainNoteContentPage(Integer page, Integer pageSize, Optional<String> courseNameOpt, Optional<String> organizationId, Optional<Integer> businessTypeOpt, Optional<Integer> switchMentorOpt);

    /**
     * 查询专题是否有发布的摘要
     */
    @Transactional(readOnly = true)
    Integer getSubjectSummaryStatus(String subjectId);

    @Transactional(readOnly = true)
    Integer getSubjectSummary(String courseIds);

    @Transactional
    boolean findCourseMainNoteIsThereAny(List<String> collect);

    @Transactional
    Map<String,String> sendMainNoteAuditAndThrough(String mainNoteId);
}
