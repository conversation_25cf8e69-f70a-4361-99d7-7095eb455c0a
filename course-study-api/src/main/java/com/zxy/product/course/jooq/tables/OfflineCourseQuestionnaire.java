/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.OfflineCourseQuestionnaireRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 线下课程调查问卷表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OfflineCourseQuestionnaire extends TableImpl<OfflineCourseQuestionnaireRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_offline_course_questionnaire</code>
     */
    public static final OfflineCourseQuestionnaire OFFLINE_COURSE_QUESTIONNAIRE = new OfflineCourseQuestionnaire();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OfflineCourseQuestionnaireRecord> getRecordType() {
        return OfflineCourseQuestionnaireRecord.class;
    }

    /**
     * The column <code>course-study.t_offline_course_questionnaire.f_id</code>. 主键ID
     */
    public final TableField<OfflineCourseQuestionnaireRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键ID");

    /**
     * The column <code>course-study.t_offline_course_questionnaire.f_name</code>. 线下课程调查问卷名
     */
    public final TableField<OfflineCourseQuestionnaireRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false), this, "线下课程调查问卷名");

    /**
     * The column <code>course-study.t_offline_course_questionnaire.f_create_member_id</code>. 创建人
     */
    public final TableField<OfflineCourseQuestionnaireRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "创建人");

    /**
     * The column <code>course-study.t_offline_course_questionnaire.f_create_time</code>. 创建时间
     */
    public final TableField<OfflineCourseQuestionnaireRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>course-study.t_offline_course_questionnaire</code> table reference
     */
    public OfflineCourseQuestionnaire() {
        this("t_offline_course_questionnaire", null);
    }

    /**
     * Create an aliased <code>course-study.t_offline_course_questionnaire</code> table reference
     */
    public OfflineCourseQuestionnaire(String alias) {
        this(alias, OFFLINE_COURSE_QUESTIONNAIRE);
    }

    private OfflineCourseQuestionnaire(String alias, Table<OfflineCourseQuestionnaireRecord> aliased) {
        this(alias, aliased, null);
    }

    private OfflineCourseQuestionnaire(String alias, Table<OfflineCourseQuestionnaireRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "线下课程调查问卷表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<OfflineCourseQuestionnaireRecord> getPrimaryKey() {
        return Keys.KEY_T_OFFLINE_COURSE_QUESTIONNAIRE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<OfflineCourseQuestionnaireRecord>> getKeys() {
        return Arrays.<UniqueKey<OfflineCourseQuestionnaireRecord>>asList(Keys.KEY_T_OFFLINE_COURSE_QUESTIONNAIRE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OfflineCourseQuestionnaire as(String alias) {
        return new OfflineCourseQuestionnaire(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public OfflineCourseQuestionnaire rename(String name) {
        return new OfflineCourseQuestionnaire(name, null);
    }
}
