/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.RemodelingExternalCourseBusiness;
import com.zxy.product.course.jooq.tables.interfaces.IRemodelingExternalCourseBusiness;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 重塑培训计划-外部在线课程与网大业务关联关系表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RemodelingExternalCourseBusinessRecord extends UpdatableRecordImpl<RemodelingExternalCourseBusinessRecord> implements Record6<String, String, String, String, String, Long>, IRemodelingExternalCourseBusiness {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_remodeling_external_course_business.f_id</code>. id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_remodeling_external_course_business.f_id</code>. id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_remodeling_external_course_business.f_external_course_id</code>. 外部课程id
     */
    @Override
    public void setExternalCourseId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_remodeling_external_course_business.f_external_course_id</code>. 外部课程id
     */
    @Override
    public String getExternalCourseId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_remodeling_external_course_business.f_app_id</code>. 各供应商分配的appid
     */
    @Override
    public void setAppId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_remodeling_external_course_business.f_app_id</code>. 各供应商分配的appid
     */
    @Override
    public String getAppId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_remodeling_external_course_business.f_course_id</code>. 时长新增到网大对应的课程id
     */
    @Override
    public void setCourseId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_remodeling_external_course_business.f_course_id</code>. 时长新增到网大对应的课程id
     */
    @Override
    public String getCourseId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_remodeling_external_course_business.f_course_reference_id</code>. 时长新增到课程内对应的章节id
     */
    @Override
    public void setCourseReferenceId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_remodeling_external_course_business.f_course_reference_id</code>. 时长新增到课程内对应的章节id
     */
    @Override
    public String getCourseReferenceId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_remodeling_external_course_business.f_create_time</code>. 记录创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_remodeling_external_course_business.f_create_time</code>. 记录创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, String, String, Long> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, String, String, Long> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.EXTERNAL_COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.APP_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.COURSE_REFERENCE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getExternalCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getAppId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getCourseReferenceId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RemodelingExternalCourseBusinessRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RemodelingExternalCourseBusinessRecord value2(String value) {
        setExternalCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RemodelingExternalCourseBusinessRecord value3(String value) {
        setAppId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RemodelingExternalCourseBusinessRecord value4(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RemodelingExternalCourseBusinessRecord value5(String value) {
        setCourseReferenceId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RemodelingExternalCourseBusinessRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RemodelingExternalCourseBusinessRecord values(String value1, String value2, String value3, String value4, String value5, Long value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IRemodelingExternalCourseBusiness from) {
        setId(from.getId());
        setExternalCourseId(from.getExternalCourseId());
        setAppId(from.getAppId());
        setCourseId(from.getCourseId());
        setCourseReferenceId(from.getCourseReferenceId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IRemodelingExternalCourseBusiness> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached RemodelingExternalCourseBusinessRecord
     */
    public RemodelingExternalCourseBusinessRecord() {
        super(RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS);
    }

    /**
     * Create a detached, initialised RemodelingExternalCourseBusinessRecord
     */
    public RemodelingExternalCourseBusinessRecord(String id, String externalCourseId, String appId, String courseId, String courseReferenceId, Long createTime) {
        super(RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS);

        set(0, id);
        set(1, externalCourseId);
        set(2, appId);
        set(3, courseId);
        set(4, courseReferenceId);
        set(5, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.RemodelingExternalCourseBusinessEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.RemodelingExternalCourseBusinessEntity pojo = (com.zxy.product.course.jooq.tables.pojos.RemodelingExternalCourseBusinessEntity)source;
        pojo.into(this);
        return true;
    }
}
