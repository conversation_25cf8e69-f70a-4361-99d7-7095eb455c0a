/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseSectionStudyLogQo;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyLogQoEntity extends BaseEntity implements ICourseSectionStudyLogQo {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  courseId;
    private String  sectionId;
    private String  sectionScromId;
    private Integer clientType;
    private Integer completedRate;
    private Integer finishStatus;
    private Integer studyTime;
    private Long    commitTime;
    private String  submitText;
    private String  auditMemberId;
    private Integer score;
    private String  comments;
    private Integer examStatus;
    private String  lessonLocation;

    public CourseSectionStudyLogQoEntity() {}

    public CourseSectionStudyLogQoEntity(CourseSectionStudyLogQoEntity value) {
        this.memberId = value.memberId;
        this.courseId = value.courseId;
        this.sectionId = value.sectionId;
        this.sectionScromId = value.sectionScromId;
        this.clientType = value.clientType;
        this.completedRate = value.completedRate;
        this.finishStatus = value.finishStatus;
        this.studyTime = value.studyTime;
        this.commitTime = value.commitTime;
        this.submitText = value.submitText;
        this.auditMemberId = value.auditMemberId;
        this.score = value.score;
        this.comments = value.comments;
        this.examStatus = value.examStatus;
        this.lessonLocation = value.lessonLocation;
    }

    public CourseSectionStudyLogQoEntity(
        String  id,
        String  memberId,
        String  courseId,
        String  sectionId,
        String  sectionScromId,
        Integer clientType,
        Integer completedRate,
        Integer finishStatus,
        Integer studyTime,
        Long    createTime,
        Long    commitTime,
        String  submitText,
        String  auditMemberId,
        Integer score,
        String  comments,
        Integer examStatus,
        String  lessonLocation
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.courseId = courseId;
        this.sectionId = sectionId;
        this.sectionScromId = sectionScromId;
        this.clientType = clientType;
        this.completedRate = completedRate;
        this.finishStatus = finishStatus;
        this.studyTime = studyTime;
        super.setCreateTime(createTime);
        this.commitTime = commitTime;
        this.submitText = submitText;
        this.auditMemberId = auditMemberId;
        this.score = score;
        this.comments = comments;
        this.examStatus = examStatus;
        this.lessonLocation = lessonLocation;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public String getSectionId() {
        return this.sectionId;
    }

    @Override
    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    @Override
    public String getSectionScromId() {
        return this.sectionScromId;
    }

    @Override
    public void setSectionScromId(String sectionScromId) {
        this.sectionScromId = sectionScromId;
    }

    @Override
    public Integer getClientType() {
        return this.clientType;
    }

    @Override
    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    @Override
    public Integer getCompletedRate() {
        return this.completedRate;
    }

    @Override
    public void setCompletedRate(Integer completedRate) {
        this.completedRate = completedRate;
    }

    @Override
    public Integer getFinishStatus() {
        return this.finishStatus;
    }

    @Override
    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    @Override
    public Integer getStudyTime() {
        return this.studyTime;
    }

    @Override
    public void setStudyTime(Integer studyTime) {
        this.studyTime = studyTime;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getCommitTime() {
        return this.commitTime;
    }

    @Override
    public void setCommitTime(Long commitTime) {
        this.commitTime = commitTime;
    }

    @Override
    public String getSubmitText() {
        return this.submitText;
    }

    @Override
    public void setSubmitText(String submitText) {
        this.submitText = submitText;
    }

    @Override
    public String getAuditMemberId() {
        return this.auditMemberId;
    }

    @Override
    public void setAuditMemberId(String auditMemberId) {
        this.auditMemberId = auditMemberId;
    }

    @Override
    public Integer getScore() {
        return this.score;
    }

    @Override
    public void setScore(Integer score) {
        this.score = score;
    }

    @Override
    public String getComments() {
        return this.comments;
    }

    @Override
    public void setComments(String comments) {
        this.comments = comments;
    }

    @Override
    public Integer getExamStatus() {
        return this.examStatus;
    }

    @Override
    public void setExamStatus(Integer examStatus) {
        this.examStatus = examStatus;
    }

    @Override
    public String getLessonLocation() {
        return this.lessonLocation;
    }

    @Override
    public void setLessonLocation(String lessonLocation) {
        this.lessonLocation = lessonLocation;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseSectionStudyLogQoEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(courseId);
        sb.append(", ").append(sectionId);
        sb.append(", ").append(sectionScromId);
        sb.append(", ").append(clientType);
        sb.append(", ").append(completedRate);
        sb.append(", ").append(finishStatus);
        sb.append(", ").append(studyTime);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(commitTime);
        sb.append(", ").append(submitText);
        sb.append(", ").append(auditMemberId);
        sb.append(", ").append(score);
        sb.append(", ").append(comments);
        sb.append(", ").append(examStatus);
        sb.append(", ").append(lessonLocation);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseSectionStudyLogQo from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setSectionId(from.getSectionId());
        setSectionScromId(from.getSectionScromId());
        setClientType(from.getClientType());
        setCompletedRate(from.getCompletedRate());
        setFinishStatus(from.getFinishStatus());
        setStudyTime(from.getStudyTime());
        setCreateTime(from.getCreateTime());
        setCommitTime(from.getCommitTime());
        setSubmitText(from.getSubmitText());
        setAuditMemberId(from.getAuditMemberId());
        setScore(from.getScore());
        setComments(from.getComments());
        setExamStatus(from.getExamStatus());
        setLessonLocation(from.getLessonLocation());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseSectionStudyLogQo> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseSectionStudyLogQoEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseSectionStudyLogQoRecord r = new com.zxy.product.course.jooq.tables.records.CourseSectionStudyLogQoRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SECTION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SECTION_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SECTION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SECTION_SCROM_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SECTION_SCROM_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SECTION_SCROM_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.CLIENT_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.CLIENT_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.CLIENT_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMPLETED_RATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMPLETED_RATE, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMPLETED_RATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.FINISH_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.FINISH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.FINISH_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMMIT_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMMIT_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMMIT_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SUBMIT_TEXT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SUBMIT_TEXT, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SUBMIT_TEXT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.AUDIT_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.AUDIT_MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.AUDIT_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SCORE, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMMENTS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMMENTS, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.COMMENTS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.EXAM_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.EXAM_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.EXAM_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.LESSON_LOCATION) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.LESSON_LOCATION, record.getValue(com.zxy.product.course.jooq.tables.CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.LESSON_LOCATION));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
