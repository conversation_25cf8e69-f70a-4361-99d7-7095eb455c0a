/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseInfoDjyp;

import javax.annotation.Generated;


/**
 * 课程清单表（党建云屏使用）
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfoDjypEntity extends BaseEntity implements ICourseInfoDjyp {

    private static final long serialVersionUID = 1L;

    private String  courseId;
    private Integer status;

    public CourseInfoDjypEntity() {}

    public CourseInfoDjypEntity(CourseInfoDjypEntity value) {
        this.courseId = value.courseId;
        this.status = value.status;
    }

    public CourseInfoDjypEntity(
        String  id,
        String  courseId,
        Long    createTime,
        Integer status
    ) {
        super.setId(id);
        this.courseId = courseId;
        super.setCreateTime(createTime);
        this.status = status;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseInfoDjypEntity (");

        sb.append(getId());
        sb.append(", ").append(courseId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(status);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseInfoDjyp from) {
        setId(from.getId());
        setCourseId(from.getCourseId());
        setCreateTime(from.getCreateTime());
        setStatus(from.getStatus());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseInfoDjyp> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseInfoDjypEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseInfoDjypRecord r = new com.zxy.product.course.jooq.tables.records.CourseInfoDjypRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseInfoDjyp.COURSE_INFO_DJYP.STATUS));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
