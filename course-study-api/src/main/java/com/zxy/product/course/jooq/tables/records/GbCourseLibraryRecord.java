/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.GbCourseLibrary;
import com.zxy.product.course.jooq.tables.interfaces.IGbCourseLibrary;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 高标党建-课程库
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GbCourseLibraryRecord extends UpdatableRecordImpl<GbCourseLibraryRecord> implements Record12<String, String, String, Integer, String, String, String, String, String, String, Long, Timestamp>, IGbCourseLibrary {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_gb_course_library.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_course_name</code>. 课程名称
     */
    @Override
    public void setCourseName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_course_name</code>. 课程名称
     */
    @Override
    public String getCourseName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_code</code>. 课程编码
     */
    @Override
    public void setCode(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_code</code>. 课程编码
     */
    @Override
    public String getCode() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_sort</code>. 序号
     */
    @Override
    public void setSort(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_sort</code>. 序号
     */
    @Override
    public Integer getSort() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_introduction</code>. 课程简介
     */
    @Override
    public void setIntroduction(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_introduction</code>. 课程简介
     */
    @Override
    public String getIntroduction() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_object_oriented</code>. 面向对象
     */
    @Override
    public void setObjectOriented(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_object_oriented</code>. 面向对象
     */
    @Override
    public String getObjectOriented() {
        return (String) get(5);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_outline</code>. 课程大纲
     */
    @Override
    public void setOutline(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_outline</code>. 课程大纲
     */
    @Override
    public String getOutline() {
        return (String) get(6);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_cover</code>. 课程封面
     */
    @Override
    public void setCover(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_cover</code>. 课程封面
     */
    @Override
    public String getCover() {
        return (String) get(7);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_cover_path</code>. 课程封面路径
     */
    @Override
    public void setCoverPath(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_cover_path</code>. 课程封面路径
     */
    @Override
    public String getCoverPath() {
        return (String) get(8);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_course_info_id</code>. 在线课程id
     */
    @Override
    public void setCourseInfoId(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_course_info_id</code>. 在线课程id
     */
    @Override
    public String getCourseInfoId() {
        return (String) get(9);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>course-study.t_gb_course_library.f_update_time</code>. 修改时间
     */
    @Override
    public void setUpdateTime(Timestamp value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_gb_course_library.f_update_time</code>. 修改时间
     */
    @Override
    public Timestamp getUpdateTime() {
        return (Timestamp) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, String, String, Integer, String, String, String, String, String, String, Long, Timestamp> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, String, String, Integer, String, String, String, String, String, String, Long, Timestamp> valuesRow() {
        return (Row12) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.COURSE_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.INTRODUCTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.OBJECT_ORIENTED;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.OUTLINE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.COVER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.COVER_PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.COURSE_INFO_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field11() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field12() {
        return GbCourseLibrary.GB_COURSE_LIBRARY.UPDATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getCourseName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getIntroduction();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getObjectOriented();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getOutline();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getCover();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getCoverPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getCourseInfoId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value11() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value12() {
        return getUpdateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value2(String value) {
        setCourseName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value3(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value4(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value5(String value) {
        setIntroduction(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value6(String value) {
        setObjectOriented(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value7(String value) {
        setOutline(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value8(String value) {
        setCover(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value9(String value) {
        setCoverPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value10(String value) {
        setCourseInfoId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value11(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord value12(Timestamp value) {
        setUpdateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GbCourseLibraryRecord values(String value1, String value2, String value3, Integer value4, String value5, String value6, String value7, String value8, String value9, String value10, Long value11, Timestamp value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IGbCourseLibrary from) {
        setId(from.getId());
        setCourseName(from.getCourseName());
        setCode(from.getCode());
        setSort(from.getSort());
        setIntroduction(from.getIntroduction());
        setObjectOriented(from.getObjectOriented());
        setOutline(from.getOutline());
        setCover(from.getCover());
        setCoverPath(from.getCoverPath());
        setCourseInfoId(from.getCourseInfoId());
        setCreateTime(from.getCreateTime());
        setUpdateTime(from.getUpdateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IGbCourseLibrary> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached GbCourseLibraryRecord
     */
    public GbCourseLibraryRecord() {
        super(GbCourseLibrary.GB_COURSE_LIBRARY);
    }

    /**
     * Create a detached, initialised GbCourseLibraryRecord
     */
    public GbCourseLibraryRecord(String id, String courseName, String code, Integer sort, String introduction, String objectOriented, String outline, String cover, String coverPath, String courseInfoId, Long createTime, Timestamp updateTime) {
        super(GbCourseLibrary.GB_COURSE_LIBRARY);

        set(0, id);
        set(1, courseName);
        set(2, code);
        set(3, sort);
        set(4, introduction);
        set(5, objectOriented);
        set(6, outline);
        set(7, cover);
        set(8, coverPath);
        set(9, courseInfoId);
        set(10, createTime);
        set(11, updateTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.GbCourseLibraryEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.GbCourseLibraryEntity pojo = (com.zxy.product.course.jooq.tables.pojos.GbCourseLibraryEntity)source;
        pojo.into(this);
        return true;
    }
}
