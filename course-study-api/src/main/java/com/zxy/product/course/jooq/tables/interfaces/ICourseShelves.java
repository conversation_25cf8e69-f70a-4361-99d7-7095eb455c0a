/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 课程上架通知表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseShelves extends Serializable {

    /**
     * Setter for <code>course-study.t_course_shelves.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_shelves.f_course_id</code>. 课程id
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_course_id</code>. 课程id
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_course_shelves.f_notice_user</code>. 是否通知用户（0：否，1：是）
     */
    public void setNoticeUser(Integer value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_notice_user</code>. 是否通知用户（0：否，1：是）
     */
    public Integer getNoticeUser();

    /**
     * Setter for <code>course-study.t_course_shelves.f_notice_user_text</code>. 通知用户内容，通知规则为“是”时有值
     */
    public void setNoticeUserText(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_notice_user_text</code>. 通知用户内容，通知规则为“是”时有值
     */
    public String getNoticeUserText();

    /**
     * Setter for <code>course-study.t_course_shelves.f_notice_manager</code>. 是否通知门户管理员（0：否，1：是）
     */
    public void setNoticeManager(Integer value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_notice_manager</code>. 是否通知门户管理员（0：否，1：是）
     */
    public Integer getNoticeManager();

    /**
     * Setter for <code>course-study.t_course_shelves.f_notice_manager_text</code>. 通知管理员内容，通知规则为“是”时有值
     */
    public void setNoticeManagerText(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_notice_manager_text</code>. 通知管理员内容，通知规则为“是”时有值
     */
    public String getNoticeManagerText();

    /**
     * Setter for <code>course-study.t_course_shelves.f_create_member_id</code>. 该条上架信息的创建人id
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_create_member_id</code>. 该条上架信息的创建人id
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>course-study.t_course_shelves.f_status</code>. 状态,0代表无效,1代表有效
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_status</code>. 状态,0代表无效,1代表有效
     */
    public Integer getStatus();

    /**
     * Setter for <code>course-study.t_course_shelves.f_rule</code>. 上架规则，0 不影响 1 影响学习中
     */
    public void setRule(Integer value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_rule</code>. 上架规则，0 不影响 1 影响学习中
     */
    public Integer getRule();

    /**
     * Setter for <code>course-study.t_course_shelves.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_shelves.f_notice_user_content</code>. 通知用户内容(带格式)，通知规则为“是”时有值
     */
    public void setNoticeUserContent(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_notice_user_content</code>. 通知用户内容(带格式)，通知规则为“是”时有值
     */
    public String getNoticeUserContent();

    /**
     * Setter for <code>course-study.t_course_shelves.f_notice_user_title</code>. 通知用户内容标题，通知规则为“是”时有值
     */
    public void setNoticeUserTitle(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_notice_user_title</code>. 通知用户内容标题，通知规则为“是”时有值
     */
    public String getNoticeUserTitle();

    /**
     * Setter for <code>course-study.t_course_shelves.f_notice_manager_content</code>. 通知管理员内容(带格式)，通知规则为“是”时有值
     */
    public void setNoticeManagerContent(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_notice_manager_content</code>. 通知管理员内容(带格式)，通知规则为“是”时有值
     */
    public String getNoticeManagerContent();

    /**
     * Setter for <code>course-study.t_course_shelves.f_notice_manager_title</code>. 通知管理员内容标题，通知规则为“是”时有值
     */
    public void setNoticeManagerTitle(String value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_notice_manager_title</code>. 通知管理员内容标题，通知规则为“是”时有值
     */
    public String getNoticeManagerTitle();

    /**
     * Setter for <code>course-study.t_course_shelves.f_is_first</code>. 是否首次发布(测试中、第一次正式发布为首次)0-是 1-否
     */
    public void setIsFirst(Integer value);

    /**
     * Getter for <code>course-study.t_course_shelves.f_is_first</code>. 是否首次发布(测试中、第一次正式发布为首次)0-是 1-否
     */
    public Integer getIsFirst();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseShelves
     */
    public void from(ICourseShelves from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseShelves
     */
    public <E extends ICourseShelves> E into(E into);
}
