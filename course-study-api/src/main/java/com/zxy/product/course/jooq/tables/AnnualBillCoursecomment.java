/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.AnnualBillCoursecommentRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnnualBillCoursecomment extends TableImpl<AnnualBillCoursecommentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_annual_bill_coursecomment</code>
     */
    public static final AnnualBillCoursecomment ANNUAL_BILL_COURSECOMMENT = new AnnualBillCoursecomment();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnnualBillCoursecommentRecord> getRecordType() {
        return AnnualBillCoursecommentRecord.class;
    }

    /**
     * The column <code>course-study.t_annual_bill_coursecomment.f_id</code>.
     */
    public final TableField<AnnualBillCoursecommentRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_annual_bill_coursecomment.f_member_id</code>.
     */
    public final TableField<AnnualBillCoursecommentRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_annual_bill_coursecomment.f_course_comment</code>. 课程下评论数量
     */
    public final TableField<AnnualBillCoursecommentRecord, Integer> COURSE_COMMENT = createField("f_course_comment", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "课程下评论数量");

    /**
     * Create a <code>course-study.t_annual_bill_coursecomment</code> table reference
     */
    public AnnualBillCoursecomment() {
        this("t_annual_bill_coursecomment", null);
    }

    /**
     * Create an aliased <code>course-study.t_annual_bill_coursecomment</code> table reference
     */
    public AnnualBillCoursecomment(String alias) {
        this(alias, ANNUAL_BILL_COURSECOMMENT);
    }

    private AnnualBillCoursecomment(String alias, Table<AnnualBillCoursecommentRecord> aliased) {
        this(alias, aliased, null);
    }

    private AnnualBillCoursecomment(String alias, Table<AnnualBillCoursecommentRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnnualBillCoursecommentRecord> getPrimaryKey() {
        return Keys.KEY_T_ANNUAL_BILL_COURSECOMMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnnualBillCoursecommentRecord>> getKeys() {
        return Arrays.<UniqueKey<AnnualBillCoursecommentRecord>>asList(Keys.KEY_T_ANNUAL_BILL_COURSECOMMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnnualBillCoursecomment as(String alias) {
        return new AnnualBillCoursecomment(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnnualBillCoursecomment rename(String name) {
        return new AnnualBillCoursecomment(name, null);
    }
}
