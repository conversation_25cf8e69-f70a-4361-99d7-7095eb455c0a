package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.ThematicChapterSectionEntity;

/**
 * 专题班课程
 * <AUTHOR>
 *
 */
public class ThematicChapterSection extends ThematicChapterSectionEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = -8740060995948614025L;

    /** 必修 */
    public static final Integer IS_REQUIRED = 1;
    /** 选修 */
    public static final Integer IS_NOT_REQUIRED = 0;

    /**
     * 专题节类型： 课程
     */
    public static final int SECTION_TYPE_COURSE = 10;

    /**
     * 节类型 - 任务
     */
    public static final int SECTION_TYPE_TASK = 8; //任务
    public static final int SECTION_TYPE_EXAM = 9; //考试
    public static final int SECTION_TYPE_RESEARCH = 12;//调研
    public static final int SECTION_TYPE_EVALUATION = 13;//评估

    /**
     * 专题节类型： 专题
     */
    public static final Integer SECTION_TYPE_SUBJECT = 16;

    /**
     * 专题节类型： 知识
     */
    public static final Integer SECTION_TYPE_KNOWLEDGE = 15;


    /**
     * 节类型 - 文档
     */
    public static final int SECTION_TYPE_DOC = 1;
    public static final int SECTION_TYPE_SCROM = 4;
    public static final int SECTION_TYPE_VIDEO = 6;

    /**  调研评估来源：0选择  */
    public static final Integer RESEARCH_SOURCE_TYPE_SELECT = 0;
    /**  调研评估来源：1自己新增 */
    public static final Integer RESEARCH_SOURCE_TYPE_ADD = 1;
    private Integer memberStatus;
    private Integer completedRate;

    private String lecturer;
    private Integer courseTime;

	public String getLecturer() {
		return lecturer;
	}
	public void setLecturer(String lecturer) {
		this.lecturer = lecturer;
	}
	public Integer getCourseTime() {
		return courseTime;
	}
	public void setCourseTime(Integer courseTime) {
		this.courseTime = courseTime;
	}
	public Integer getMemberStatus() {
		return memberStatus;
	}
	public void setMemberStatus(Integer memberStatus) {
		this.memberStatus = memberStatus;
	}

	public Integer getCompletedRate() {
		return completedRate;
	}

	public void setCompletedRate(Integer completedRate) {
		this.completedRate = completedRate;
	}

}
