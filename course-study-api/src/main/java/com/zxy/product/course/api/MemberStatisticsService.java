package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.MemberStatistics;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/7/19.
 */
@RemoteService
public interface MemberStatisticsService {


    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<MemberStatistics> rankForMemberCourse(int size, String organizationId, List<String> memberIds);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<MemberStatistics> rankForMemberStudyTime (int size, String organizationId, List<String> memberIds);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    MemberStatistics getByMemberId(String memberId);

    /**
     * 查询当前用户的学时排名
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer getCurrentMemberStudyTimeRank(String memberId, String organizationId, List<String> memberIds);

    /**
     * 查询当前用户的课程数排名
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer getCurrentMemberCourseRank(String memberId, String organizationId, List<String> memberIds);

}
