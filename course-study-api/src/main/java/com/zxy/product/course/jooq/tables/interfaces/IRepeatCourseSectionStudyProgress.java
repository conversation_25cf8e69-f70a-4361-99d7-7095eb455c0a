/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 重复专题学习进度记录备份
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IRepeatCourseSectionStudyProgress extends Serializable {

    /**
     * Setter for <code>course-study.t_repeat_course_section_study_progress.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_repeat_course_section_study_progress.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_repeat_course_section_study_progress.f_member_id</code>. 用户ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_repeat_course_section_study_progress.f_member_id</code>. 用户ID
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_repeat_course_section_study_progress.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_repeat_course_section_study_progress.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_repeat_course_section_study_progress.f_section_id</code>. 课程节ID
     */
    public void setSectionId(String value);

    /**
     * Getter for <code>course-study.t_repeat_course_section_study_progress.f_section_id</code>. 课程节ID
     */
    public String getSectionId();

    /**
     * Setter for <code>course-study.t_repeat_course_section_study_progress.f_section_type</code>. 学习开始时间
     */
    public void setSectionType(Long value);

    /**
     * Getter for <code>course-study.t_repeat_course_section_study_progress.f_section_type</code>. 学习开始时间
     */
    public Long getSectionType();

    /**
     * Setter for <code>course-study.t_repeat_course_section_study_progress.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷
     */
    public void setFinishStatus(Integer value);

    /**
     * Getter for <code>course-study.t_repeat_course_section_study_progress.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷
     */
    public Integer getFinishStatus();

    /**
     * Setter for <code>course-study.t_repeat_course_section_study_progress.f_study_total_time</code>. 学习累计时长，单位秒
     */
    public void setStudyTotalTime(Integer value);

    /**
     * Getter for <code>course-study.t_repeat_course_section_study_progress.f_study_total_time</code>. 学习累计时长，单位秒
     */
    public Integer getStudyTotalTime();

    /**
     * Setter for <code>course-study.t_repeat_course_section_study_progress.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_repeat_course_section_study_progress.f_create_time</code>.
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IRepeatCourseSectionStudyProgress
     */
    public void from(IRepeatCourseSectionStudyProgress from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IRepeatCourseSectionStudyProgress
     */
    public <E extends IRepeatCourseSectionStudyProgress> E into(E into);
}
