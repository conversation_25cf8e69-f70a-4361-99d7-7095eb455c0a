/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IAiQuestion;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AiQuestionEntity extends BaseEntity implements IAiQuestion {

    private static final long serialVersionUID = 1L;

    private String question;

    public AiQuestionEntity() {}

    public AiQuestionEntity(AiQuestionEntity value) {
        this.question = value.question;
    }

    public AiQuestionEntity(
        String id,
        String question
    ) {
        super.setId(id);
        this.question = question;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getQuestion() {
        return this.question;
    }

    @Override
    public void setQuestion(String question) {
        this.question = question;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AiQuestionEntity (");

        sb.append(getId());
        sb.append(", ").append(question);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAiQuestion from) {
        setId(from.getId());
        setQuestion(from.getQuestion());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAiQuestion> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends AiQuestionEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.AiQuestionRecord r = new com.zxy.product.course.jooq.tables.records.AiQuestionRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.AiQuestion.AI_QUESTION.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.AiQuestion.AI_QUESTION.ID, record.getValue(com.zxy.product.course.jooq.tables.AiQuestion.AI_QUESTION.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.AiQuestion.AI_QUESTION.QUESTION) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.AiQuestion.AI_QUESTION.QUESTION, record.getValue(com.zxy.product.course.jooq.tables.AiQuestion.AI_QUESTION.QUESTION));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
