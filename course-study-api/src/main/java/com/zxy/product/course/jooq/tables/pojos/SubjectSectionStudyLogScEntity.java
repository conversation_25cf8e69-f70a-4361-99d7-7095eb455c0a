/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ISubjectSectionStudyLogSc;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectSectionStudyLogScEntity extends BaseEntity implements ISubjectSectionStudyLogSc {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  subjectId;
    private String  sectionId;
    private Integer clientType;
    private Integer finishStatus;
    private Integer studyTime;

    public SubjectSectionStudyLogScEntity() {}

    public SubjectSectionStudyLogScEntity(SubjectSectionStudyLogScEntity value) {
        this.memberId = value.memberId;
        this.subjectId = value.subjectId;
        this.sectionId = value.sectionId;
        this.clientType = value.clientType;
        this.finishStatus = value.finishStatus;
        this.studyTime = value.studyTime;
    }

    public SubjectSectionStudyLogScEntity(
        String  id,
        String  memberId,
        String  subjectId,
        String  sectionId,
        Integer clientType,
        Integer finishStatus,
        Integer studyTime,
        Long    createTime
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.subjectId = subjectId;
        this.sectionId = sectionId;
        this.clientType = clientType;
        this.finishStatus = finishStatus;
        this.studyTime = studyTime;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getSubjectId() {
        return this.subjectId;
    }

    @Override
    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }

    @Override
    public String getSectionId() {
        return this.sectionId;
    }

    @Override
    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    @Override
    public Integer getClientType() {
        return this.clientType;
    }

    @Override
    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    @Override
    public Integer getFinishStatus() {
        return this.finishStatus;
    }

    @Override
    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    @Override
    public Integer getStudyTime() {
        return this.studyTime;
    }

    @Override
    public void setStudyTime(Integer studyTime) {
        this.studyTime = studyTime;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SubjectSectionStudyLogScEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(subjectId);
        sb.append(", ").append(sectionId);
        sb.append(", ").append(clientType);
        sb.append(", ").append(finishStatus);
        sb.append(", ").append(studyTime);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubjectSectionStudyLogSc from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setSubjectId(from.getSubjectId());
        setSectionId(from.getSectionId());
        setClientType(from.getClientType());
        setFinishStatus(from.getFinishStatus());
        setStudyTime(from.getStudyTime());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubjectSectionStudyLogSc> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends SubjectSectionStudyLogScEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.SubjectSectionStudyLogScRecord r = new com.zxy.product.course.jooq.tables.records.SubjectSectionStudyLogScRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.SUBJECT_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.SUBJECT_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.SUBJECT_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.SECTION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.SECTION_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.SECTION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.CLIENT_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.CLIENT_TYPE, record.getValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.CLIENT_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.FINISH_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.FINISH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.FINISH_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
