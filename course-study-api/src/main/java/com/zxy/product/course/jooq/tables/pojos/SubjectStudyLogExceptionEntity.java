/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ISubjectStudyLogException;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectStudyLogExceptionEntity extends BaseEntity implements ISubjectStudyLogException {

    private static final long serialVersionUID = 1L;

    private String  logId;
    private String  memberId;
    private String  sectionId;
    private String  subjectId;
    private Integer studyTime;
    private Integer finishStatus;
    private Long    createTimeOld;
    private Integer flag;

    public SubjectStudyLogExceptionEntity() {}

    public SubjectStudyLogExceptionEntity(SubjectStudyLogExceptionEntity value) {
        this.logId = value.logId;
        this.memberId = value.memberId;
        this.sectionId = value.sectionId;
        this.subjectId = value.subjectId;
        this.studyTime = value.studyTime;
        this.finishStatus = value.finishStatus;
        this.createTimeOld = value.createTimeOld;
        this.flag = value.flag;
    }

    public SubjectStudyLogExceptionEntity(
        String  id,
        String  logId,
        String  memberId,
        String  sectionId,
        String  subjectId,
        Integer studyTime,
        Integer finishStatus,
        Long    createTimeOld,
        Integer flag,
        Long    createTime
    ) {
        super.setId(id);
        this.logId = logId;
        this.memberId = memberId;
        this.sectionId = sectionId;
        this.subjectId = subjectId;
        this.studyTime = studyTime;
        this.finishStatus = finishStatus;
        this.createTimeOld = createTimeOld;
        this.flag = flag;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getLogId() {
        return this.logId;
    }

    @Override
    public void setLogId(String logId) {
        this.logId = logId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getSectionId() {
        return this.sectionId;
    }

    @Override
    public void setSectionId(String sectionId) {
        this.sectionId = sectionId;
    }

    @Override
    public String getSubjectId() {
        return this.subjectId;
    }

    @Override
    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }

    @Override
    public Integer getStudyTime() {
        return this.studyTime;
    }

    @Override
    public void setStudyTime(Integer studyTime) {
        this.studyTime = studyTime;
    }

    @Override
    public Integer getFinishStatus() {
        return this.finishStatus;
    }

    @Override
    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    @Override
    public Long getCreateTimeOld() {
        return this.createTimeOld;
    }

    @Override
    public void setCreateTimeOld(Long createTimeOld) {
        this.createTimeOld = createTimeOld;
    }

    @Override
    public Integer getFlag() {
        return this.flag;
    }

    @Override
    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SubjectStudyLogExceptionEntity (");

        sb.append(getId());
        sb.append(", ").append(logId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(sectionId);
        sb.append(", ").append(subjectId);
        sb.append(", ").append(studyTime);
        sb.append(", ").append(finishStatus);
        sb.append(", ").append(createTimeOld);
        sb.append(", ").append(flag);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubjectStudyLogException from) {
        setId(from.getId());
        setLogId(from.getLogId());
        setMemberId(from.getMemberId());
        setSectionId(from.getSectionId());
        setSubjectId(from.getSubjectId());
        setStudyTime(from.getStudyTime());
        setFinishStatus(from.getFinishStatus());
        setCreateTimeOld(from.getCreateTimeOld());
        setFlag(from.getFlag());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubjectStudyLogException> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends SubjectStudyLogExceptionEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.SubjectStudyLogExceptionRecord r = new com.zxy.product.course.jooq.tables.records.SubjectStudyLogExceptionRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.LOG_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.LOG_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.LOG_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.SECTION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.SECTION_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.SECTION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.SUBJECT_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.SUBJECT_ID, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.SUBJECT_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.STUDY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.STUDY_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.STUDY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.FINISH_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.FINISH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.FINISH_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.CREATE_TIME_OLD) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.CREATE_TIME_OLD, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.CREATE_TIME_OLD));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.FLAG) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.FLAG, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.FLAG));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
