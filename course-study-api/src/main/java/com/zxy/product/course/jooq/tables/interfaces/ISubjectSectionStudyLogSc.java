/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISubjectSectionStudyLogSc extends Serializable {

    /**
     * Setter for <code>course-study.t_subject_section_study_log_sc.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_subject_section_study_log_sc.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_subject_section_study_log_sc.f_member_id</code>.
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_subject_section_study_log_sc.f_member_id</code>.
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_subject_section_study_log_sc.f_subject_id</code>.
     */
    public void setSubjectId(String value);

    /**
     * Getter for <code>course-study.t_subject_section_study_log_sc.f_subject_id</code>.
     */
    public String getSubjectId();

    /**
     * Setter for <code>course-study.t_subject_section_study_log_sc.f_section_id</code>.
     */
    public void setSectionId(String value);

    /**
     * Getter for <code>course-study.t_subject_section_study_log_sc.f_section_id</code>.
     */
    public String getSectionId();

    /**
     * Setter for <code>course-study.t_subject_section_study_log_sc.f_client_type</code>.
     */
    public void setClientType(Integer value);

    /**
     * Getter for <code>course-study.t_subject_section_study_log_sc.f_client_type</code>.
     */
    public Integer getClientType();

    /**
     * Setter for <code>course-study.t_subject_section_study_log_sc.f_finish_status</code>.
     */
    public void setFinishStatus(Integer value);

    /**
     * Getter for <code>course-study.t_subject_section_study_log_sc.f_finish_status</code>.
     */
    public Integer getFinishStatus();

    /**
     * Setter for <code>course-study.t_subject_section_study_log_sc.f_study_time</code>.
     */
    public void setStudyTime(Integer value);

    /**
     * Getter for <code>course-study.t_subject_section_study_log_sc.f_study_time</code>.
     */
    public Integer getStudyTime();

    /**
     * Setter for <code>course-study.t_subject_section_study_log_sc.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_subject_section_study_log_sc.f_create_time</code>.
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISubjectSectionStudyLogSc
     */
    public void from(ISubjectSectionStudyLogSc from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISubjectSectionStudyLogSc
     */
    public <E extends ISubjectSectionStudyLogSc> E into(E into);
}
