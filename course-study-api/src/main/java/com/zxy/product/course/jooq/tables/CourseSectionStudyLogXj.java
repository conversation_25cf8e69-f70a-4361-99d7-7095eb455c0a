/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseSectionStudyLogXjRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyLogXj extends TableImpl<CourseSectionStudyLogXjRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_section_study_log_xj</code>
     */
    public static final CourseSectionStudyLogXj COURSE_SECTION_STUDY_LOG_XJ = new CourseSectionStudyLogXj();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseSectionStudyLogXjRecord> getRecordType() {
        return CourseSectionStudyLogXjRecord.class;
    }

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_id</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_member_id</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_course_id</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_section_id</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> SECTION_ID = createField("f_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_section_scrom_id</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> SECTION_SCROM_ID = createField("f_section_scrom_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_client_type</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_completed_rate</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, Integer> COMPLETED_RATE = createField("f_completed_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_finish_status</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_study_time</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_create_time</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_commit_time</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, Long> COMMIT_TIME = createField("f_commit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_submit_text</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> SUBMIT_TEXT = createField("f_submit_text", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_audit_member_id</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> AUDIT_MEMBER_ID = createField("f_audit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_score</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_comments</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> COMMENTS = createField("f_comments", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_exam_status</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, Integer> EXAM_STATUS = createField("f_exam_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_log_xj.f_lesson_location</code>.
     */
    public final TableField<CourseSectionStudyLogXjRecord, String> LESSON_LOCATION = createField("f_lesson_location", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * Create a <code>course-study.t_course_section_study_log_xj</code> table reference
     */
    public CourseSectionStudyLogXj() {
        this("t_course_section_study_log_xj", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_section_study_log_xj</code> table reference
     */
    public CourseSectionStudyLogXj(String alias) {
        this(alias, COURSE_SECTION_STUDY_LOG_XJ);
    }

    private CourseSectionStudyLogXj(String alias, Table<CourseSectionStudyLogXjRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseSectionStudyLogXj(String alias, Table<CourseSectionStudyLogXjRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseSectionStudyLogXjRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_SECTION_STUDY_LOG_XJ_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseSectionStudyLogXjRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseSectionStudyLogXjRecord>>asList(Keys.KEY_T_COURSE_SECTION_STUDY_LOG_XJ_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyLogXj as(String alias) {
        return new CourseSectionStudyLogXj(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseSectionStudyLogXj rename(String name) {
        return new CourseSectionStudyLogXj(name, null);
    }
}
