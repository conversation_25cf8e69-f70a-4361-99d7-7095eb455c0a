/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.SubjectSectionStudyLogNmRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubjectSectionStudyLogNm extends TableImpl<SubjectSectionStudyLogNmRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_subject_section_study_log_nm</code>
     */
    public static final SubjectSectionStudyLogNm SUBJECT_SECTION_STUDY_LOG_NM = new SubjectSectionStudyLogNm();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubjectSectionStudyLogNmRecord> getRecordType() {
        return SubjectSectionStudyLogNmRecord.class;
    }

    /**
     * The column <code>course-study.t_subject_section_study_log_nm.f_id</code>.
     */
    public final TableField<SubjectSectionStudyLogNmRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_nm.f_member_id</code>.
     */
    public final TableField<SubjectSectionStudyLogNmRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_nm.f_subject_id</code>.
     */
    public final TableField<SubjectSectionStudyLogNmRecord, String> SUBJECT_ID = createField("f_subject_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_nm.f_section_id</code>.
     */
    public final TableField<SubjectSectionStudyLogNmRecord, String> SECTION_ID = createField("f_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_nm.f_client_type</code>.
     */
    public final TableField<SubjectSectionStudyLogNmRecord, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_nm.f_finish_status</code>.
     */
    public final TableField<SubjectSectionStudyLogNmRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_nm.f_study_time</code>.
     */
    public final TableField<SubjectSectionStudyLogNmRecord, Integer> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_subject_section_study_log_nm.f_create_time</code>.
     */
    public final TableField<SubjectSectionStudyLogNmRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>course-study.t_subject_section_study_log_nm</code> table reference
     */
    public SubjectSectionStudyLogNm() {
        this("t_subject_section_study_log_nm", null);
    }

    /**
     * Create an aliased <code>course-study.t_subject_section_study_log_nm</code> table reference
     */
    public SubjectSectionStudyLogNm(String alias) {
        this(alias, SUBJECT_SECTION_STUDY_LOG_NM);
    }

    private SubjectSectionStudyLogNm(String alias, Table<SubjectSectionStudyLogNmRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubjectSectionStudyLogNm(String alias, Table<SubjectSectionStudyLogNmRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubjectSectionStudyLogNmRecord> getPrimaryKey() {
        return Keys.KEY_T_SUBJECT_SECTION_STUDY_LOG_NM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubjectSectionStudyLogNmRecord>> getKeys() {
        return Arrays.<UniqueKey<SubjectSectionStudyLogNmRecord>>asList(Keys.KEY_T_SUBJECT_SECTION_STUDY_LOG_NM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubjectSectionStudyLogNm as(String alias) {
        return new SubjectSectionStudyLogNm(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubjectSectionStudyLogNm rename(String name) {
        return new SubjectSectionStudyLogNm(name, null);
    }
}
