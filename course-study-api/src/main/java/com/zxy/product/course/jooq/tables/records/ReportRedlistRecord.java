/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.ReportRedlist;
import com.zxy.product.course.jooq.tables.interfaces.IReportRedlist;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 报表红名单管理表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ReportRedlistRecord extends UpdatableRecordImpl<ReportRedlistRecord> implements Record4<String, String, Long, Timestamp>, IReportRedlist {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_report_redlist.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_report_redlist.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_report_redlist.f_member_id</code>. 管理员id
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_report_redlist.f_member_id</code>. 管理员id
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_report_redlist.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_report_redlist.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>course-study.t_report_redlist.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_report_redlist.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(3);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, String, Long, Timestamp> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, String, Long, Timestamp> valuesRow() {
        return (Row4) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ReportRedlist.REPORT_REDLIST.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ReportRedlist.REPORT_REDLIST.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field3() {
        return ReportRedlist.REPORT_REDLIST.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field4() {
        return ReportRedlist.REPORT_REDLIST.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value3() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value4() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ReportRedlistRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ReportRedlistRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ReportRedlistRecord value3(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ReportRedlistRecord value4(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ReportRedlistRecord values(String value1, String value2, Long value3, Timestamp value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IReportRedlist from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IReportRedlist> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ReportRedlistRecord
     */
    public ReportRedlistRecord() {
        super(ReportRedlist.REPORT_REDLIST);
    }

    /**
     * Create a detached, initialised ReportRedlistRecord
     */
    public ReportRedlistRecord(String id, String memberId, Long createTime, Timestamp modifyDate) {
        super(ReportRedlist.REPORT_REDLIST);

        set(0, id);
        set(1, memberId);
        set(2, createTime);
        set(3, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.ReportRedlistEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.ReportRedlistEntity pojo = (com.zxy.product.course.jooq.tables.pojos.ReportRedlistEntity)source;
        pojo.into(this);
        return true;
    }
}
