package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.AuthenticatedGroupEntity;

public class AuthenticatedGroup extends AuthenticatedGroupEntity {
    private Integer order;
    private String audiences;
    private String zoneId;
    private String bannerPath;
    private String coverPath;
    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getAudiences() {
        return audiences;
    }

    public void setAudiences(String audiences) {
        this.audiences = audiences;
    }

    public String getZoneId() {
        return zoneId;
    }

    public void setZoneId(String zoneId) {
        this.zoneId = zoneId;
    }

    public String getBannerPath() {
        return bannerPath;
    }

    public void setBannerPath(String bannerPath) {
        this.bannerPath = bannerPath;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }
}
