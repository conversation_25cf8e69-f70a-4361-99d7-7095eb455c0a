/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProcess_2017;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProcess_2017Entity extends BaseEntity implements ICourseStudyProcess_2017 {

    private static final long serialVersionUID = 1L;

    private String    courseId;
    private String    memberId;
    private String    clientType;
    private Timestamp startTime;
    private Timestamp endTime;
    private Integer   duration;

    public CourseStudyProcess_2017Entity() {}

    public CourseStudyProcess_2017Entity(CourseStudyProcess_2017Entity value) {
        this.courseId = value.courseId;
        this.memberId = value.memberId;
        this.clientType = value.clientType;
        this.startTime = value.startTime;
        this.endTime = value.endTime;
        this.duration = value.duration;
    }

    public CourseStudyProcess_2017Entity(
        String    id,
        String    courseId,
        String    memberId,
        String    clientType,
        Timestamp startTime,
        Timestamp endTime,
        Integer   duration
    ) {
        super.setId(id);
        this.courseId = courseId;
        this.memberId = memberId;
        this.clientType = clientType;
        this.startTime = startTime;
        this.endTime = endTime;
        this.duration = duration;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getClientType() {
        return this.clientType;
    }

    @Override
    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    @Override
    public Timestamp getStartTime() {
        return this.startTime;
    }

    @Override
    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    @Override
    public Timestamp getEndTime() {
        return this.endTime;
    }

    @Override
    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    @Override
    public Integer getDuration() {
        return this.duration;
    }

    @Override
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseStudyProcess_2017Entity (");

        sb.append(getId());
        sb.append(", ").append(courseId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(clientType);
        sb.append(", ").append(startTime);
        sb.append(", ").append(endTime);
        sb.append(", ").append(duration);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseStudyProcess_2017 from) {
        setId(from.getId());
        setCourseId(from.getCourseId());
        setMemberId(from.getMemberId());
        setClientType(from.getClientType());
        setStartTime(from.getStartTime());
        setEndTime(from.getEndTime());
        setDuration(from.getDuration());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseStudyProcess_2017> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseStudyProcess_2017Entity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseStudyProcess_2017Record r = new com.zxy.product.course.jooq.tables.records.CourseStudyProcess_2017Record();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.CLIENT_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.CLIENT_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.CLIENT_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.START_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.START_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.START_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.END_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.END_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.END_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.DURATION) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.DURATION, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.DURATION));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
