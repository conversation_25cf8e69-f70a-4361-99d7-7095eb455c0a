package com.zxy.product.course.dto.knowledge.graph;

import java.io.Serializable;

/**
 * 知识图谱——专区必须课时DTO
 *
 * <AUTHOR>
 * @date 2023年10月20日 14:34
 */
public class ZoneRequiredHoursDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /**课程Id*/
    private String courseId;

    /**专区必修课程主键*/
    private String requiredCourseId;

    /**专区必修课程名称*/
    private String requiredCourseName;

    /**专区必须课程时长*/
    private Integer requiredCourseTime;

    public String getCourseId() { return courseId; }

    public void setCourseId(String courseId) { this.courseId = courseId; }

    public String getRequiredCourseId() { return requiredCourseId; }

    public void setRequiredCourseId(String requiredCourseId) {
        this.requiredCourseId = requiredCourseId;
    }

    public String getRequiredCourseName() {
        return requiredCourseName;
    }

    public void setRequiredCourseName(String requiredCourseName) {
        this.requiredCourseName = requiredCourseName;
    }

    public Integer getRequiredCourseTime() {
        return requiredCourseTime;
    }

    public void setRequiredCourseTime(Integer requiredCourseTime) {
        this.requiredCourseTime = requiredCourseTime;
    }

    @Override
    public String toString() {
        return "ZoneRequiredHoursDTO{" +
                "courseId='" + courseId + '\'' +
                ", requiredCourseId='" + requiredCourseId + '\'' +
                ", requiredCourseName='" + requiredCourseName + '\'' +
                ", requiredCourseTime=" + requiredCourseTime +
                '}';
    }
}
