/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseNoteRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 课程笔记表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseNote extends TableImpl<CourseNoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_note</code>
     */
    public static final CourseNote COURSE_NOTE = new CourseNote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseNoteRecord> getRecordType() {
        return CourseNoteRecord.class;
    }

    /**
     * The column <code>course-study.t_course_note.f_id</code>.
     */
    public final TableField<CourseNoteRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_note.f_course_id</code>. 课程id
     */
    public final TableField<CourseNoteRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程id");

    /**
     * The column <code>course-study.t_course_note.f_member_id</code>. 用户id
     */
    public final TableField<CourseNoteRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>course-study.t_course_note.f_content</code>. 评论内容
     */
    public final TableField<CourseNoteRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "评论内容");

    /**
     * The column <code>course-study.t_course_note.f_create_time</code>. 创建时间
     */
    public final TableField<CourseNoteRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>course-study.t_course_note</code> table reference
     */
    public CourseNote() {
        this("t_course_note", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_note</code> table reference
     */
    public CourseNote(String alias) {
        this(alias, COURSE_NOTE);
    }

    private CourseNote(String alias, Table<CourseNoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseNote(String alias, Table<CourseNoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "课程笔记表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseNoteRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_NOTE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseNoteRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseNoteRecord>>asList(Keys.KEY_T_COURSE_NOTE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseNote as(String alias) {
        return new CourseNote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseNote rename(String name) {
        return new CourseNote(name, null);
    }
}
