/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.ApplicationConfig;
import com.zxy.product.course.jooq.tables.interfaces.IApplicationConfig;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学习助手应用管理
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ApplicationConfigRecord extends UpdatableRecordImpl<ApplicationConfigRecord> implements Record9<String, Long, String, Integer, String, String, Integer, Integer, Integer>, IApplicationConfig {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_application_config.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_application_config.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>course-study.t_application_config.f_name</code>. 名称
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_name</code>. 名称
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_application_config.f_type</code>. 打开方式 0弹窗 1新窗口
     */
    @Override
    public void setType(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_type</code>. 打开方式 0弹窗 1新窗口
     */
    @Override
    public Integer getType() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>course-study.t_application_config.f_uri</code>. 应用链接
     */
    @Override
    public void setUri(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_uri</code>. 应用链接
     */
    @Override
    public String getUri() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_application_config.f_icon</code>. 图标
     */
    @Override
    public void setIcon(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_icon</code>. 图标
     */
    @Override
    public String getIcon() {
        return (String) get(5);
    }

    /**
     * Setter for <code>course-study.t_application_config.f_order</code>. 序号
     */
    @Override
    public void setOrder(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_order</code>. 序号
     */
    @Override
    public Integer getOrder() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_application_config.f_status</code>. 状态 0隐藏 1显示
     */
    @Override
    public void setStatus(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_status</code>. 状态 0隐藏 1显示
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>course-study.t_application_config.f_init</code>. 是否默认 0否 1是
     */
    @Override
    public void setInit(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_application_config.f_init</code>. 是否默认 0否 1是
     */
    @Override
    public Integer getInit() {
        return (Integer) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<String, Long, String, Integer, String, String, Integer, Integer, Integer> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<String, Long, String, Integer, String, String, Integer, Integer, Integer> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<String> field1() {
        return ApplicationConfig.APPLICATION_CONFIG.ID;
    }

    @Override
    public Field<Long> field2() {
        return ApplicationConfig.APPLICATION_CONFIG.CREATE_TIME;
    }

    @Override
    public Field<String> field3() {
        return ApplicationConfig.APPLICATION_CONFIG.NAME;
    }

    @Override
    public Field<Integer> field4() {
        return ApplicationConfig.APPLICATION_CONFIG.TYPE;
    }

    @Override
    public Field<String> field5() {
        return ApplicationConfig.APPLICATION_CONFIG.URI;
    }

    @Override
    public Field<String> field6() {
        return ApplicationConfig.APPLICATION_CONFIG.ICON;
    }

    @Override
    public Field<Integer> field7() {
        return ApplicationConfig.APPLICATION_CONFIG.ORDER;
    }

    @Override
    public Field<Integer> field8() {
        return ApplicationConfig.APPLICATION_CONFIG.STATUS;
    }

    @Override
    public Field<Integer> field9() {
        return ApplicationConfig.APPLICATION_CONFIG.INIT;
    }

    @Override
    public String value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getCreateTime();
    }

    @Override
    public String value3() {
        return getName();
    }

    @Override
    public Integer value4() {
        return getType();
    }

    @Override
    public String value5() {
        return getUri();
    }

    @Override
    public String value6() {
        return getIcon();
    }

    @Override
    public Integer value7() {
        return getOrder();
    }

    @Override
    public Integer value8() {
        return getStatus();
    }

    @Override
    public Integer value9() {
        return getInit();
    }

    @Override
    public ApplicationConfigRecord value1(String value) {
        setId(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord value3(String value) {
        setName(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord value4(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord value5(String value) {
        setUri(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord value6(String value) {
        setIcon(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord value7(Integer value) {
        setOrder(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord value8(Integer value) {
        setStatus(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord value9(Integer value) {
        setInit(value);
        return this;
    }

    @Override
    public ApplicationConfigRecord values(String value1, Long value2, String value3, Integer value4, String value5, String value6, Integer value7, Integer value8, Integer value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IApplicationConfig from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setName(from.getName());
        setType(from.getType());
        setUri(from.getUri());
        setIcon(from.getIcon());
        setOrder(from.getOrder());
        setStatus(from.getStatus());
        setInit(from.getInit());
    }

    @Override
    public <E extends IApplicationConfig> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ApplicationConfigRecord
     */
    public ApplicationConfigRecord() {
        super(ApplicationConfig.APPLICATION_CONFIG);
    }

    /**
     * Create a detached, initialised ApplicationConfigRecord
     */
    public ApplicationConfigRecord(String id, Long createTime, String name, Integer type, String uri, String icon, Integer order, Integer status, Integer init) {
        super(ApplicationConfig.APPLICATION_CONFIG);

        set(0, id);
        set(1, createTime);
        set(2, name);
        set(3, type);
        set(4, uri);
        set(5, icon);
        set(6, order);
        set(7, status);
        set(8, init);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.ApplicationConfigEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.ApplicationConfigEntity pojo = (com.zxy.product.course.jooq.tables.pojos.ApplicationConfigEntity)source;
        pojo.into(this);
        return true;
    }
}
