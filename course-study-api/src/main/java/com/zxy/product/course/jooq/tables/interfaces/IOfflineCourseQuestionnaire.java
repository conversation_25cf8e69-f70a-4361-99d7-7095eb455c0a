/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 线下课程调查问卷表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IOfflineCourseQuestionnaire extends Serializable {

    /**
     * Setter for <code>course-study.t_offline_course_questionnaire.f_id</code>. 主键ID
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_offline_course_questionnaire.f_id</code>. 主键ID
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_offline_course_questionnaire.f_name</code>. 线下课程调查问卷名
     */
    public void setName(String value);

    /**
     * Getter for <code>course-study.t_offline_course_questionnaire.f_name</code>. 线下课程调查问卷名
     */
    public String getName();

    /**
     * Setter for <code>course-study.t_offline_course_questionnaire.f_create_member_id</code>. 创建人
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>course-study.t_offline_course_questionnaire.f_create_member_id</code>. 创建人
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>course-study.t_offline_course_questionnaire.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_offline_course_questionnaire.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IOfflineCourseQuestionnaire
     */
    public void from(IOfflineCourseQuestionnaire from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IOfflineCourseQuestionnaire
     */
    public <E extends IOfflineCourseQuestionnaire> E into(E into);
}
