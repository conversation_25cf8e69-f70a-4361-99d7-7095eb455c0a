package com.zxy.product.course.api.remodeling;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.CourseQuestionnaireRecord;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;


/**
 * <AUTHOR>
 */
@RemoteService
public interface CourseQuestionnaireRecordService {


    /**
     * 下线满意度问卷-分页查询列表
     * @param page
     * @param pageSize
     * @param courseQuestionaryId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseQuestionnaireRecord> findPagedResult(Integer page, Integer pageSize, String courseQuestionaryId,
                                                           Optional<String> memberName, Optional<String> memberFullName,
                                                           Optional<String> organizationId);

    /**
     * 线下满意度问卷-查询学员作答详情
     * @param courseQuestionaryId
     * @param recordId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseQuestionnaireRecord findOfflineAnswerDetail(String courseQuestionaryId, String recordId, String memberId);

    /**
     * 线下满意度问卷-学员进入问卷
     * @param courseQuestionaryId
     * @param memberId
     * @return
     */
    @Transactional
    CourseQuestionnaireRecord findOfflineFront(String courseQuestionaryId, String memberId);


    /**
     * 新增问卷记录
     * @param courseQuestionaryId 问卷id
     * @param memberId 学员id
     * @param startTime 开始时间
     * @param type 类型 1：线上 2：线下
     * @return
     */
    @Transactional
    CourseQuestionnaireRecord insertCourseQuestionnaireRecord(String courseQuestionaryId, String memberId,
                                                              Long startTime, Integer type);

    /**
     * 提交问卷作答信息
     * @param courseQuestionaryId 问卷id
     * @param recordId 记录id
     * @param answers 作答json(questionnaireChapterId,questionId,answer)
     * @param memberId 学员id
     * @return
     */
    @Transactional
    void submitOffline(String courseQuestionaryId, String recordId, String answers, String memberId);

    /**
     * 线下满意度问卷-查询学员作答记录数
     * @param courseQuestionaryId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer findOfflineAnswerRecordCount(String courseQuestionaryId);
}
