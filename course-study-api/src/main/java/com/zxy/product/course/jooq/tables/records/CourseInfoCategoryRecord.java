/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseInfoCategory;
import com.zxy.product.course.jooq.tables.interfaces.ICourseInfoCategory;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程详情-目录中间表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfoCategoryRecord extends UpdatableRecordImpl<CourseInfoCategoryRecord> implements Record7<String, String, String, Integer, Long, String, Timestamp>, ICourseInfoCategory {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_info_category.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_info_category.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_info_category.f_course_info_id</code>. 课程/专题详情id
     */
    @Override
    public void setCourseInfoId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_info_category.f_course_info_id</code>. 课程/专题详情id
     */
    @Override
    public String getCourseInfoId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_info_category.f_course_category_id</code>. 课程目录id
     */
    @Override
    public void setCourseCategoryId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_info_category.f_course_category_id</code>. 课程目录id
     */
    @Override
    public String getCourseCategoryId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_info_category.f_is_prime</code>. 是否为主序列 0:不是 1:是
     */
    @Override
    public void setIsPrime(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_info_category.f_is_prime</code>. 是否为主序列 0:不是 1:是
     */
    @Override
    public Integer getIsPrime() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_info_category.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_info_category.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_info_category.f_create_member</code>. 创建人
     */
    @Override
    public void setCreateMember(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_info_category.f_create_member</code>. 创建人
     */
    @Override
    public String getCreateMember() {
        return (String) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_info_category.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_info_category.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, Integer, Long, String, Timestamp> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, Integer, Long, String, Timestamp> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseInfoCategory.COURSE_INFO_CATEGORY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseInfoCategory.COURSE_INFO_CATEGORY.COURSE_INFO_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseInfoCategory.COURSE_INFO_CATEGORY.COURSE_CATEGORY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return CourseInfoCategory.COURSE_INFO_CATEGORY.IS_PRIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return CourseInfoCategory.COURSE_INFO_CATEGORY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return CourseInfoCategory.COURSE_INFO_CATEGORY.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field7() {
        return CourseInfoCategory.COURSE_INFO_CATEGORY.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getCourseInfoId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCourseCategoryId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getIsPrime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value7() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoCategoryRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoCategoryRecord value2(String value) {
        setCourseInfoId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoCategoryRecord value3(String value) {
        setCourseCategoryId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoCategoryRecord value4(Integer value) {
        setIsPrime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoCategoryRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoCategoryRecord value6(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoCategoryRecord value7(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoCategoryRecord values(String value1, String value2, String value3, Integer value4, Long value5, String value6, Timestamp value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseInfoCategory from) {
        setId(from.getId());
        setCourseInfoId(from.getCourseInfoId());
        setCourseCategoryId(from.getCourseCategoryId());
        setIsPrime(from.getIsPrime());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseInfoCategory> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseInfoCategoryRecord
     */
    public CourseInfoCategoryRecord() {
        super(CourseInfoCategory.COURSE_INFO_CATEGORY);
    }

    /**
     * Create a detached, initialised CourseInfoCategoryRecord
     */
    public CourseInfoCategoryRecord(String id, String courseInfoId, String courseCategoryId, Integer isPrime, Long createTime, String createMember, Timestamp modifyDate) {
        super(CourseInfoCategory.COURSE_INFO_CATEGORY);

        set(0, id);
        set(1, courseInfoId);
        set(2, courseCategoryId);
        set(3, isPrime);
        set(4, createTime);
        set(5, createMember);
        set(6, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseInfoCategoryEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseInfoCategoryEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseInfoCategoryEntity)source;
        pojo.into(this);
        return true;
    }
}
