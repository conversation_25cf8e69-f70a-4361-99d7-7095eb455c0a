/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAnnualBill_2019 extends Serializable {

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_member_id</code>.
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_member_id</code>.
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_study_time</code>. 2019年学习总时长+直播学习时长
     */
    public void setStudyTime(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_study_time</code>. 2019年学习总时长+直播学习时长
     */
    public Integer getStudyTime();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_study_day_num</code>. 学习天数
     */
    public void setStudyDayNum(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_study_day_num</code>. 学习天数
     */
    public Integer getStudyDayNum();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_finish_course_num</code>. 完成课程数量
     */
    public void setFinishCourseNum(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_finish_course_num</code>. 完成课程数量
     */
    public Integer getFinishCourseNum();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_study_subject_num</code>. 浏览专题数量
     */
    public void setStudySubjectNum(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_study_subject_num</code>. 浏览专题数量
     */
    public Integer getStudySubjectNum();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_study_gensee_num</code>. 观看直播数量
     */
    public void setStudyGenseeNum(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_study_gensee_num</code>. 观看直播数量
     */
    public Integer getStudyGenseeNum();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_study_knowledge_num</code>. 下载上传知识的数量
     */
    public void setStudyKnowledgeNum(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_study_knowledge_num</code>. 下载上传知识的数量
     */
    public Integer getStudyKnowledgeNum();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_newenergy</code>. 是否参加全员5G+ 0：未参加；1：参加
     */
    public void setNewenergy(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_newenergy</code>. 是否参加全员5G+ 0：未参加；1：参加
     */
    public Integer getNewenergy();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_thematic</code>. 是否参加专题班 0：未参加；1：参加
     */
    public void setThematic(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_thematic</code>. 是否参加专题班 0：未参加；1：参加
     */
    public Integer getThematic();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_exam</code>. 是否参加网维认证考试 0：未参加；1：参加
     */
    public void setExam(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_exam</code>. 是否参加网维认证考试 0：未参加；1：参加
     */
    public Integer getExam();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_integrity</code>. 是否参加反腐倡廉学习 0：未参加；1：参加
     */
    public void setIntegrity(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_integrity</code>. 是否参加反腐倡廉学习 0：未参加；1：参加
     */
    public Integer getIntegrity();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_secrecy</code>. 是否参加保密专区学习 0：未参加；1：参加
     */
    public void setSecrecy(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_secrecy</code>. 是否参加保密专区学习 0：未参加；1：参加
     */
    public Integer getSecrecy();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_law</code>. 是否参加和法树学习  0：未参加；1：参加
     */
    public void setLaw(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_law</code>. 是否参加和法树学习  0：未参加；1：参加
     */
    public Integer getLaw();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_course_comment</code>. 课程下评论以及点赞数量
     */
    public void setCourseComment(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_course_comment</code>. 课程下评论以及点赞数量
     */
    public Integer getCourseComment();

    /**
     * Setter for <code>course-study.t_annual_bill_2019.f_askbar_comment</code>. 问吧话题下参加讨论数量
     */
    public void setAskbarComment(Integer value);

    /**
     * Getter for <code>course-study.t_annual_bill_2019.f_askbar_comment</code>. 问吧话题下参加讨论数量
     */
    public Integer getAskbarComment();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAnnualBill_2019
     */
    public void from(IAnnualBill_2019 from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAnnualBill_2019
     */
    public <E extends IAnnualBill_2019> E into(E into);
}
