/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseStudyProgressArchived_17Record;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * course_study_progress已归档数据表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProgressArchived_17 extends TableImpl<CourseStudyProgressArchived_17Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_study_progress_archived_17</code>
     */
    public static final CourseStudyProgressArchived_17 COURSE_STUDY_PROGRESS_ARCHIVED_17 = new CourseStudyProgressArchived_17();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseStudyProgressArchived_17Record> getRecordType() {
        return CourseStudyProgressArchived_17Record.class;
    }

    /**
     * The column <code>course-study.t_course_study_progress_archived_17.f_id</code>.
     */
    public final TableField<CourseStudyProgressArchived_17Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_archived_17.f_member_id</code>. 用户ID
     */
    public final TableField<CourseStudyProgressArchived_17Record, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户ID");

    /**
     * The column <code>course-study.t_course_study_progress_archived_17.f_course_id</code>. 课程ID
     */
    public final TableField<CourseStudyProgressArchived_17Record, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程ID");

    /**
     * The column <code>course-study.t_course_study_progress_archived_17.f_begin_time</code>. 归档时间
     */
    public final TableField<CourseStudyProgressArchived_17Record, Long> BEGIN_TIME = createField("f_begin_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("(unix_timestamp() * 1000)", org.jooq.impl.SQLDataType.BIGINT)), this, "归档时间");

    /**
     * The column <code>course-study.t_course_study_progress_archived_17.f_finish_status</code>. 完成状态
     */
    public final TableField<CourseStudyProgressArchived_17Record, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "完成状态");

    /**
     * Create a <code>course-study.t_course_study_progress_archived_17</code> table reference
     */
    public CourseStudyProgressArchived_17() {
        this("t_course_study_progress_archived_17", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_study_progress_archived_17</code> table reference
     */
    public CourseStudyProgressArchived_17(String alias) {
        this(alias, COURSE_STUDY_PROGRESS_ARCHIVED_17);
    }

    private CourseStudyProgressArchived_17(String alias, Table<CourseStudyProgressArchived_17Record> aliased) {
        this(alias, aliased, null);
    }

    private CourseStudyProgressArchived_17(String alias, Table<CourseStudyProgressArchived_17Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "course_study_progress已归档数据表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseStudyProgressArchived_17Record> getPrimaryKey() {
        return Keys.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseStudyProgressArchived_17Record>> getKeys() {
        return Arrays.<UniqueKey<CourseStudyProgressArchived_17Record>>asList(Keys.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_PRIMARY, Keys.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgressArchived_17 as(String alias) {
        return new CourseStudyProgressArchived_17(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseStudyProgressArchived_17 rename(String name) {
        return new CourseStudyProgressArchived_17(name, null);
    }
}
