/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IKnowledgeViewRecord;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 知识查看记录，用于统计浏览人数
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class KnowledgeViewRecordEntity extends BaseEntity implements IKnowledgeViewRecord {

    private static final long serialVersionUID = 1L;

    private Integer   type;
    private String    knowledgeId;
    private String    createMemberId;
    private Timestamp modifyDate;

    public KnowledgeViewRecordEntity() {}

    public KnowledgeViewRecordEntity(KnowledgeViewRecordEntity value) {
        this.type = value.type;
        this.knowledgeId = value.knowledgeId;
        this.createMemberId = value.createMemberId;
        this.modifyDate = value.modifyDate;
    }

    public KnowledgeViewRecordEntity(
        String    id,
        Integer   type,
        String    knowledgeId,
        Long      createTime,
        String    createMemberId,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.type = type;
        this.knowledgeId = knowledgeId;
        super.setCreateTime(createTime);
        this.createMemberId = createMemberId;
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String getKnowledgeId() {
        return this.knowledgeId;
    }

    @Override
    public void setKnowledgeId(String knowledgeId) {
        this.knowledgeId = knowledgeId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getCreateMemberId() {
        return this.createMemberId;
    }

    @Override
    public void setCreateMemberId(String createMemberId) {
        this.createMemberId = createMemberId;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("KnowledgeViewRecordEntity (");

        sb.append(getId());
        sb.append(", ").append(type);
        sb.append(", ").append(knowledgeId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(createMemberId);
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IKnowledgeViewRecord from) {
        setId(from.getId());
        setType(from.getType());
        setKnowledgeId(from.getKnowledgeId());
        setCreateTime(from.getCreateTime());
        setCreateMemberId(from.getCreateMemberId());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IKnowledgeViewRecord> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends KnowledgeViewRecordEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.KnowledgeViewRecordRecord r = new com.zxy.product.course.jooq.tables.records.KnowledgeViewRecordRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.ID, record.getValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.TYPE, record.getValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.KNOWLEDGE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.KNOWLEDGE_ID, record.getValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.KNOWLEDGE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.CREATE_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.CREATE_MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.CREATE_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
