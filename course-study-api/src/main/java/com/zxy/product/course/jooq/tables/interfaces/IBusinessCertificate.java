/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 证书与业务关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IBusinessCertificate extends Serializable {

    /**
     * Setter for <code>course-study.t_business_certificate.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_business_certificate.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_business_certificate.f_business_id</code>. 关联业务id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>course-study.t_business_certificate.f_business_id</code>. 关联业务id
     */
    public String getBusinessId();

    /**
     * Setter for <code>course-study.t_business_certificate.f_business_type</code>. 资源类型 0-课程；1-专题；2-知识；3-直播
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>course-study.t_business_certificate.f_business_type</code>. 资源类型 0-课程；1-专题；2-知识；3-直播
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>course-study.t_business_certificate.f_certificate_id</code>. 证书id
     */
    public void setCertificateId(String value);

    /**
     * Getter for <code>course-study.t_business_certificate.f_certificate_id</code>. 证书id
     */
    public String getCertificateId();

    /**
     * Setter for <code>course-study.t_business_certificate.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_business_certificate.f_create_time</code>.
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IBusinessCertificate
     */
    public void from(IBusinessCertificate from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IBusinessCertificate
     */
    public <E extends IBusinessCertificate> E into(E into);
}
