/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseSectionStudyProgressFfclc_2023;
import com.zxy.product.course.jooq.tables.interfaces.ICourseSectionStudyProgressFfclc_2023;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record21;
import org.jooq.Row21;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程节学习进度(2023反复倡廉课程)
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyProgressFfclc_2023Record extends UpdatableRecordImpl<CourseSectionStudyProgressFfclc_2023Record> implements Record21<String, String, String, String, Long, Integer, Long, Integer, Integer, Long, Integer, String, Long, Long, String, String, Integer, String, Integer, Integer, Timestamp>, ICourseSectionStudyProgressFfclc_2023 {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_member_id</code>. 用户ID
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_member_id</code>. 用户ID
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_course_id</code>. 课程ID
     */
    @Override
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_course_id</code>. 课程ID
     */
    @Override
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_section_id</code>. 课程节ID
     */
    @Override
    public void setSectionId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_section_id</code>. 课程节ID
     */
    @Override
    public String getSectionId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_begin_time</code>. 学习开始时间
     */
    @Override
    public void setBeginTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_begin_time</code>. 学习开始时间
     */
    @Override
    public Long getBeginTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷
     */
    @Override
    public void setFinishStatus(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷
     */
    @Override
    public Integer getFinishStatus() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_finish_time</code>. 完成时间
     */
    @Override
    public void setFinishTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_finish_time</code>. 完成时间
     */
    @Override
    public Long getFinishTime() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_completed_rate</code>. 完成进度(百分比)
     */
    @Override
    public void setCompletedRate(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_completed_rate</code>. 完成进度(百分比)
     */
    @Override
    public Integer getCompletedRate() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_study_total_time</code>. 学习累计时长，单位秒
     */
    @Override
    public void setStudyTotalTime(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_study_total_time</code>. 学习累计时长，单位秒
     */
    @Override
    public Integer getStudyTotalTime() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_last_access_time</code>. 最后访问时间
     */
    @Override
    public void setLastAccessTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_last_access_time</code>. 最后访问时间
     */
    @Override
    public Long getLastAccessTime() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_exam_status</code>. 考试状态 0 未通过 1通过 2待评卷 3.待考试
     */
    @Override
    public void setExamStatus(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_exam_status</code>. 考试状态 0 未通过 1通过 2待评卷 3.待考试
     */
    @Override
    public Integer getExamStatus() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_lesson_location</code>. 最后学习退出的位置
     */
    @Override
    public void setLessonLocation(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_lesson_location</code>. 最后学习退出的位置
     */
    @Override
    public String getLessonLocation() {
        return (String) get(11);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_commit_time</code>. 提交时间
     */
    @Override
    public void setCommitTime(Long value) {
        set(13, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_commit_time</code>. 提交时间
     */
    @Override
    public Long getCommitTime() {
        return (Long) get(13);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_submit_text</code>. 提交内容
     */
    @Override
    public void setSubmitText(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_submit_text</code>. 提交内容
     */
    @Override
    public String getSubmitText() {
        return (String) get(14);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_audit_member_id</code>. 审核人ID
     */
    @Override
    public void setAuditMemberId(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_audit_member_id</code>. 审核人ID
     */
    @Override
    public String getAuditMemberId() {
        return (String) get(15);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_score</code>. 评分，用于存储考试、评估、作业评分
     */
    @Override
    public void setScore(Integer value) {
        set(16, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_score</code>. 评分，用于存储考试、评估、作业评分
     */
    @Override
    public Integer getScore() {
        return (Integer) get(16);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_comments</code>. 作业审核评语
     */
    @Override
    public void setComments(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_comments</code>. 作业审核评语
     */
    @Override
    public String getComments() {
        return (String) get(17);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_audit_pass</code>. 作业审核是否通过，1-通过；2-打回重新提交
     */
    @Override
    public void setAuditPass(Integer value) {
        set(18, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_audit_pass</code>. 作业审核是否通过，1-通过；2-打回重新提交
     */
    @Override
    public Integer getAuditPass() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_visits</code>.
     */
    @Override
    public void setVisits(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_visits</code>.
     */
    @Override
    public Integer getVisits() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(20, value);
    }

    /**
     * Getter for <code>course-study.t_course_section_study_progress_ffclc_2023.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(20);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record21 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row21<String, String, String, String, Long, Integer, Long, Integer, Integer, Long, Integer, String, Long, Long, String, String, Integer, String, Integer, Integer, Timestamp> fieldsRow() {
        return (Row21) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row21<String, String, String, String, Long, Integer, Long, Integer, Integer, Long, Integer, String, Long, Long, String, String, Integer, String, Integer, Integer, Timestamp> valuesRow() {
        return (Row21) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.SECTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.BEGIN_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.FINISH_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.FINISH_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.COMPLETED_RATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.STUDY_TOTAL_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.LAST_ACCESS_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field11() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.EXAM_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.LESSON_LOCATION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field13() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field14() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.COMMIT_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field15() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.SUBMIT_TEXT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field16() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.AUDIT_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field17() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.SCORE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field18() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.COMMENTS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field19() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.AUDIT_PASS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field20() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.VISITS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field21() {
        return CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getSectionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getBeginTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getFinishStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getFinishTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getCompletedRate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getStudyTotalTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getLastAccessTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value11() {
        return getExamStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getLessonLocation();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value13() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value14() {
        return getCommitTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value15() {
        return getSubmitText();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value16() {
        return getAuditMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value17() {
        return getScore();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value18() {
        return getComments();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value19() {
        return getAuditPass();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value20() {
        return getVisits();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value21() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value3(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value4(String value) {
        setSectionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value5(Long value) {
        setBeginTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value6(Integer value) {
        setFinishStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value7(Long value) {
        setFinishTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value8(Integer value) {
        setCompletedRate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value9(Integer value) {
        setStudyTotalTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value10(Long value) {
        setLastAccessTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value11(Integer value) {
        setExamStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value12(String value) {
        setLessonLocation(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value13(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value14(Long value) {
        setCommitTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value15(String value) {
        setSubmitText(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value16(String value) {
        setAuditMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value17(Integer value) {
        setScore(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value18(String value) {
        setComments(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value19(Integer value) {
        setAuditPass(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value20(Integer value) {
        setVisits(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record value21(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressFfclc_2023Record values(String value1, String value2, String value3, String value4, Long value5, Integer value6, Long value7, Integer value8, Integer value9, Long value10, Integer value11, String value12, Long value13, Long value14, String value15, String value16, Integer value17, String value18, Integer value19, Integer value20, Timestamp value21) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        value21(value21);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseSectionStudyProgressFfclc_2023 from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setSectionId(from.getSectionId());
        setBeginTime(from.getBeginTime());
        setFinishStatus(from.getFinishStatus());
        setFinishTime(from.getFinishTime());
        setCompletedRate(from.getCompletedRate());
        setStudyTotalTime(from.getStudyTotalTime());
        setLastAccessTime(from.getLastAccessTime());
        setExamStatus(from.getExamStatus());
        setLessonLocation(from.getLessonLocation());
        setCreateTime(from.getCreateTime());
        setCommitTime(from.getCommitTime());
        setSubmitText(from.getSubmitText());
        setAuditMemberId(from.getAuditMemberId());
        setScore(from.getScore());
        setComments(from.getComments());
        setAuditPass(from.getAuditPass());
        setVisits(from.getVisits());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseSectionStudyProgressFfclc_2023> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseSectionStudyProgressFfclc_2023Record
     */
    public CourseSectionStudyProgressFfclc_2023Record() {
        super(CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023);
    }

    /**
     * Create a detached, initialised CourseSectionStudyProgressFfclc_2023Record
     */
    public CourseSectionStudyProgressFfclc_2023Record(String id, String memberId, String courseId, String sectionId, Long beginTime, Integer finishStatus, Long finishTime, Integer completedRate, Integer studyTotalTime, Long lastAccessTime, Integer examStatus, String lessonLocation, Long createTime, Long commitTime, String submitText, String auditMemberId, Integer score, String comments, Integer auditPass, Integer visits, Timestamp modifyDate) {
        super(CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023);

        set(0, id);
        set(1, memberId);
        set(2, courseId);
        set(3, sectionId);
        set(4, beginTime);
        set(5, finishStatus);
        set(6, finishTime);
        set(7, completedRate);
        set(8, studyTotalTime);
        set(9, lastAccessTime);
        set(10, examStatus);
        set(11, lessonLocation);
        set(12, createTime);
        set(13, commitTime);
        set(14, submitText);
        set(15, auditMemberId);
        set(16, score);
        set(17, comments);
        set(18, auditPass);
        set(19, visits);
        set(20, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyProgressFfclc_2023Entity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyProgressFfclc_2023Entity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseSectionStudyProgressFfclc_2023Entity)source;
        pojo.into(this);
        return true;
    }
}
