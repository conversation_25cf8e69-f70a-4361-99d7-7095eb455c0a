/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseSectionStudyProgressChbncRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * CHBN活动课程章节进度表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSectionStudyProgressChbnc extends TableImpl<CourseSectionStudyProgressChbncRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_section_study_progress_chbnc</code>
     */
    public static final CourseSectionStudyProgressChbnc COURSE_SECTION_STUDY_PROGRESS_CHBNC = new CourseSectionStudyProgressChbnc();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseSectionStudyProgressChbncRecord> getRecordType() {
        return CourseSectionStudyProgressChbncRecord.class;
    }

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_id</code>.
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_member_id</code>. 用户ID
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户ID");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_course_id</code>. 课程ID
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程ID");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_section_id</code>. 课程节ID
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, String> SECTION_ID = createField("f_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程节ID");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_begin_time</code>. 学习开始时间
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Long> BEGIN_TIME = createField("f_begin_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "学习开始时间");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习状态，0-未开始，1-学习中，2-已完成，3-已放弃，4-标记完成，5-待审核，6-审核未通过，7-待评卷");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_finish_time</code>. 完成时间
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Long> FINISH_TIME = createField("f_finish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "完成时间");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_completed_rate</code>. 完成进度(百分比)
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Integer> COMPLETED_RATE = createField("f_completed_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "完成进度(百分比)");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_study_total_time</code>. 学习累计时长，单位秒
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Integer> STUDY_TOTAL_TIME = createField("f_study_total_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习累计时长，单位秒");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_last_access_time</code>. 最后访问时间
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Long> LAST_ACCESS_TIME = createField("f_last_access_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "最后访问时间");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_exam_status</code>. 考试状态 0 未通过 1通过 2待评卷 3.待考试
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Integer> EXAM_STATUS = createField("f_exam_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试状态 0 未通过 1通过 2待评卷 3.待考试");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_lesson_location</code>. 最后学习退出的位置
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, String> LESSON_LOCATION = createField("f_lesson_location", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "最后学习退出的位置");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_create_time</code>.
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_commit_time</code>. 提交时间
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Long> COMMIT_TIME = createField("f_commit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "提交时间");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_submit_text</code>. 提交内容
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, String> SUBMIT_TEXT = createField("f_submit_text", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "提交内容");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_audit_member_id</code>. 审核人ID
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, String> AUDIT_MEMBER_ID = createField("f_audit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "审核人ID");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_score</code>. 评分，用于存储考试、评估、作业评分
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "评分，用于存储考试、评估、作业评分");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_comments</code>. 作业审核评语
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, String> COMMENTS = createField("f_comments", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "作业审核评语");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_audit_pass</code>. 作业审核是否通过，1-通过；2-打回重新提交
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Integer> AUDIT_PASS = createField("f_audit_pass", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "作业审核是否通过，1-通过；2-打回重新提交");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_visits</code>.
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Integer> VISITS = createField("f_visits", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_section_study_progress_chbnc.f_modify_date</code>. 修改时间
     */
    public final TableField<CourseSectionStudyProgressChbncRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>course-study.t_course_section_study_progress_chbnc</code> table reference
     */
    public CourseSectionStudyProgressChbnc() {
        this("t_course_section_study_progress_chbnc", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_section_study_progress_chbnc</code> table reference
     */
    public CourseSectionStudyProgressChbnc(String alias) {
        this(alias, COURSE_SECTION_STUDY_PROGRESS_CHBNC);
    }

    private CourseSectionStudyProgressChbnc(String alias, Table<CourseSectionStudyProgressChbncRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseSectionStudyProgressChbnc(String alias, Table<CourseSectionStudyProgressChbncRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "CHBN活动课程章节进度表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseSectionStudyProgressChbncRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseSectionStudyProgressChbncRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseSectionStudyProgressChbncRecord>>asList(Keys.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_PRIMARY, Keys.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSectionStudyProgressChbnc as(String alias) {
        return new CourseSectionStudyProgressChbnc(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseSectionStudyProgressChbnc rename(String name) {
        return new CourseSectionStudyProgressChbnc(name, null);
    }
}
