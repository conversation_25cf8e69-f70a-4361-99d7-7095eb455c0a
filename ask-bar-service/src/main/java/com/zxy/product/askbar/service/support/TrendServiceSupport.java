package com.zxy.product.askbar.service.support;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.askbar.api.TrendService;
import com.zxy.product.askbar.content.MessageHeaderContent;
import com.zxy.product.askbar.content.MessageTypeContent;
import com.zxy.product.askbar.entity.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.askbar.jooq.Tables.*;
//import static com.zxy.product.askbar.service.util.SecurePathCdnUtils.generateSecurePathCdn;
import static java.util.stream.Collectors.*;

/**
 * 动态业务实现
 *
 * <AUTHOR>
 * @date 2017年8月3日 下午6:03:21
 */
@Service
public class TrendServiceSupport implements TrendService {
	
	public static final Logger logger = LoggerFactory.getLogger(TrendServiceSupport.class);

	private MessageSender messageSender;
	private CommonDao<Trend> trendDao;
	private CommonDao<Attention> attentionDao;
	private CommonDao<QuestionTopic> questionTopicDao;
	private CommonDao<Praise> praiseDao;

	@Autowired
	public void setPraiseDao(CommonDao<Praise> praiseDao) {
		this.praiseDao = praiseDao;
	}

	@Autowired
	public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}

	@Autowired
	public void setTrendDao(CommonDao<Trend> trendDao) {
		this.trendDao = trendDao;
	}

	@Autowired
	public void setAttentionDao(CommonDao<Attention> attentionDao) {
		this.attentionDao = attentionDao;
	}

	@Autowired
	public void setQuestionTopicDao(CommonDao<QuestionTopic> questionTopicDao) {
		this.questionTopicDao = questionTopicDao;
	}

	@Override
	public Trend insert(Integer businessType, String businessId, String questionId, Optional<String> discussId,
			String createMemberId, String rootOrganizationId, Long createTime) {
		Trend trend = new Trend();
		trend.forInsert();
		trend.setBusinessType(businessType);
		trend.setBusinessId(businessId);
		trend.setQuestionId(questionId);
		trend.setCreateMemberId(createMemberId);
		trend.setRootOrganizationId(rootOrganizationId);
		trend.setCreateTime(createTime);
		trend.setIsCurrent(Trend.IS_CURRENT_YES);// 置为最新动态
		discussId.ifPresent(trend::setDiscussId);
		// 将其他动态置为非最新
		trendDao.execute(e -> {
			return e.update(TREND).set(TREND.IS_CURRENT, Trend.IS_CURRENT_NO).where(TREND.QUESTION_ID.eq(questionId))
					.execute();
		});
		trendDao.insert(trend);
		messageSender.send(MessageTypeContent.BAR_QUESTION_TREND_INSERT, MessageHeaderContent.ID, trend.getId());
		return trend;
	}

	@Override
	public PagedResult<Question> findMyAttentionContent(Integer start, Integer pageSize, String currentMemberId,
			String rootOrganizationId, Optional<String> type, Optional<String> timeOrder) {
		Field<String> expertId = EXPERT.ID.as("expert_id");
		return trendDao.execute(e -> {
			SelectSelectStep<Record> selectFields = e.select(Fields.start()
					.add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
							QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
							QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
							QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.ATTENTION_NUM, QUESTION.CREATE_TIME,
							QUESTION.LAST_MODIFY_TIME, QUESTION.SHARE_TITLE, QUESTION.SHARE_OBJECT_ID,
							QUESTION.SHARE_TYPE, QUESTION.CREATE_MEMBER_ID)
					.add(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.HEAD_PORTRAIT_PATH).add(expertId)
					.add(ATTENTION.ID).add(PRAISE.ID).end());

			SelectSelectStep<Record> countField = e.select(Fields.start().add(QUESTION.ID.countDistinct()).end());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
				Condition conditions = QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)
						.and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
						.and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))
						.and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId))
						.and(ATTENTION.ID.isNotNull())
						.and(QUESTION.HIDDEN.eq(Question.HIDDEN_FLAG_NO));
				if (type.isPresent()) {
					if (type.get().equals("question")) {
						conditions = conditions.and(QUESTION.TYPE.eq(Question.TYPE_QUESTION));
					} else if (type.get().equals("article")) {
						conditions = conditions.and(QUESTION.TYPE.eq(Question.TYPE_ARTICLE));
					}
				}

				return a.from(QUESTION).leftJoin(ATTENTION)
						.on(ATTENTION.BUSINESS_ID.eq(QUESTION.ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))
						.leftJoin(PRAISE)
						.on(PRAISE.OBJECT_ID.eq(QUESTION.ID).and(PRAISE.CREATE_MEMBER_ID.eq(currentMemberId)))
						.leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
						.on(EXPERT.MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)
								.and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE)).and(
										EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
						.where(conditions);
			};

			SelectSeekStep1<Record, Long> step;
			if (timeOrder.isPresent() && timeOrder.get().equals("asc")) {
				step = stepFunc.apply(selectFields).orderBy(ATTENTION.CREATE_TIME.asc());
			} else {
				step = stepFunc.apply(selectFields).orderBy(ATTENTION.CREATE_TIME.desc());
			}

			Integer count = stepFunc.apply(countField).fetchOne().getValue(0, Integer.class);

			List<Question> questions = step.limit(start, pageSize).fetch(r -> {
				Question question = r.into(Question.class);
				Attention attention = r.into(Attention.class);
				question.setAttention(attention);
				Praise praise = r.into(Praise.class);
				question.setPraise(praise);
				Member member = r.into(Member.class);
				member.setIsExpert(!StringUtils.isEmpty(r.getValue(expertId)));
				question.setCreateMember(member);
				return question;
			});
			handlerQuestionTopic(questions, currentMemberId);
			return PagedResult.create(count, questions);
		});
	}

	@Override
	public PagedResult<Question> findMyAttentionContentMerge(Integer start, Integer pageSize, String currentMember,
			List<String> currentMemberIds, List<String> rootOrganizationId, Optional<String> type,
			Optional<String> timeOrder) {
		// TODO Auto-generated method stub
		Field<String> expertId = EXPERT.ID.as("expert_id");
		Field<String> createMemberIdAttention = ATTENTION.CREATE_MEMBER_ID.as("create_member_id_attention");

		return trendDao.execute(e -> {
			SelectSelectStep<Record> selectFields = e.select(Fields.start()
					.add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
							QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
							QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
							QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.ATTENTION_NUM, QUESTION.CREATE_TIME,
							QUESTION.LAST_MODIFY_TIME, QUESTION.SHARE_TITLE, QUESTION.SHARE_OBJECT_ID,
							QUESTION.SHARE_TYPE, QUESTION.CREATE_MEMBER_ID)
					.add(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.HEAD_PORTRAIT_PATH).add(expertId)
					.add(ATTENTION.ID, createMemberIdAttention).end());

			SelectSelectStep<Record> countField = e.select(Fields.start().add(QUESTION.ID.countDistinct()).end());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
				Condition conditions = QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)
						.and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
						.and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)).and(ATTENTION.ID.isNotNull());
				if (type.isPresent()) {
					if (type.get().equals("question")) {
						conditions = conditions.and(QUESTION.TYPE.eq(Question.TYPE_QUESTION));
					} else if (type.get().equals("article")) {
						conditions = conditions.and(QUESTION.TYPE.eq(Question.TYPE_ARTICLE));
					}
				}

				return a.from(QUESTION).leftJoin(ATTENTION)
						.on(ATTENTION.BUSINESS_ID.eq(QUESTION.ID).and(ATTENTION.CREATE_MEMBER_ID.in(currentMemberIds)))
						.leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
						.on(EXPERT.MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)
								.and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE)).and(
										EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
						.where(conditions);
			};

			SelectSeekStep1<Record, Long> step;
			if (timeOrder.isPresent() && timeOrder.get().equals("asc")) {
				step = stepFunc.apply(selectFields).orderBy(ATTENTION.CREATE_TIME.asc());
			} else {
				step = stepFunc.apply(selectFields).orderBy(ATTENTION.CREATE_TIME.desc());
			}

			Integer count = stepFunc.apply(countField).fetchOne().getValue(0, Integer.class);

			List<Question> questions = step.limit(start, pageSize).fetch(r -> {
				Question question = r.into(Question.class);
				Attention attention = r.into(Attention.class);
				attention.setCreateMemberId(r.getValue(createMemberIdAttention));
				question.setAttention(attention);
				// question.setCreateMemberId(r.getValue(ATTENTION.CREATE_MEMBER_ID));
//				Praise praise = r.into(PRAISE).into(Praise.class);
//				praise.setCreateMemberId(r.getValue(createMemberIdPraise));
//				question.setPraise(praise);
				Member member = r.into(Member.class);
				member.setIsExpert(!StringUtils.isEmpty(r.getValue(expertId)));
				question.setCreateMember(member);
				return question;
			});

			//新增查询当前用户记录
			handlerQuestionTopicMerge(questions, currentMember);
			handlerQuestionPariseMerge(questions, currentMember);
			return PagedResult.create(count, questions);
		});
	}

	private void handlerQuestionPariseMerge(List<Question> questions, String currentMember){
		questions.stream().forEach(item -> {
			List<Praise> praises = praiseDao.fetch(PRAISE.OBJECT_ID.eq(item.getId()).and(PRAISE.CREATE_MEMBER_ID.eq(item.getAttention().getCreateMemberId())));
			if(praises != null && praises.size() > 0){
				item.setPraise(praises.get(0));
			}else {
				Praise praise = new Praise();
				praise.setId(null);
				praise.setCreateMemberId(null);
				item.setPraise(praise);
			}
		});
	}

	private void handlerQuestionTopicMerge(List<Question> questions, String currentMember) {
		questionTopicDao.execute(e -> {
			// 问题关联话题
			Result<Record> record = e
					.select(Fields.start()
							.add(QUESTION_TOPIC.ID, QUESTION_TOPIC.QUESTION_ID, TOPIC.ID, TOPIC.NAME, TOPIC.TYPE_ID,
									TOPIC.GROUP, TOPIC.IS_BAR_TOPIC, TOPIC_MANAGER.ID)
							.end())
					.from(QUESTION_TOPIC)
					.leftJoin(
							TOPIC)
					.on(QUESTION_TOPIC.TOPIC_ID.eq(TOPIC.ID)).leftJoin(TOPIC_MANAGER)
					.on(TOPIC_MANAGER.TOPIC_ID.eq(TOPIC.ID).and(TOPIC_MANAGER.MEMBER_ID.eq(currentMember)))
					.where(QUESTION_TOPIC.QUESTION_ID
							.in(questions.stream().map(a -> a.getId()).collect(Collectors.toList()))
							.and(TOPIC.STATUS.eq(Topic.ENABLED)).and(TOPIC.DELETE_FLAG.eq(Topic.DELETE_FLASE)))
					.fetch();
			Map<String, List<QuestionTopic>> topicMap = new ArrayList<>(record.map(r -> {
				QuestionTopic questionTopic = r.into(QuestionTopic.class);
				Topic topic = r.into(Topic.class);
				questionTopic.setIsManager(!StringUtils.isEmpty(r.getValue(TOPIC_MANAGER.ID)));
				questionTopic.setTopic(topic);
				return questionTopic;
			}).stream().filter(s -> s.getId() != null && s.getQuestionId() != null)
					.collect(toMap(QuestionTopic::getId, p -> p, (p, q) -> q)).values())// 转map去重
							.stream().collect(groupingBy(QuestionTopic::getQuestionId));// 专家id分组
			// 话题处理
			questions.forEach(q -> {
				List<QuestionTopic> questionTopics = topicMap.getOrDefault(q.getId(), new ArrayList<>());
				q.setQuestionTopics(questionTopics);
				// 是否为话题管理员
				Optional<Boolean> isManager = questionTopics.stream().map(t -> t.getIsManager())
						.reduce((a, b) -> a || b);
				q.setIsManager(isManager.orElse(false));
				// 是否为创建者
				q.setIsCreator(currentMember.equals(q.getCreateMemberId()));
			});

			return questions;
		});

	}

	private void handlerQuestionTopic(List<Question> questions, String currentMemberId) {
		questionTopicDao.execute(e -> {
			// 问题关联话题
			Result<Record> record = e
					.select(Fields.start()
							.add(QUESTION_TOPIC.ID, QUESTION_TOPIC.QUESTION_ID, TOPIC.ID, TOPIC.NAME, TOPIC.TYPE_ID,
									TOPIC.GROUP, TOPIC.IS_BAR_TOPIC, TOPIC_MANAGER.ID)
							.end())
					.from(QUESTION_TOPIC)
					.leftJoin(
							TOPIC)
					.on(QUESTION_TOPIC.TOPIC_ID.eq(TOPIC.ID)).leftJoin(TOPIC_MANAGER)
					.on(TOPIC_MANAGER.TOPIC_ID.eq(TOPIC.ID).and(TOPIC_MANAGER.MEMBER_ID.eq(currentMemberId)))
					.where(QUESTION_TOPIC.QUESTION_ID
							.in(questions.stream().map(a -> a.getId()).collect(Collectors.toList()))
							.and(TOPIC.STATUS.eq(Topic.ENABLED)).and(TOPIC.DELETE_FLAG.eq(Topic.DELETE_FLASE)))
					.fetch();
			Map<String, List<QuestionTopic>> topicMap = new ArrayList<>(record.map(r -> {
				QuestionTopic questionTopic = r.into(QuestionTopic.class);
				Topic topic = r.into(Topic.class);
				questionTopic.setIsManager(!StringUtils.isEmpty(r.getValue(TOPIC_MANAGER.ID)));
				questionTopic.setTopic(topic);
				return questionTopic;
			}).stream().filter(s -> s.getId() != null && s.getQuestionId() != null)
					.collect(toMap(QuestionTopic::getId, p -> p, (p, q) -> q)).values())// 转map去重
							.stream().collect(groupingBy(QuestionTopic::getQuestionId));// 专家id分组
			// 话题处理
			questions.forEach(q -> {
				List<QuestionTopic> questionTopics = topicMap.getOrDefault(q.getId(), new ArrayList<>());
				q.setQuestionTopics(questionTopics);
				// 是否为话题管理员
				Optional<Boolean> isManager = questionTopics.stream().map(t -> t.getIsManager())
						.reduce((a, b) -> a || b);
				q.setIsManager(isManager.orElse(false));
				// 是否为创建者
				q.setIsCreator(currentMemberId.equals(q.getCreateMemberId()));
			});

			return questions;
		});
	}

	@Override
	public PagedResult<Trend> findTrendByTopicId(int start, int pageSize, String topicId, String rootOrganizationId,
			String currentMemberId) {
		Field<String> expertId = EXPERT.ID.as("expert_id");
		Field<Long> trendCreateTime = TREND.CREATE_TIME.as("trend_create_time");
		Field<String> trendMemberId = TREND.CREATE_MEMBER_ID.as("trend_create_member_id");
		Field<Long> questionCreateTime = QUESTION.CREATE_TIME.as("question_create_time");
		Field<String> questionMemberId = QUESTION.CREATE_MEMBER_ID.as("question_create_member_id");
		return trendDao.execute(e -> {
			SelectSelectStep<Record> selectFields = e.select(Fields.start()
					.add(TREND.ID, TREND.BUSINESS_ID, TREND.BUSINESS_TYPE, TREND.QUESTION_ID, TREND.DISCUSS_ID,
							trendMemberId, trendCreateTime, TREND.ROOT_ORGANIZATION_ID,TREND.CREATE_TIME)
					.add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
							QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
							QUESTION.ESSENCE_STATUS, QUESTION.TOP_STATUS, QUESTION.TOPIC_TOP_STATUS,
							QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
							QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.ATTENTION_NUM, questionCreateTime,
							QUESTION.LAST_MODIFY_TIME, QUESTION.SHARE_TITLE, QUESTION.SHARE_OBJECT_ID,
							QUESTION.SHARE_TYPE, questionMemberId,QUESTION.CREATE_TIME, QUESTION.TOPIC_STATUS)
					.add(DISCUSS.ID, DISCUSS.TYPE, DISCUSS.CONTENT, DISCUSS.CONTENT_TEXT, DISCUSS.AUDIT_STATUS,
							DISCUSS.IMG, DISCUSS.PRAISE_NUM, DISCUSS.REPLY_NUM, DISCUSS.CREATE_TIME,
							DISCUSS.CREATE_MEMBER_ID)
					.add(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.HEAD_PORTRAIT_PATH).add(expertId)
					.add(ATTENTION.ID).add(PRAISE.ID).end());

			SelectSelectStep<Record> countFields = e.select(Fields.start().add(TREND.ID.countDistinct()).end());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a.from(TREND)
					.leftJoin(QUESTION).on(QUESTION.ID.eq(TREND.QUESTION_ID)).leftJoin(ATTENTION)
					.on(ATTENTION.BUSINESS_ID.eq(TREND.BUSINESS_ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))
					.leftJoin(PRAISE)
					.on(PRAISE.OBJECT_ID.eq(TREND.BUSINESS_ID).and(PRAISE.CREATE_MEMBER_ID.eq(currentMemberId)))
					.leftJoin(DISCUSS).on(DISCUSS.ID.eq(TREND.DISCUSS_ID)).leftJoin(MEMBER)
					.on(MEMBER.ID.eq(TREND.CREATE_MEMBER_ID)).leftJoin(QUESTION_TOPIC)
					.on(QUESTION_TOPIC.QUESTION_ID.eq(QUESTION.ID)).leftJoin(EXPERT)
					.on(EXPERT.MEMBER_ID.eq(TREND.CREATE_MEMBER_ID)
							.and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
							.and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
					.where(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)
							.and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
							.and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))
							.and(QUESTION_TOPIC.TOPIC_ID.eq(topicId)).and(TREND.IS_CURRENT.eq(Trend.IS_CURRENT_YES))
							.and(TREND.ROOT_ORGANIZATION_ID.eq(rootOrganizationId)));

			Integer count = stepFunc.apply(countFields).fetchOne().getValue(0, Integer.class);

			List<Trend> trends = stepFunc.apply(selectFields)
					.orderBy(QUESTION.TOPIC_TOP_STATUS.desc(),QUESTION.TOPIC_TOP_TIME.desc(),TREND.CREATE_TIME.desc())
					.limit(start, pageSize)
					.fetch(r -> {
						Trend trend = r.into(Trend.class);
						Attention attention = r.into(Attention.class);
						trend.setAttention(attention);
						Praise praise = r.into(Praise.class);
						trend.setPraise(praise);
						Member member = r.into(Member.class);
						member.setIsExpert(!StringUtils.isEmpty(r.getValue(expertId)));
						trend.setCreateMember(member);
						Question question = r.into(Question.class);
						question.setCreateMemberId(r.getValue(questionMemberId));
						trend.setQuestion(question);
						Discuss discuss = r.into(Discuss.class);
						trend.setDiscuss(discuss);
						return trend;
					});
			handlerTrendsTopic(trends, currentMemberId);
			return PagedResult.create(count, trends);
		});
	}

	@Override
	public PagedResult<Trend> findArticleByTopicId(int start, int pageSize, String topicId, String rootOrganizationId,
			String currentMemberId) {
		Field<String> expertId = EXPERT.ID.as("expert_id");

		return trendDao.execute(e -> {
			SelectSelectStep<Record> selectFields = e.select(Fields.start()
					.add(TREND.ID, TREND.BUSINESS_ID, TREND.BUSINESS_TYPE, TREND.QUESTION_ID, TREND.DISCUSS_ID,
							TREND.CREATE_MEMBER_ID, TREND.CREATE_TIME, TREND.ROOT_ORGANIZATION_ID)
					.add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
							QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
							QUESTION.ESSENCE_STATUS, QUESTION.TOP_STATUS, QUESTION.TOPIC_TOP_STATUS,
							QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
							QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.ATTENTION_NUM, QUESTION.CREATE_TIME,
							QUESTION.LAST_MODIFY_TIME, QUESTION.SHARE_TITLE, QUESTION.SHARE_OBJECT_ID,
							QUESTION.SHARE_TYPE, QUESTION.CREATE_MEMBER_ID, QUESTION.TOP_STATUS, QUESTION.TOPIC_STATUS)
					.add(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.HEAD_PORTRAIT_PATH).add(expertId)
					.add(ATTENTION.ID).add(PRAISE.ID).end());

			// SelectSelectStep<Record> countField = e.select(Fields.start().add(TREND.ID.countDistinct()).end());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> selectFunc = a -> a.from(TREND)
					.leftJoin(QUESTION).on(QUESTION.ID.eq(TREND.QUESTION_ID)).leftJoin(ATTENTION)
					.on(ATTENTION.BUSINESS_ID.eq(TREND.BUSINESS_ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))
					.leftJoin(PRAISE)
					.on(PRAISE.OBJECT_ID.eq(TREND.BUSINESS_ID).and(PRAISE.CREATE_MEMBER_ID.eq(currentMemberId)))
					.leftJoin(QUESTION_TOPIC).on(QUESTION_TOPIC.QUESTION_ID.eq(QUESTION.ID)).leftJoin(MEMBER)
					.on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
					.on(EXPERT.MEMBER_ID.eq(TREND.CREATE_MEMBER_ID))
					.where(TREND.BUSINESS_TYPE.eq(Trend.BUSINESS_TYPE_ARTICLE)
							.and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))
							.and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
							.and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))
							.and(TREND.ROOT_ORGANIZATION_ID.eq(rootOrganizationId))
							.and(QUESTION_TOPIC.TOPIC_ID.eq(topicId)));

			// Integer count = selectFunc.apply(countField).fetchOne().getValue(0, Integer.class);

			List<Trend> trends = selectFunc.apply(selectFields)
					.orderBy(QUESTION.TOPIC_TOP_STATUS.desc(),QUESTION.TOPIC_TOP_TIME.desc(),TREND.CREATE_TIME.desc())
					.limit(start, pageSize).fetch(r -> {
						Trend trend = r.into(Trend.class);
						Attention attention = r.into(Attention.class);
						trend.setAttention(attention);
						Praise praise = r.into(Praise.class);
						trend.setPraise(praise);
						Member member = r.into(Member.class);
						member.setIsExpert(!StringUtils.isEmpty(r.getValue(expertId)));
						trend.setCreateMember(member);
						Question question = r.into(Question.class);
						trend.setQuestion(question);
						return trend;
					});

			handlerTrendsTopic(trends, currentMemberId);

			// 计算假分页总数
			int count = start + trends.size();
			if (trends.size() == pageSize) {
				count++; // 当前记录查满了一页，总数+1 使前端能继续下拉加载更多
			}
			return PagedResult.create(count, trends);
		});
	}

	@Override
	public PagedResult<Trend> findTrendsAll(int start, int pageSize, String currentMemberId,
											String rootOrganizationId, Boolean askBarHomeOrderFlag) {

		Field<Long> createTime = TREND.CREATE_TIME.as("createTime");
		Field<String> createMemberId = TREND.CREATE_MEMBER_ID.as("createMemberId");

		Field<Long> createTimeQuestion = QUESTION.CREATE_TIME.as("createTimeQuestion");
		Field<String> createMemberIdQuestion = TREND.CREATE_MEMBER_ID.as("createMemberIdQuestion");
		logger.error("before get trend all list:" + System.currentTimeMillis());

		//1.查询基础数据
		List<Trend> trends = trendDao.execute(e -> {

			SelectSelectStep<Record> selectListField = e.select(Fields.start().add(TREND.ID).add(TREND.BUSINESS_ID)
					.add(TREND.BUSINESS_TYPE).add(TREND.QUESTION_ID).add(TREND.DISCUSS_ID).add(createMemberId)
					.add(createTime).add(TREND.ROOT_ORGANIZATION_ID)
					.add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT_TXT, QUESTION.IMG,
							QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.ESSENCE_STATUS, QUESTION.TOP_STATUS, QUESTION.ACCUSE_STATUS,
							QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM, QUESTION.DISCUSS_NUM,
							QUESTION.PRAISE_NUM, QUESTION.SHARE_NUM, createTimeQuestion, QUESTION.SHARE_OBJECT_ID,
							QUESTION.SHARE_TITLE, QUESTION.SHARE_TYPE, createMemberIdQuestion, QUESTION.TOPIC_STATUS).end());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> selectFunc = a -> {
				SelectConditionStep<Record> select = a.from(TREND)
						.leftJoin(QUESTION)
						.on(QUESTION.ID.eq(TREND.QUESTION_ID).and(QUESTION.HIDDEN.eq(Question.HIDDEN_FLAG_NO)))
						.where(TREND.IS_CURRENT.eq(Trend.IS_CURRENT_YES))
						.and(TREND.ROOT_ORGANIZATION_ID.eq(rootOrganizationId))
						.and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS).or(QUESTION.CREATE_MEMBER_ID.eq(currentMemberId)))
						.and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))
						.and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO));
				return select;
			};

			List<Trend> trendList = new ArrayList<>();

			//排序查询开关校验 如果开关开启则不排序
			if (askBarHomeOrderFlag != null && askBarHomeOrderFlag){

				trendList = selectFunc.apply(selectListField)
						.orderBy(TREND.CREATE_TIME.desc())
						.limit(start, pageSize + 1).fetch(r -> {
					Trend trend = r.into(TREND).into(Trend.class);
					trend.setCreateMemberId(r.getValue(createMemberId));
					trend.setCreateTime(r.getValue(createTime));
					Question question = r.into(QUESTION).into(Question.class);
					question.setCreateMemberId(r.getValue(createMemberIdQuestion));
					question.setCreateTime(r.getValue(createTimeQuestion));
					trend.setQuestion(question);
					return trend;
				});
			}else {

				trendList = selectFunc.apply(selectListField)
						.orderBy(QUESTION.TOP_STATUS.desc(),QUESTION.TOP_TIME.desc(),TREND.CREATE_TIME.desc())
						.limit(start, pageSize + 1).fetch(r -> {
					Trend trend = r.into(TREND).into(Trend.class);
					trend.setCreateMemberId(r.getValue(createMemberId));
					trend.setCreateTime(r.getValue(createTime));
					Question question = r.into(QUESTION).into(Question.class);
					question.setCreateMemberId(r.getValue(createMemberIdQuestion));
					question.setCreateTime(r.getValue(createTimeQuestion));
					trend.setQuestion(question);
					return trend;
				});
			}

			return trendList;
		});

		int count = start + trends.size();

		//为0直接返回
		if (trends.size() < 1 ){

			return PagedResult.create(count, trends);
		}

		//返回值转map ,获取id集合
		Map<String, Trend> trendMap = trends.stream().collect(Collectors.toMap(Trend::getId, v -> v, (v1, v2) -> v1));
		List<String> TrendIds = trends.stream().map(Trend::getId).collect(Collectors.toList());

		// 因为做了性能优化一次性查询pageSize条数据,数据组装需要去掉最后一个元素
		if (TrendIds.size() > pageSize) {
			TrendIds.remove(TrendIds.size() - 1);
		}

		//2.查询附加业务数据
		return trendDao.execute(e -> {

			com.zxy.product.askbar.jooq.tables.Member createMember = MEMBER.as("createMember");
			SelectSelectStep<Record> selectListField = e.select(Fields.start().add(TREND.ID)
					.add(DISCUSS.ID, DISCUSS.TYPE, DISCUSS.CONTENT_TEXT, DISCUSS.AUDIT_STATUS, DISCUSS.IMG,
							DISCUSS.PRAISE_NUM, DISCUSS.ACCUSE_STATUS, DISCUSS.REPLY_NUM, DISCUSS.CREATE_TIME,
							DISCUSS.CREATE_MEMBER_ID)
					.add(ATTENTION.ID).add(PRAISE.ID).add(createMember.ID).add(createMember.FULL_NAME)
					.add(createMember.HEAD_PORTRAIT).add(createMember.HEAD_PORTRAIT_PATH).add(EXPERT.ID).end());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> selectFunc = a -> {
				SelectConditionStep<Record> select =
						a.from(TREND)

						.leftJoin(createMember)
						.on(createMember.ID.eq(TREND.CREATE_MEMBER_ID))

						.leftJoin(EXPERT)
						.on(EXPERT.MEMBER_ID.eq(TREND.CREATE_MEMBER_ID)
								.and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
								.and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))

						.leftJoin(DISCUSS)
						.on(DISCUSS.ID.eq(TREND.DISCUSS_ID))

						.leftJoin(ATTENTION)
						.on(ATTENTION.BUSINESS_ID.eq(TREND.BUSINESS_ID)
								.and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))

						.leftJoin(PRAISE)
						.on(PRAISE.OBJECT_ID.eq(TREND.BUSINESS_ID)
								.and(PRAISE.CREATE_MEMBER_ID.eq(currentMemberId)))

						.where(TREND.ID.in(TrendIds));
				return select;
			};

			List<Trend> resultList = selectFunc.apply(selectListField).fetch(r -> {

				Trend trend = trendMap.get(r.getValue(TREND.ID));
				if ( trend != null){
					Member member = r.into(createMember).into(Member.class);
					member.setIsExpert(!StringUtils.isEmpty(r.getValue(EXPERT.ID)));
					member.setHeadPortraitPath(generateSecurePathCdn(member.getHeadPortraitPath()));
					trend.setCreateMember(member);
					Discuss discuss = r.into(DISCUSS).into(Discuss.class);
					discuss.setCreateMemberId(r.getValue(DISCUSS.CREATE_MEMBER_ID));
					trend.setDiscuss(discuss);
					Attention attention = r.into(ATTENTION).into(Attention.class);
					trend.setAttention(attention);
					Praise praise = r.into(PRAISE).into(Praise.class);
					trend.setPraise(praise);
				}
				return trend;
			});
			logger.error("after get trend all list:" + System.currentTimeMillis());
			handlerTrendsTopic(resultList, currentMemberId);
			logger.error("after handler trend topic:" + System.currentTimeMillis());
			return PagedResult.create(count, resultList);
		});
	}

	@Override
	public PagedResult<Trend> findTrendsMine(int start, int pageSize, String currentMemberId,
			String rootOrganizationId) {
		Field<Long> createTime = TREND.CREATE_TIME.as("createTime");
		Field<String> createMemberId = TREND.CREATE_MEMBER_ID.as("createMemberId");

		Field<Long> createTimeQuestion = QUESTION.CREATE_TIME.as("createTimecreateTimeQuestion");
		Field<String> createMemberIdQuestion = QUESTION.CREATE_MEMBER_ID.as("createMemberIdQuestion");
		return trendDao.execute(e -> {

			// 我的关注
			List<Attention> myAttention = attentionDao.fetch(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId));
			// 关注的话题
			List<Attention> topics = myAttention.stream()
					.filter(a -> Attention.BUSINESS_TYPE_TOPIC == a.getBusinessType()).collect(Collectors.toList());
			SelectConditionStep<Record> topicStep = e.select(Fields.start().add(TREND.ID, TREND.CREATE_TIME).end())
					.from(TREND)
					.where(TREND.IS_CURRENT.eq(Trend.IS_CURRENT_YES)
							.and(TREND.ROOT_ORGANIZATION_ID.eq(rootOrganizationId)))
					.and(DSL.exists(e.select(QUESTION_TOPIC.ID).from(QUESTION_TOPIC)
							.where(QUESTION_TOPIC.QUESTION_ID.eq(TREND.QUESTION_ID)).and(QUESTION_TOPIC.TOPIC_ID
									.in(topics.stream().map(Attention::getBusinessId).collect(Collectors.toList())))));
			// 关注的专家
			List<Attention> experts = myAttention.stream()
					.filter(a -> Attention.BUSINESS_TYPE_EXPERT == a.getBusinessType()).collect(Collectors.toList());
			SelectConditionStep<Record> expertStep = e.select(Fields.start().add(TREND.ID, TREND.CREATE_TIME).end())
					.from(TREND).leftJoin(EXPERT).on(EXPERT.MEMBER_ID.eq(TREND.CREATE_MEMBER_ID))
					.where(TREND.IS_CURRENT.eq(Trend.IS_CURRENT_YES)
							.and(TREND.ROOT_ORGANIZATION_ID.eq(rootOrganizationId)))
					.and(EXPERT.ID.in(experts.stream().map(Attention::getBusinessId).collect(Collectors.toList())));
			// 关注的内容（问题、文章）
			List<Attention> contents = myAttention.stream()
					.filter(a -> Attention.BUSINESS_TYPE_QUESTION == a.getBusinessType()
							|| Attention.BUSINESS_TYPE_ARTICLE == a.getBusinessType()
							|| Attention.BUSINESS_TYPE_SHARE == a.getBusinessType())
					.collect(Collectors.toList());
			SelectConditionStep<Record> contentStep = e.select(Fields.start().add(TREND.ID, TREND.CREATE_TIME).end())
					.from(TREND)
					.where(TREND.IS_CURRENT.eq(Trend.IS_CURRENT_YES)
							.and(TREND.ROOT_ORGANIZATION_ID.eq(rootOrganizationId)))
					.and(TREND.QUESTION_ID
							.in(contents.stream().map(Attention::getBusinessId).collect(Collectors.toList())));
			SelectOrderByStep<Record> union = topicStep.union(expertStep).union(contentStep);
			int count = e.fetchCount(union);
			List<Trend> simpleTrends = union.orderBy(TREND.CREATE_TIME.desc()).limit(start, pageSize).fetch(r -> {
				Trend trend = r.into(TREND).into(Trend.class);
				return trend;
			});

			// 查找本页数据并关联表
			com.zxy.product.askbar.jooq.tables.Member createMember = MEMBER.as("createMember");
			List<Trend> trends = e
					.select(Fields.start()
							.add(TREND.ID, TREND.BUSINESS_ID, TREND.BUSINESS_TYPE, TREND.QUESTION_ID, TREND.DISCUSS_ID,
									createMemberId, createTime, TREND.ROOT_ORGANIZATION_ID)
							.add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
									QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
									QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM,
									QUESTION.PRAISE_NUM, QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM,
									QUESTION.ATTENTION_NUM, createTimeQuestion, QUESTION.LAST_MODIFY_TIME,
									QUESTION.SHARE_TITLE, QUESTION.SHARE_OBJECT_ID, QUESTION.SHARE_TYPE,
									createMemberIdQuestion, QUESTION.TOPIC_STATUS)
							.add(DISCUSS.ID, DISCUSS.TYPE, DISCUSS.CONTENT, DISCUSS.CONTENT_TEXT, DISCUSS.AUDIT_STATUS,
									DISCUSS.IMG, DISCUSS.ACCUSE_STATUS, DISCUSS.PRAISE_NUM, DISCUSS.REPLY_NUM,
									DISCUSS.CREATE_TIME, DISCUSS.CREATE_MEMBER_ID)
							.add(ATTENTION.ID).add(PRAISE.ID).add(createMember.ID).add(createMember.FULL_NAME)
							.add(createMember.HEAD_PORTRAIT).add(createMember.HEAD_PORTRAIT_PATH).add(EXPERT.ID).end())
					.from(TREND).leftJoin(createMember).on(createMember.ID.eq(TREND.CREATE_MEMBER_ID)).leftJoin(EXPERT)
					.on(EXPERT.MEMBER_ID.eq(TREND.CREATE_MEMBER_ID)
							.and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE)).and(
									EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
					.leftJoin(QUESTION).on(QUESTION.ID.eq(TREND.QUESTION_ID).and(QUESTION.HIDDEN.eq(Question.HIDDEN_FLAG_NO))).leftJoin(DISCUSS)
					.on(DISCUSS.ID.eq(TREND.DISCUSS_ID)).leftJoin(ATTENTION)
					.on(ATTENTION.BUSINESS_ID.eq(TREND.BUSINESS_ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))
					.leftJoin(PRAISE)
					.on(PRAISE.OBJECT_ID.eq(TREND.BUSINESS_ID).and(PRAISE.CREATE_MEMBER_ID.eq(currentMemberId)))
					.where(TREND.ID.in(simpleTrends.stream().map(Trend::getId).collect(Collectors.toList())))
					.groupBy(TREND.ID).orderBy(TREND.CREATE_TIME.desc()).fetch(r -> {
						Trend trend = r.into(TREND).into(Trend.class);
						trend.setCreateMemberId(r.getValue(createMemberId));
						trend.setCreateTime(r.getValue(createTime));
						Member member = r.into(createMember).into(Member.class);
						member.setIsExpert(!StringUtils.isEmpty(r.getValue(EXPERT.ID)));
						member.setHeadPortraitPath(generateSecurePathCdn(member.getHeadPortraitPath()));
						trend.setCreateMember(member);
						Question question = r.into(QUESTION).into(Question.class);
						question.setCreateMemberId(r.getValue(createMemberIdQuestion));
						trend.setQuestion(question);
						Discuss discuss = r.into(DISCUSS).into(Discuss.class);
						discuss.setCreateMemberId(r.getValue(DISCUSS.CREATE_MEMBER_ID));
						trend.setDiscuss(discuss);
						Attention attention = r.into(ATTENTION).into(Attention.class);
						trend.setAttention(attention);
						Praise praise = r.into(PRAISE).into(Praise.class);
						trend.setPraise(praise);
						return trend;
					});
			handlerTrendsTopic(trends, currentMemberId);

			return PagedResult.create(count, trends);
		});
	}

	@Override
	public PagedResult<Trend> findTrendsArticle(int start, int pageSize, String currentMemberId,
			String rootOrganizationId) {
		Field<String> createMemberId = QUESTION.CREATE_MEMBER_ID.as("createMemberId");
		Field<Long> createTime = QUESTION.CREATE_TIME.as("createTime");
		return trendDao.execute((DSLContext e) -> {
			SelectSelectStep<Record> selectListField = e.select(Fields.start()
					.add(TREND.ID, TREND.BUSINESS_ID, TREND.BUSINESS_TYPE, TREND.QUESTION_ID, TREND.DISCUSS_ID,
							TREND.CREATE_MEMBER_ID, TREND.CREATE_TIME, TREND.ROOT_ORGANIZATION_ID)
					.add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
							QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,QUESTION.TOP_STATUS,
							QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
							QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.ATTENTION_NUM, createTime,
							QUESTION.LAST_MODIFY_TIME, QUESTION.SHARE_TITLE, QUESTION.SHARE_OBJECT_ID,
							QUESTION.SHARE_TYPE, createMemberId, QUESTION.TOPIC_STATUS)
					.add(ATTENTION.ID).add(PRAISE.ID).add(MEMBER.ID).add(MEMBER.FULL_NAME).add(MEMBER.HEAD_PORTRAIT)
					.add(MEMBER.HEAD_PORTRAIT_PATH).add(EXPERT.ID).end());

			SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(TREND.ID.countDistinct()).end());

			List<Condition> conditions = Stream.of(Optional.of(TREND.BUSINESS_TYPE.eq(Trend.BUSINESS_TYPE_ARTICLE)))
					.filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

			Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a.from(TREND)
					.leftJoin(QUESTION).on(QUESTION.ID.eq(TREND.QUESTION_ID)
							.and(QUESTION.PARENT_ID.isNull())
							.and(QUESTION.HIDDEN.eq(Question.HIDDEN_FLAG_NO)))
					.leftJoin(ATTENTION)
					.on(ATTENTION.BUSINESS_ID.eq(TREND.BUSINESS_ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))
					.leftJoin(PRAISE)
					.on(PRAISE.OBJECT_ID.eq(TREND.BUSINESS_ID).and(PRAISE.CREATE_MEMBER_ID.eq(currentMemberId)))
					.leftJoin(MEMBER).on(MEMBER.ID.eq(TREND.CREATE_MEMBER_ID)).leftJoin(EXPERT)
					.on(EXPERT.MEMBER_ID.eq(TREND.CREATE_MEMBER_ID)
							.and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
							.and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
					.where(conditions);
			// .where(conditions).and(TREND.ROOT_ORGANIZATION_ID.eq(rootOrganizationId));

			int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

			List<Trend> trends = stepFunc.apply(selectListField).groupBy(TREND.BUSINESS_ID)
					.orderBy(QUESTION.TOP_STATUS.desc(),QUESTION.TOP_TIME.desc(),QUESTION.CREATE_TIME.desc()).limit(start, pageSize).fetch(r -> {
						Trend trend = r.into(Trend.class);
						Question question = r.into(Question.class);
						question.setCreateMemberId(r.getValue(createMemberId));
						question.setCreateTime(r.getValue(createTime));
						Attention attention = r.into(Attention.class);
						Praise praise = r.into(Praise.class);
						Member member = r.into(Member.class);
						member.setIsExpert(!StringUtils.isEmpty(r.getValue(EXPERT.ID)));
						member.setHeadPortraitPath(generateSecurePathCdn(member.getHeadPortraitPath()));
						trend.setQuestion(question);
						trend.setAttention(attention);
						trend.setPraise(praise);
						trend.setCreateMember(member);
						return trend;
					});
			handlerTrendsTopic(trends, currentMemberId);

			return PagedResult.create(count, trends);
		});
	}

	/** 文件安全下载的计算秘钥CDN */
	@Value("${zxy.secure.file.secret4Cdn:zhixueyuncdnexp1234}")
	private String secureFileSecret4Cdn;


	private String generateSecurePathCdn(String uri) {
		if (org.jooq.tools.StringUtils.isEmpty(uri)) return "";
		// 当前服务器时间
		String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
		// uuid
		String rand = UUID.randomUUID().toString().replaceAll("-", "");
		// uid
		String uid = "0";
		String value = "/" + uri + "-" + timestamp + "-" + rand + "-" + uid + "-" + secureFileSecret4Cdn;
		String otherMd5 = DigestUtils.md5Hex(value);
		return uri + "?auth_key=" + timestamp + "-" + rand + "-" + uid + "-" + otherMd5;
	}


	private void handlerTrendsTopic(List<Trend> trends, String currentMemberId) {
		questionTopicDao.execute(e -> {
			List<Question> questions = trends.stream().map(t -> t.getQuestion()).collect(Collectors.toList());
			// 问题关联话题
			Result<Record> record = e
					.select(Fields.start()
							.add(QUESTION_TOPIC.ID, QUESTION_TOPIC.QUESTION_ID, TOPIC.ID, TOPIC.NAME, TOPIC.TYPE_ID,
									TOPIC.GROUP, TOPIC.IS_BAR_TOPIC, TOPIC_MANAGER.ID, QUESTION_TOPIC.RSMART_LABEL)
							.end())
					.from(QUESTION_TOPIC)
					.leftJoin(TOPIC)
					.on(QUESTION_TOPIC.TOPIC_ID.eq(TOPIC.ID)).leftJoin(TOPIC_MANAGER)
					.on(TOPIC_MANAGER.TOPIC_ID.eq(TOPIC.ID).and(TOPIC_MANAGER.MEMBER_ID.eq(currentMemberId)))
					.where(QUESTION_TOPIC.QUESTION_ID
							.in(questions.stream().map(a -> a.getId()).collect(Collectors.toList()))
							.and(TOPIC.STATUS.eq(Topic.ENABLED)).and(TOPIC.DELETE_FLAG.eq(Topic.DELETE_FLASE)))
					.fetch();
			logger.error("after get question_topic:" + System.currentTimeMillis());
			Map<String, List<QuestionTopic>> topicMap = new ArrayList<>(record.map(r -> {
				QuestionTopic questionTopic = r.into(QuestionTopic.class);
				Topic topic = r.into(Topic.class);
				questionTopic.setIsManager(!StringUtils.isEmpty(r.getValue(TOPIC_MANAGER.ID)));
				questionTopic.setTopic(topic);
				return questionTopic;
			}).stream().filter(s -> s.getId() != null && s.getQuestionId() != null)
					.collect(toMap(QuestionTopic::getId, p -> p, (p, q) -> q)).values())// 转map去重
							.stream().collect(groupingBy(QuestionTopic::getQuestionId));// 专家id分组
			logger.error("after get topicMap :" + System.currentTimeMillis());
			// 话题处理
			trends.forEach(td -> {
				Question question = td.getQuestion();
				List<QuestionTopic> questionTopics = topicMap.getOrDefault(td.getQuestionId(), new ArrayList<>());
				if(question.getTopicStatus() != null && question.getTopicStatus() != Question.TOPIC_STATUS_Y) {//标签未发布
					questionTopics = questionTopics.stream().filter(qt -> QuestionTopic.RSMART_LABEL_0 == qt.getRsmartLabel()).collect(toList());//过滤出非智能标签数据
				}
				question.setQuestionTopics(questionTopics);
				// 是否为话题管理员
				Optional<Boolean> isManager = questionTopics.stream().map(t -> t.getIsManager())
						.reduce((a, b) -> a || b);
				question.setIsManager(isManager.orElse(false));
				question.setIsCreator(currentMemberId.equals(question.getCreateMemberId()));
				Discuss discuss = td.getDiscuss();
				if (discuss != null) {
					discuss.setIsCreator(currentMemberId.equals(discuss.getCreateMemberId()));
				}
			});
			logger.error("after trans topic:" + System.currentTimeMillis());
			return trends;
		});
	}

	@Override
	public String deleteByQuestionId(String questionId) {
		trendDao.delete(TREND.QUESTION_ID.eq(questionId));
		return questionId;
	}

	@Override
	public String deleteByBusinessId(String businessId) {
		Optional<Trend> trend = trendDao.fetchOne(TREND.BUSINESS_ID.eq(businessId));
		trend.ifPresent(t -> {
			trendDao.delete(TREND.BUSINESS_ID.eq(businessId));
			// 将上一条动态置为最新
			String questionId = t.getQuestionId();
			Table<Record1<String>> subTable = DSL.select(TREND.ID).from(TREND).where(TREND.QUESTION_ID.eq(questionId))
					.orderBy(TREND.CREATE_TIME.desc()).limit(1).asTable("t");
			trendDao.execute(e -> {
				return e.update(TREND).set(TREND.IS_CURRENT, Trend.IS_CURRENT_YES)
						.where(TREND.ID.eq(DSL.select(subTable.field(0, String.class)).from(subTable))).execute();
			});
		});
		return businessId;
	}

}
