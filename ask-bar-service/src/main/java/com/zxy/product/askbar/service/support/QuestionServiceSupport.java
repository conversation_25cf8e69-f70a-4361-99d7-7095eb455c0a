package com.zxy.product.askbar.service.support;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.askbar.api.*;
import com.zxy.product.askbar.content.ErrorCode;
import com.zxy.product.askbar.content.MessageHeaderContent;
import com.zxy.product.askbar.content.MessageTypeContent;
import com.zxy.product.askbar.entity.*;
import com.zxy.product.askbar.service.util.DateUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.askbar.jooq.Tables.*;
import static com.zxy.product.askbar.service.util.SecurePathCdnUtils.generateSecurePathCdn;
import static java.util.stream.Collectors.*;

/**
 * 内容（问题、文章、分享）业务实现类
 *
 * <AUTHOR>
 * @date 2017年7月31日 下午2:43:40
 */
@Service
public class QuestionServiceSupport implements QuestionService, EnvironmentAware {

    private MessageSender messageSender;
    private AuditService auditService;
    private CommonDao<Question> questionDao;
    private QuestionTopicService questionTopicService;
    private AttachmentService attachmentService;
    private TrendService trendService;
    private CommonDao<QuestionTopic> questionTopicDao;
    private CommonDao<ExpertTopic> expertTopicDao;
    private CommonDao<Expert> expertDao;
    private CommonDao<Topic> topicDao;
    private CommonDao<Praise> praiseDao;
    private CommonDao<Attention> attentionDao;
    private ExpertService expertService;
    private TopicService topicService;
    private CommonDao<Audit> auditDao;
    private CommonDao<QuestionExpert> questionExpertCommonDao;
    private StudioService studioService;
    private StudioContentService studioContentService;
    private StudioContentAuditService studioContentAuditService;
    private Cache cache;
    private long hours;

    @Autowired
    public void setStudioService(StudioService studioService) {
        this.studioService = studioService;
    }

    @Autowired
    public void setStudioContentService(StudioContentService studioContentService) {
        this.studioContentService = studioContentService;
    }

    @Autowired
    public void setStudioContentAuditService(StudioContentAuditService studioContentAuditService) {
        this.studioContentAuditService = studioContentAuditService;
    }

    @Autowired
    public void setAttentionDao(CommonDao<Attention> attentionDao) {
        this.attentionDao = attentionDao;
    }

    @Autowired
    public void setQuestionExpertCommonDao(CommonDao<QuestionExpert> questionExpertCommonDao) {
        this.questionExpertCommonDao = questionExpertCommonDao;
    }

    @Autowired
    public void setAuditDao(CommonDao<Audit> auditDao) {
        this.auditDao = auditDao;
    }

    @Autowired
    public void setTopicService(TopicService topicService) {
        this.topicService = topicService;
    }

    @Autowired
    public void setExpertService(ExpertService expertService) {
        this.expertService = expertService;
    }

    @Autowired
    public void setPraiseDao(CommonDao<Praise> praiseDao) {
        this.praiseDao = praiseDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setQuestionDao(CommonDao<Question> questionDao) {
        this.questionDao = questionDao;
    }

    @Autowired
    public void setAuditService(AuditService auditService) {
        this.auditService = auditService;
    }

    @Autowired
    public void setQuestionTopicService(QuestionTopicService questionTopicService) {
        this.questionTopicService = questionTopicService;
    }

    @Autowired
    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    @Autowired
    public void setTrendService(TrendService trendService) {
        this.trendService = trendService;
    }

    @Autowired
    public void setQuestionTopicDao(CommonDao<QuestionTopic> questionTopicDao) {
        this.questionTopicDao = questionTopicDao;
    }

    @Autowired
    public void setExpertDao(CommonDao<Expert> expertDao) {
        this.expertDao = expertDao;
    }

    @Autowired
    public void setTopicDao(CommonDao<Topic> topicDao) {
        this.topicDao = topicDao;
    }

    @Autowired
    public void setExpertTopicDao(CommonDao<ExpertTopic> expertTopicDao) {
        this.expertTopicDao = expertTopicDao;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("system", "speech-set-audit");
    }

    @Override
    public Question get(String id) {
        return questionDao.get(id);
    }

    @Override
    public Optional<Question> getOptional(String id) {
        return questionDao.getOptional(id);
    }

    @Override
    public Question get(String id, String currentMemberId) {
        Field<String> createMemberIdAttention = ATTENTION.CREATE_MEMBER_ID.as("create_member_id_attention");
        Field<String> createMemberIdPraise = PRAISE.CREATE_MEMBER_ID.as("creatememberIdPraise");
        return questionDao.execute(e -> {
            Record record = e
                    .select(Fields.start()
                            .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                                    QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                                    QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM,
                                    QUESTION.PRAISE_NUM, QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE,
                                    QUESTION.SHARE_TYPE, QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME,
                                    QUESTION.CREATE_MEMBER_ID,QUESTION.SOURCE_NAME, QUESTION.FROM_STUDIO,
                                    QUESTION.QUESTION_SOURCE_NAME,QUESTION.SOURCE_URL,QUESTION.TOPIC_STATUS)
                            .add(MEMBER.ID).add(MEMBER.FULL_NAME).add(MEMBER.HEAD_PORTRAIT)
                            .add(MEMBER.HEAD_PORTRAIT_PATH).add(PRAISE.ID).add(createMemberIdPraise).add(ATTENTION.ID).add(createMemberIdAttention).add(AUDIT.ID)
                            .add(AUDIT.AUDIT_NOTE).add(EXPERT.ID).end())
                    .from(QUESTION).leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
                    .on(EXPERT.MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)
                            .and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
                            .and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
                    .leftJoin(PRAISE)
                    .on(PRAISE.OBJECT_ID.eq(QUESTION.ID).and(PRAISE.CREATE_MEMBER_ID.eq(currentMemberId)))
                    .leftJoin(ATTENTION)
                    .on(ATTENTION.BUSINESS_ID.eq(QUESTION.ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))
                    .leftJoin(AUDIT).on(AUDIT.BUSINESS_ID.eq(QUESTION.ID).and(AUDIT.ACCUSED_MEMBER_ID.isNull()))
                    .where(QUESTION.ID.eq(id)).fetchOne();

            if (record == null)
                throw new UnprocessableException(com.zxy.common.rpc.filter.ErrorCode.DataNotExists);

            Question question = record.into(Question.class);

            if (Question.DELETE_FLAG_YES == question.getDeleteFlag()) {
                throw new UnprocessableException(com.zxy.common.rpc.filter.ErrorCode.DataNotExists);
            }
            Member member = record.into(Member.class);
            member.setIsExpert(!StringUtils.isEmpty(record.getValue(EXPERT.ID)));
            question.setCreateMember(member);
            Praise praise = record.into(Praise.class);
            praise.setCreateMemberId(record.getValue(createMemberIdPraise));
            question.setPraise(praise);
            Attention attention = record.into(Attention.class);
            attention.setCreateMemberId(record.getValue(createMemberIdAttention));
            question.setAttention(attention);

            Audit audit = record.into(Audit.class);
            question.setAudit(audit);
            List<Attachment> attachments = attachmentService.getByBusinessId(id);
            question.setAttachments(attachments);
            handlerQuestionTopic(Arrays.asList(question), currentMemberId);
            handlerQuestionExperts(question);
            return question;
        });
    }

    @Override
    public Question getFront(String id, String currentMemberId) {
        Question question = this.get(id, currentMemberId);
        // 被举报通过了
        if (Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(com.zxy.common.rpc.filter.ErrorCode.DataNotExists);
        }
        return question;
    }

    @Override
    public PagedResult<Question> findMyQuestions(int start, int pageSize, Optional<Integer> auditStatus,
                                                 String memberId, Optional<String> timeOrder) {
        return findContentPage(start, pageSize, memberId, Question.TYPE_QUESTION, memberId, auditStatus,
                Optional.of(Question.ACCUSE_STATUS_NO), timeOrder);
    }

    @Override
    public PagedResult<Question> findMyQuestionsMerge(int start, int pageSize, Optional<Integer> auditStatus,
                                                      List<String> memberIds, Optional<String> timeOrder) {
        return findContentPageMerge(start, pageSize, memberIds, Question.TYPE_QUESTION, memberIds, auditStatus,
                Optional.of(Question.ACCUSE_STATUS_NO), timeOrder);
    }

    @Override
    public PagedResult<Question> findMyArticles(int start, int pageSize, Optional<Integer> auditStatus, String memberId,
                                                Optional<String> timeOrder) {
        return findContentPage(start, pageSize, memberId, Question.TYPE_ARTICLE, memberId, auditStatus,
                Optional.of(Question.ACCUSE_STATUS_NO), timeOrder);
    }

    @Override
    public PagedResult<Question> findMyArticlesMerge(int start, int pageSize, Optional<Integer> auditStatus,
                                                     List<String> memberIds, Optional<String> timeOrder) {
        return findContentPageMerge(start, pageSize, memberIds, Question.TYPE_ARTICLE, memberIds, auditStatus,
                Optional.of(Question.ACCUSE_STATUS_NO), timeOrder);
    }

    @Override
    public PagedResult<Question> findExpertArticles(int start, int pageSize, String expertMemberId,
                                                    String currentMemberId) {
        return findContentPage(start, pageSize, expertMemberId, Question.TYPE_ARTICLE, currentMemberId,
                Optional.of(Question.AUDIT_STATUS_PASS), Optional.of(Question.ACCUSE_STATUS_NO), Optional.empty());
    }

    /**
     * @param auditStatus
     *            审核状态
     * @param accuseStatus
     *            举报状态
     */
    private PagedResult<Question> findContentPage(int start, int pageSize, String memberId, Integer type,
                                                  String currentMemberId, Optional<Integer> auditStatus, Optional<Integer> accuseStatus,
                                                  Optional<String> timeOrderOptional) {
        return questionDao.execute(e -> {

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                            QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                            QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
                            QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE, QUESTION.SHARE_TYPE,
                            QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME, QUESTION.LAST_MODIFY_TIME,
                            QUESTION.CREATE_MEMBER_ID, QUESTION.TOPIC_STATUS)
                    .add(MEMBER.ID).add(MEMBER.FULL_NAME).add(MEMBER.HEAD_PORTRAIT).add(MEMBER.HEAD_PORTRAIT_PATH)
                    .add(PRAISE.ID).add(ATTENTION.ID).add(AUDIT.ID).add(AUDIT.AUDIT_NOTE).add(EXPERT.ID).end());

            SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(QUESTION.ID.countDistinct()).end());

            List<Condition> conditions = Stream
                    .of(auditStatus.map(QUESTION.AUDIT_STATUS::eq), accuseStatus.map(QUESTION.ACCUSE_STATUS::eq))
                    .filter(Optional::isPresent).map(Optional::get).collect(toList());

            Integer auditBusinessType = Question.TYPE_QUESTION == type ? Audit.BUSINESS_TYPE_QUESTION
                    : Audit.BUSINESS_TYPE_ARTICLE;
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(QUESTION).leftJoin(MEMBER)
                        .on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
                        .on(EXPERT.MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)
                                .and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
                                .and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
                        .leftJoin(PRAISE)
                        .on(PRAISE.OBJECT_ID.eq(QUESTION.ID).and(PRAISE.CREATE_MEMBER_ID.eq(currentMemberId)))
                        .leftJoin(ATTENTION)
                        .on(ATTENTION.BUSINESS_ID.eq(QUESTION.ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))
                        .leftJoin(AUDIT)
                        .on(AUDIT.BUSINESS_ID.eq(QUESTION.ID).and(AUDIT.BUSINESS_TYPE.eq(auditBusinessType)))
                        .where(conditions).and(QUESTION.TYPE.eq(type))
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)) // 未删除
                        .and(QUESTION.HIDDEN.eq(Question.HIDDEN_FLAG_NO)) // 未删除
                        .and(QUESTION.CREATE_MEMBER_ID.eq(memberId).and(QUESTION.PARENT_ID.isNull())); // 查询条件为空

                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> apply = stepFunc.apply(selectListField);

            if (timeOrderOptional.isPresent() && timeOrderOptional.get().equals("asc")) {
                apply.orderBy(QUESTION.AUDIT_STATUS.asc(), QUESTION.CREATE_TIME.asc());
            } else {
                apply.orderBy(QUESTION.AUDIT_STATUS.asc(), QUESTION.CREATE_TIME.desc());
            }

            List<Question> questions = apply.groupBy(QUESTION.ID).limit(start, pageSize).fetch(r -> {
                Question question = r.into(Question.class);
                Member member = r.into(Member.class);
                member.setIsExpert(!StringUtils.isEmpty(r.getValue(EXPERT.ID)));
                member.setHeadPortraitPath(generateSecurePathCdn(member.getHeadPortraitPath()));
                question.setCreateMember(member);
                Praise praise = r.into(Praise.class);
                question.setPraise(praise);
                Attention attention = r.into(Attention.class);
                question.setAttention(attention);
                Audit audit = r.into(Audit.class);
                question.setAudit(audit);
                handlerQuestionExperts(question);
                return question;
            });
            handlerQuestionTopic(questions, currentMemberId);

            return PagedResult.create(count, questions);

        });
    }

    /**
     * @param auditStatus
     *            审核状态
     * @param accuseStatus
     *            举报状态
     */
    private PagedResult<Question> findContentPageMerge(int start, int pageSize, List<String> memberIds, Integer type,
                                                       List<String> currentMemberIds, Optional<Integer> auditStatus, Optional<Integer> accuseStatus,
                                                       Optional<String> timeOrderOptional) {
        return questionDao.execute(e -> {

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                            QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                            QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
                            QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE, QUESTION.SHARE_TYPE,
                            QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME, QUESTION.LAST_MODIFY_TIME,
                            QUESTION.CREATE_MEMBER_ID, QUESTION.TOPIC_STATUS)
                    .add(MEMBER.ID).add(MEMBER.FULL_NAME).add(MEMBER.HEAD_PORTRAIT).add(MEMBER.HEAD_PORTRAIT_PATH).add(MEMBER.NAME).add(MEMBER.FULL_NAME)
                    .add(ATTENTION.ID).add(AUDIT.ID).add(AUDIT.AUDIT_NOTE).add(EXPERT.ID).end());

            SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(QUESTION.ID.countDistinct()).end());

            List<Condition> conditions = Stream
                    .of(auditStatus.map(QUESTION.AUDIT_STATUS::eq), accuseStatus.map(QUESTION.ACCUSE_STATUS::eq))
                    .filter(Optional::isPresent).map(Optional::get).collect(toList());

            Integer auditBusinessType = Question.TYPE_QUESTION == type ? Audit.BUSINESS_TYPE_QUESTION
                    : Audit.BUSINESS_TYPE_ARTICLE;

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(QUESTION).leftJoin(MEMBER)
                        .on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
                        .on(EXPERT.MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)
                                .and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
                                .and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
                        .leftJoin(ATTENTION)
                        .on(ATTENTION.BUSINESS_ID.eq(QUESTION.ID).and(ATTENTION.CREATE_MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)))
                        .leftJoin(AUDIT)
                        .on(AUDIT.BUSINESS_ID.eq(QUESTION.ID).and(AUDIT.BUSINESS_TYPE.eq(auditBusinessType)))
                        .where(conditions).and(QUESTION.TYPE.eq(type))
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)) // 未删除
                        .and(QUESTION.CREATE_MEMBER_ID.in(memberIds).and(QUESTION.PARENT_ID.isNull())); // 查询条件为空

                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> apply = stepFunc.apply(selectListField);

            if (timeOrderOptional.isPresent() && timeOrderOptional.get().equals("asc")) {
                apply.orderBy(QUESTION.AUDIT_STATUS.asc(), QUESTION.CREATE_TIME.asc());
            } else {
                apply.orderBy(QUESTION.AUDIT_STATUS.asc(), QUESTION.CREATE_TIME.desc());
            }

            List<Question> questions = apply.limit(start, pageSize).fetch(r -> {
                Question question = r.into(Question.class);
                Member member = r.into(Member.class);
                member.setId(r.getValue(MEMBER.ID));
                member.setIsExpert(!StringUtils.isEmpty(r.getValue(EXPERT.ID)));
                member.setHeadPortrait(r.getValue(MEMBER.HEAD_PORTRAIT));
                member.setHeadPortraitPath(r.getValue(MEMBER.HEAD_PORTRAIT_PATH));
                member.setName(r.getValue(MEMBER.NAME));
                member.setFullName(r.getValue(MEMBER.FULL_NAME));
                question.setCreateMember(member);
                Attention attention = r.into(Attention.class);
                question.setAttention(attention);
                Audit audit = r.into(Audit.class);
                question.setAudit(audit);
                handlerQuestionExperts(question);
                return question;
            });
            handlerQuestionTopicMerge(questions, currentMemberIds);
            handlerQuestionPariseMerge(questions);
            return PagedResult.create(count, questions);

        });
    }

    private void handlerQuestionPariseMerge(List<Question> questions){
        questions.stream().forEach(item -> {
            List<Praise> praises = praiseDao.fetch(PRAISE.OBJECT_ID.eq(item.getId()).and(PRAISE.CREATE_MEMBER_ID.eq(item.getCreateMember().getId())));
            if(praises != null && praises.size() > 0){
                item.setPraise(praises.get(0));
            }else {
                Praise praise = new Praise();
                praise.setId(null);
                praise.setCreateMemberId(null);
                item.setPraise(praise);
            }
        });
    }

    private void handlerQuestionTopicMerge(List<Question> questions, List<String> currentMemberIds) {
        //兼岗用户数据甄别。
        Map<String,List<Question>> map = new HashMap<String,List<Question>>();
        for(int i = 0; i < currentMemberIds.size(); i++){
            String memberId = currentMemberIds.get(i);
            List<Question> question = new ArrayList<Question>();
            for(int j = 0; j < questions.size(); j++ ){
                if(memberId.equalsIgnoreCase(questions.get(j).getCreateMemberId())){
                    question.add(questions.get(j));
                }
            }
            map.put(memberId, question);
        }

        //Map 遍历
        for (Map.Entry<String,List<Question>> entry : map.entrySet()) {
            questionTopicDao.execute(e -> {
                // 问题关联话题
                Result<Record> record = e
                        .select(Fields.start()
                                .add(QUESTION_TOPIC.ID, QUESTION_TOPIC.QUESTION_ID, TOPIC.ID, TOPIC.NAME, TOPIC.TYPE_ID,
                                        TOPIC.GROUP, TOPIC.IS_BAR_TOPIC, TOPIC_MANAGER.ID, QUESTION_TOPIC.RSMART_LABEL)
                                .end())
                        .from(QUESTION_TOPIC)
                        .leftJoin(TOPIC)
                        .on(QUESTION_TOPIC.TOPIC_ID.eq(TOPIC.ID)).leftJoin(TOPIC_MANAGER)
                        .on(TOPIC_MANAGER.TOPIC_ID.eq(TOPIC.ID).and(TOPIC_MANAGER.MEMBER_ID.eq(entry.getKey())))
                        .where(QUESTION_TOPIC.QUESTION_ID
                                .in(entry.getValue().stream().map(a -> a.getId()).collect(toList()))
                                .and(TOPIC.STATUS.eq(Topic.ENABLED)).and(TOPIC.DELETE_FLAG.eq(Topic.DELETE_FLASE)))
                        .fetch();
                Map<String, List<QuestionTopic>> topicMap = new ArrayList<>(record.map(r -> {
                    QuestionTopic questionTopic = r.into(QuestionTopic.class);
                    Topic topic = r.into(Topic.class);
                    questionTopic.setIsManager(!StringUtils.isEmpty(r.getValue(TOPIC_MANAGER.ID)));
                    questionTopic.setTopic(topic);
                    return questionTopic;
                }).stream().filter(s -> s.getId() != null && s.getQuestionId() != null)
                        .collect(toMap(QuestionTopic::getId, p -> p, (p, q) -> q)).values())// 转map去重
                        .stream().collect(groupingBy(QuestionTopic::getQuestionId));// 专家id分组

                // 话题处理
                entry.getValue().forEach(q -> {
                    List<QuestionTopic> questionTopics = topicMap.getOrDefault(q.getId(), new ArrayList<>());
                    if(q.getTopicStatus() != null && q.getTopicStatus() != Question.TOPIC_STATUS_Y) {//标签未发布
                        questionTopics = questionTopics.stream().filter(qt -> QuestionTopic.RSMART_LABEL_0 == qt.getRsmartLabel()).collect(toList());//过滤出非智能标签数据
                    }
                    q.setQuestionTopics(questionTopics);
                    // 是否为话题管理员
                    Optional<Boolean> isManager = questionTopics.stream().map(t -> t.getIsManager())
                            .reduce((a, b) -> a || b);
                    q.setIsManager(isManager.orElse(false));
                    // 是否为创建者
                    q.setIsCreator(entry.getKey().equals(q.getCreateMemberId()));
                });

                return entry.getValue();
            });
        }

        //重新复制
        List<Question> copy = new ArrayList<Question>();
        for (Map.Entry<String,List<Question>> result : map.entrySet()) {
            copy.addAll(result.getValue());
        }
        questions = copy;




    }

    private void handlerQuestionExperts(Question question) {
        List<String> expertIds = questionExpertCommonDao.execute(e -> e
                .select(Fields.start().add(QUESTION_EXPERT.EXPERT_ID).end())
                .from(QUESTION_EXPERT)
                .where(QUESTION_EXPERT.QUESTION_ID.eq(question.getId()))
                .fetch(r -> r.getValue(QUESTION_EXPERT.EXPERT_ID)));
        List<Expert> experts = expertDao.execute(e -> e
                .select(Fields.start().add(
                        EXPERT.ID,
                        EXPERT.MEMBER_ID,
                        EXPERT.INTRODUCE,
                        MEMBER.ID,
                        MEMBER.FULL_NAME,
                        MEMBER.HEAD_PORTRAIT,
                        MEMBER.HEAD_PORTRAIT_PATH).end())
                .from(EXPERT)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(EXPERT.MEMBER_ID))
                .where(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
                .and(EXPERT.ID.in(expertIds))).fetch(r -> {
            Expert expert = r.into(Expert.class);
            expert.setName(r.getValue(MEMBER.FULL_NAME));
            return expert;
        });
        question.setExperts(experts);
    }

    private void handlerQuestionTopic(List<Question> questions, String currentMemberId) {
        questionTopicDao.execute(e -> {
            // 问题关联话题
            Result<Record> record = e
                    .select(Fields.start()
                            .add(QUESTION_TOPIC.ID, QUESTION_TOPIC.QUESTION_ID, TOPIC.ID, TOPIC.NAME, TOPIC.TYPE_ID,
                                    TOPIC.GROUP, TOPIC.IS_BAR_TOPIC, TOPIC_MANAGER.ID, QUESTION_TOPIC.RSMART_LABEL)
                            .end())
                    .from(QUESTION_TOPIC)
                    .leftJoin(TOPIC)
                    .on(QUESTION_TOPIC.TOPIC_ID.eq(TOPIC.ID)).leftJoin(TOPIC_MANAGER)
                    .on(TOPIC_MANAGER.TOPIC_ID.eq(TOPIC.ID).and(TOPIC_MANAGER.MEMBER_ID.eq(currentMemberId)))
                    .where(QUESTION_TOPIC.QUESTION_ID
                            .in(questions.stream().map(a -> a.getId()).collect(toList()))
                            .and(TOPIC.STATUS.eq(Topic.ENABLED)).and(TOPIC.DELETE_FLAG.eq(Topic.DELETE_FLASE)).and(TOPIC.FRONT_IS_DISPLAYED.eq(Topic.FRONT_IS_DISPLAYED_YES))
                    ) //.and(QUESTION_TOPIC.RSMART_LABEL.ne(1))
                    .fetch();
            Map<String, List<QuestionTopic>> topicMap = new ArrayList<>(record.map(r -> {
                QuestionTopic questionTopic = r.into(QuestionTopic.class);
                Topic topic = r.into(Topic.class);
                questionTopic.setIsManager(!StringUtils.isEmpty(r.getValue(TOPIC_MANAGER.ID)));
                questionTopic.setTopic(topic);
                return questionTopic;
            }).stream().filter(s -> s.getId() != null && s.getQuestionId() != null)
                    .collect(toMap(QuestionTopic::getId, p -> p, (p, q) -> q)).values())// 转map去重
                    .stream().collect(groupingBy(QuestionTopic::getQuestionId));// 专家id分组

            // 话题处理
            questions.forEach(q -> {
                List<QuestionTopic> questionTopics = topicMap.getOrDefault(q.getId(), new ArrayList<>());
                if(q.getTopicStatus() != null && q.getTopicStatus() != Question.TOPIC_STATUS_Y) {//标签未发布
                    questionTopics = questionTopics.stream().filter(qt -> QuestionTopic.RSMART_LABEL_0 == qt.getRsmartLabel()).collect(toList());//过滤出非智能标签数据
                }
                q.setQuestionTopics(questionTopics);
                // 是否为话题管理员
                Optional<Boolean> isManager = questionTopics.stream().map(t -> t.getIsManager())
                        .reduce((a, b) -> a || b);
                q.setIsManager(isManager.orElse(false));
                // 是否为创建者
                q.setIsCreator(currentMemberId.equals(q.getCreateMemberId()));
            });

            return questions;
        });
    }

    @Override
    public int delete(String id, String currentMemberId) {
        Optional<Question> questionOptional = questionDao.getOptional(id);
        questionOptional.ifPresent(question -> {
            // 有人关注的不能删除
//            if (question.getAttentionNum() != null && question.getAttentionNum() > 0) {
//                throw new UnprocessableException(ErrorCode.QuestionNotAllowedDeleteBeAttend);
//            }
            // 被分享过的不能删除
//            if (question.getShareNum() != null && question.getShareNum() > 0) {
//                throw new UnprocessableException(ErrorCode.QuestionNotAllowedDeleteBeShared);
//            }
            //防止越权操作，只能删除本人创建的提问
            if (Question.FROM_STUDIO_NO.equals(question.getFromStudio())) {
                if(!currentMemberId.equals(question.getCreateMemberId())) {
                    throw new UnprocessableException(ErrorCode.QuestionDeleteError);
                }
            }

            // 有人讨论的不能删除
            if (question.getDiscussNum() != null && question.getDiscussNum() > 0) {
                throw new UnprocessableException(ErrorCode.QuestionNotAllowedDeleteBeDiscussed);
            }
            // 删除审核记录
            if (Question.AUDIT_STATUS_WAIT == question.getAuditStatus()) { // 审核中
                questionDao.delete(id);
                auditService.deleteByBusinessId(id);
            } else {
                question.setDeleteFlag(Question.DELETE_FLAG_YES);
                questionDao.update(question);
                // 数字、动态等内容处理
                this.updateForDelAndAccuse(question,false);
            }

            messageSender.send(MessageTypeContent.BAR_QUESTION_DELETE, MessageHeaderContent.ID, question.getId(),
                    MessageHeaderContent.MEMBER_ID, currentMemberId);
        });
        return 1;
    }

    @Override
    public Question close(String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除、被举报
        if (Question.DELETE_FLAG_YES.equals(question.getDeleteFlag())
                || Question.ACCUSE_STATUS_YES.equals(question.getAccuseStatus())) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }

        if (Question.FROM_STUDIO_YES.equals(question.getFromStudio())) {
            question.setAuditStatus(Question.AUDIT_STATUS_WAIT);
        }
        if (Question.STATUS_ACTIVE.equals(question.getStatus())) {
            question.setStatus(Question.STATUS_CLOSE);

            messageSender.send(MessageTypeContent.BAR_QUESTION_CLOSE, MessageHeaderContent.ID, question.getId(),
                    MessageHeaderContent.MEMBER_ID, currentMemberId);
        } else { // 已经关闭，不能重复操作
            throw new UnprocessableException(ErrorCode.QuestionHasBeenClosed);
        }
        questionDao.update(question);
        return question;
    }

    @Override
    public Question hidden(Boolean isHidden, String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除
        if (Question.DELETE_FLAG_YES.equals(question.getDeleteFlag())) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }
        if(isHidden) {
            question.setHidden(Question.HIDDEN_FLAG_YES);
            //全局搜索需过滤已隐藏的问题，此处发消息到report
            messageSender.send(MessageTypeContent.BAR_QUESTION_DELETE, MessageHeaderContent.ID, question.getId(),
                    MessageHeaderContent.MEMBER_ID, currentMemberId,MessageHeaderContent.HIDDEN, "true");
        }else{
            question.setHidden(Question.HIDDEN_FLAG_NO);
            //取消隐藏需发消息到report，增加该问题的全局搜索功能
            messageSender.send(MessageTypeContent.BAR_QUESTION_INSERT, MessageHeaderContent.ID, question.getId(),
                    MessageHeaderContent.MEMBER_ID, currentMemberId,MessageHeaderContent.HIDDEN, "true");
        }
        questionDao.update(question);
        updateForDelAndAccuse(question,true);
        return question;
    }

    @Override
    public Question active(String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除、被举报
        if (Question.DELETE_FLAG_YES == question.getDeleteFlag()
                || Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }

        if (Question.STATUS_ACTIVE == question.getStatus()) { // 已经激活，不能重复操作
            throw new UnprocessableException(ErrorCode.QuestionHasBeenActived);
        } else {
            question.setStatus(Question.STATUS_ACTIVE);
        }
        questionDao.update(question);
        return question;
    }

    @Override
    public Question essence(String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除、被举报
        if (Question.DELETE_FLAG_YES == question.getDeleteFlag()
                || Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }

        if (Question.ESSENCE_STATUS_YES == question.getEssenceStatus()) { // 已经加精
            throw new UnprocessableException(ErrorCode.QuestionHasBeenEssence);
        } else {
            question.setEssenceStatus(Question.ESSENCE_STATUS_YES);
            question.setEssenceTime(System.currentTimeMillis());

            messageSender.send(MessageTypeContent.BAR_QUESTION_ESSENCE, MessageHeaderContent.ID, question.getId(),
                    MessageHeaderContent.MEMBER_ID, currentMemberId);
        }
        questionDao.update(question);
        return question;
    }

    @Override
    public Question unessence(String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除、被举报
        if (Question.DELETE_FLAG_YES == question.getDeleteFlag()
                || Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }

        if (Question.ESSENCE_STATUS_YES == question.getEssenceStatus()) {
            question.setEssenceStatus(Question.ESSENCE_STATUS_NO);
            question.setEssenceTime(null);
        } else { // 已经取消加精
            throw new UnprocessableException(ErrorCode.QuestionHasBeenUnessence);
        }
        questionDao.update(question);
        return question;
    }

    @Override
    public Question top(String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除、被举报
        if (Question.DELETE_FLAG_YES == question.getDeleteFlag()
                || Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }

        if (Question.TOP_STATUS_YES == question.getTopStatus()) { // 已经置顶
            throw new UnprocessableException(ErrorCode.QuestionHasBeenTop);
        } else {
            question.setTopStatus(Question.TOP_STATUS_YES);
            question.setTopTime(System.currentTimeMillis());

            messageSender.send(MessageTypeContent.BAR_QUESTION_TOP, MessageHeaderContent.ID, question.getId(),
                    MessageHeaderContent.MEMBER_ID, currentMemberId);
        }
        questionDao.update(question);
        return question;
    }

    @Override
    public Question untop(String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除、被举报
        if (Question.DELETE_FLAG_YES == question.getDeleteFlag()
                || Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }

        if (Question.TOP_STATUS_YES == question.getTopStatus()) {
            question.setTopStatus(Question.TOP_STATUS_NO);
            question.setTopTime(null);
        } else { // 已经取消置顶
            throw new UnprocessableException(ErrorCode.QuestionHasBeenUntop);
        }
        questionDao.update(question);
        return question;
    }

    @Override
    public Question topicTop(String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除、被举报
        if (Question.DELETE_FLAG_YES == question.getDeleteFlag()
                || Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }

        if (Question.TOPIC_TOP_STATUS_YES == question.getTopicTopStatus()) { // 已经置顶
            throw new UnprocessableException(ErrorCode.QuestionHasBeenTop);
        } else {
            question.setTopicTopStatus(Question.TOPIC_TOP_STATUS_YES);
            question.setTopicTopTime(System.currentTimeMillis());

//            messageSender.send(MessageTypeContent.BAR_QUESTION_TOP, MessageHeaderContent.ID, question.getId(),
//                    MessageHeaderContent.MEMBER_ID, currentMemberId);
        }
        questionDao.update(question);
        return question;
    }
    @Override
    public Question unTopicTop(String id, String currentMemberId) {
        Question question = questionDao.getOptional(id)
                .orElseThrow(() -> new UnprocessableException(ErrorCode.QuestionHasBeenDeleted));
        // 已删除、被举报
        if (Question.DELETE_FLAG_YES == question.getDeleteFlag()
                || Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(ErrorCode.QuestionHasBeenDeleted);
        }

        if (Question.TOPIC_TOP_STATUS_YES == question.getTopicTopStatus()) {
            question.setTopicTopStatus(Question.TOPIC_TOP_STATUS_NO);
            question.setTopicTopTime(null);
        } else { // 已经取消置顶
            throw new UnprocessableException(ErrorCode.QuestionHasBeenUntop);
        }
        questionDao.update(question);
        return question;
    }

    @Override
    public Question insertQuestion(String title, Optional<String> content, Optional<String> contentTxt,
                                   Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String currentUserId,
                                   String organizationId, String rootOrganizationId, boolean auditOn) {
        return insert(title, content, contentTxt, img, topics, attachments, currentUserId, organizationId,
                rootOrganizationId, Question.TYPE_QUESTION, auditOn,null, false);
    }

    @Override
    public Question updateQuestion(String id, String title, Optional<String> content, Optional<String> contentTxt,
                                   Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String currentUserId,
                                   String organizationId, String rootOrganizationId, boolean auditOn,List<String> experts) {
        return update(id, Optional.of(title), content, contentTxt, img, topics, attachments, currentUserId,
                organizationId, rootOrganizationId, auditOn,experts);
    }

    @Override
    public Question insertArticle(String title, Optional<String> content, Optional<String> contentTxt,
                                  Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String currentUserId,
                                  String organizationId, String rootOrganizationId, boolean auditOn) {
        return insert(title, content, contentTxt, img, topics, attachments, currentUserId, organizationId,
                rootOrganizationId, Question.TYPE_ARTICLE, auditOn ,null, false);
    }

    @Override
    public Question updateArticle(String id, String title, Optional<String> content, Optional<String> contentTxt,
                                  Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String currentUserId,
                                  String organizationId, String rootOrganizationId, boolean auditOn) {
        return update(id, Optional.of(title), content, contentTxt, img, topics, attachments, currentUserId,
                organizationId, rootOrganizationId, auditOn,null);
    }

    @Override
    public Question importQuestion(String title, Optional<String> content, Optional<String> contentTxt,
                                   Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String currentUserId,
                                   String organizationId, String rootOrganizationId) {
        Question question = new Question();
        question.forInsert();
        question.setTitle(title);
        content.ifPresent(question::setContent);
        contentTxt.ifPresent(question::setContentTxt);
        question.setAttentionNum(0);
        question.setPraiseNum(0);
        question.setShareNum(0);
        question.setBrowseNum(0);
        question.setDiscussNum(0);
        question.setAccuseStatus(Question.ACCUSE_STATUS_NO);
        question.setTopStatus(Question.TOP_STATUS_NO);
        question.setTopicTopStatus(Question.TOPIC_TOP_STATUS_NO);
        question.setEssenceStatus(Question.ESSENCE_STATUS_NO);
        question.setCreateMemberId(currentUserId);
        question.setOrganizationId(organizationId);
        question.setDeleteFlag(Question.DELETE_FLAG_NO);
        img.ifPresent(question::setImg);
        question.setType(Question.TYPE_QUESTION);
        question.setRootOrganizationId(rootOrganizationId);
        question.setStatus(Question.STATUS_ACTIVE);
        question.setAuditStatus(Question.AUDIT_STATUS_PASS);
        question.setRedBoatQuestionAuditStatus(Question.RED_BOAT_AUDIT_STATUS_WAIT);
        questionDao.insert(question);
        doUpdate(question, false);
        return question;
    }

    @Override
    public Question importArticle(String title, Optional<String> content, Optional<String> contentTxt,
                                  Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String currentUserId,
                                  String organizationId, String rootOrganizationId) {
        Question question = new Question();
        question.forInsert();
        question.setTitle(title);
        content.ifPresent(question::setContent);
        contentTxt.ifPresent(question::setContentTxt);
        question.setAttentionNum(0);
        question.setPraiseNum(0);
        question.setShareNum(0);
        question.setBrowseNum(0);
        question.setDiscussNum(0);
        question.setAccuseStatus(Question.ACCUSE_STATUS_NO);
        question.setTopStatus(Question.TOP_STATUS_NO);
        question.setTopicTopStatus(Question.TOPIC_TOP_STATUS_NO);
        question.setEssenceStatus(Question.ESSENCE_STATUS_NO);
        question.setCreateMemberId(currentUserId);
        question.setOrganizationId(organizationId);
        question.setDeleteFlag(Question.DELETE_FLAG_NO);
        img.ifPresent(question::setImg);
        question.setType(Question.TYPE_ARTICLE);
        question.setRootOrganizationId(rootOrganizationId);
        question.setStatus(Question.STATUS_ACTIVE);
        question.setAuditStatus(Question.AUDIT_STATUS_PASS);
        question.setRedBoatQuestionAuditStatus(Question.RED_BOAT_AUDIT_STATUS_WAIT);
        questionDao.insert(question);
        doUpdate(question, false);
        return question;
    }

    @Override
    public Question insertShare(String shareObjectId, int shareType, String shareTitle, String title,
                                Optional<String> content, Optional<String> contentTxt, Optional<String> img, List<QuestionTopic> topics,
                                String currentUserId, String organizationId, String rootOrganizationId, boolean auditOn) {

        Question question = new Question();
        question.forInsert();
        question.setShareType(shareType);
        question.setShareObjectId(shareObjectId);
        question.setShareTitle(shareTitle);
        question.setTitle(title);
        content.ifPresent(question::setContent);
        contentTxt.ifPresent(question::setContentTxt);
        question.setAttentionNum(0);
        question.setPraiseNum(0);
        question.setShareNum(0);
        question.setBrowseNum(0);
        question.setDiscussNum(0);
        question.setAccuseStatus(Question.ACCUSE_STATUS_NO);
        question.setTopStatus(Question.TOP_STATUS_NO);
        question.setTopicTopStatus(Question.TOPIC_TOP_STATUS_NO);
        question.setEssenceStatus(Question.ESSENCE_STATUS_NO);
        question.setCreateMemberId(currentUserId);
        question.setOrganizationId(organizationId);
        question.setDeleteFlag(Question.DELETE_FLAG_NO);
        img.ifPresent(question::setImg);
        question.setType(Question.TYPE_SHARE);
        question.setRootOrganizationId(rootOrganizationId);
        question.setAuditStatus(auditOn ? Question.AUDIT_STATUS_WAIT : Question.AUDIT_STATUS_PASS);
        question.setStatus(auditOn ? null : Question.STATUS_ACTIVE);
        question.setRedBoatQuestionAuditStatus(Question.RED_BOAT_AUDIT_STATUS_WAIT);
        questionDao.insert(question);

        // 话题关联
        questionTopicService.insert(question.getId(), topics);
        if (auditOn) { // 需要审核
            // 写入审核表
            String redStatus = cache.get("red_boat_audit" + "#" + "1", String.class);
            if (!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus)){
                auditService.insert(question.getId(), Audit.BUSINESS_TYPE_SHARE, Audit.RED_BOAT_AUDIT_STATUS, question.getId(), question.getTitle(),
                        Optional.empty(), currentUserId, organizationId, Optional.empty(), Optional.empty());
            }else {
                Audit insertResult =  auditService.insert(question.getId(), Audit.BUSINESS_TYPE_SHARE, question.getId(), question.getTitle(),
                        Optional.empty(), currentUserId, organizationId, Optional.empty());
                // 课程与专题分享自动审核通过 [CMU-2597]
                final Stream<Integer> courseSharTypes = Stream.of(1, 8);
                if (courseSharTypes.anyMatch(type-> type.equals(shareType))){
                    auditService.update(insertResult.getId(), insertResult.getCreateMemberId(),Audit.AUDIT_STATUS_PASS,
                            Optional.of("自动审核"),"1");
                    question.setAuditStatus(Audit.AUDIT_STATUS_PASS);
                }
            }
        } else { // 不需要审核
            updateContentForPass(question, false);
        }
        // 取消隐藏
        hidden(false, question.getId(), currentUserId);
        // 异步消息，计算积分、发送通知等
        // 问吧问题分享不增加积分
        // messageSender.send(MessageTypeContent.BAR_QUESTION_INSERT, MessageHeaderContent.ID, question.getId());
        return question;
    }

    private Question insert(String title, Optional<String> content, Optional<String> contentTxt, Optional<String> img,
                            List<QuestionTopic> topics, List<Attachment> attachments, String createMemberId, String organizationId,
                            String rootOrganizationId, Integer type, boolean auditOn, List<String> experts, boolean isOK) {
        Question question = new Question();
        question.forInsert();
        question.setTitle(title);
        content.ifPresent(question::setContent);
        contentTxt.ifPresent(question::setContentTxt);
        question.setAttentionNum(0);
        question.setPraiseNum(0);
        question.setShareNum(0);
        question.setBrowseNum(0);
        question.setDiscussNum(0);
        question.setAccuseStatus(Question.ACCUSE_STATUS_NO);
        question.setTopStatus(Question.TOP_STATUS_NO);
        question.setTopicTopStatus(Question.TOPIC_TOP_STATUS_NO);
        question.setEssenceStatus(Question.ESSENCE_STATUS_NO);
        question.setCreateMemberId(createMemberId);
        question.setOrganizationId(organizationId);
        question.setDeleteFlag(Question.DELETE_FLAG_NO);
        img.ifPresent(question::setImg);
        question.setType(type);
        question.setRootOrganizationId(rootOrganizationId);
        question.setAuditStatus(auditOn ? Question.AUDIT_STATUS_WAIT : Question.AUDIT_STATUS_PASS);
        question.setStatus(auditOn ? null : Question.STATUS_ACTIVE);
        question.setHidden(Question.HIDDEN_FLAG_NO);
        question.setRedBoatQuestionAuditStatus(Question.RED_BOAT_AUDIT_STATUS_WAIT);
        question.setTopicStatus(isOK ? Question.TOPIC_STATUS_Y : Question.TOPIC_STATUS_NULL);
        // 话题关联
        questionTopicService.insert(question.getId(), topics);
        //专家关联
        insertQuestionExpert(question.getId(), experts);
        // 附件
        attachmentService.insert(question.getId(), Attachment.BUSINESS_TYPE_QUESTION, attachments);

        questionDao.insert(question);

        if (auditOn) { // 需要审核
            // 写入审核表
            Integer auditBusinessType;
            auditBusinessType = Audit.BUSINESS_TYPE_QUESTION;// 审核表业务类型
            if (type == Question.TYPE_ARTICLE) {
                auditBusinessType = Audit.BUSINESS_TYPE_ARTICLE;
            } else if (type == Question.TYPE_SHARE) {
                auditBusinessType = Audit.BUSINESS_TYPE_SHARE;
            }
            String redStatus = cache.get("red_boat_audit" + "#" + "1", String.class);
            if (!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus)){
                auditService.insert(question.getId(), auditBusinessType, Audit.RED_BOAT_AUDIT_STATUS, question.getId(), question.getTitle(),
                        Optional.empty(), createMemberId, organizationId, Optional.empty(), Optional.empty());
            }else {
                auditService.insert(question.getId(), auditBusinessType, question.getId(), question.getTitle(),
                        Optional.empty(), createMemberId, organizationId, Optional.empty());
            }

        } else { // 不需要审核
            updateContentForPass(question, false);
        }
        // 异步消息，计算积分、发送通知等
        messageSender.send(MessageTypeContent.BAR_QUESTION_INSERT, MessageHeaderContent.ID, question.getId());

        if (!isOK) {
            // 智能标签生成
            messageSender.send(MessageTypeContent.INTELLECT_TOPIC,MessageHeaderContent.ID,question.getId());
        }
        return question;
    }

    private Question update(String id, Optional<String> title, Optional<String> content, Optional<String> contentTxt,
                            Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String createMemberId,
                            String organizationId, String rootOrganizationId, boolean auditOn,List<String> experts) {

        Question question = questionDao.get(id);

        //防止越权操作，非本人不能修改此文章可问题
        if(question != null && !createMemberId.equals(question.getCreateMemberId())){
            throw new UnprocessableException(ErrorCode.QuestionUpdateError);
        }

        List<QuestionTopic> list = questionTopicDao.fetch(QUESTION_TOPIC.QUESTION_ID.eq(question.getId()));
        //问题被举报，无法进行编辑
//        accuseDao.fetchOne(ACCUSE.BUSINESS_ID.eq(question.getId())).ifPresent(item -> {
//            if (item.getAuditStatus() == Accuse.AUDIT_STATUS_WAIT) {
//                throw new UnprocessableException(ErrorCode.QuestionHasBeenReport);
//            }
//        });
        String redStatus = cache.get("red_boat_audit" + "#" + "1", String.class);

        Question createQuestion = new Question();
        createQuestion.forInsert();
        title.ifPresent(createQuestion::setTitle);
        content.ifPresent(createQuestion::setContent);
        contentTxt.ifPresent(createQuestion::setContentTxt);
        createQuestion.setAttentionNum(0);
        createQuestion.setPraiseNum(0);
        createQuestion.setShareNum(0);
        createQuestion.setBrowseNum(0);
        createQuestion.setDiscussNum(0);
        createQuestion.setAccuseStatus(Question.ACCUSE_STATUS_NO);
        createQuestion.setTopStatus(Question.TOP_STATUS_NO);
        createQuestion.setTopicTopStatus(Question.TOPIC_TOP_STATUS_NO);
        createQuestion.setEssenceStatus(Question.ESSENCE_STATUS_NO);
        createQuestion.setCreateMemberId(createMemberId);
        createQuestion.setOrganizationId(organizationId);
        createQuestion.setDeleteFlag(Question.DELETE_FLAG_NO);
        createQuestion.setRedBoatQuestionAuditStatus(Question.RED_BOAT_AUDIT_STATUS_WAIT);
        img.ifPresent(question::setImg);
        createQuestion.setType(question.getType());
        createQuestion.setRootOrganizationId(rootOrganizationId);
        createQuestion.setHidden(Question.HIDDEN_FLAG_NO);
        // 专家关联
        insertQuestionExpert(question.getId(), experts);

        // 审核状态
        Integer auditStatus = question.getAuditStatus();
        // 问题类型
        Integer type = question.getType();
        String auditId = "";

        if (auditOn) { // 是否需要审核
            if (auditStatus.equals(Question.AUDIT_STATUS_WAIT)) {
                title.ifPresent(question::setTitle);
                if(!content.isPresent()){
                    question.setContent(null);
                }else{
                    content.ifPresent(question::setContent);
                }
                if(!contentTxt.isPresent()){
                    question.setContentTxt(null);
                }else{
                    contentTxt.ifPresent(question::setContentTxt);
                }
                if(!img.isPresent()){
                    question.setImg(null);
                }else{
                    img.ifPresent(question::setImg);
                }

                // 编辑状态
                question.setIsEdit(Question.IS_EDIT);
                // 待审核状态
                question.setAuditStatus(Question.AUDIT_STATUS_WAIT);
                // 问题状态
                question.setStatus(Question.STATUS_ACTIVE);

                // 查询审核记录,变更审核记录内容
                List<Audit> audits = auditService.getAuditByBusinessId(question.getId());
                if (audits != null && audits.size() > 0) {
                    audits.stream().forEach(item -> {
                        item.setContent(title.get());
                        item.setRedBoatBusinessType(!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus) ? Audit.RED_BOAT_BUSINESS_TYPE_TWO : Audit.RED_BOAT_BUSINESS_TYPE_ONE);
                        auditDao.update(item);
                    });
                    auditId = audits.get(0).getId();
                }
                questionDao.update(question);

                messageSender.send(MessageTypeContent.BAR_QUESTION_UPDATE, MessageHeaderContent.ID, id);

                if (!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus)){
                    messageSender.send(com.zxy.product.askbar.content.MessageTypeContent.RED_SHIP_MATERIAL_SPLIT,
                            com.zxy.product.askbar.content.MessageHeaderContent.BUSINESS_TYPE, String.valueOf(question.getType()),
                            com.zxy.product.askbar.content.MessageHeaderContent.BUSINESS_ID, question.getId(),
                            com.zxy.product.askbar.content.MessageHeaderContent.AUDIT_ID, auditId);
                }

            } else if (auditStatus.equals(Question.AUDIT_STATUS_REFUSE)) {
                // 审核拒绝
                // 待审核状态
                createQuestion.setAuditStatus(Question.AUDIT_STATUS_WAIT);
                // 父类ID
                createQuestion.setParentId(question.getId());
                // 问题状态
                createQuestion.setStatus(Question.STATUS_ACTIVE);
                createQuestion.setIsEdit(Question.IS_EDIT);

                if(!img.isPresent()){
                    question.setImg(null);
                }else{
                    img.ifPresent(question::setImg);
                }
                // 话题关联
                if(list != null && list.size() > 0){
                    questionTopicService.insert(createQuestion.getId(), list);
                }

                questionDao.insert(createQuestion);

                Integer auditBusinessType = Audit.BUSINESS_TYPE_QUESTION;// 审核表业务类型
                if (type == Question.TYPE_ARTICLE)
                    auditBusinessType = Audit.BUSINESS_TYPE_ARTICLE;
                else if (type == Question.TYPE_SHARE)
                    auditBusinessType = Audit.BUSINESS_TYPE_SHARE;
                if (!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus)){
                    auditService.insert(createQuestion.getId(), auditBusinessType, Audit.RED_BOAT_AUDIT_STATUS, createQuestion.getId(), createQuestion.getTitle(),
                            Optional.empty(), createMemberId, organizationId, Optional.empty(), Optional.empty());
                }else {
                    auditService.insert(createQuestion.getId(), auditBusinessType, createQuestion.getId(),
                            createQuestion.getTitle(), Optional.empty(), createMemberId, organizationId, Optional.empty());
                }
                // 异步消息，计算积分、发送通知等
                messageSender.send(MessageTypeContent.BAR_QUESTION_INSERT, MessageHeaderContent.ID,
                        createQuestion.getId());

            } else if (auditStatus.equals(Question.AUDIT_STATUS_PASS)) {

                // 父类ID
                createQuestion.setParentId(question.getId());
                // 审核等待
                createQuestion.setAuditStatus(Question.AUDIT_STATUS_WAIT);
                // 问题状态
                createQuestion.setStatus(Question.STATUS_ACTIVE);
                createQuestion.setIsEdit(Question.IS_EDIT);
                if(!img.isPresent()){
                    question.setImg(null);
                }else{
                    img.ifPresent(question::setImg);
                }
                // 话题关联
                if(list != null && list.size() > 0){
                    questionTopicService.insert(createQuestion.getId(), list);
                }
                // questionTopicService.insert(createQuestion.getId(), topics);
                questionDao.insert(createQuestion);

                // 写入审核表
                Integer auditBusinessType = Audit.BUSINESS_TYPE_QUESTION;// 审核表业务类型
                if (type == Question.TYPE_ARTICLE)
                    auditBusinessType = Audit.BUSINESS_TYPE_ARTICLE;
                else if (type == Question.TYPE_SHARE)
                    auditBusinessType = Audit.BUSINESS_TYPE_SHARE;
                if (!StringUtils.isEmpty(redStatus) && Audit.ONE.equals(redStatus)){
                    auditService.insert(createQuestion.getId(), auditBusinessType, Audit.RED_BOAT_AUDIT_STATUS, createQuestion.getId(), createQuestion.getTitle(),
                            Optional.empty(), createMemberId, organizationId, Optional.empty(), Optional.empty());
                }else {
                    auditService.insert(createQuestion.getId(), auditBusinessType, createQuestion.getId(),
                            createQuestion.getTitle(), Optional.empty(), createMemberId, organizationId, Optional.empty());
                }

                // 异步消息，计算积分、发送通知等
                messageSender.send(MessageTypeContent.BAR_QUESTION_INSERT, MessageHeaderContent.ID,
                        createQuestion.getId());
            }

        } else {
            title.ifPresent(question::setTitle);
            if(!content.isPresent()){
                question.setContent(null);
            }else{
                content.ifPresent(question::setContent);
            }
            if(!contentTxt.isPresent()){
                question.setContentTxt(null);
            }else{
                contentTxt.ifPresent(question::setContentTxt);
            }
            if(!img.isPresent()){
                question.setImg(null);
            }else{
                img.ifPresent(question::setImg);
            }
            // 编辑状态
            question.setIsEdit(Question.IS_EDIT);
            // 更新问题状态和审核状态
            question.setStatus(Question.STATUS_ACTIVE);
            question.setAuditStatus(Question.AUDIT_STATUS_PASS);
            questionDao.update(question);
            //给专家发送通知
            messageSender.send(MessageTypeContent.BAR_QUESTION_EXPERTS, MessageHeaderContent.ID, question.getId());

        }
        // 检测是否产生新的提问。
        if (StringUtils.isEmpty(createQuestion.getParentId())) {
            return question;
        } else {
            return createQuestion;
        }
    }


    @Override
    public PagedResult<Question> waitDiscuss4Me(int start, int pageSize, String expertId, String currentMemberId,
                                                String rootOrganizationId) {
        return questionDao.execute(e -> {

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                            QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                            QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
                            QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE, QUESTION.SHARE_TYPE,
                            QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME, QUESTION.LAST_MODIFY_TIME,
                            QUESTION.CREATE_MEMBER_ID, QUESTION.TOPIC_STATUS)
                    .add(ATTENTION.ID).add(EXPERT.ID)
                    .add(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.HEAD_PORTRAIT_PATH).end());

            SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(QUESTION.ID.countDistinct()).end());

            // 专家擅长话题
            List<ExpertTopic> topics = expertTopicDao.fetch(
                    EXPERT_TOPIC.EXPERT_ID.eq(expertId).and(EXPERT_TOPIC.STATUS.eq(ExpertTopic.STATUS_EFFECTIVE)));

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(QUESTION).leftJoin(MEMBER)
                        .on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
                        .on(EXPERT.MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)
                                .and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
                                .and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
                        .leftJoin(ATTENTION)
                        .on(ATTENTION.BUSINESS_ID.eq(QUESTION.ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentMemberId)))

                        .where(QUESTION.TYPE.eq(Question.TYPE_QUESTION))
                        .and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId)) // 根组织
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)) // 未删除
                        .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE)) // 活动问题
                        .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)) // 审核通过
                        .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO)) // 未被举报
                        .and(QUESTION.DISCUSS_NUM.eq(0)) // 未被讨论
                        .and(DSL.exists(e.select(QUESTION_TOPIC.ID).from(QUESTION_TOPIC)

                                .where(QUESTION_TOPIC.QUESTION_ID.eq(QUESTION.ID)).and(QUESTION_TOPIC.TOPIC_ID.in(
                                        topics.stream().map(ExpertTopic::getTopicId).collect(toList())))));

                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            List<Question> questions = stepFunc.apply(selectListField).orderBy(QUESTION.CREATE_TIME.desc())
                    .limit(start, pageSize).fetch(r -> {
                        Question question = r.into(QUESTION).into(Question.class);
                        Attention attention = r.into(ATTENTION).into(Attention.class);
                        Member member = r.into(MEMBER).into(Member.class);
                        member.setIsExpert(!StringUtils.isEmpty(r.getValue(EXPERT.ID)));
                        question.setCreateMember(member);
                        question.setAttention(attention);
                        return question;
                    });
            handlerQuestionTopic(questions, currentMemberId);
            return PagedResult.create(count, questions);

        });
    }

    @Override
    public PagedResult<Question> findWaitReply(int start, int pageSize, String topicId, String currentUserId,
                                               String rootOrganizationId) {
        Field<String> expertId = EXPERT.ID.as("expert_id_field");

        return questionDao.execute(e -> {
            SelectSelectStep<Record> selectFields = e.select(Fields.start()

                    .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                            QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                            QUESTION.ESSENCE_STATUS, QUESTION.TOP_STATUS, QUESTION.TOPIC_TOP_STATUS,
                            QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
                            QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE, QUESTION.SHARE_TYPE,
                            QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME, QUESTION.LAST_MODIFY_TIME,
                            QUESTION.CREATE_MEMBER_ID, QUESTION.TOPIC_STATUS)
                    .add(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.HEAD_PORTRAIT_PATH).add(expertId)

                    .add(ATTENTION.ID).end());

            SelectSelectStep<Record> countFields = e.select(Fields.start().add(QUESTION.ID.countDistinct()).end());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> topicFunc = a -> a.from(QUESTION)
                    .leftJoin(QUESTION_TOPIC).on(QUESTION_TOPIC.QUESTION_ID.eq(QUESTION.ID)).leftJoin(MEMBER)
                    .on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
                    .on(EXPERT.MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)
                            .and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
                            .and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
                    .leftJoin(ATTENTION)
                    .on(ATTENTION.BUSINESS_ID.eq(QUESTION.ID).and(ATTENTION.CREATE_MEMBER_ID.eq(currentUserId)))
                    .where(QUESTION.DISCUSS_NUM.eq(0).and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId))
                            .and(QUESTION_TOPIC.TOPIC_ID.eq(topicId))
                            .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))
                            .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))
                            .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
                            .and(QUESTION.TYPE.eq(Question.TYPE_QUESTION)));

            int count = topicFunc.apply(countFields).fetchOne().getValue(0, Integer.class);

            List<Question> questions = topicFunc.apply(selectFields)
                    .orderBy(QUESTION.TOPIC_TOP_STATUS.desc(),QUESTION.TOPIC_TOP_TIME.desc(),QUESTION.CREATE_TIME.desc())
                    .limit(start, pageSize).fetch(r -> {
                        Question question = r.into(QUESTION).into(Question.class);
                        Member member = r.into(MEMBER).into(Member.class);
                        member.setIsExpert(!StringUtils.isEmpty(r.getValue(expertId)));
                        question.setCreateMember(member);
                        Attention attention = r.into(ATTENTION).into(Attention.class);
                        question.setAttention(attention);
                        return question;
                    });

            handlerQuestionTopic(questions, currentUserId);
            return PagedResult.create(count, questions);
        });
    }

    @Override
    public List<Question> findRelativeQuestions(String topicIds, Integer type, Optional<String> questionId,
                                                Optional<Integer> limit, String rootOrganizationId) {
        return questionDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e
                    .select(Fields.start().add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE).end());

            List<Condition> conditions = Stream.of(questionId.map(QUESTION.ID::ne)).filter(Optional::isPresent)
                    .map(Optional::get).collect(toList());
            // 话题
            List<String> topics = Arrays.asList(topicIds.split(","));
            List<String> questionIds = questionTopicDao.execute(qt -> qt.selectDistinct(QUESTION_TOPIC.QUESTION_ID)
                    .from(QUESTION_TOPIC).leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_TOPIC.QUESTION_ID))
                    .where(conditions).and(QUESTION_TOPIC.TOPIC_ID.in(topics)).and(QUESTION.TYPE.eq(type))

                    .and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId))// 根组织
                    .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)) // 未删除
                    .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE))// 激活的
                    .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))// 审核通过
                    .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
                    .orderBy(QUESTION.ATTENTION_NUM.desc(), QUESTION.CREATE_TIME.desc())
                    .limit(0, limit.orElse(Question.RELATIVE_QUESTIONS_LIMIT_DEFAULT)))// 未被举报
                    .fetch(QUESTION_TOPIC.QUESTION_ID);

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(QUESTION).where(QUESTION.ID.in(questionIds));
                return select;
            };

            List<Question> list = stepFunc.apply(selectListField).fetch(r -> {
                Question question = r.into(QUESTION).into(Question.class);
                return question;
            });

            return list;
        });
    }

    @Override
    public List<Question> findHotQuestions(String topicIds, Integer type, Optional<Integer> limit,
                                           String rootOrganizationId) {
        return questionDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e
                    .select(Fields.start().add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE).end());

            // 话题
            List<String> topics = Arrays.asList(topicIds.split(","));
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(QUESTION).where(QUESTION.TYPE.eq(type))

                        .and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId))// 根组织
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))// 未删除
                        .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE))// 激活的
                        .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))// 审核通过
                        .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))// 未被举报
                        .and(QUESTION.HIDDEN.eq(Question.HIDDEN_FLAG_NO))// 未被隐藏
                        .and(DSL.exists(e.select(QUESTION_TOPIC.ID).from(QUESTION_TOPIC)
                                .where(QUESTION_TOPIC.QUESTION_ID.eq(QUESTION.ID))

                                .and(QUESTION_TOPIC.TOPIC_ID.in(topics))));
                return select;
            };

            List<Question> list = stepFunc.apply(selectListField)
                    .orderBy(QUESTION.DISCUSS_NUM.desc(), QUESTION.CREATE_TIME.desc())

                    .limit(0, limit.orElse(Question.HOT_QUESTIONS_LIMIT_DEFAULT)).fetch(r -> {
                        Question question = r.into(QUESTION).into(Question.class);
                        return question;
                    });

            return list;
        });
    }

    @Override
    public PagedResult<Question> findQuestion(String memberId, Integer page, Integer pageSize, Optional<String> title,
                                              Optional<Integer> type, Optional<Integer> status, Optional<Integer> essenceStatus,
                                              Optional<Integer> auditStatus, Optional<String> createMemberId, Optional<Long> createTimeStart,
                                              Optional<Long> createTimeEnd, List<String> organizationIds, Optional<String> createMemberOrganizationId,
                                              Optional<Integer> topStatus, Optional<Integer> redBoatQuestionAuditStatus, Optional<Integer> redBoatQuestionToReview,
                                              boolean redStatus, Optional<Integer> topicStatus, Optional<String> discussOrder, Optional<Integer> contain,
                                              Optional<Integer> hidden, Optional<Integer> deleteFlag) {
        long now = System.currentTimeMillis();
        return questionDao.execute(e -> {
            List<Condition> conditions = Stream.of(title.map(QUESTION.TITLE::contains), type.map(QUESTION.TYPE::eq),
                    status.map(QUESTION.STATUS::eq), essenceStatus.map(QUESTION.ESSENCE_STATUS::eq),
                    topStatus.map(QUESTION.TOP_STATUS::eq),
                    auditStatus.map(QUESTION.AUDIT_STATUS::eq), createMemberId.map(QUESTION.CREATE_MEMBER_ID::eq),
                    createTimeStart.map(QUESTION.CREATE_TIME::ge), createTimeEnd.map(QUESTION.CREATE_TIME::lt),
                    redBoatQuestionAuditStatus.map(rs -> {
                        // 超时，查询红船审核状态为审核中 且 送审时间超过0.5h
                        if (rs == Question.RED_BOAT_AUDIT_STATUS_OVERTIME || rs == Question.RED_BOAT_AUDIT_STATUS_PASS) {
                            return (QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS.eq(Question.RED_BOAT_AUDIT_STATUS_PASS)
                                    .and((QUESTION.CREATE_TIME.plus(hours)).lt(now)))
                                    .or(QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS.eq(Question.RED_BOAT_AUDIT_STATUS_OVERTIME));
                        }
                        return QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS.eq(rs);
                    }),
                    redBoatQuestionToReview.map(QUESTION.RED_BOAT_QUESTION_TO_REVIEW::eq),
                    topicStatus.map(QUESTION.TOPIC_STATUS::eq),
                    Optional.of(redStatus).map(w -> QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)
                         /*   .or(QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS.eq(Question.RED_BOAT_AUDIT_STATUS_PASS))
                            .or(QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS.eq(Question.RED_BOAT_AUDIT_STATUS_OVERTIME))*/
                            .or(QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS.in(Question.RED_BOAT_AUDIT_STATUS_PASS,Question.RED_BOAT_AUDIT_STATUS_OVERTIME,Question.RED_BOAT_AUDIT_STATUS_REFUSE))),
                    //.and(AUDIT.BUSINESS_TYPE.isNull().or(AUDIT.BUSINESS_TYPE.notIn(6, 7, 8)))
                    Optional.of(organizationIds).map(ORGANIZATION.ID::in),
                    // 处理提交人归属部门筛选，支持contain参数
                    createMemberOrganizationId.map(orgId -> {
                        Integer containValue = contain.orElse(0); // 默认不包含子级
                        if (containValue == 1) {
                            // 包含子级：查询指定组织及其所有子组织的成员
                            return MEMBER.ORGANIZATION_ID.in(
                                e.select(ORGANIZATION_DETAIL.SUB)
                                 .from(ORGANIZATION_DETAIL)
                                 .where(ORGANIZATION_DETAIL.ROOT.eq(orgId))
                            );
                        } else {
                            // 不包含子级：只查询指定组织的成员
                            return MEMBER.ORGANIZATION_ID.eq(orgId);
                        }
                    }),
                    // 隐藏状态筛选
                    Optional.of(hidden.orElse(0)).map(QUESTION.HIDDEN::eq),
                    // 删除状态筛选
                    Optional.of(deleteFlag.orElse(0)).map(QUESTION.DELETE_FLAG::eq),
                    Optional.of(QUESTION.ACCUSE_STATUS.notEqual(Question.ACCUSE_STATUS_YES))).filter(Optional::isPresent)
                    .map(Optional::get).collect(toList());

            // 为提交人组织创建别名，避免与问题组织冲突
            com.zxy.product.askbar.jooq.tables.Organization memberOrganization = ORGANIZATION.as("memberOrganization");

            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                            QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                            QUESTION.TOP_STATUS,QUESTION.TOP_TIME,
                            QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM, QUESTION.PRAISE_NUM,
                            QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE, QUESTION.SHARE_TYPE,
                            QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME, QUESTION.LAST_MODIFY_TIME,
                            QUESTION.CREATE_MEMBER_ID, QUESTION.ORGANIZATION_ID,QUESTION.HIDDEN,
                            QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS, QUESTION.RED_BOAT_QUESTION_TO_REVIEW,
                            QUESTION.RED_BOAT_CREATE_TIME, QUESTION.TOPIC_STATUS
                    )
                    .add(MEMBER.ID).add(MEMBER.FULL_NAME).add(MEMBER.ORGANIZATION_ID)
                    .add(memberOrganization.NAME.as("memberOrganizationName")) // 添加提交人组织名称
//                    .add(AUDIT.RED_BOAT_TO_REVIEW)
                    .end());
            SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(QUESTION.ID.count()).end());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(QUESTION)
//                        .leftJoin(AUDIT).on(AUDIT.BUSINESS_ID.eq(QUESTION.ID))
                        .leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID))
                        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(QUESTION.ORGANIZATION_ID))
                        .leftJoin(memberOrganization).on(memberOrganization.ID.eq(MEMBER.ORGANIZATION_ID)) // JOIN 提交人组织
                        .where(conditions);
                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> recordStep = stepFunc.apply(selectListField);

            // 处理排序逻辑
            if (discussOrder.isPresent() && discussOrder.get().equals("asc")) {
                recordStep.orderBy(QUESTION.DISCUSS_NUM.asc(), QUESTION.CREATE_TIME.desc());
            } else if (discussOrder.isPresent() && discussOrder.get().equals("desc")) {
                recordStep.orderBy(QUESTION.DISCUSS_NUM.desc(), QUESTION.CREATE_TIME.desc());
            } else {
                // 默认按创建时间降序排序
                recordStep.orderBy(QUESTION.CREATE_TIME.desc());
            }

            List<Question> questions = recordStep
                    .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                        Question question = r.into(QUESTION).into(Question.class);
                        Member member = r.into(MEMBER).into(Member.class);

                        // 设置提交人的组织信息
                        String memberOrganizationName = r.getValue("memberOrganizationName", String.class);
                        if (memberOrganizationName != null) {
                            member.setOrganizationName(memberOrganizationName);
                        }

                        question.setCreateMember(member);
//                        question.setRedBoatQuestionToReview(r.getValue(AUDIT.RED_BOAT_TO_REVIEW));
                        return question;
                    });

            for (Question question : questions) {
                // 超时，查询红船审核状态为审核中 且 送审时间超过0.5h
                if (question != null
                        && question.getRedBoatQuestionAuditStatus() == Question.RED_BOAT_AUDIT_STATUS_PASS
                        && (now - question.getCreateTime() >= hours)) {
                    question.setRedBoatQuestionAuditStatus(Question.RED_BOAT_AUDIT_STATUS_OVERTIME);
                }
            }

            return PagedResult.create(count, questions);

        });
    }

    @Override
    public PagedResult<Audit> findQuestionAudit(String memberId, Integer page, Integer pageSize,
                                                Optional<String> content, Optional<Integer> businessType, Optional<Integer> auditStatus,
                                                Optional<String> createMemberId, Optional<Long> createTimeStart, Optional<Long> createTimeEnd,
                                                Optional<Long> auditTimeStart, Optional<Long> auditTimeEnd, List<String> organizationIds,
                                                boolean redStatus, Optional<Integer> reviewStatus) {

//        if (redStatus && auditStatus.isPresent() && auditStatus.get() != Audit.AUDIT_STATUS_WAIT){
//            if (auditStatus.get() == Audit.AUDIT_STATUS_PASS){// 红船审核开启的时候，审核条件筛选的是相反的值
//                auditStatus = Optional.of(Audit.AUDIT_STATUS_REFUSE);
//            }else {
//                auditStatus = Optional.of(Audit.AUDIT_STATUS_PASS);
//            }
//        }
//        Optional<Integer> finalAuditStatus = auditStatus;

        redStatus = true; //无论红船开不开起都可以查询红船都数据
        if (businessType.isPresent() && businessType.get() == 2){//举报类型
            redStatus = false;
        }

        boolean finalRedStatus = redStatus;
        return questionDao.execute(e -> {
            List<Condition> conditions = Stream.of(content.map(AUDIT.CONTENT::contains), businessType.map(t -> {
                                if (t == 1) {
                                    return AUDIT.BUSINESS_TYPE.le(5).and(AUDIT.RED_BOAT_BUSINESS_TYPE.eq(Audit.RED_BOAT_BUSINESS_TYPE_ONE));
                                }else if (t == 2){
                                    return AUDIT.BUSINESS_TYPE.ge(6);
                                }else if (t == 3){
                                    return AUDIT.RED_BOAT_BUSINESS_TYPE.eq(Audit.RED_BOAT_BUSINESS_TYPE_TWO)
                                            .and(AUDIT.BUSINESS_TYPE.le(5));
                                }else {
                                    return AUDIT.BUSINESS_TYPE.le(5);
//                            return AUDIT.BUSINESS_TYPE.gt(5).and(AUDIT.RED_BOAT_BUSINESS_TYPE.eq(1));
                                }
                            }),
                            auditStatus.map(w -> AUDIT.AUDIT_STATUS.eq(w).and(AUDIT.BUSINESS_TYPE.le(5))),
                            reviewStatus.map(w -> AUDIT.RED_BOAT_TO_REVIEW.eq(w).and(AUDIT.BUSINESS_TYPE.le(5))),
                            createMemberId.map(AUDIT.CREATE_MEMBER_ID::eq),
                            createTimeStart.map(AUDIT.CREATE_TIME::ge), createTimeEnd.map(AUDIT.CREATE_TIME::lt),
                            auditTimeStart.map(AUDIT.AUDIT_TIME::ge), auditTimeEnd.map(AUDIT.AUDIT_TIME::lt),
                            Optional.of(finalRedStatus).map(w -> w ?
                                    (AUDIT.BUSINESS_TYPE.le(Audit.BUSINESS_TYPE_REPLY).and(AUDIT.RED_BOAT_BUSINESS_TYPE.eq(Audit.RED_BOAT_BUSINESS_TYPE_ONE)))
                                    .or(AUDIT.BUSINESS_TYPE.le(Audit.BUSINESS_TYPE_REPLY).and(AUDIT.RED_BOAT_BUSINESS_TYPE.eq(Audit.RED_BOAT_BUSINESS_TYPE_TWO)).and( AUDIT.RED_BOAT_AUDIT_STATUS.eq(Audit.RED_BOAT_AUDIT_STATUS_N)))
                                    :
                                    AUDIT.RED_BOAT_BUSINESS_TYPE.eq(Audit.RED_BOAT_BUSINESS_TYPE_ONE)),
                            Optional.of(organizationIds).map(AUDIT.ORGANIZATION_ID::in)).filter(Optional::isPresent)
                    .map(Optional::get).collect(toList());

            com.zxy.product.askbar.jooq.tables.Member auditMemberTable = MEMBER.as("auditMemberTable");
            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start().add(AUDIT).add(MEMBER.ID, MEMBER.FULL_NAME).add(auditMemberTable.FULL_NAME).end());
            SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(AUDIT.ID.count()).end());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(AUDIT)
                        .leftJoin(MEMBER).on(MEMBER.ID.eq(AUDIT.CREATE_MEMBER_ID))
                        .leftJoin(auditMemberTable)
                        .on(auditMemberTable.ID.eq(AUDIT.AUDIT_MEMBER_ID))
                        .where(conditions);
                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> recordStep = stepFunc.apply(selectListField);

            List<Audit> items = recordStep.orderBy(AUDIT.AUDIT_STATUS.asc(), AUDIT.CREATE_TIME.desc())
                    .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                        Audit audit = r.into(AUDIT).into(Audit.class);
                        audit.setAuditMember(r.getValue(auditMemberTable.FULL_NAME));

                        Member commitMember = r.into(MEMBER).into(Member.class);
                        audit.setCommitMember(commitMember);

                        return audit;
                    });

            return PagedResult.create(count, items);
        });
    }

    @Override
    public List<Question> findReleated(String name, String rootOrganizationId, Optional<Integer> limit) {
        return questionDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e
                    .select(Fields.start().add(QUESTION.ID, QUESTION.TITLE, QUESTION.DISCUSS_NUM).end());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(QUESTION).where(QUESTION.TYPE.eq(Question.TYPE_QUESTION))

                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))// 未删除
                        .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE))// 激活的
                        .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))// 审核通过
                        .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))// 未被举报
                        .and(QUESTION.DISCUSS_NUM.gt(0)).and(QUESTION.TITLE.contains(name))

                        .and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId));// 集团内
                return select;
            };

            List<Question> list = stepFunc.apply(selectListField)
                    .orderBy(QUESTION.DISCUSS_NUM.desc(), QUESTION.CREATE_TIME.desc())
                    .limit(0, limit.orElse(Question.RELATIED_QUESTIONS_LIMIT_DEFAULT)).fetch(r -> {
                        Question question = r.into(QUESTION).into(Question.class);
                        return question;
                    });

            return list;
        });
    }

    @Override
    public PagedResult<Question> getArchives(Integer page, Integer pageSize, String currentMemberId) {
        SelectConditionStep<Record> step = questionDao.execute(e -> e.selectDistinct(Fields.start().add(QUESTION).end())
                .from(QUESTION).where(QUESTION.CREATE_MEMBER_ID.eq(currentMemberId))
                .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)));

        Integer count = questionDao.execute(e -> e.fetchCount(step));

        List<Question> questions = step.orderBy(QUESTION.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize)
                .fetchInto(Question.class);

        return PagedResult.create(count, questions);
    }

    @Override
    public List<Question> getArchivesAll(String currentMemberId) {
        return questionDao.execute(e -> e
                .select(Fields.start()
                        .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                                QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                                QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM,
                                QUESTION.PRAISE_NUM, QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE,
                                QUESTION.SHARE_TYPE, QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME,
                                QUESTION.LAST_MODIFY_TIME, QUESTION.CREATE_MEMBER_ID)
                        .end())
                .from(QUESTION)
                .where(QUESTION.CREATE_MEMBER_ID.eq(currentMemberId)
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)))
                .orderBy(QUESTION.CREATE_TIME.desc())).fetchInto(Question.class);
    }

    @Override
    public PagedResult<Question> findQuestion(int page, int pageSize, Optional<String> name,
                                              String rootOrganizationId) {
        return findContent(page, pageSize, name, rootOrganizationId, Question.TYPE_QUESTION);
    }

    @Override
    public PagedResult<Question> findArticle(int page, int pageSize, Optional<String> name, String rootOrganizationId) {
        return findContent(page, pageSize, name, rootOrganizationId, Question.TYPE_ARTICLE);
    }

    /**
     * 全局搜索
     */
    private PagedResult<Question> findContent(int page, int pageSize, Optional<String> name, String rootOrganizationId,
                                              Integer type) {
        return questionDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e.select(Fields.start()
                    .add(QUESTION.ID, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT, QUESTION.CREATE_TIME,
                            QUESTION.CREATE_MEMBER_ID)
                    .add(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.HEAD_PORTRAIT_PATH).end());

            List<Condition> conditionList = Stream
                    .of(name.map(n -> QUESTION.TITLE.startsWith(n).or(QUESTION.CONTENT_TXT.startsWith(n))))

                    .filter(Optional::isPresent).map(Optional::get).collect(toList());

            SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(QUESTION.ID.countDistinct()).end());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(QUESTION).leftJoin(MEMBER)
                        .on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).where(conditionList).and(QUESTION.TYPE.eq(type))
                        .and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId)) // 根组织
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)) // 未删除
                        // .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE)) //
                        // 活动问题
                        .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)) // 审核通过
                        .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO)); // 未被举报

                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            List<Question> questions = stepFunc.apply(selectListField).orderBy(QUESTION.CREATE_TIME.desc())
                    .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                        Question question = r.into(QUESTION).into(Question.class);
                        Member member = r.into(MEMBER).into(Member.class);
                        question.setCreateMember(member);
                        return question;
                    });

            return PagedResult.create(count, questions);
        });
    }

    /**
     * 内容（问题、文章、分享）无需审核/审核通过的一系列逻辑处理
     * <p>
     * 1、更新关联话题的问题数/文章数[问题、文章]
     * </p>
     * <p>
     * 2、如果发布人是专家则更新专家的文章数[文章]
     * </p>
     * <p>
     * 3、写入动态表[问题、文章、分享]
     * </p>
     * <p>
     * 4、发送通过的消息由异步处理相关消息通知和积分
     * </p>
     */
    private void updateContentForPass(Question question, boolean isAudit) {

        doUpdate(question, isAudit);
        if(StringUtils.isEmpty(question.getParentId())){
            // 问题通过的消息，再由其监听处理后续业务：发送消息邀请专家回答、统计标签使用次数等
            messageSender.send(MessageTypeContent.BAR_QUESTION_PASS, MessageHeaderContent.ID, question.getId());
        }else{
            Optional<Question> oldQue = questionDao.fetchOneWithOptional(Stream.of(Optional.of(question.getParentId())
                    .map(QUESTION.ID::eq)));
            if(oldQue.isPresent()){
                // 问题通过的消息，再由其监听处理后续业务：发送消息邀请专家回答、统计标签使用次数等
                messageSender.send(MessageTypeContent.BAR_QUESTION_PASS, MessageHeaderContent.ID, oldQue.get().getId());
            }

        }
    }

    private void doUpdate(Question question, boolean isAudit) {
        if(StringUtils.isEmpty(question.getParentId())){
            if (!Question.AUDIT_STATUS_PASS.equals(question.getAuditStatus())) {
                // 更新问题状态和审核状态
                question.setStatus(Question.STATUS_ACTIVE);
                question.setAuditStatus(Question.AUDIT_STATUS_PASS);
                questionDao.update(question);
            }
            // 写入动态表
            trendService.insert(question.getType(), question.getId(), question.getId(), Optional.empty(),
                    question.getCreateMemberId(), question.getRootOrganizationId(), question.getCreateTime());
        } else {
            Optional<Question> oldQue = questionDao.fetchOneWithOptional(Stream.of(Optional.of(question.getParentId())
                    .map(QUESTION.ID::eq)));
            if(oldQue.isPresent()){
                if (!Question.AUDIT_STATUS_PASS.equals(question.getAuditStatus())) {
                    // 更新问题状态和审核状态
                    oldQue.get().setStatus(Question.STATUS_ACTIVE);
                    oldQue.get().setAuditStatus(Question.AUDIT_STATUS_PASS);
                    questionDao.update( oldQue.get());
                }
                // 写入动态表
                trendService.insert( oldQue.get().getType(),  oldQue.get().getId(),  oldQue.get().getId(), Optional.empty(),
                        oldQue.get().getCreateMemberId(),  oldQue.get().getRootOrganizationId(),  oldQue.get().getCreateTime());
            }
        }


        if (isAudit) {
            if(StringUtils.isEmpty(question.getParentId())){
                updateTopicReleatedCount(question);
            }else {
                Optional<Question> oldQue = questionDao.fetchOneWithOptional(Stream.of(Optional.of(question.getParentId())
                        .map(QUESTION.ID::eq)));
                if(oldQue.isPresent()){
                    updateTopicReleatedCount(oldQue.get());
                }

            }

        }
    }

    @Override
    public void updateTopicReleatedCount(Question question) {
        List<Topic> topics = topicDao.execute(e -> e.select(TOPIC.fields()).from(TOPIC).leftJoin(QUESTION_TOPIC)
                .on(QUESTION_TOPIC.TOPIC_ID.eq(TOPIC.ID)).where(QUESTION_TOPIC.QUESTION_ID.eq(question.getId())))
                .fetchInto(Topic.class);
        switch (question.getType()) {
            case Question.TYPE_QUESTION:
                // 更新话题问题数
                if (topics.size() > 0) {
                    topics.forEach(t -> t.setQuestionCount(
                            t.getQuestionCount() == null || t.getQuestionCount() < 1 ? 1 : t.getQuestionCount() + 1));
                    topicDao.update(topics);
                }
                break;
            case Question.TYPE_ARTICLE:
                // 更新话题文章数
                if (topics.size() > 0) {
                    topics.forEach(t -> t.setArticleCount(
                            t.getArticleCount() == null || t.getArticleCount() < 1 ? 1 : t.getArticleCount() + 1));
                    topicDao.update(topics);
                }
                // 如果是专家，增加专家的文章分享数
                Optional<Expert> expert = expertDao.fetchOneWithOptional(Stream.of(Optional.of(EXPERT.MEMBER_ID
                        .eq(question.getCreateMemberId()).and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE)))));
                expert.ifPresent(e -> expertService.computeArticleCount(e));
                break;
            case Question.TYPE_SHARE:
                break;
        }

    }

    /**
     * 问题、文章、分享无需审核/审核通过的一系列数据处理：动态、计算各种数量、发送消息邀请专家回答、统计标签使用次数等
     */
    @Override
    public Optional<Question> updateContentForAudit(String id, Integer auditStatus) {
        // 没有被删除的内容

        Optional<Question> question = questionDao
                .fetchOne(QUESTION.ID.eq(id).and(QUESTION.DELETE_FLAG.ne(Question.DELETE_FLAG_YES)));

        return question.map(q -> {
            if (Question.AUDIT_STATUS_PASS.equals(auditStatus)) {
                updateContentForPass(q, true);
            } else {
                if(StringUtils.isEmpty(q.getParentId())){
                    q.setAuditStatus(auditStatus);
                    questionDao.update(q);
                }
            }

            if(!StringUtils.isEmpty(q.getParentId())){
                Optional<Question> oldQue = questionDao.fetchOneWithOptional(Stream.of(Optional.of(q.getParentId())
                        .map(QUESTION.ID::eq)));
                return oldQue.orElse(new Question());
            }
            return q;
        });
    }

    /**
     * 问题被删除/举报通过的一系列逻辑处理
     * <p>
     * 1、更新关联话题的问题数/文章数
     * </p>
     * <p>
     * 2、如果发布人是专家则更新专家的文章数
     * </p>
     * <p>
     * 3、删除动态数据
     * </p>
     */
    private void updateForDelAndAccuse(Question question,Boolean isHidden) {
        List<Topic> topics = topicDao.execute(e -> e.select(TOPIC.fields()).from(TOPIC).leftJoin(QUESTION_TOPIC)
                .on(QUESTION_TOPIC.TOPIC_ID.eq(TOPIC.ID)).where(QUESTION_TOPIC.QUESTION_ID.eq(question.getId())))
                .fetchInto(Topic.class);

        switch (question.getType()) {
            case Question.TYPE_QUESTION:
                // 减少关联话题的问题数
                if (topics.size() > 0) {
                    topics.forEach(t -> topicService.computeQuestionCount(t.getId()));
                }
                break;
            case Question.TYPE_ARTICLE:
                // 减少关联话题的文章数
                if (topics.size() > 0) {
                    topics.forEach(t -> topicService.computeArticleCount(t.getId()));
                }
                // 如果是专家，减少专家的文章数
                Optional<Expert> expert = expertDao
                        .fetchOneWithOptional(Stream.of(Optional.of(EXPERT.MEMBER_ID.eq(question.getCreateMemberId()))));
                expert.ifPresent(e -> expertService.computeArticleCount(e));
                break;
            case Question.TYPE_SHARE:
                break;
        }
        if(isHidden){
            return;
        }
        // 减少该问题下的专家讨论数
        expertDao
                .execute(e -> e.select(EXPERT.fields()).from(EXPERT).leftJoin(DISCUSS)
                        .on(DISCUSS.CREATE_MEMBER_ID.eq(EXPERT.MEMBER_ID))
                        .where(DISCUSS.QUESTION_ID.eq(question.getId()).and(DISCUSS.TYPE.eq(Discuss.TYPE_DISCUSS))
                                .and(DISCUSS.ACCUSE_STATUS.eq(Discuss.ACCUSE_STATUS_NO))
                                .and(DISCUSS.AUDIT_STATUS.eq(Discuss.AUDIT_STATUS_PASS))
                                .and(DISCUSS.DELETE_FLAG.eq(Discuss.DELETE_FLAG_NO)))
                        .groupBy(EXPERT.ID))
                .fetchInto(Expert.class).forEach(ex -> expertService.computeDiscussCount(ex));

        // 删除动态
        trendService.deleteByQuestionId(question.getId());
        // 重算该内容下专家的讨论数
        if (question.getDiscussNum() > 0) {

        }
    }

    /**
     * 问题、文章、分享被举报通过之后一系列数字更新、动态剔除
     */
    @Override
    public Optional<Question> updateForAccusePass(String id) {
        // 没有被删除的内容
        Optional<Question> question = questionDao
                .fetchOne(QUESTION.ID.eq(id).and(QUESTION.DELETE_FLAG.ne(Question.DELETE_FLAG_YES)));
        question.ifPresent(q -> {
            q.setAccuseStatus(Question.ACCUSE_STATUS_YES);
            questionDao.update(q);
            updateForDelAndAccuse(q,false);
            //用于人工智能数据同步推送
            if(q.getType()!=null&&(q.getType()==Question.TYPE_ARTICLE||q.getType()==Question.TYPE_QUESTION)){
                messageSender.send(MessageTypeContent.BAR_QUESTION_ACCUSE,MessageHeaderContent.ID,id);
            }
        });
        return question;
    }

    @Override
    public Optional<Question> access(String id, String currentUserId) {
        Optional<Question> question = questionDao.getOptional(id);
        question.ifPresent(q -> questionDao.execute(e -> e.update(QUESTION)
                .set(QUESTION.BROWSE_NUM, QUESTION.BROWSE_NUM.add(1)).where(QUESTION.ID.eq(q.getId())).execute()));

        messageSender.send(MessageTypeContent.BAR_QUESTION_BROWSE, MessageHeaderContent.ID, id);
        return question;
    }

    @Override
    public Optional<Question> share(String id, String currentUserId) {
        Optional<Question> question = questionDao.getOptional(id);
        question.ifPresent(q -> questionDao.execute(e -> e.update(QUESTION)
                .set(QUESTION.SHARE_NUM, QUESTION.SHARE_NUM.add(1)).where(QUESTION.ID.eq(q.getId())).execute()));

        return question;
    }

    @Override
    public int computeProblemCount(String memberId) {
        long current=System.currentTimeMillis();//当前时间毫秒数
        long zero=current/(1000*3600*24)*(1000*3600*24)-TimeZone.getDefault().getRawOffset();//今天零点零分零秒的毫秒数
        return questionDao.count(QUESTION.CREATE_MEMBER_ID.eq(memberId).and(QUESTION.CREATE_TIME.gt(zero)));
    }

    @Override
    public void computeDiscussCount(String questionId) {
        questionDao.execute(e -> {
            int discussCount = e.select(DISCUSS.ID.count()).from(DISCUSS)
                    .where(DISCUSS.QUESTION_ID.eq(questionId).and(DISCUSS.ACCUSE_STATUS.eq(Discuss.ACCUSE_STATUS_NO))
                            .and(DISCUSS.AUDIT_STATUS.eq(Discuss.AUDIT_STATUS_PASS))
                            .and(DISCUSS.DELETE_FLAG.eq(Discuss.DELETE_FLAG_NO))
                            .and(DISCUSS.TYPE.eq(Discuss.TYPE_DISCUSS)))
                    .fetchOne(DISCUSS.ID.count());
            return e.update(QUESTION).set(QUESTION.DISCUSS_NUM, discussCount).where(QUESTION.ID.eq(questionId))
                    .execute();
        });
    }

    @Override
    public void computePraiseCount(String questionId) {
        praiseDao.execute(e -> {
            int praiseCount = e.select(PRAISE.ID.count()).from(PRAISE).leftJoin(QUESTION)
                    .on(QUESTION.ID.eq(PRAISE.OBJECT_ID))
                    .where(PRAISE.OBJECT_ID.eq(questionId).and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
                            .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)).and(
                                    QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)))
                    .fetchOne(PRAISE.ID.count());
            return e.update(QUESTION).set(QUESTION.PRAISE_NUM, praiseCount).where(QUESTION.ID.eq(questionId)).execute();
        });
    }

    @Override
    public void computeAttentionCount(String questionId) {
        praiseDao.execute(e -> {
            int questionAttentionCount = e.select(ATTENTION.ID.count()).from(ATTENTION).leftJoin(QUESTION)
                    .on(QUESTION.ID.eq(ATTENTION.BUSINESS_ID))
                    .where(ATTENTION.BUSINESS_ID.eq(questionId)
                            .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))
                            .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
                            .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)))
                    .fetchOne(ATTENTION.ID.count());
            return e.update(QUESTION).set(QUESTION.ATTENTION_NUM, questionAttentionCount)
                    .where(QUESTION.ID.eq(questionId)).execute();
        });
    }

    @Override
    public List<Question> findHomeHot(String rootOrganizationId, Integer size) {
        return questionDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e
                    .select(Fields.start().add(QUESTION.ID, QUESTION.TYPE, QUESTION.BROWSE_NUM, QUESTION.TITLE).end());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(QUESTION).where(QUESTION.TYPE.eq(Question.TYPE_QUESTION))
                        .and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId))// 根组织
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))// 未删除
                        .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE))// 激活的
                        .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))// 审核通过
                        .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO));// 未被举报

                return select;
            };

            List<Question> list = stepFunc.apply(selectListField)
                    .orderBy(QUESTION.BROWSE_NUM.desc(), QUESTION.CREATE_TIME.desc()).limit(0, size).fetch(r -> {
                        Question question = r.into(QUESTION).into(Question.class);
                        return question;
                    });

            return list;
        });
    }

    @Override
    public List<Question> rankingList(String organizationId, int size, Optional<Integer> year, Optional<String> month) {
        Condition condition = Stream.of(year.map(QUESTION_MONTH_LIST.YEAR::eq),
                month.map(QUESTION_MONTH_LIST.MONTH::eq))
                .filter(Optional::isPresent).map(Optional::get)
                .reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());
        Field<BigDecimal> sumBrowseCount = QUESTION_MONTH_LIST.BROWSE_COUNT.sum().as("sum_browse_count");
        Field<String> questionId = QUESTION.ID.as("questionId");
        Field<String> questionName = QUESTION.TITLE.as("questionName");
        Field<String> orgId = QUESTION.ORGANIZATION_ID.as("orgId");

        return questionDao.execute(d -> {
            Table<Record> basic = (d.select(
                    Fields.start()
                            .add(questionId, questionName, orgId, sumBrowseCount)
                            .end())
                    .from(QUESTION_MONTH_LIST)
                    .leftJoin(QUESTION).on(QUESTION_MONTH_LIST.QUESTION_ID.eq(QUESTION.ID))
                    .where(condition
                            .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))
                            .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE))
                            .and(QUESTION.TYPE.eq(Question.TYPE_QUESTION))
                            .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))
                            .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO)))
                    .groupBy(QUESTION_MONTH_LIST.QUESTION_ID)
                    .orderBy(sumBrowseCount.desc())
                    .limit(0, size)).asTable("month_count");

            return d.selectDistinct(basic.fields()).from(basic)
                    .leftJoin(ORGANIZATION_DETAIL).on(basic.field(orgId).eq(ORGANIZATION_DETAIL.SUB))
                    .where(ORGANIZATION_DETAIL.ROOT.eq(organizationId))
                    .orderBy(basic.field(sumBrowseCount).desc())
                    .fetch().map(r -> {
                        Question info = new Question();
                        info.setId(r.get(questionId));
                        info.setTitle(r.get(questionName));
                        BigDecimal browseCount = r.get(sumBrowseCount);
                        if(browseCount != null) {
                            info.setBrowseNum(browseCount.intValue());
                        }
                        return info;
                    });
        });
    }

    @Override
    public List<Question> findMonthHot(String rootOrganizationId, Integer size, Optional<String> month) {
        long start, end;
        if (month.isPresent() && month.get().length() == 6) {
            start = getMonthStart(month.get(), 0);
            end = getMonthStart(month.get(), 1);
        } else {
            start = DateUtil.getMonthStart();
            end = DateUtil.getMonthStart(1);
        }
        return questionDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e
                    .select(Fields.start().add(QUESTION.ID, QUESTION.TYPE, QUESTION.BROWSE_NUM, QUESTION.TITLE).end());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(QUESTION).where(QUESTION.TYPE.eq(Question.TYPE_QUESTION))
                        .and(QUESTION.ROOT_ORGANIZATION_ID.eq(rootOrganizationId))// 根组织
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))// 未删除
                        .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE))// 激活的
                        .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))// 审核通过
                        .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))// 未被举报
                        .and(QUESTION.CREATE_TIME.between(start, end));// 当前月度查询
                return select;
            };

            List<Question> list = stepFunc.apply(selectListField)
                    .orderBy(QUESTION.BROWSE_NUM.desc(), QUESTION.CREATE_TIME.desc()).limit(0, size).fetch(r -> {
                        Question question = r.into(QUESTION).into(Question.class);
                        return question;
                    });

            return list;
        });
    }

    @Override
    public List<String> getAllQuestionIds(int start, int limit) {
        List<Integer> list=Stream.of(Question.TYPE_QUESTION,Question.TYPE_ARTICLE).collect(toList());
        return questionDao.execute(dslContext -> {
            return dslContext.select(QUESTION.ID)
                    .from(QUESTION)
                    .where(QUESTION.TYPE.in(list))
                    .limit(start, limit).fetch(QUESTION.ID);
        });
    }

    @Override
    public List<String> getAllQuestionIds(int start, int limit, List<Integer> types) {
        return questionDao.execute(dslContext -> dslContext.select(QUESTION.ID)
                .from(QUESTION)
                .where(QUESTION.TYPE.in(types))
                .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO).or(QUESTION.DELETE_FLAG.isNull()))
                .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
                .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))
                .limit(start, limit).fetch(QUESTION.ID));
    }

    @Override
    public PagedResult<Question> getArchivesTime(Integer page, Integer pageSize, String currentMemberId,
                                                 Optional<Long> startTime, Optional<Long> endTime) {

        SelectConditionStep<Record> step = questionDao.execute(e -> e.selectDistinct(Fields.start().add(QUESTION).end())
                .from(QUESTION).where(QUESTION.CREATE_MEMBER_ID.eq(currentMemberId))
                .and(startTime.map(QUESTION.CREATE_TIME::ge).orElse(DSL.trueCondition()))
                .and(endTime.map(QUESTION.CREATE_TIME::lt).orElse(DSL.trueCondition()))
                .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)));

        Integer count = questionDao.execute(e -> e.fetchCount(step));

        List<Question> questions = step.orderBy(QUESTION.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize)
                .fetchInto(Question.class);

        return PagedResult.create(count, questions);

    }

    private static long getMonthStart(String month, int monthAdd) {
        Integer year = Integer.valueOf(month.substring(0, 4));
        Integer mm = Integer.valueOf(month.substring(4));
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, (mm.intValue() - 1));
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);

        cal.add(Calendar.MONTH, monthAdd);
        return cal.getTimeInMillis();
    }

    @Override
    public PagedResult<Question> getArchivesTimeMerge(Integer page, Integer pageSize, List<String> currentMemberIds,
                                                      Optional<Long> startTime, Optional<Long> endTime) {
        SelectConditionStep<Record> step = questionDao.execute(e -> e.selectDistinct(Fields.start().add(QUESTION).end())
                .from(QUESTION).where(QUESTION.CREATE_MEMBER_ID.in(currentMemberIds))
                .and(startTime.map(QUESTION.CREATE_TIME::ge).orElse(DSL.trueCondition()))
                .and(endTime.map(QUESTION.CREATE_TIME::le).orElse(DSL.trueCondition()))
                .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)));

        Integer count = questionDao.execute(e -> e.fetchCount(step));

        List<Question> questions = step.orderBy(QUESTION.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize + 1)
                .fetchInto(Question.class);

        return PagedResult.create(count, questions);
    }

    @Override
    public List<Question> getArchivesAllMeger(List<String> currentMemberIds,
                                              Optional<Long> startTime, Optional<Long> endTime) {
        return questionDao.execute(e -> e
                .select(Fields.start()
                        .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                                QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                                QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM,
                                QUESTION.PRAISE_NUM, QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE,
                                QUESTION.SHARE_TYPE, QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME,
                                QUESTION.LAST_MODIFY_TIME, QUESTION.CREATE_MEMBER_ID)
                        .end())
                .from(QUESTION)
                .where(QUESTION.CREATE_MEMBER_ID.in(currentMemberIds)
                        .and(startTime.map(QUESTION.CREATE_TIME::ge).orElse(DSL.trueCondition()))
                        .and(endTime.map(QUESTION.CREATE_TIME::le).orElse(DSL.trueCondition()))
                        .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)))
                .orderBy(QUESTION.CREATE_TIME.desc())).fetchInto(Question.class);
    }

    @Override
    public Question getCommonFront(String id) {
        Question question = this.getCommon(id);
        // 被举报通过了
        if (Question.ACCUSE_STATUS_YES == question.getAccuseStatus()) {
            throw new UnprocessableException(com.zxy.common.rpc.filter.ErrorCode.DataNotExists);
        }
        return question;
    }

    @Override
    public Question getCommon(String id) {
        return questionDao.execute(e -> {
            Record record = e
                    .select(Fields.start()
                            .add(QUESTION.ID, QUESTION.TYPE, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                                    QUESTION.STATUS, QUESTION.AUDIT_STATUS, QUESTION.IMG, QUESTION.ACCUSE_STATUS,
                                    QUESTION.ESSENCE_STATUS, QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM,
                                    QUESTION.PRAISE_NUM, QUESTION.DISCUSS_NUM, QUESTION.SHARE_NUM, QUESTION.SHARE_TITLE,
                                    QUESTION.SHARE_TYPE, QUESTION.SHARE_OBJECT_ID, QUESTION.CREATE_TIME,
                                    QUESTION.CREATE_MEMBER_ID,QUESTION.SOURCE_NAME,
                                    QUESTION.QUESTION_SOURCE_NAME,QUESTION.SOURCE_URL)
                            .add(MEMBER.ID).add(MEMBER.FULL_NAME).add(MEMBER.HEAD_PORTRAIT)
                            .add(MEMBER.HEAD_PORTRAIT_PATH).add(AUDIT.ID)
                            .add(AUDIT.AUDIT_NOTE).add(EXPERT.ID).end())
                    .from(QUESTION).leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID)).leftJoin(EXPERT)
                    .on(EXPERT.MEMBER_ID.eq(QUESTION.CREATE_MEMBER_ID)
                            .and(EXPERT.ACTIVE_STATUS.eq(Expert.ACTIVE_STATUS_ACTIVE))
                            .and(EXPERT.AUDIT_STATUS.eq(Expert.AUDIT_STATUS_PASS)))
                    .leftJoin(AUDIT).on(AUDIT.BUSINESS_ID.eq(QUESTION.ID).and(AUDIT.ACCUSED_MEMBER_ID.isNull()))
                    .where(QUESTION.ID.eq(id)).fetchOne();

            if (record == null)
                throw new UnprocessableException(com.zxy.common.rpc.filter.ErrorCode.DataNotExists);

            Question question = record.into(Question.class);

            if (Question.DELETE_FLAG_YES == question.getDeleteFlag()) {
                throw new UnprocessableException(com.zxy.common.rpc.filter.ErrorCode.DataNotExists);
            }
            Member member = record.into(Member.class);
            member.setIsExpert(!StringUtils.isEmpty(record.getValue(EXPERT.ID)));
            question.setCreateMember(member);
            Audit audit = record.into(Audit.class);
            question.setAudit(audit);
            List<Attachment> attachments = attachmentService.getByBusinessId(id);
            question.setAttachments(attachments);
            List<Praise> praises = praiseDao.fetch(PRAISE.OBJECT_ID.eq(id));
            question.setPraises(praises);
            List<Attention> attentions = attentionDao.fetch(ATTENTION.BUSINESS_ID.eq(id));
            question.setAttentions(attentions);
            return question;
        });
    }
    @Override
    public Question insertQuestion(String title, Optional<String> content, Optional<String> contentTxt,
                                   Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String currentUserId,
                                   String organizationId, String rootOrganizationId, boolean auditOn, Optional<String> md5Token, List<String> experts, boolean isOK) {
        checkAttachmentToken(attachments, currentUserId, md5Token);
        return insert(title, content, contentTxt, img, topics, attachments, currentUserId, organizationId,
                rootOrganizationId, Question.TYPE_QUESTION, auditOn ,experts, isOK);
    }
    private void checkAttachmentToken(List<Attachment> attachments, String currentUserId, Optional<String> md5Token) {
        //验证附件是不是被篡改
        md5Token.ifPresent(x -> {
            String attachmentIds = attachments.stream().sorted(Comparator.comparing(Attachment::getAttachmentId)).map(Attachment::getAttachmentId)
                    .collect(Collectors.joining());
            if (!x.equals(DigestUtils.md5DigestAsHex((currentUserId + attachmentIds).getBytes()))){
                throw new UnprocessableException(ErrorCode.QuestionAttachmentsError);
            }
        });
    }
    @Override
    public Question insertArticle(String title, Optional<String> content, Optional<String> contentTxt,
                                  Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments, String currentUserId,
                                  String organizationId, String rootOrganizationId, boolean auditOn, Optional<String> md5Token, boolean isOk) {
        checkAttachmentToken(attachments, currentUserId, md5Token);
        return insert(title, content, contentTxt, img, topics, attachments, currentUserId, organizationId,
                rootOrganizationId, Question.TYPE_ARTICLE, auditOn , null, isOk);
    }

    @Override
    public Question insertStudioArticle(String title, String studioId, Optional<String> content, Optional<String> contentTxt,
                                        Optional<String> img, List<QuestionTopic> topics, List<Attachment> attachments,
                                        String currentUserId, String organizationId, String rootOrganizationId, boolean auditOn, Integer articleType) {

        Question question = new Question();
        question.forInsert();
        question.setTitle(title);
        content.ifPresent(question::setContent);
        contentTxt.ifPresent(question::setContentTxt);
        question.setAttentionNum(0);
        question.setPraiseNum(0);
        question.setShareNum(0);
        question.setBrowseNum(0);
        question.setDiscussNum(0);
        question.setCollectNum(0);
        question.setAccuseStatus(Question.ACCUSE_STATUS_NO);
        question.setTopStatus(Question.TOP_STATUS_NO);
        question.setTopicTopStatus(Question.TOPIC_TOP_STATUS_NO);
        question.setEssenceStatus(Question.ESSENCE_STATUS_NO);
        question.setCreateMemberId(currentUserId);
        question.setOrganizationId(organizationId);
        question.setDeleteFlag(Question.DELETE_FLAG_NO);
        img.ifPresent(question::setImg);
        question.setType(Question.TYPE_ARTICLE);
        question.setRootOrganizationId(rootOrganizationId);
        question.setAuditStatus(auditOn ? Question.AUDIT_STATUS_WAIT : Question.AUDIT_STATUS_PASS);
        question.setStatus(auditOn ? null : Question.STATUS_ACTIVE);
        question.setHidden(Question.HIDDEN_FLAG_NO);
        question.setFromStudio(Question.FROM_STUDIO_YES);
        // 话题关联
        questionTopicService.insert(question.getId(), topics);
        // 新增
        questionDao.insert(question);
        // 写入内容表
        StudioContent studioContent =
                studioContentService.insertArticleType(studioId, question.getId(), title,
                        StudioContent.BUSINESS_TYPE_ARTICLE,articleType);
        if (auditOn) {
            // 写入内容审核表
            studioContentAuditService.save(studioContent.getId(), currentUserId);
        } else {
            messageSender.send(MessageTypeContent.SYSTEM_POINT_CHANGE, MessageHeaderContent.MEMBER_ID,currentUserId
                    ,PointConfig.RULE_KEY, MessageHeaderContent.UPLOAD_EXPERT);
            doUpdate(question, false);
            // 修改内容表状态
            studioContentService.release(studioContent.getId());
            // 工作室内容数加1
            studioService.incrContentNum(studioId);
        }

        return question;
    }

    @Override
    public Question updateStudioArticle(String id, Optional<String> title, Optional<String> content,
                                        Optional<String> contentTxt, Optional<String> img, List<QuestionTopic> topics,
                                        List<Attachment> attachments,
                                        String rootOrganizationId, boolean auditOn) {
        Optional<Question> question = questionDao.getOptional(id);

        question.ifPresent(q -> {
            title.ifPresent(q::setTitle);
            content.ifPresent(q::setContent);
            contentTxt.ifPresent(q::setContentTxt);
            img.ifPresent(q::setImg);
            q.setAuditStatus(auditOn ? Question.AUDIT_STATUS_WAIT : Question.AUDIT_STATUS_PASS);
            q.setStatus(auditOn ? null : Question.STATUS_ACTIVE);
            q.setRootOrganizationId(rootOrganizationId);
            // 修改关联标签
            questionTopicService.deleteByQuestionId(id);
            questionTopicService.insert(id, topics);
            // 修改内容
            questionDao.update(q);
            // 不需要审核
            if (!auditOn) {
                doUpdate(q, false);
            }
        });

        return question.<UnprocessableException>orElseThrow(() -> {
            throw new UnprocessableException(ErrorCode.STUDIO_CONTENT_DOES_NOT_EXIST);
        });
    }

    @Override
    public int deleteBack(String id, String currentMemberId) {
        Optional<Question> questionOptional = questionDao.getOptional(id);
        questionOptional.ifPresent(question -> {
            // 有人讨论的不能删除
            if (question.getDiscussNum() != null && question.getDiscussNum() > 0) {
                throw new UnprocessableException(ErrorCode.QuestionNotAllowedDeleteBeDiscussed);
            }
            // 删除审核记录
            if (Question.AUDIT_STATUS_WAIT == question.getAuditStatus()) { // 审核中
                questionDao.delete(id);
                auditService.deleteByBusinessId(id);
            } else {
                question.setDeleteFlag(Question.DELETE_FLAG_YES);
                questionDao.update(question);
                // 数字、动态等内容处理
                this.updateForDelAndAccuse(question,false);
            }

            //工作室文章更新内容表为删除状态,删除审核记录
            if (Question.FROM_STUDIO_YES.equals(question.getFromStudio())) {
                studioContentService.deleteArticle(question.getId());
            }

            messageSender.send(MessageTypeContent.BAR_QUESTION_DELETE, MessageHeaderContent.ID, question.getId(),
                    MessageHeaderContent.MEMBER_ID, currentMemberId);
        });
        return 1;
    }

    @Override
    public Map<String, Object> getType(String id) {
        HashMap<String, Object> map = new HashMap<>();
        List<Integer> list = questionDao.execute(dsl -> dsl.select(Fields.start().add(QUESTION.TYPE).end())
                .from(QUESTION)
                .where(QUESTION.ID.eq(id))
                .fetch(QUESTION.TYPE));
        if (list != null && list.size() > 0)
            map.put("type", list.get(0));
        else
            map.put("type", 0);
        return map;
    }

    @Override
    public List<Question> findQuestionInfo(String... ids) {
        // 未被举报，审核通过的问题或者文章
        return questionDao.execute(x -> x.select(QUESTION.ID, QUESTION.TITLE, QUESTION.CONTENT,
                QUESTION.CONTENT_TXT, QUESTION.TYPE, QUESTION.STATUS, QUESTION.ACCUSE_STATUS,
                QUESTION.BROWSE_NUM, QUESTION.ATTENTION_NUM,QUESTION.CREATE_TIME,
                QUESTION.DISCUSS_NUM,
                        QUESTION.AUDIT_STATUS,QUESTION.HIDDEN,QUESTION.IMG,QUESTION.CREATE_MEMBER_ID,
                        QUESTION.SOURCE_URL,QUESTION.PRAISE_NUM,QUESTION.SHARE_NUM)
                .from(QUESTION)
                .where(QUESTION.ID.in(ids))
                .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO).or(QUESTION.DELETE_FLAG.isNull()))
                .and(QUESTION.ACCUSE_STATUS.eq(Question.ACCUSE_STATUS_NO))
                .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))
                .fetch(r -> {
                    Question q = new Question();
                    q.setId(r.getValue(QUESTION.ID));
                    q.setTitle(r.getValue(QUESTION.TITLE));
                    q.setContentTxt(r.getValue(QUESTION.CONTENT_TXT));
                    q.setContent(r.getValue(QUESTION.CONTENT));
                    q.setType(r.getValue(QUESTION.TYPE));
                    q.setBrowseNum(r.getValue(QUESTION.BROWSE_NUM));
                    q.setAttentionNum(r.getValue(QUESTION.ATTENTION_NUM));
                    q.setDiscussNum(r.getValue(QUESTION.DISCUSS_NUM));
                    q.setCreateTime(r.getValue(QUESTION.CREATE_TIME));
                    q.setAuditStatus(r.getValue(QUESTION.AUDIT_STATUS));
                    q.setHidden(r.getValue(QUESTION.HIDDEN));
                    q.setStatus(r.getValue(QUESTION.STATUS));
                    q.setImg(r.getValue(QUESTION.IMG));
                    q.setCreateMemberId(r.getValue(QUESTION.CREATE_MEMBER_ID));
                    q.setSourceUrl(r.getValue(QUESTION.SOURCE_URL));
                    q.setPraiseNum(r.getValue(QUESTION.PRAISE_NUM));
                    q.setShareNum(r.getValue(QUESTION.SHARE_NUM));
                    return q;
                })
        );
    }

    @Override
    public void insertQuestionExpert(String questionId, List<String> experts) {
        if(experts==null || experts.isEmpty()){
            return ;
        }
        questionExpertCommonDao.delete(QUESTION_EXPERT.QUESTION_ID.eq(questionId));
        List<QuestionExpert> results = new ArrayList<>();
        for (String expert : experts) {
            QuestionExpert questionExpert = new QuestionExpert();
            questionExpert.forInsert();
            questionExpert.setExpertId(expert);
            questionExpert.setQuestionId(questionId);
            results.add(questionExpert);
        }
        questionExpertCommonDao.insert(results);
    }

    /**
     * 查看标签关联的问吧列表
     * @param page 当前页数
     * @param pageSize 每页大小
     * @param currentUserId 当前登录用户id
     * @param topicId 标签id
     * @param title 内容标题
     * @param type 内容类型
     * @param submitPerson 提交人
     * @param submitStartTime 提交开始时间
     * @param submitEndTime 提交结束时间
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    public PagedResult<Question> findQuestionInfoByTopic(int page, int pageSize, String currentUserId, String topicId,
                                                         Optional<String> title, Optional<Integer> type, Optional<String> submitPerson, Optional<Long> submitStartTime,Optional<Long> submitEndTime) {
        List<Condition> conditions = Stream.of(title.map(QUESTION.TITLE::contains),
                        submitPerson.map(QUESTION.CREATE_MEMBER_ID::eq),
                        type.map(QUESTION.TYPE::eq),
                        submitStartTime.map(QUESTION.CREATE_TIME::ge),
                        submitEndTime.map(QUESTION.CREATE_TIME::le))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(QUESTION_TOPIC.TOPIC_ID.eq(topicId));

        SelectOnConditionStep<Record> stepSelect = questionDao.execute(e->e.selectDistinct(Fields.start()
                        .add(QUESTION.ID,QUESTION.TITLE,QUESTION.TYPE,QUESTION.CREATE_TIME)
                        .add(MEMBER.FULL_NAME).end())
                .from(QUESTION)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID))
                .leftJoin(QUESTION_TOPIC).on(QUESTION_TOPIC.QUESTION_ID.eq(QUESTION.ID))
        );

        SelectOnConditionStep<Record> stepCount = questionDao.execute(e->e.select(Fields.start()
                        .add(QUESTION.ID.countDistinct()).end())
                .from(QUESTION)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID))
                .leftJoin(QUESTION_TOPIC).on(QUESTION_TOPIC.QUESTION_ID.eq(QUESTION.ID))
        );

        int firstResult = (page - 1) * pageSize;
        Integer count = stepCount.where(conditions).fetchOne().getValue(0, Integer.class);
        List<Question> items = new ArrayList<>();
        items = stepSelect.where(conditions).orderBy(QUESTION.CREATE_TIME.desc()).limit(firstResult,pageSize)
                .fetch(item->{
                    Question question = new Question();
                    question.setId(item.getValue(QUESTION.ID));
                    question.setTitle(item.getValue(QUESTION.TITLE));
                    question.setType(item.getValue(QUESTION.TYPE));
                    question.setCreateTime(item.getValue(QUESTION.CREATE_TIME));
                    question.setCreateMemberName(item.getValue(MEMBER.FULL_NAME));
                    return question;
                });
        return PagedResult.create(count, items);
    }

    /**
     * 查看问吧列表
     * @param page 当前页数
     * @param pageSize 每页大小
     * @param currentUserId 当前登录用户id
     * @param title 内容标题
     * @param type 内容类型
     * @param submitPerson 提交人
     * @param submitStartTime 提交开始时间
     * @param submitEndTime 提交结束时间
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    public PagedResult<Question> findQuestionInfo(int page, int pageSize, String currentUserId, String topicId,
                                                  Optional<String> title, Optional<Integer> type, Optional<String> submitPerson, Optional<Long> submitStartTime,Optional<Long> submitEndTime) {
        //根据标签id查询问吧id列表
        List<String> ids = questionTopicDao.fetch(QUESTION_TOPIC.TOPIC_ID.eq(topicId))
                .stream().map(QuestionTopic::getQuestionId).collect(Collectors.toList());

        List<Condition> conditions = Stream.of(title.map(QUESTION.TITLE::contains),
                        submitPerson.map(QUESTION.CREATE_MEMBER_ID::eq),
                        type.map(QUESTION.TYPE::eq),
                        submitStartTime.map(QUESTION.CREATE_TIME::ge),
                        submitEndTime.map(QUESTION.CREATE_TIME::le))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(QUESTION.ID.notIn(ids));

        SelectOnConditionStep<Record> stepSelect = questionDao.execute(e->e.selectDistinct(Fields.start()
                        .add(QUESTION.ID,QUESTION.TITLE,QUESTION.TYPE,QUESTION.CREATE_TIME,
                                QUESTION.STATUS,QUESTION.AUDIT_STATUS,QUESTION.HIDDEN)
                        .add(MEMBER.FULL_NAME).end())
                .from(QUESTION)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID))
        );

        SelectOnConditionStep<Record> stepCount = questionDao.execute(e->e.select(Fields.start()
                        .add(QUESTION.ID.countDistinct()).end())
                .from(QUESTION)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID))
        );

        int firstResult = (page - 1) * pageSize;
        Integer count = stepCount.where(conditions).fetchOne().getValue(0, Integer.class);
        List<Question> items = new ArrayList<>();
        items = stepSelect.where(conditions).orderBy(QUESTION.CREATE_TIME.desc()).limit(firstResult,pageSize)
                .fetch(item->{
                    Question question = new Question();
                    question.setId(item.getValue(QUESTION.ID));
                    question.setTitle(item.getValue(QUESTION.TITLE));
                    question.setType(item.getValue(QUESTION.TYPE));
                    question.setCreateTime(item.getValue(QUESTION.CREATE_TIME));
                    question.setCreateMemberName(item.getValue(MEMBER.FULL_NAME));
                    question.setStatus(item.getValue(QUESTION.STATUS));
                    question.setAuditStatus(item.getValue(QUESTION.AUDIT_STATUS));
                    question.setHidden(item.getValue(QUESTION.HIDDEN));
                    return question;
                });
        return PagedResult.create(count, items);
    }

    /**
     * 批量新增问吧标签
     * @param topicId 标签id
     * @param businessIds 问吧id
     * @return
     */
    public void insertQuestionTopicInfo(String topicId,String[] businessIds) {
        for(String businessId : businessIds) {
            List<QuestionTopic> topics =new ArrayList<>();
            QuestionTopic topic = new QuestionTopic();
            topic.forInsert();
            topic.setQuestionId(businessId);
            topic.setTopicId(topicId);
            topics.add(topic);
            questionTopicService.insertQuestionTopic(businessId,topics);
        }
    }

    /**
     * 批量删除问吧标签
     * @param topicId 标签id
     * @param businessIds 问吧id
     * @return
     */
    @Override
    public void deleteQuestionTopicInfo(String topicId,String[] businessIds) {
        for(String businessId : businessIds) {
            questionTopicService.deleteQuestionTopic(businessId, topicId);
        }
    }
    @Override
    public List<Question> getBottomDataQuestion(int offset, int limit) {
        return   questionDao.execute(dslContext -> dslContext.select(QUESTION.ID,QUESTION.TYPE).from(QUESTION)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(QUESTION.CREATE_MEMBER_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(QUESTION.ORGANIZATION_ID))
                .where(QUESTION.TYPE.in(Question.TYPE_QUESTION,Question.TYPE_ARTICLE))
                .and(QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS))
                .and(QUESTION.STATUS.eq(Question.STATUS_ACTIVE))
                .and(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO))
                .and(QUESTION.ACCUSE_STATUS.notEqual(Question.ACCUSE_STATUS_YES))
                .orderBy(QUESTION.CREATE_TIME.desc()).limit(offset,limit).fetch(record ->{
                    Question question = new Question();
                    question.setId(record.getValue(QUESTION.ID));
                    question.setType(record.getValue(QUESTION.TYPE));
                    return question;
                }));
    }

    @Override
    public List<Question> getByIds(List<String> ids) {
        return questionDao.execute(dsl ->
                dsl.select(QUESTION.ID, QUESTION.TITLE, QUESTION.CONTENT, QUESTION.CONTENT_TXT,
                                QUESTION.TYPE, QUESTION.DISCUSS_NUM, QUESTION.PRAISE_NUM, QUESTION.BROWSE_NUM,
                                QUESTION.IMG, QUESTION.CREATE_MEMBER_ID, QUESTION.CREATE_TIME,
                                QUESTION.DELETE_FLAG, QUESTION.HIDDEN, QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS,
                                MEMBER.FULL_NAME)
                        .from(QUESTION).leftJoin(MEMBER).on(QUESTION.CREATE_MEMBER_ID.eq(MEMBER.ID))
                        .where(QUESTION.ID.in(ids))
                        .fetch(f -> {
                            Question question = f.into(QUESTION).into(Question.class);
                            Member createMember = new Member();
                            createMember.setFullName(f.getValue(MEMBER.FULL_NAME));
                            question.setCreateMember(createMember);
                            return question;
                        })
                        .stream().sorted(Comparator.comparing(Question::getCreateTime).reversed()).collect(Collectors.toList())
        );
    }

    @Override
    public Long sumBrowseNum(List<String> ids) {
        return questionDao.execute(dsl ->
                dsl.select(DSL.sum(DSL.ifnull(QUESTION.BROWSE_NUM, 0)))
                        .from(QUESTION)
                        .where(QUESTION.ID.in(ids))
                        .fetchOne()
                        .getValue(0, Long.class)
        );
    }

    @Override
    public List<Question> getImgs(List<String> ids) {
        return questionDao.execute(dsl ->
                dsl.select(QUESTION.ID, QUESTION.IMG)
                        .from(QUESTION)
                        .where(QUESTION.ID.in(ids))
                        .fetchInto(Question.class)
        );
    }

    @Override
    public List<Question> getBrowseNumByIds(List<String> ids) {
        return questionDao.execute(dsl ->
                dsl.select(QUESTION.ID, QUESTION.BROWSE_NUM)
                        .from(QUESTION)
                        .where(QUESTION.ID.in(ids))
                        .fetchInto(Question.class)
        );
    }

    @Override
    public List<Question> getCountInformationIds(List<String> ids) {
        return questionDao.execute(dsl ->
                dsl.select(QUESTION.ID, QUESTION.BROWSE_NUM, QUESTION.PRAISE_NUM, QUESTION.DISCUSS_NUM, MEMBER.FULL_NAME,
                                MEMBER.NAME)
                        .from(QUESTION).leftJoin(MEMBER).on(QUESTION.CREATE_MEMBER_ID.eq(MEMBER.ID))
                        .where(QUESTION.ID.in(ids))
                        .fetch(f->{
                            Question question = f.into(QUESTION).into(Question.class);
                            question.setCreateMember(f.into(MEMBER).into(Member.class));
                            return question;
                        })
        );
    }

    @Override
    public void updateStudioArticleCollectNum(String articleId, Integer collectNum) {
        questionDao.execute(e ->
                e.update(QUESTION).set(QUESTION.COLLECT_NUM, collectNum).where(QUESTION.ID.eq(articleId))
                        .execute());
    }




    @Override
    public void updateQuestion(Question question) {
        questionDao.update(question);
    }


    @Override
    public List<Question> findQuestion(Integer type, Long startTime, Long endTime) {
        return questionDao.execute(r->
                r.select(Fields.start().
                                add(QUESTION.ID).
                                add(QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS).
                                add(QUESTION.RED_BOAT_QUESTION_TO_REVIEW).
                                add(AUDIT.ID).
                                add(AUDIT.RED_BOAT_CREATE_TIME).
                                add(AUDIT.RED_BOAT_AUDIT_STATUS).
                                end())
                        .from(QUESTION)
                        .leftJoin(AUDIT).on(QUESTION.ID.eq(AUDIT.BUSINESS_ID))
                        .where(QUESTION.DELETE_FLAG.ne(Question.DELETE_FLAG_YES))
                        .and(QUESTION.CREATE_TIME.ge(startTime))
                        .and(QUESTION.CREATE_TIME.le(endTime))
                        .and(QUESTION.TYPE.eq(type))
                        .fetch(o->{
                            Question question = o.into(Question.class);
                            Audit audit = o.into(Audit.class);
                            question.setAudit(audit);
                            return  question;
                        })
        );
    }

    @Override
    public Map<String, String> otherQuestion(String id, boolean redStatus) {

        Question question = questionDao.get(id);

        List<String> nextList = questionDao.execute(r -> {
            List<Condition> conditions = Stream.of(Optional.of(redStatus).map(w -> w ? QUESTION.AUDIT_STATUS.ne(2) :
                                    QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)),
                            Optional.of(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)), Optional.of(QUESTION.ACCUSE_STATUS.notEqual(Question.ACCUSE_STATUS_YES))).filter(Optional::isPresent)
                    .map(Optional::get).collect(toList());

            return r.select(Fields.start().add(QUESTION.ID).end())
                    .from(QUESTION)
                    .leftJoin(AUDIT).on(QUESTION.ID.eq(AUDIT.BUSINESS_ID))
                    .where(conditions)
                    .and(QUESTION.CREATE_TIME.lt(question.getCreateTime()))
                    .and(AUDIT.BUSINESS_TYPE.isNull().or(AUDIT.BUSINESS_TYPE.notIn(6, 7, 8)))
                    .and(QUESTION.TOPIC_STATUS.eq(Question.TOPIC_STATUS_Y).or(QUESTION.TOPIC_STATUS.eq(Question.TOPIC_STATUS_N)))
                    .orderBy(QUESTION.CREATE_TIME.desc())
                    .limit(1)
                    .fetch(QUESTION.ID);
        });


        List<String> preList = questionDao.execute(r -> {
            List<Condition> conditions = Stream.of(Optional.of(redStatus).map(w -> w ? QUESTION.AUDIT_STATUS.ne(2) :
                                    QUESTION.AUDIT_STATUS.eq(Question.AUDIT_STATUS_PASS)),
                            Optional.of(QUESTION.DELETE_FLAG.eq(Question.DELETE_FLAG_NO)), Optional.of(QUESTION.ACCUSE_STATUS.notEqual(Question.ACCUSE_STATUS_YES))).filter(Optional::isPresent)
                    .map(Optional::get).collect(toList());

            return r.select(Fields.start().add(QUESTION.ID).end())
                    .from(QUESTION)
                    .leftJoin(AUDIT).on(QUESTION.ID.eq(AUDIT.BUSINESS_ID))
                    .where(conditions)
                    .and(QUESTION.CREATE_TIME.gt(question.getCreateTime()))
                    .and(AUDIT.BUSINESS_TYPE.isNull().or(AUDIT.BUSINESS_TYPE.notIn(6, 7, 8)))
                    .and(QUESTION.TOPIC_STATUS.eq(Question.TOPIC_STATUS_Y).or(QUESTION.TOPIC_STATUS.eq(Question.TOPIC_STATUS_N)))
                    .orderBy(QUESTION.CREATE_TIME.asc())
                    .limit(1)
                    .fetch(QUESTION.ID);
        });

        Map<String, String> map = new HashMap<>();
        map.put("preQuestionId", CollectionUtils.isEmpty(preList) ? null : preList.get(0));
        map.put("nextQuestionId", CollectionUtils.isEmpty(nextList) ? null : nextList.get(0));
        return map;
    }


    /**
     * 智能标签问题上传九天
     * @param typeId  智能标签id
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<Question> findIntellectTopicQuestion(String typeId, Long startTime, Long endTime) {
        //子查询
        List<Question> execute = questionDao.execute(r -> {
            Table<Record2<String, String>> topic1 = r.select(DSL.groupConcat(TOPIC.NAME).as("originalTopic"), QUESTION_TOPIC.QUESTION_ID.as("f_question_id")).from(TOPIC)
                    .leftJoin(QUESTION_TOPIC).on(TOPIC.ID.eq(QUESTION_TOPIC.TOPIC_ID))
                    .where(TOPIC.TYPE_ID.notEqual(typeId)).groupBy(QUESTION_TOPIC.QUESTION_ID).asTable("TOPIC1");
            Table<Record2<String, String>> topic2 = r.select(DSL.groupConcat(TOPIC.NAME).as("intellectTopic"), QUESTION_TOPIC.QUESTION_ID.as("f_question_id")).from(TOPIC)
                    .leftJoin(QUESTION_TOPIC).on(TOPIC.ID.eq(QUESTION_TOPIC.TOPIC_ID))
                    .where(TOPIC.TYPE_ID.eq(typeId)).groupBy(QUESTION_TOPIC.QUESTION_ID).asTable("TOPIC2");

            List<Question> questionList = r.select(Fields.start().
                            add(QUESTION.ID).
                            add(QUESTION.TITLE).
                            add(QUESTION.TYPE).
                            add(QUESTION.CONTENT_TXT).
                            add(topic1.field("originalTopic", String.class)).
                            add(topic2.field("intellectTopic", String.class)).
                            end())
                    .from(QUESTION)
                    .leftJoin(topic1).on(topic1.field("f_question_id", String.class).eq(QUESTION.ID))
                    .leftJoin(topic2).on(topic2.field("f_question_id", String.class).eq(QUESTION.ID))
                    .where(QUESTION.DELETE_FLAG.ne(Question.DELETE_FLAG_YES))
                    .and(QUESTION.CREATE_TIME.ge(startTime))
                    .and(QUESTION.CREATE_TIME.le(endTime))
                    .fetch(o -> {
                        Question question = o.into(Question.class);
                        question.setOriginalTopic(o.getValue("originalTopic",String.class));
                        question.setIntellectTopic(o.getValue("intellectTopic",String.class));
                        return question;
                    });
            return questionList;
        });
        return execute;
    }

    @Override
    public void redShipUpdate(String id) {
        questionDao.execute(r->
            r.update(QUESTION).
                    set(QUESTION.RED_BOAT_QUESTION_AUDIT_STATUS, Question.RED_BOAT_AUDIT_STATUS_PASS).
                    set(QUESTION.AUDIT_STATUS, Audit.AUDIT_STATUS_WAIT).
                    set(QUESTION.ACCUSE_STATUS, Question.ACCUSE_STATUS_NO).
                    where(QUESTION.ID.eq(id)).execute()
        );
    }

    @Override
    public void smartLabelUpdate(String id, Integer status) {
        questionDao.execute(r->
                r.update(QUESTION).
                        set(QUESTION.TOPIC_STATUS, status).
                        where(QUESTION.ID.eq(id)).execute()
        );
    }

    @Override
    public void setEnvironment(Environment environment) {
        hours = environment.getProperty("red.hours", long.class, 7200000L);
    }
}
